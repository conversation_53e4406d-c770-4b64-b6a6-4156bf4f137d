# This file was generated using idf.py save-defconfig. It can be edited manually.
# Espressif IoT Development Framework (ESP-IDF) 5.3.0 Project Minimal Configuration
#
CONFIG_IDF_TARGET="esp32s3"
CONFIG_ESPTOOLPY_FLASHMODE_QIO=y
CONFIG_ESPTOOLPY_FLASHSIZE_16MB=y
CONFIG_SPIRAM=y
CONFIG_SPIRAM_MODE_OCT=y
CONFIG_SPIRAM_SPEED_80M=y
CONFIG_ESP32S3_INSTRUCTION_CACHE_32KB=y
CONFIG_ESP32S3_DATA_CACHE_64KB=y
CONFIG_ESP32S3_DATA_CACHE_LINE_64B=y
CONFIG_ESP_SYSTEM_PANIC_PRINT_HALT=y
CONFIG_ESP_CONSOLE_USB_SERIAL_JTAG=y
CONFIG_ESP_INT_WDT=n
CONFIG_ESP_TASK_WDT_EN=n
CONFIG_BSP_LCD_DRAW_BUF_HEIGHT=10
CONFIG_LV_CONF_MINIMAL=y
CONFIG_LV_MEM_CUSTOM=y
CONFIG_LV_USE_ASSERT_NULL=y
CONFIG_LV_USE_ASSERT_MALLOC=y
CONFIG_LV_USE_PERF_MONITOR=y
CONFIG_LV_FONT_UNSCII_8=n
CONFIG_LV_FONT_DEFAULT_MONTSERRAT_14=y
CONFIG_LV_TXT_ENC_UTF8=y
CONFIG_LV_USE_ARC=y
CONFIG_LV_USE_BTN=y
CONFIG_LV_USE_BTNMATRIX=y
CONFIG_LV_USE_CANVAS=y
CONFIG_LV_USE_CHECKBOX=y
CONFIG_LV_USE_DROPDOWN=y
CONFIG_LV_USE_LINE=y
CONFIG_LV_USE_ROLLER=y
CONFIG_LV_USE_SLIDER=y
CONFIG_LV_USE_SWITCH=y
CONFIG_LV_USE_TEXTAREA=y
CONFIG_LV_USE_TABLE=y
CONFIG_LV_USE_ANIMIMG=y
CONFIG_LV_USE_CALENDAR=y
CONFIG_LV_USE_CHART=y
CONFIG_LV_USE_COLORWHEEL=y
CONFIG_LV_USE_IMGBTN=y
CONFIG_LV_USE_KEYBOARD=y
CONFIG_LV_USE_LED=y
CONFIG_LV_USE_LIST=y
CONFIG_LV_USE_MENU=y
CONFIG_LV_USE_METER=y
CONFIG_LV_USE_MSGBOX=y
CONFIG_LV_USE_SPAN=y
CONFIG_LV_USE_SPINBOX=y
CONFIG_LV_USE_SPINNER=y
CONFIG_LV_USE_TABVIEW=y
CONFIG_LV_USE_TILEVIEW=y
CONFIG_LV_USE_WIN=y
CONFIG_LV_USE_THEME_DEFAULT=y
CONFIG_LV_USE_THEME_BASIC=y
CONFIG_LV_USE_FLEX=y
CONFIG_LV_USE_GRID=y
CONFIG_LV_USE_FS_POSIX=y
CONFIG_LV_FS_POSIX_LETTER=83
CONFIG_LV_USE_PNG=y
CONFIG_LV_USE_BMP=y
CONFIG_LV_USE_SJPG=y
CONFIG_LV_USE_GIF=y
CONFIG_LV_USE_SNAPSHOT=y
CONFIG_LV_BUILD_EXAMPLES=y
