# Implementation Plan

- [x] 1. 在CAN通信模块中添加RPDO发送功能


  - 在canopen.h中声明canopen_send_rpdo函数
  - 在canopen.c中实现RPDO消息构造和发送逻辑
  - 实现目标值的大端序数据打包
  - 添加发送错误处理和日志记录
  - _Requirements: 1.3, 4.1, 4.2, 4.3, 4.4_

- [ ] 2. 实现发送按钮的事件处理函数








  - 在ui_setup_wave.c中添加Wave_Page_btn_send_event_handler函数
  - 实现滑块值获取逻辑（使用lv_slider_get_value）
  - 实现下拉框选择获取逻辑（使用lv_dropdown_get_selected）
  - 添加UI对象有效性检查和错误处理
  - _Requirements: 1.1, 1.2, 3.3_

- [ ] 3. 实现控制模式到RPDO类型的映射逻辑
  - 创建模式索引到RPDO类型的映射函数
  - 实现0x180-0x184的RPDO类型分配逻辑
  - 添加未知模式的默认处理（默认为力矩环0x180）
  - 添加模式映射的日志记录
  - _Requirements: 2.1, 2.2, 2.3, 2.4, 2.5_

- [ ] 4. 集成UI事件处理和CAN发送功能
  - 在事件处理函数中调用canopen_send_rpdo函数
  - 传递正确的RPDO类型、节点ID和目标值参数
  - 实现发送成功和失败的日志记录
  - 添加按钮点击事件的日志记录
  - _Requirements: 1.4, 3.1, 3.2, 3.4_

- [ ] 5. 在Wave页面初始化中绑定发送按钮事件
  - 修改events_init_Wave_Page函数
  - 添加Wave_Page_btn_send的事件回调绑定
  - 确保事件绑定在UI对象创建之后执行
  - 验证事件绑定的正确性
  - _Requirements: 1.1_

- [ ] 6. 添加错误处理和日志记录功能
  - 实现CAN发送超时处理（10ms超时）
  - 添加详细的成功和失败日志格式
  - 实现UI组件访问错误的安全处理
  - 添加数据获取失败的默认值处理
  - _Requirements: 3.1, 3.2, 3.3, 3.4_
