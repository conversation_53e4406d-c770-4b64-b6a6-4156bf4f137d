/**
 * @file lv_example_libs.h
 *
 */

#ifndef LV_EXAMPLE_LIBS_H
#define LV_EXAMPLE_LIBS_H

#ifdef __cplusplus
extern "C" {
#endif

/*********************
 *      INCLUDES
 *********************/
#include "bmp/lv_example_bmp.h"
#include "gif/lv_example_gif.h"
#include "png/lv_example_png.h"
#include "sjpg/lv_example_sjpg.h"
#include "qrcode/lv_example_qrcode.h"
#include "freetype/lv_example_freetype.h"
#include "rlottie/lv_example_rlottie.h"
#include "ffmpeg/lv_example_ffmpeg.h"

/*********************
 *      DEFINES
 *********************/

/**********************
 *      TYPEDEFS
 **********************/

/**********************
 * GLOBAL PROTOTYPES
 **********************/

/**********************
 *      MACROS
 **********************/

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif /*LV_EXAMPLE_LIBS_H*/
