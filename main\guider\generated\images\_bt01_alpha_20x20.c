#if defined(LV_LVGL_H_INCLUDE_SIMPLE)
#include "lvgl.h"
#else
#include "lvgl/lvgl.h"
#endif


#ifndef LV_ATTRIBUTE_MEM_ALIGN
#define LV_ATTRIBUTE_MEM_ALIGN
#endif

#ifndef LV_ATTRIBUTE_IMG__BT01_ALPHA_20X20
#define LV_ATTRIBUTE_IMG__BT01_ALPHA_20X20
#endif

const LV_ATTRIBUTE_MEM_ALIGN LV_ATTRIBUTE_LARGE_CONST LV_ATTRIBUTE_IMG__BT01_ALPHA_20X20 uint8_t _bt01_alpha_20x20_map[] = {
#if LV_COLOR_DEPTH == 1 || LV_COLOR_DEPTH == 8
  /*Pixel format: Alpha 8 bit, Red: 3 bit, Green: 3 bit, Blue: 2 bit*/
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x37, 0x08, 0x37, 0xf5, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x37, 0x28, 0x37, 0xff, 0x37, 0xef, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x37, 0x28, 0x37, 0xff, 0x37, 0xe3, 0x37, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x37, 0x60, 0x33, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x37, 0x28, 0x37, 0xff, 0x17, 0x07, 0x37, 0xe3, 0x37, 0xef, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x37, 0x7f, 0x37, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x37, 0xf7, 0x37, 0xe7, 0x00, 0x00, 0x00, 0x00, 0x37, 0x28, 0x37, 0xff, 0x00, 0x00, 0x00, 0x00, 0x37, 0xe3, 0x37, 0xff, 0x00, 0x00, 0x00, 0x00, 0x37, 0x36, 0x00, 0x00, 0x00, 0x00, 0x37, 0xe2, 0x37, 0xcc, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x37, 0x0d, 0x37, 0xf7, 0x37, 0xdf, 0x00, 0x00, 0x37, 0x28, 0x37, 0xff, 0x00, 0x00, 0x00, 0x00, 0x37, 0xf7, 0x37, 0xe7, 0x00, 0x00, 0x00, 0x00, 0x37, 0xff, 0x37, 0x34, 0x00, 0x00, 0x37, 0x08, 0x37, 0xff, 0x17, 0x21, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x37, 0xf7, 0x37, 0xe7, 0x37, 0x28, 0x37, 0xff, 0x37, 0x0d, 0x37, 0xf7, 0x37, 0xdf, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x37, 0x50, 0x37, 0xff, 0x00, 0x00, 0x00, 0x00, 0x37, 0xf5, 0x37, 0x79, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x37, 0x0d, 0x37, 0xf7, 0x37, 0xf1, 0x37, 0xff, 0x37, 0xf7, 0x37, 0xe7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1f, 0x01, 0x37, 0xff, 0x33, 0x09, 0x00, 0x00, 0x37, 0x4d, 0x37, 0xff, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x37, 0xf7, 0x37, 0xff, 0x37, 0xdf, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x37, 0xe4, 0x37, 0x90, 0x00, 0x00, 0x37, 0x40, 0x37, 0xff, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x37, 0xf7, 0x37, 0xff, 0x37, 0xdf, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x37, 0xe4, 0x37, 0x90, 0x00, 0x00, 0x37, 0x40, 0x37, 0xff, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x37, 0x0d, 0x37, 0xf7, 0x37, 0xf1, 0x37, 0xff, 0x37, 0xf7, 0x37, 0xe7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1f, 0x01, 0x37, 0xff, 0x33, 0x08, 0x00, 0x00, 0x37, 0x4d, 0x37, 0xff, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x37, 0xf7, 0x37, 0xe7, 0x37, 0x28, 0x37, 0xff, 0x37, 0x0d, 0x37, 0xf8, 0x37, 0xdf, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x37, 0x50, 0x37, 0xff, 0x00, 0x00, 0x00, 0x00, 0x37, 0xf5, 0x37, 0x79, 0x00, 0x00, 
  0x00, 0x00, 0x37, 0x0d, 0x37, 0xf7, 0x37, 0xdf, 0x00, 0x00, 0x37, 0x28, 0x37, 0xff, 0x00, 0x00, 0x00, 0x00, 0x37, 0xf9, 0x37, 0xe7, 0x00, 0x00, 0x00, 0x00, 0x37, 0xff, 0x37, 0x36, 0x00, 0x00, 0x37, 0x08, 0x37, 0xff, 0x17, 0x21, 0x00, 0x00, 
  0x00, 0x00, 0x37, 0xf7, 0x37, 0xe7, 0x00, 0x00, 0x00, 0x00, 0x37, 0x28, 0x37, 0xff, 0x00, 0x00, 0x00, 0x00, 0x37, 0xdd, 0x37, 0xff, 0x00, 0x00, 0x00, 0x00, 0x37, 0x38, 0x00, 0x00, 0x00, 0x00, 0x37, 0xe2, 0x37, 0xcc, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x13, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x37, 0x28, 0x37, 0xff, 0x13, 0x06, 0x37, 0xe0, 0x37, 0xef, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x37, 0x83, 0x37, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x37, 0x28, 0x37, 0xff, 0x37, 0xe2, 0x37, 0xff, 0x03, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x37, 0x60, 0x13, 0x12, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x37, 0x28, 0x37, 0xff, 0x37, 0xf0, 0x1f, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x33, 0x09, 0x37, 0xf6, 0x12, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
#endif
#if LV_COLOR_DEPTH == 16 && LV_COLOR_16_SWAP == 0
  /*Pixel format: Alpha 8 bit, Red: 5 bit, Green: 6 bit, Blue: 5 bit*/
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1c, 0x25, 0x08, 0xdc, 0x14, 0xf5, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xbb, 0x14, 0x28, 0xdb, 0x14, 0xff, 0xdb, 0x14, 0xef, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xbb, 0x14, 0x28, 0xdb, 0x14, 0xff, 0xbb, 0x14, 0xe3, 0xdb, 0x14, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xbb, 0x14, 0x60, 0x9c, 0x14, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xbb, 0x14, 0x28, 0xdb, 0x14, 0xff, 0xbb, 0x04, 0x07, 0xbb, 0x14, 0xe3, 0xdb, 0x14, 0xef, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdb, 0x14, 0x7f, 0xdb, 0x14, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0xdb, 0x14, 0xf7, 0xdb, 0x14, 0xe7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xbb, 0x14, 0x28, 0xdb, 0x14, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xbb, 0x14, 0xe3, 0xdb, 0x14, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdc, 0x14, 0x36, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdb, 0x14, 0xe2, 0xdc, 0x14, 0xcc, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0xfb, 0x1c, 0x0d, 0xdb, 0x14, 0xf7, 0xdb, 0x14, 0xdf, 0x00, 0x00, 0x00, 0xbb, 0x14, 0x28, 0xdb, 0x14, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdb, 0x14, 0xf7, 0xdb, 0x14, 0xe7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdb, 0x14, 0xff, 0xbb, 0x1c, 0x34, 0x00, 0x00, 0x00, 0x1c, 0x25, 0x08, 0xdb, 0x14, 0xff, 0xbb, 0x14, 0x21, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdb, 0x14, 0xf7, 0xdb, 0x14, 0xe7, 0xbb, 0x14, 0x28, 0xdb, 0x14, 0xff, 0xfb, 0x1c, 0x0d, 0xdb, 0x14, 0xf7, 0xdb, 0x14, 0xdf, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xbb, 0x14, 0x50, 0xdb, 0x14, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdc, 0x14, 0xf5, 0xdb, 0x14, 0x79, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfb, 0x1c, 0x0d, 0xdb, 0x14, 0xf7, 0xdb, 0x14, 0xf1, 0xdb, 0x14, 0xff, 0xdb, 0x14, 0xf7, 0xdb, 0x14, 0xe7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0x07, 0x01, 0xdb, 0x14, 0xff, 0x99, 0x24, 0x09, 0x00, 0x00, 0x00, 0xbb, 0x14, 0x4d, 0xdb, 0x14, 0xff, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdb, 0x14, 0xf7, 0xdb, 0x14, 0xff, 0xdb, 0x14, 0xdf, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdb, 0x14, 0xe4, 0xdc, 0x14, 0x90, 0x00, 0x00, 0x00, 0xbb, 0x14, 0x40, 0xdb, 0x14, 0xff, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdb, 0x14, 0xf7, 0xdb, 0x14, 0xff, 0xdb, 0x14, 0xdf, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdb, 0x14, 0xe4, 0xdc, 0x14, 0x90, 0x00, 0x00, 0x00, 0xbb, 0x14, 0x40, 0xdb, 0x14, 0xff, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfb, 0x1c, 0x0d, 0xdb, 0x14, 0xf7, 0xdb, 0x14, 0xf1, 0xdb, 0x14, 0xff, 0xdb, 0x14, 0xf7, 0xdb, 0x14, 0xe7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0x07, 0x01, 0xdb, 0x14, 0xff, 0x1c, 0x24, 0x08, 0x00, 0x00, 0x00, 0xbb, 0x14, 0x4d, 0xdb, 0x14, 0xff, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdb, 0x14, 0xf7, 0xdb, 0x14, 0xe7, 0xbb, 0x14, 0x28, 0xdb, 0x14, 0xff, 0xfd, 0x1c, 0x0d, 0xdb, 0x14, 0xf8, 0xdb, 0x14, 0xdf, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xbb, 0x14, 0x50, 0xdb, 0x14, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdc, 0x14, 0xf5, 0xdb, 0x14, 0x79, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0xfb, 0x1c, 0x0d, 0xdb, 0x14, 0xf7, 0xdb, 0x14, 0xdf, 0x00, 0x00, 0x00, 0xbb, 0x14, 0x28, 0xdb, 0x14, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdb, 0x14, 0xf9, 0xdb, 0x14, 0xe7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdb, 0x14, 0xff, 0xdb, 0x14, 0x36, 0x00, 0x00, 0x00, 0x1c, 0x25, 0x08, 0xdb, 0x14, 0xff, 0xbb, 0x14, 0x21, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0xdb, 0x14, 0xf7, 0xdb, 0x14, 0xe7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xbb, 0x14, 0x28, 0xdb, 0x14, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdb, 0x14, 0xdd, 0xdb, 0x14, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdb, 0x14, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdb, 0x14, 0xe2, 0xdc, 0x14, 0xcc, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x1f, 0x04, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xbb, 0x14, 0x28, 0xdb, 0x14, 0xff, 0x1b, 0x04, 0x06, 0xdc, 0x14, 0xe0, 0xdb, 0x14, 0xef, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdc, 0x14, 0x83, 0xdb, 0x14, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xbb, 0x14, 0x28, 0xdb, 0x14, 0xff, 0xdb, 0x14, 0xe2, 0xdb, 0x14, 0xff, 0x1f, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xbb, 0x14, 0x60, 0x9b, 0x14, 0x12, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xbb, 0x14, 0x28, 0xdb, 0x14, 0xff, 0xdb, 0x14, 0xf0, 0xff, 0x07, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x9c, 0x24, 0x09, 0xbb, 0x14, 0xf6, 0x10, 0x04, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
#endif
#if LV_COLOR_DEPTH == 16 && LV_COLOR_16_SWAP != 0
  /*Pixel format: Alpha 8 bit, Red: 5 bit, Green: 6 bit, Blue: 5 bit  BUT the 2  color bytes are swapped*/
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x25, 0x1c, 0x08, 0x14, 0xdc, 0xf5, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x14, 0xbb, 0x28, 0x14, 0xdb, 0xff, 0x14, 0xdb, 0xef, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x14, 0xbb, 0x28, 0x14, 0xdb, 0xff, 0x14, 0xbb, 0xe3, 0x14, 0xdb, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x14, 0xbb, 0x60, 0x14, 0x9c, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x14, 0xbb, 0x28, 0x14, 0xdb, 0xff, 0x04, 0xbb, 0x07, 0x14, 0xbb, 0xe3, 0x14, 0xdb, 0xef, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x14, 0xdb, 0x7f, 0x14, 0xdb, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x14, 0xdb, 0xf7, 0x14, 0xdb, 0xe7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x14, 0xbb, 0x28, 0x14, 0xdb, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x14, 0xbb, 0xe3, 0x14, 0xdb, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x14, 0xdc, 0x36, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x14, 0xdb, 0xe2, 0x14, 0xdc, 0xcc, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x1c, 0xfb, 0x0d, 0x14, 0xdb, 0xf7, 0x14, 0xdb, 0xdf, 0x00, 0x00, 0x00, 0x14, 0xbb, 0x28, 0x14, 0xdb, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x14, 0xdb, 0xf7, 0x14, 0xdb, 0xe7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x14, 0xdb, 0xff, 0x1c, 0xbb, 0x34, 0x00, 0x00, 0x00, 0x25, 0x1c, 0x08, 0x14, 0xdb, 0xff, 0x14, 0xbb, 0x21, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x14, 0xdb, 0xf7, 0x14, 0xdb, 0xe7, 0x14, 0xbb, 0x28, 0x14, 0xdb, 0xff, 0x1c, 0xfb, 0x0d, 0x14, 0xdb, 0xf7, 0x14, 0xdb, 0xdf, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x14, 0xbb, 0x50, 0x14, 0xdb, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x14, 0xdc, 0xf5, 0x14, 0xdb, 0x79, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1c, 0xfb, 0x0d, 0x14, 0xdb, 0xf7, 0x14, 0xdb, 0xf1, 0x14, 0xdb, 0xff, 0x14, 0xdb, 0xf7, 0x14, 0xdb, 0xe7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0xff, 0x01, 0x14, 0xdb, 0xff, 0x24, 0x99, 0x09, 0x00, 0x00, 0x00, 0x14, 0xbb, 0x4d, 0x14, 0xdb, 0xff, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x14, 0xdb, 0xf7, 0x14, 0xdb, 0xff, 0x14, 0xdb, 0xdf, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x14, 0xdb, 0xe4, 0x14, 0xdc, 0x90, 0x00, 0x00, 0x00, 0x14, 0xbb, 0x40, 0x14, 0xdb, 0xff, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x14, 0xdb, 0xf7, 0x14, 0xdb, 0xff, 0x14, 0xdb, 0xdf, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x14, 0xdb, 0xe4, 0x14, 0xdc, 0x90, 0x00, 0x00, 0x00, 0x14, 0xbb, 0x40, 0x14, 0xdb, 0xff, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1c, 0xfb, 0x0d, 0x14, 0xdb, 0xf7, 0x14, 0xdb, 0xf1, 0x14, 0xdb, 0xff, 0x14, 0xdb, 0xf7, 0x14, 0xdb, 0xe7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0xff, 0x01, 0x14, 0xdb, 0xff, 0x24, 0x1c, 0x08, 0x00, 0x00, 0x00, 0x14, 0xbb, 0x4d, 0x14, 0xdb, 0xff, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x14, 0xdb, 0xf7, 0x14, 0xdb, 0xe7, 0x14, 0xbb, 0x28, 0x14, 0xdb, 0xff, 0x1c, 0xfd, 0x0d, 0x14, 0xdb, 0xf8, 0x14, 0xdb, 0xdf, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x14, 0xbb, 0x50, 0x14, 0xdb, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x14, 0xdc, 0xf5, 0x14, 0xdb, 0x79, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x1c, 0xfb, 0x0d, 0x14, 0xdb, 0xf7, 0x14, 0xdb, 0xdf, 0x00, 0x00, 0x00, 0x14, 0xbb, 0x28, 0x14, 0xdb, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x14, 0xdb, 0xf9, 0x14, 0xdb, 0xe7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x14, 0xdb, 0xff, 0x14, 0xdb, 0x36, 0x00, 0x00, 0x00, 0x25, 0x1c, 0x08, 0x14, 0xdb, 0xff, 0x14, 0xbb, 0x21, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x14, 0xdb, 0xf7, 0x14, 0xdb, 0xe7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x14, 0xbb, 0x28, 0x14, 0xdb, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x14, 0xdb, 0xdd, 0x14, 0xdb, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x14, 0xdb, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x14, 0xdb, 0xe2, 0x14, 0xdc, 0xcc, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x04, 0x1f, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x14, 0xbb, 0x28, 0x14, 0xdb, 0xff, 0x04, 0x1b, 0x06, 0x14, 0xdc, 0xe0, 0x14, 0xdb, 0xef, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x14, 0xdc, 0x83, 0x14, 0xdb, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x14, 0xbb, 0x28, 0x14, 0xdb, 0xff, 0x14, 0xdb, 0xe2, 0x14, 0xdb, 0xff, 0x00, 0x1f, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x14, 0xbb, 0x60, 0x14, 0x9b, 0x12, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x14, 0xbb, 0x28, 0x14, 0xdb, 0xff, 0x14, 0xdb, 0xf0, 0x07, 0xff, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x24, 0x9c, 0x09, 0x14, 0xbb, 0xf6, 0x04, 0x10, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
#endif
#if LV_COLOR_DEPTH == 32
  /*Pixel format: Alpha 8 bit, Red: 8 bit, Green: 8 bit, Blue: 8 bit*/
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdf, 0x9f, 0x20, 0x08, 0xdc, 0x96, 0x12, 0xf5, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xd9, 0x93, 0x13, 0x28, 0xdb, 0x96, 0x12, 0xff, 0xdb, 0x96, 0x12, 0xef, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xd9, 0x93, 0x13, 0x28, 0xdb, 0x96, 0x12, 0xff, 0xdb, 0x95, 0x12, 0xe3, 0xdb, 0x96, 0x12, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xda, 0x95, 0x10, 0x60, 0xdf, 0x8f, 0x10, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xd9, 0x93, 0x13, 0x28, 0xdb, 0x96, 0x12, 0xff, 0xdb, 0x92, 0x00, 0x07, 0xdb, 0x95, 0x12, 0xe3, 0xdb, 0x96, 0x12, 0xef, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdb, 0x97, 0x12, 0x7f, 0xdb, 0x96, 0x12, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0xdb, 0x96, 0x12, 0xf7, 0xdb, 0x96, 0x12, 0xe7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xd9, 0x93, 0x13, 0x28, 0xdb, 0x96, 0x12, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdb, 0x95, 0x12, 0xe3, 0xdb, 0x96, 0x12, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xde, 0x97, 0x13, 0x36, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdb, 0x96, 0x12, 0xe2, 0xdc, 0x96, 0x11, 0xcc, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0xd8, 0x9d, 0x14, 0x0d, 0xdb, 0x96, 0x12, 0xf7, 0xda, 0x96, 0x12, 0xdf, 0x00, 0x00, 0x00, 0x00, 0xd9, 0x93, 0x13, 0x28, 0xdb, 0x96, 0x12, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdb, 0x96, 0x12, 0xf7, 0xdb, 0x96, 0x12, 0xe7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdb, 0x96, 0x12, 0xff, 0xd8, 0x93, 0x14, 0x34, 0x00, 0x00, 0x00, 0x00, 0xdf, 0x9f, 0x20, 0x08, 0xdb, 0x96, 0x12, 0xff, 0xd8, 0x93, 0x0f, 0x21, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdb, 0x96, 0x12, 0xf7, 0xdb, 0x96, 0x12, 0xe7, 0xd9, 0x93, 0x13, 0x28, 0xdb, 0x96, 0x12, 0xff, 0xd8, 0x9d, 0x14, 0x0d, 0xdb, 0x96, 0x12, 0xf7, 0xda, 0x96, 0x12, 0xdf, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xd9, 0x93, 0x13, 0x50, 0xdb, 0x96, 0x12, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdc, 0x96, 0x12, 0xf5, 0xdb, 0x96, 0x11, 0x79, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xd8, 0x9d, 0x14, 0x0d, 0xdb, 0x96, 0x12, 0xf7, 0xdb, 0x96, 0x12, 0xf1, 0xdb, 0x96, 0x12, 0xff, 0xdb, 0x96, 0x12, 0xf7, 0xdb, 0x96, 0x12, 0xe7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0x00, 0x01, 0xdb, 0x96, 0x12, 0xff, 0xc6, 0x8e, 0x1c, 0x09, 0x00, 0x00, 0x00, 0x00, 0xdb, 0x95, 0x11, 0x4d, 0xdb, 0x96, 0x12, 0xff, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdb, 0x96, 0x12, 0xf7, 0xdb, 0x96, 0x12, 0xff, 0xda, 0x96, 0x12, 0xdf, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdb, 0x96, 0x12, 0xe4, 0xdc, 0x97, 0x12, 0x90, 0x00, 0x00, 0x00, 0x00, 0xdb, 0x93, 0x10, 0x40, 0xdb, 0x96, 0x12, 0xff, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdb, 0x96, 0x12, 0xf7, 0xdb, 0x96, 0x12, 0xff, 0xda, 0x96, 0x12, 0xdf, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdb, 0x96, 0x12, 0xe4, 0xdc, 0x97, 0x12, 0x90, 0x00, 0x00, 0x00, 0x00, 0xdb, 0x93, 0x10, 0x40, 0xdb, 0x96, 0x12, 0xff, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xd8, 0x9d, 0x14, 0x0d, 0xdb, 0x96, 0x12, 0xf7, 0xdb, 0x96, 0x12, 0xf1, 0xdb, 0x96, 0x12, 0xff, 0xdb, 0x96, 0x12, 0xf7, 0xdb, 0x96, 0x12, 0xe7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0x00, 0x01, 0xdb, 0x96, 0x12, 0xff, 0xdf, 0x80, 0x20, 0x08, 0x00, 0x00, 0x00, 0x00, 0xdb, 0x95, 0x11, 0x4d, 0xdb, 0x96, 0x12, 0xff, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdb, 0x96, 0x12, 0xf7, 0xdb, 0x96, 0x12, 0xe7, 0xd9, 0x93, 0x13, 0x28, 0xdb, 0x96, 0x12, 0xff, 0xeb, 0x9d, 0x14, 0x0d, 0xdb, 0x96, 0x11, 0xf8, 0xda, 0x96, 0x12, 0xdf, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xd9, 0x93, 0x13, 0x50, 0xdb, 0x96, 0x12, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdc, 0x96, 0x12, 0xf5, 0xdb, 0x96, 0x11, 0x79, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0xd8, 0x9d, 0x14, 0x0d, 0xdb, 0x96, 0x12, 0xf7, 0xda, 0x96, 0x12, 0xdf, 0x00, 0x00, 0x00, 0x00, 0xd9, 0x93, 0x13, 0x28, 0xdb, 0x96, 0x12, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdb, 0x96, 0x11, 0xf9, 0xdb, 0x96, 0x12, 0xe7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdb, 0x96, 0x12, 0xff, 0xd9, 0x97, 0x13, 0x36, 0x00, 0x00, 0x00, 0x00, 0xdf, 0x9f, 0x20, 0x08, 0xdb, 0x96, 0x12, 0xff, 0xd8, 0x93, 0x0f, 0x21, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0xdb, 0x96, 0x12, 0xf7, 0xdb, 0x96, 0x12, 0xe7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xd9, 0x93, 0x13, 0x28, 0xdb, 0x96, 0x12, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdb, 0x96, 0x12, 0xdd, 0xdb, 0x96, 0x12, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdb, 0x96, 0x12, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdb, 0x96, 0x12, 0xe2, 0xdc, 0x96, 0x11, 0xcc, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0xff, 0x80, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xd9, 0x93, 0x13, 0x28, 0xdb, 0x96, 0x12, 0xff, 0xd4, 0x80, 0x00, 0x06, 0xdc, 0x96, 0x12, 0xe0, 0xdb, 0x96, 0x12, 0xef, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdc, 0x96, 0x12, 0x83, 0xdb, 0x96, 0x12, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xd9, 0x93, 0x13, 0x28, 0xdb, 0x96, 0x12, 0xff, 0xdb, 0x96, 0x12, 0xe2, 0xdb, 0x96, 0x12, 0xff, 0xff, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xda, 0x95, 0x10, 0x60, 0xd5, 0x8e, 0x0e, 0x12, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xd9, 0x93, 0x13, 0x28, 0xdb, 0x96, 0x12, 0xff, 0xdb, 0x96, 0x12, 0xf0, 0xff, 0xff, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe3, 0x8e, 0x1c, 0x09, 0xdb, 0x95, 0x12, 0xf6, 0x80, 0x80, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
#endif
};

const lv_img_dsc_t _bt01_alpha_20x20 = {
  .header.cf = LV_IMG_CF_TRUE_COLOR_ALPHA,
  .header.always_zero = 0,
  .header.reserved = 0,
  .header.w = 20,
  .header.h = 20,
  .data_size = 400 * LV_IMG_PX_SIZE_ALPHA_BYTE,
  .data = _bt01_alpha_20x20_map,
};
