/*
* Copyright 2025 NXP
* NXP Proprietary. This software is owned or controlled by NXP and may only be used strictly in
* accordance with the applicable license terms. By expressly accepting such terms or by downloading, installing,
* activating and/or otherwise using the software, you are agreeing that you have read, and that you agree to
* comply with and are bound by, such license terms.  If you do not agree to be bound by the applicable license
* terms, then you may not retain, install, activate or otherwise use the software.
*/

#include "lvgl.h"
#include <stdio.h>
#include "gui_guider.h"
#include "events_init.h"
#include "widgets_init.h"
#include "custom.h"



void setup_scr_Settings_Page(lv_ui *ui)
{
    //Write codes Settings_Page
    ui->Settings_Page = lv_obj_create(NULL);
    lv_obj_set_size(ui->Settings_Page, 320, 240);
    lv_obj_set_scrollbar_mode(ui->Settings_Page, LV_SCROLLBAR_MODE_OFF);

    //Write style for Settings_Page, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->Settings_Page, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->Settings_Page, lv_color_hex(0x242424), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->Settings_Page, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes Settings_Page_imgbtn_1
    ui->Settings_Page_imgbtn_1 = lv_imgbtn_create(ui->Settings_Page);
    lv_obj_add_flag(ui->Settings_Page_imgbtn_1, LV_OBJ_FLAG_CHECKABLE);
    lv_imgbtn_set_src(ui->Settings_Page_imgbtn_1, LV_IMGBTN_STATE_RELEASED, NULL, &_back01_ico_alpha_26x26, NULL);
    ui->Settings_Page_imgbtn_1_label = lv_label_create(ui->Settings_Page_imgbtn_1);
    lv_label_set_text(ui->Settings_Page_imgbtn_1_label, "");
    lv_label_set_long_mode(ui->Settings_Page_imgbtn_1_label, LV_LABEL_LONG_WRAP);
    lv_obj_align(ui->Settings_Page_imgbtn_1_label, LV_ALIGN_CENTER, 0, 0);
    lv_obj_set_style_pad_all(ui->Settings_Page_imgbtn_1, 0, LV_STATE_DEFAULT);
    lv_obj_set_pos(ui->Settings_Page_imgbtn_1, 4, 5);
    lv_obj_set_size(ui->Settings_Page_imgbtn_1, 26, 26);

    //Write style for Settings_Page_imgbtn_1, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_text_color(ui->Settings_Page_imgbtn_1, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->Settings_Page_imgbtn_1, &lv_font_montserratMedium_32, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->Settings_Page_imgbtn_1, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->Settings_Page_imgbtn_1, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->Settings_Page_imgbtn_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_clip_corner(ui->Settings_Page_imgbtn_1, true, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->Settings_Page_imgbtn_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write style for Settings_Page_imgbtn_1, Part: LV_PART_MAIN, State: LV_STATE_PRESSED.
    lv_obj_set_style_img_recolor_opa(ui->Settings_Page_imgbtn_1, 0, LV_PART_MAIN|LV_STATE_PRESSED);
    lv_obj_set_style_img_opa(ui->Settings_Page_imgbtn_1, 255, LV_PART_MAIN|LV_STATE_PRESSED);
    lv_obj_set_style_text_color(ui->Settings_Page_imgbtn_1, lv_color_hex(0xFF33FF), LV_PART_MAIN|LV_STATE_PRESSED);
    lv_obj_set_style_text_font(ui->Settings_Page_imgbtn_1, &lv_font_montserratMedium_10, LV_PART_MAIN|LV_STATE_PRESSED);
    lv_obj_set_style_text_opa(ui->Settings_Page_imgbtn_1, 255, LV_PART_MAIN|LV_STATE_PRESSED);
    lv_obj_set_style_shadow_width(ui->Settings_Page_imgbtn_1, 0, LV_PART_MAIN|LV_STATE_PRESSED);

    //Write style for Settings_Page_imgbtn_1, Part: LV_PART_MAIN, State: LV_STATE_CHECKED.
    lv_obj_set_style_img_recolor_opa(ui->Settings_Page_imgbtn_1, 0, LV_PART_MAIN|LV_STATE_CHECKED);
    lv_obj_set_style_img_opa(ui->Settings_Page_imgbtn_1, 255, LV_PART_MAIN|LV_STATE_CHECKED);
    lv_obj_set_style_text_color(ui->Settings_Page_imgbtn_1, lv_color_hex(0xFF33FF), LV_PART_MAIN|LV_STATE_CHECKED);
    lv_obj_set_style_text_font(ui->Settings_Page_imgbtn_1, &lv_font_montserratMedium_10, LV_PART_MAIN|LV_STATE_CHECKED);
    lv_obj_set_style_text_opa(ui->Settings_Page_imgbtn_1, 255, LV_PART_MAIN|LV_STATE_CHECKED);
    lv_obj_set_style_shadow_width(ui->Settings_Page_imgbtn_1, 0, LV_PART_MAIN|LV_STATE_CHECKED);

    //Write style for Settings_Page_imgbtn_1, Part: LV_PART_MAIN, State: LV_IMGBTN_STATE_RELEASED.
    lv_obj_set_style_img_recolor_opa(ui->Settings_Page_imgbtn_1, 0, LV_PART_MAIN|LV_IMGBTN_STATE_RELEASED);
    lv_obj_set_style_img_opa(ui->Settings_Page_imgbtn_1, 255, LV_PART_MAIN|LV_IMGBTN_STATE_RELEASED);

    //Write codes Settings_Page_list_1
    ui->Settings_Page_list_1 = lv_list_create(ui->Settings_Page);
    ui->Settings_Page_list_1_item0 = lv_list_add_btn(ui->Settings_Page_list_1, &_bt01_alpha_20x20, "网络连接");
    lv_obj_set_pos(ui->Settings_Page_list_1, 4, 37);
    lv_obj_set_size(ui->Settings_Page_list_1, 314, 197);
    lv_obj_set_scrollbar_mode(ui->Settings_Page_list_1, LV_SCROLLBAR_MODE_OFF);

    //Write style state: LV_STATE_DEFAULT for &style_Settings_Page_list_1_main_main_default
    static lv_style_t style_Settings_Page_list_1_main_main_default;
    ui_init_style(&style_Settings_Page_list_1_main_main_default);

    lv_style_set_pad_top(&style_Settings_Page_list_1_main_main_default, 0);
    lv_style_set_pad_left(&style_Settings_Page_list_1_main_main_default, 0);
    lv_style_set_pad_right(&style_Settings_Page_list_1_main_main_default, 0);
    lv_style_set_pad_bottom(&style_Settings_Page_list_1_main_main_default, 0);
    lv_style_set_bg_opa(&style_Settings_Page_list_1_main_main_default, 255);
    lv_style_set_bg_color(&style_Settings_Page_list_1_main_main_default, lv_color_hex(0x282294));
    lv_style_set_bg_grad_dir(&style_Settings_Page_list_1_main_main_default, LV_GRAD_DIR_VER);
    lv_style_set_bg_grad_color(&style_Settings_Page_list_1_main_main_default, lv_color_hex(0x292222));
    lv_style_set_bg_main_stop(&style_Settings_Page_list_1_main_main_default, 0);
    lv_style_set_bg_grad_stop(&style_Settings_Page_list_1_main_main_default, 255);
    lv_style_set_border_width(&style_Settings_Page_list_1_main_main_default, 0);
    lv_style_set_radius(&style_Settings_Page_list_1_main_main_default, 0);
    lv_style_set_shadow_width(&style_Settings_Page_list_1_main_main_default, 0);
    lv_obj_add_style(ui->Settings_Page_list_1, &style_Settings_Page_list_1_main_main_default, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write style state: LV_STATE_FOCUSED for &style_Settings_Page_list_1_main_main_focused
    static lv_style_t style_Settings_Page_list_1_main_main_focused;
    ui_init_style(&style_Settings_Page_list_1_main_main_focused);

    lv_style_set_pad_top(&style_Settings_Page_list_1_main_main_focused, 5);
    lv_style_set_pad_left(&style_Settings_Page_list_1_main_main_focused, 5);
    lv_style_set_pad_right(&style_Settings_Page_list_1_main_main_focused, 5);
    lv_style_set_pad_bottom(&style_Settings_Page_list_1_main_main_focused, 5);
    lv_style_set_bg_opa(&style_Settings_Page_list_1_main_main_focused, 255);
    lv_style_set_bg_color(&style_Settings_Page_list_1_main_main_focused, lv_color_hex(0xffffff));
    lv_style_set_bg_grad_dir(&style_Settings_Page_list_1_main_main_focused, LV_GRAD_DIR_NONE);
    lv_style_set_border_width(&style_Settings_Page_list_1_main_main_focused, 1);
    lv_style_set_border_opa(&style_Settings_Page_list_1_main_main_focused, 255);
    lv_style_set_border_color(&style_Settings_Page_list_1_main_main_focused, lv_color_hex(0xe1e6ee));
    lv_style_set_border_side(&style_Settings_Page_list_1_main_main_focused, LV_BORDER_SIDE_FULL);
    lv_style_set_radius(&style_Settings_Page_list_1_main_main_focused, 3);
    lv_style_set_shadow_width(&style_Settings_Page_list_1_main_main_focused, 0);
    lv_obj_add_style(ui->Settings_Page_list_1, &style_Settings_Page_list_1_main_main_focused, LV_PART_MAIN|LV_STATE_FOCUSED);

    //Write style state: LV_STATE_DEFAULT for &style_Settings_Page_list_1_main_scrollbar_default
    static lv_style_t style_Settings_Page_list_1_main_scrollbar_default;
    ui_init_style(&style_Settings_Page_list_1_main_scrollbar_default);

    lv_style_set_radius(&style_Settings_Page_list_1_main_scrollbar_default, 3);
    lv_style_set_bg_opa(&style_Settings_Page_list_1_main_scrollbar_default, 255);
    lv_style_set_bg_color(&style_Settings_Page_list_1_main_scrollbar_default, lv_color_hex(0xffffff));
    lv_style_set_bg_grad_dir(&style_Settings_Page_list_1_main_scrollbar_default, LV_GRAD_DIR_NONE);
    lv_obj_add_style(ui->Settings_Page_list_1, &style_Settings_Page_list_1_main_scrollbar_default, LV_PART_SCROLLBAR|LV_STATE_DEFAULT);

    //Write style state: LV_STATE_DEFAULT for &style_Settings_Page_list_1_extra_btns_main_default
    static lv_style_t style_Settings_Page_list_1_extra_btns_main_default;
    ui_init_style(&style_Settings_Page_list_1_extra_btns_main_default);

    lv_style_set_pad_top(&style_Settings_Page_list_1_extra_btns_main_default, 3);
    lv_style_set_pad_left(&style_Settings_Page_list_1_extra_btns_main_default, 8);
    lv_style_set_pad_right(&style_Settings_Page_list_1_extra_btns_main_default, 0);
    lv_style_set_pad_bottom(&style_Settings_Page_list_1_extra_btns_main_default, 3);
    lv_style_set_border_width(&style_Settings_Page_list_1_extra_btns_main_default, 1);
    lv_style_set_border_opa(&style_Settings_Page_list_1_extra_btns_main_default, 255);
    lv_style_set_border_color(&style_Settings_Page_list_1_extra_btns_main_default, lv_color_hex(0x2F92DA));
    lv_style_set_border_side(&style_Settings_Page_list_1_extra_btns_main_default, LV_BORDER_SIDE_BOTTOM);
    lv_style_set_text_color(&style_Settings_Page_list_1_extra_btns_main_default, lv_color_hex(0xfef6ea));
    lv_style_set_text_font(&style_Settings_Page_list_1_extra_btns_main_default, &lv_font_HarmonyOS_Sans_SC_Regular_14);
    lv_style_set_text_opa(&style_Settings_Page_list_1_extra_btns_main_default, 255);
    lv_style_set_radius(&style_Settings_Page_list_1_extra_btns_main_default, 0);
    lv_style_set_bg_opa(&style_Settings_Page_list_1_extra_btns_main_default, 0);
    lv_obj_add_style(ui->Settings_Page_list_1_item0, &style_Settings_Page_list_1_extra_btns_main_default, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write style state: LV_STATE_PRESSED for &style_Settings_Page_list_1_extra_btns_main_pressed
    static lv_style_t style_Settings_Page_list_1_extra_btns_main_pressed;
    ui_init_style(&style_Settings_Page_list_1_extra_btns_main_pressed);

    lv_style_set_pad_top(&style_Settings_Page_list_1_extra_btns_main_pressed, 3);
    lv_style_set_pad_left(&style_Settings_Page_list_1_extra_btns_main_pressed, 8);
    lv_style_set_pad_right(&style_Settings_Page_list_1_extra_btns_main_pressed, 0);
    lv_style_set_pad_bottom(&style_Settings_Page_list_1_extra_btns_main_pressed, 3);
    lv_style_set_border_width(&style_Settings_Page_list_1_extra_btns_main_pressed, 1);
    lv_style_set_border_opa(&style_Settings_Page_list_1_extra_btns_main_pressed, 255);
    lv_style_set_border_color(&style_Settings_Page_list_1_extra_btns_main_pressed, lv_color_hex(0xe1e6ee));
    lv_style_set_border_side(&style_Settings_Page_list_1_extra_btns_main_pressed, LV_BORDER_SIDE_FULL);
    lv_style_set_radius(&style_Settings_Page_list_1_extra_btns_main_pressed, 3);
    lv_style_set_text_color(&style_Settings_Page_list_1_extra_btns_main_pressed, lv_color_hex(0x0D3055));
    lv_style_set_text_font(&style_Settings_Page_list_1_extra_btns_main_pressed, &lv_font_HarmonyOS_Sans_SC_Regular_14);
    lv_style_set_text_opa(&style_Settings_Page_list_1_extra_btns_main_pressed, 255);
    lv_style_set_bg_opa(&style_Settings_Page_list_1_extra_btns_main_pressed, 255);
    lv_style_set_bg_color(&style_Settings_Page_list_1_extra_btns_main_pressed, lv_color_hex(0xffffff));
    lv_style_set_bg_grad_dir(&style_Settings_Page_list_1_extra_btns_main_pressed, LV_GRAD_DIR_NONE);
    lv_obj_add_style(ui->Settings_Page_list_1_item0, &style_Settings_Page_list_1_extra_btns_main_pressed, LV_PART_MAIN|LV_STATE_PRESSED);

    //Write style state: LV_STATE_FOCUSED for &style_Settings_Page_list_1_extra_btns_main_focused
    static lv_style_t style_Settings_Page_list_1_extra_btns_main_focused;
    ui_init_style(&style_Settings_Page_list_1_extra_btns_main_focused);

    lv_style_set_pad_top(&style_Settings_Page_list_1_extra_btns_main_focused, 3);
    lv_style_set_pad_left(&style_Settings_Page_list_1_extra_btns_main_focused, 8);
    lv_style_set_pad_right(&style_Settings_Page_list_1_extra_btns_main_focused, 0);
    lv_style_set_pad_bottom(&style_Settings_Page_list_1_extra_btns_main_focused, 3);
    lv_style_set_border_width(&style_Settings_Page_list_1_extra_btns_main_focused, 1);
    lv_style_set_border_opa(&style_Settings_Page_list_1_extra_btns_main_focused, 255);
    lv_style_set_border_color(&style_Settings_Page_list_1_extra_btns_main_focused, lv_color_hex(0xe1e6ee));
    lv_style_set_border_side(&style_Settings_Page_list_1_extra_btns_main_focused, LV_BORDER_SIDE_FULL);
    lv_style_set_radius(&style_Settings_Page_list_1_extra_btns_main_focused, 3);
    lv_style_set_text_color(&style_Settings_Page_list_1_extra_btns_main_focused, lv_color_hex(0x0D3055));
    lv_style_set_text_font(&style_Settings_Page_list_1_extra_btns_main_focused, &lv_font_HarmonyOS_Sans_SC_Regular_14);
    lv_style_set_text_opa(&style_Settings_Page_list_1_extra_btns_main_focused, 255);
    lv_style_set_bg_opa(&style_Settings_Page_list_1_extra_btns_main_focused, 255);
    lv_style_set_bg_color(&style_Settings_Page_list_1_extra_btns_main_focused, lv_color_hex(0xffffff));
    lv_style_set_bg_grad_dir(&style_Settings_Page_list_1_extra_btns_main_focused, LV_GRAD_DIR_NONE);
    lv_obj_add_style(ui->Settings_Page_list_1_item0, &style_Settings_Page_list_1_extra_btns_main_focused, LV_PART_MAIN|LV_STATE_FOCUSED);

    //Write style state: LV_STATE_DEFAULT for &style_Settings_Page_list_1_extra_texts_main_default
    static lv_style_t style_Settings_Page_list_1_extra_texts_main_default;
    ui_init_style(&style_Settings_Page_list_1_extra_texts_main_default);

    lv_style_set_pad_top(&style_Settings_Page_list_1_extra_texts_main_default, 6);
    lv_style_set_pad_left(&style_Settings_Page_list_1_extra_texts_main_default, 5);
    lv_style_set_pad_right(&style_Settings_Page_list_1_extra_texts_main_default, 0);
    lv_style_set_pad_bottom(&style_Settings_Page_list_1_extra_texts_main_default, 0);
    lv_style_set_border_width(&style_Settings_Page_list_1_extra_texts_main_default, 0);
    lv_style_set_text_color(&style_Settings_Page_list_1_extra_texts_main_default, lv_color_hex(0x0D3055));
    lv_style_set_text_font(&style_Settings_Page_list_1_extra_texts_main_default, &lv_font_HarmonyOS_Sans_SC_Regular_14);
    lv_style_set_text_opa(&style_Settings_Page_list_1_extra_texts_main_default, 255);
    lv_style_set_radius(&style_Settings_Page_list_1_extra_texts_main_default, 3);
    lv_style_set_transform_width(&style_Settings_Page_list_1_extra_texts_main_default, 0);
    lv_style_set_bg_opa(&style_Settings_Page_list_1_extra_texts_main_default, 0);

    //The custom code of Settings_Page.


    //Update current screen layout.
    lv_obj_update_layout(ui->Settings_Page);

}
