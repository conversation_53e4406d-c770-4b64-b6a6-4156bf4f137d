#ifndef _XZAI_THINGS_H
#define _XZAI_THINGS_H

#ifdef __cplusplus
extern "C" {
#endif

#include <string.h>
#include <stdbool.h>
#include "cJSON.h"

void xzai_thing_init(void);
void xzai_thing_deinit(void);
void xzai_thing_get_descriptors_json(char *buff);
bool xzai_thing_get_state_json(char *buff, bool delta);
void xzai_thing_invoke(const cJSON* command);

#ifdef __cplusplus
} /*extern "C"*/
#endif


#endif

