# Makefile for zlib.  Modified for emx/rsxnt by <PERSON><PERSON><PERSON>, 6/16/98.
# Copyright (C) 1995-1998 <PERSON><PERSON><PERSON><PERSON> Gail<PERSON>.
# For conditions of distribution and use, see copyright notice in zlib.h

# To compile, or to compile and test, type:
#
#   make -fmakefile.emx;  make test -fmakefile.emx
#

CC=gcc -Zwin32

#CFLAGS=-MMD -O
#CFLAGS=-O -DMAX_WBITS=14 -DMAX_MEM_LEVEL=7
#CFLAGS=-MMD -g -DZLIB_DEBUG
CFLAGS=-MMD -O3 $(BUTT) -Wall -Wwrite-strings -Wpointer-arith -Wconversion \
             -Wstrict-prototypes -Wmissing-prototypes

# If cp.exe is available, replace "copy /Y" with "cp -fp" .
CP=copy /Y
# If gnu install.exe is available, replace $(CP) with ginstall.
INSTALL=$(CP)
# The default value of RM is "rm -f."  If "rm.exe" is found, comment out:
RM=del
LDLIBS=-L. -lzlib
LD=$(CC) -s -o
LDSHARED=$(CC)

INCL=zlib.h zconf.h
LIBS=zlib.a

AR=ar rcs

prefix=/usr/local
exec_prefix = $(prefix)

OBJS = adler32.o compress.o crc32.o deflate.o gzclose.o gzlib.o gzread.o \
       gzwrite.o infback.o inffast.o inflate.o inftrees.o trees.o uncompr.o zutil.o

TEST_OBJS = example.o minigzip.o

all: example.exe minigzip.exe

test: all
	./example
	echo hello world | .\minigzip | .\minigzip -d

%.o : %.c
	$(CC) $(CFLAGS) -c $< -o $@

zlib.a: $(OBJS)
	$(AR) $@ $(OBJS)

%.exe : %.o $(LIBS)
	$(LD) $@ $< $(LDLIBS)


.PHONY : clean

clean:
	$(RM) *.d
	$(RM) *.o
	$(RM) *.exe
	$(RM) zlib.a
	$(RM) foo.gz

DEPS := $(wildcard *.d)
ifneq ($(DEPS),)
include $(DEPS)
endif
