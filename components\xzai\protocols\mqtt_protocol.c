#include <string.h>
#include <esp_crt_bundle.h>
#include "mqtt_protocol.h"
#include "audio_mem.h"
#include "esp_log.h"
#include "hal_wifi.h"

#include <unistd.h>
#include <arpa/inet.h>
#include <sys/socket.h>
#include <netdb.h>

#define TAG "MQTT_PROTOCOL"

static void mqtt_parse_server_hello(struct mqtt_protocol *protocol, const cJSON *root);

static const char hex_chars[] = "0123456789ABCDEF";
// 辅助函数，将单个十六进制字符转换为对应的数值
static inline uint8_t char_to_hex(char c)
{
    if (c >= '0' && c <= '9')
        return c - '0';
    if (c >= 'A' && c <= 'F')
        return c - 'A' + 10;
    if (c >= 'a' && c <= 'f')
        return c - 'a' + 10;
    return 0; // 对于无效输入，返回0
}

void decode_hex_str(const char *hex_string, char *out)
{
    const int len = strlen(hex_string);
    char decoded[len];
    for (size_t i = 0; i < len; i += 2)
    {
        char byte = (char_to_hex(hex_string[i]) << 4) | char_to_hex(hex_string[i + 1]);
        decoded[i / 2] = byte;
    }
    memset(out, 0x0, sizeof(out));
    memcpy(out, decoded, len);
}

static void mqtt_disconnect_cb(mqtt_protocol_t *protocol)
{
    ESP_LOGI(TAG, "Disconnected from endpoint");
}

static void mqtt_message_cb(mqtt_protocol_t *protocol, esp_mqtt_event_handle_t event)
{
    int total_data_len = 0;

    if (event->data == NULL)
        return;

    if (protocol->mqtt_msg_buff == NULL)
    {
        total_data_len = sizeof(char) * event->total_data_len + 1;
        protocol->mqtt_msg_len = 0;
        protocol->mqtt_msg_buff = (char *)audio_malloc(total_data_len);
        if (protocol->mqtt_msg_buff == NULL)
            return;

        memset(protocol->mqtt_msg_buff, 0x0, total_data_len);
    }

    ESP_LOGI(TAG, "mqtt msg %d/%d", event->data_len, event->total_data_len);

    if (event->data_len == event->total_data_len)
    {
        memcpy(protocol->mqtt_msg_buff, event->data, event->data_len);
        protocol->mqtt_msg_len = event->data_len;
    }
    else
    {
        memcpy(protocol->mqtt_msg_buff + protocol->mqtt_msg_len, event->data, event->data_len);
        protocol->mqtt_msg_len += event->data_len;
        if (protocol->mqtt_msg_len < event->total_data_len)
            return;
    }

    char topic[event->topic_len + 1];
    memcpy(topic, event->topic, event->topic_len);

    protocol->mqtt_msg_buff[total_data_len - 1] = '\0';

    ESP_LOGI(TAG, "topic: %s\n data: %s", topic, protocol->mqtt_msg_buff);

    cJSON *root = cJSON_Parse(event->data);
    if (root == NULL)
    {
        ESP_LOGE(TAG, "Failed to parse json message %s", event->data);
        return;
    }
    cJSON *type = cJSON_GetObjectItem(root, "type");
    if (type == NULL)
    {
        ESP_LOGE(TAG, "Message type is not specified");
        cJSON_Delete(root);
        return;
    }

    if (strcmp(type->valuestring, "hello") == 0)
    {
        mqtt_parse_server_hello(protocol, root);
    }
    else if (strcmp(type->valuestring, "goodbye") == 0)
    {
        cJSON *session_id = cJSON_GetObjectItem(root, "session_id");
        ESP_LOGI(TAG, "Received goodbye message, session_id: %s", session_id ? session_id->valuestring : "null");
        if (session_id == NULL || strcmp(session_id->valuestring, protocol->base.session_id) == 0)
        {
            // 关闭音频通道
            if (protocol->base.close_audio_channel)
            {
                protocol->base.close_audio_channel(&protocol->base);
            }
        }
    }
    else if (protocol->base.incoming_json_cb != NULL)
    {
        protocol->base.incoming_json_cb(protocol->base.user_data, root);
    }
    cJSON_Delete(root);
    protocol->base.last_incoming_time = time(NULL);

    if (protocol->mqtt_msg_buff)
    {
        audio_free(protocol->mqtt_msg_buff);
        protocol->mqtt_msg_buff = NULL;

        protocol->mqtt_msg_len = 0;
    }
}

static void udp_message_cb(void *arg, const char *data, size_t len)
{
    mqtt_protocol_t *protocol = (mqtt_protocol_t *)arg;

    ESP_LOGI(TAG, "udp recv message %d/%d", len, protocol->aes_nonce_size);

    if (len < protocol->aes_nonce_size)
    {
        ESP_LOGE(TAG, "Invalid audio packet size: %zu", len);
        return;
    }
    if (data[0] != 0x01)
    {
        ESP_LOGE(TAG, "Invalid audio packet type: %x", data[0]);
        return;
    }
    uint32_t timestamp = ntohl(data[8]);
    uint32_t sequence = ntohl(data[12]);
    if (sequence < protocol->remote_sequence)
    {
        ESP_LOGW(TAG, "Received audio packet with old sequence: %lu, expected: %lu", sequence, protocol->remote_sequence);
        return;
    }
    if (sequence != protocol->remote_sequence + 1)
    {
        ESP_LOGW(TAG, "Received audio packet with wrong sequence: %lu, expected: %lu", sequence, protocol->remote_sequence + 1);
    }

    size_t decrypted_size = len - protocol->aes_nonce_size;
    size_t nc_off = 0;
    uint8_t stream_block[16] = {0};
    uint8_t *decrypted = (uint8_t *)audio_malloc(sizeof(uint8_t) * decrypted_size);

    uint8_t *nonce = data;
    uint8_t *encrypted = data + protocol->aes_nonce_size;
    audio_stream_packet_t packet;
    packet.timestamp = timestamp;
    packet.payload = decrypted;
    int ret = mbedtls_aes_crypt_ctr(&protocol->aes_ctx, decrypted_size, &nc_off, nonce, stream_block, encrypted, packet.payload);
    if (ret != 0)
    {
        ESP_LOGE(TAG, "Failed to decrypt audio data, ret: %d", ret);
        return;
    }
    if (protocol->base.incoming_audio_cb)
    {
        protocol->base.incoming_audio_cb(protocol->base.user_data, &packet);
    }
    protocol->remote_sequence = sequence;
    protocol->base.last_incoming_time = time(NULL);

    if (decrypted)
    {
        audio_free(decrypted);
        decrypted = NULL;
    }
}

static void mqtt_parse_server_hello(struct mqtt_protocol *protocol, const cJSON *root)
{
    cJSON *transport = cJSON_GetObjectItem(root, "transport");
    if (transport == NULL || strcmp(transport->valuestring, "udp") != 0)
    {
        ESP_LOGE(TAG, "Unsupported transport: %s", transport->valuestring);
        return;
    }

    cJSON *session_id = cJSON_GetObjectItem(root, "session_id");
    if (session_id != NULL)
    {
        memset(protocol->base.session_id, 0x0, sizeof(protocol->base.session_id));
        memcpy(protocol->base.session_id, session_id->valuestring, sizeof(protocol->base.session_id));
        ESP_LOGI(TAG, "Session ID: %s", protocol->base.session_id);
    }

    // Get sample rate from hello message
    cJSON *audio_params = cJSON_GetObjectItem(root, "audio_params");
    if (audio_params != NULL)
    {
        cJSON *sample_rate = cJSON_GetObjectItem(audio_params, "sample_rate");
        if (sample_rate != NULL)
        {
            protocol->base.server_sample_rate = sample_rate->valueint;
        }
    }

    cJSON *udp = cJSON_GetObjectItem(root, "udp");
    if (udp == NULL)
    {
        ESP_LOGE(TAG, "UDP is not specified");
        return;
    }

    const char *server = cJSON_GetObjectItem(udp, "server")->valuestring;
    int port = cJSON_GetObjectItem(udp, "port")->valueint;
    const char *key = cJSON_GetObjectItem(udp, "key")->valuestring;
    const char *nonce = cJSON_GetObjectItem(udp, "nonce")->valuestring;
    const char *encryption = cJSON_GetObjectItem(udp, "encryption")->valuestring;

    ESP_LOGI(TAG, "UDP server: %s, port: %d, encryption: %s", server, port, encryption);

    memset(protocol->udp_host, 0x0, sizeof(protocol->udp_host));
    memcpy(protocol->udp_host, server, sizeof(protocol->udp_host));

    protocol->udp_port = port;

    protocol->aes_nonce_size = strlen(nonce) / 2;
    decode_hex_str(nonce, protocol->aes_nonce);

    char decode_key[strlen(key) / 2];
    decode_hex_str(key, decode_key);

    mbedtls_aes_init(&protocol->aes_ctx);
    mbedtls_aes_setkey_enc(&protocol->aes_ctx, (const unsigned char *)decode_key, 128);

    protocol->local_sequence = 0;
    protocol->remote_sequence = 0;
    xEventGroupSetBits(protocol->event_group_handle, MQTT_SERVER_HELLO_EVENT);
}

static void mqtt_event_handler(void *handler_args, esp_event_base_t base, int32_t event_id, void *event_data)
{
    ESP_LOGD(TAG, "Event dispatched from event loop base=%s, event_id=%" PRIi32, base, event_id);
    esp_mqtt_event_handle_t event = event_data;
    esp_mqtt_client_handle_t client = event->client;
    int msg_id;

    mqtt_protocol_t *protocol = (mqtt_protocol_t *)handler_args;

    switch ((esp_mqtt_event_id_t)event_id)
    {
    case MQTT_EVENT_CONNECTED:
        xEventGroupSetBits(protocol->event_group_handle, MQTT_CONNECTED_EVENT);
        break;
    case MQTT_EVENT_DISCONNECTED:
        protocol->connected = false;
        xEventGroupSetBits(protocol->event_group_handle, MQTT_DISCONNECTED_EVENT);
        mqtt_disconnect_cb(protocol);
        break;
    case MQTT_EVENT_SUBSCRIBED:
        break;
    case MQTT_EVENT_UNSUBSCRIBED:
        break;
    case MQTT_EVENT_PUBLISHED:
        break;
    case MQTT_EVENT_DATA:
        mqtt_message_cb(protocol, event);
        break;
    case MQTT_EVENT_ERROR:
        xEventGroupSetBits(protocol->event_group_handle, MQTT_ERROR_EVENT);
        ESP_LOGI(TAG, "MQTT error occurred: %s", esp_err_to_name(event->error_handle->esp_tls_last_esp_err));
        break;
    default:
        ESP_LOGI(TAG, "Other event id:%d", event->event_id);
        break;
    }
}

static void _start(struct protocol_base *base)
{
    mqtt_protocol_t *protocol = __containerof(base, mqtt_protocol_t, base);

    if (protocol->client != NULL)
    {
        ESP_LOGW(TAG, "Mqtt client already started");
        esp_mqtt_client_destroy(protocol->client);
    }

    if (protocol->mqtt_cfg.endpoint == NULL || strlen(protocol->mqtt_cfg.endpoint) == 0)
    {
        ESP_LOGW(TAG, "MQTT endpoint is not specified");
        return;
    }

    esp_mqtt_client_config_t mqtt_cfg = {0};
    mqtt_cfg.broker.address.hostname = protocol->mqtt_cfg.endpoint;
    mqtt_cfg.broker.address.port = MQTT_ADDRESS_PORT;
    if (mqtt_cfg.broker.address.port == 8883)
    {
        mqtt_cfg.broker.address.transport = MQTT_TRANSPORT_OVER_SSL;
        mqtt_cfg.broker.verification.crt_bundle_attach = esp_crt_bundle_attach;
    }
    else
    {
        mqtt_cfg.broker.address.transport = MQTT_TRANSPORT_OVER_TCP;
    }
    // mqtt_cfg.session.protocol_ver = MQTT_PROTOCOL_VER;
    mqtt_cfg.credentials.client_id = protocol->mqtt_cfg.client_id;
    mqtt_cfg.credentials.username = protocol->mqtt_cfg.username;
    mqtt_cfg.credentials.authentication.password = protocol->mqtt_cfg.password;
    mqtt_cfg.session.keepalive = MQTT_KEEPALIVE_TIME;

    protocol->client = esp_mqtt_client_init(&mqtt_cfg);
    if (protocol->client == NULL)
    {
        ESP_LOGE(TAG, "Failed to connect to endpoint");
        return;
    }

    esp_mqtt_client_register_event(protocol->client, ESP_EVENT_ANY_ID, mqtt_event_handler, protocol);
    esp_mqtt_client_start(protocol->client);

    EventBits_t bits = xEventGroupWaitBits(protocol->event_group_handle, MQTT_CONNECTED_EVENT | MQTT_DISCONNECTED_EVENT | MQTT_ERROR_EVENT,
                                           pdTRUE, pdFALSE, pdMS_TO_TICKS(MQTT_CONNECT_TIMEOUT_MS));

    protocol->connected = (bits & MQTT_CONNECTED_EVENT) != 0;

    if (!protocol->connected)
    {
        ESP_LOGE(TAG, "Failed to connect to endpoint");
        protocol->base.set_error(&protocol->base, "无法连接服务，请稍后再试");
        return;
    }

    ESP_LOGI(TAG, "Connected to endpoint");
}

static bool _open_audio_channel(struct protocol_base *base)
{
    mqtt_protocol_t *protocol = __containerof(base, mqtt_protocol_t, base);

    if (hal_wifi_get_connect_state() == false || !protocol->connected)
    {
        ESP_LOGI(TAG, "MQTT is not connected, try to connect now");
        _start(&protocol->base);
        if (!protocol->connected)
            return false;
    }

    protocol->base.error_occurred = false;
    memset(protocol->base.session_id, 0x0, sizeof(protocol->base.session_id));
    xEventGroupClearBits(protocol->event_group_handle, MQTT_SERVER_HELLO_EVENT);

    // 发送 hello 消息申请 UDP 通道
    char message[256];
    sprintf(message, "{\"type\":\"hello\",\"version\":3,\"transport\":\"udp\",\"audio_params\":{"
                     "\"format\":\"opus\",\"sample_rate\":16000,\"channels\":1,\"frame_duration\":%d}}",
            OPUS_FRAME_DURATION_MS);

    protocol->base.send_text(&protocol->base, message);

    // 等待服务器响应
    EventBits_t bits = xEventGroupWaitBits(protocol->event_group_handle, MQTT_SERVER_HELLO_EVENT, pdTRUE, pdFALSE, pdMS_TO_TICKS(10000));
    if (!(bits & MQTT_SERVER_HELLO_EVENT))
    {
        ESP_LOGE(TAG, "Failed to receive server hello");
        protocol->base.set_error(&protocol->base, "等待响应超时");
        return false;
    }

    // 创建UDP连接
    if (protocol->udp != NULL)
    {
        udp_client_destroy(protocol->udp);
    }
    ESP_LOGI(TAG, "Create udp client");
    protocol->udp = udp_client_create();
    udp_client_set_user_data(protocol->udp, protocol);
    udp_client_set_msg_callback(protocol->udp, udp_message_cb);
    udp_client_connect(protocol->udp, protocol->udp_host, protocol->udp_port);

    if (protocol->base.audio_channel_opened_cb)
    {
        protocol->base.audio_channel_opened_cb(protocol->base.user_data);
    }

    return true;
}

static void _close_audio_channel(struct protocol_base *base)
{
    mqtt_protocol_t *protocol = __containerof(base, mqtt_protocol_t, base);

    if (protocol->udp)
    {
        udp_client_destroy(protocol->udp);
        protocol->udp = NULL;
    }

    char message[128];

    sprintf(message, "{\"session_id\":\"%s\",\"type\":\"goodbye\"}", protocol->base.session_id);
    protocol->base.send_text(&protocol->base, message);

    if (protocol->base.audio_channel_closed_cb)
    {
        protocol->base.audio_channel_closed_cb(protocol->base.user_data);
    }
}

static bool _is_audio_channel_opened(struct protocol_base *base)
{
    mqtt_protocol_t *protocol = __containerof(base, mqtt_protocol_t, base);

    if (protocol->udp == NULL || protocol->base.error_occurred || protocol->base.is_timeout(&protocol->base))
        return false;

    return true;
}

static void _send_text(struct protocol_base *base, const char *text)
{
    // mqtt_protocol_t *protocol = (mqtt_protocol_t *)((char *)base - offsetof(mqtt_protocol_t, base));
    mqtt_protocol_t *protocol = __containerof(base, mqtt_protocol_t, base);

    if (protocol == NULL || protocol->mqtt_cfg.publish_topic == NULL)
        return;

    if (!protocol->connected)
        goto MQTT_SEND_FAILED;

    ESP_LOGI(TAG, "mqtt send %s", text);

    bool res = esp_mqtt_client_publish(protocol->client,
                                       protocol->mqtt_cfg.publish_topic,
                                       text,
                                       strlen(text),
                                       0,
                                       0) == 0;

    if (res)
        return;

MQTT_SEND_FAILED:
    ESP_LOGE(TAG, "Failed to publish message: %s", text);
    protocol->base.set_error(&protocol->base, "发送失败，请检查网络");
}

static void _send_audio(struct protocol_base *base, audio_stream_packet_t *packet)
{
    mqtt_protocol_t *protocol = __containerof(base, mqtt_protocol_t, base);

    if (protocol->udp == NULL)
        return;

    char nonce[protocol->aes_nonce_size];
    memcpy(nonce, protocol->aes_nonce, protocol->aes_nonce_size);

    nonce[2] = htons(packet->size);
    nonce[8] = htons(packet->timestamp);
    nonce[12] = htonl(++protocol->local_sequence);

    size_t encrypted_size = protocol->aes_nonce_size + packet->size;
    char *encrypted = (char *)audio_malloc(sizeof(char) * encrypted_size);
    if (encrypted == NULL)
        return;

    memcpy(encrypted, nonce, sizeof(nonce));

    size_t nc_off = 0;
    uint8_t stream_block[16] = {0};
    if (mbedtls_aes_crypt_ctr(&protocol->aes_ctx, packet->size, &nc_off, (uint8_t *)nonce, stream_block,
                              (uint8_t *)packet->payload, (uint8_t *)encrypted + sizeof(nonce)) != 0)
    {
        ESP_LOGE(TAG, "Failed to encrypt audio data");
        return;
    }
    udp_client_send(protocol->udp, encrypted, encrypted_size);
    ESP_LOGI(TAG, "udp send data size %d", encrypted_size);

    audio_free(encrypted);
    encrypted = NULL;
}

mqtt_protocol_t *mqtt_protocol_create(mqtt_protocol_cfg_t *cfg)
{
    mqtt_protocol_t *protocol = (mqtt_protocol_t *)audio_calloc(1, sizeof(mqtt_protocol_t));

    if (protocol == NULL)
        goto PROTOCOL_CREATE_FAILED;

    protocol->event_group_handle = xEventGroupCreate();
    if (protocol->event_group_handle == NULL)
        goto PROTOCOL_CREATE_FAILED;

    memcpy(&protocol->mqtt_cfg, cfg, sizeof(mqtt_protocol_cfg_t));

    // 初始化基类函数
    protocol_base_init(&protocol->base);

    protocol->base.send_text = _send_text;
    protocol->base.start = _start;
    protocol->base.open_audio_channel = _open_audio_channel;
    protocol->base.close_audio_channel = _close_audio_channel;
    protocol->base.send_audio = _send_audio;
    protocol->base.is_audio_channel_opened = _is_audio_channel_opened;

    return protocol;
PROTOCOL_CREATE_FAILED:
    mqtt_protocol_destroy(protocol);
    return NULL;
}

void mqtt_protocol_destroy(struct mqtt_protocol *protocol)
{
    if (protocol)
    {
        if (protocol->mqtt_msg_buff)
        {
            audio_free(protocol->mqtt_msg_buff);
            protocol->mqtt_msg_buff = NULL;
        }

        if (protocol->client)
        {
            esp_mqtt_client_disconnect(protocol->client);
            esp_mqtt_client_destroy(protocol->client);
            protocol->client = NULL;
            protocol->connected = false;
            xEventGroupClearBits(protocol->event_group_handle, MQTT_CONNECTED_EVENT | MQTT_DISCONNECTED_EVENT | MQTT_ERROR_EVENT);
        }

        if (protocol->event_group_handle)
        {
            vEventGroupDelete(protocol->event_group_handle);
        }

        if (protocol->udp)
        {
            udp_client_destroy(protocol->udp);
            protocol->udp = NULL;
        }

        memset(protocol, 0x0, sizeof(mqtt_protocol_t));
        audio_free(protocol);
        protocol = NULL;
    }
}