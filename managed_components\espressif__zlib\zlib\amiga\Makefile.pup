# Amiga powerUP (TM) Makefile
# makefile for libpng and SAS C V6.58/7.00 PPC compiler
# Copyright (C) 1998 by <PERSON>

LIBNAME	= libzip.a

CC	= scppc
CFLAGS	= NOSTKCHK NOSINT OPTIMIZE OPTGO OPTPEEP OPTINLOCAL OPTINL \
	  OPTLOOP OPTRDEP=8 OPTDEP=8 OPTCOMP=8 NOVER
AR	= ppc-amigaos-ar cr
RANLIB	= ppc-amigaos-ranlib
LD	= ppc-amigaos-ld -r
LDFLAGS	= -o
LDLIBS	= LIB:scppc.a LIB:end.o
RM	= delete quiet

OBJS = adler32.o compress.o crc32.o gzclose.o gzlib.o gzread.o gzwrite.o \
       uncompr.o deflate.o trees.o zutil.o inflate.o infback.o inftrees.o inffast.o

TEST_OBJS = example.o minigzip.o

all: example minigzip

check: test
test: all
	example
	echo hello world | minigzip | minigzip -d

$(LIBNAME): $(OBJS)
	$(AR) $@ $(OBJS)
	-$(RANLIB) $@

example: example.o $(LIBNAME)
	$(LD) $(LDFLAGS) $@ LIB:c_ppc.o $@.o $(LIBNAME) $(LDLIBS)

minigzip: minigzip.o $(LIBNAME)
	$(LD) $(LDFLAGS) $@ LIB:c_ppc.o $@.o $(LIBNAME) $(LDLIBS)

mostlyclean: clean
clean:
	$(RM) *.o example minigzip $(LIBNAME) foo.gz

zip:
	zip -ul9 zlib README ChangeLog Makefile Make????.??? Makefile.?? \
	  descrip.mms *.[ch]

tgz:
	cd ..; tar cfz zlib/zlib.tgz zlib/README zlib/ChangeLog zlib/Makefile \
	  zlib/Make????.??? zlib/Makefile.?? zlib/descrip.mms zlib/*.[ch]

# DO NOT DELETE THIS LINE -- make depend depends on it.

adler32.o: zlib.h zconf.h
compress.o: zlib.h zconf.h
crc32.o: crc32.h zlib.h zconf.h
deflate.o: deflate.h zutil.h zlib.h zconf.h
example.o: zlib.h zconf.h
gzclose.o: zlib.h zconf.h gzguts.h
gzlib.o: zlib.h zconf.h gzguts.h
gzread.o: zlib.h zconf.h gzguts.h
gzwrite.o: zlib.h zconf.h gzguts.h
inffast.o: zutil.h zlib.h zconf.h inftrees.h inflate.h inffast.h
inflate.o: zutil.h zlib.h zconf.h inftrees.h inflate.h inffast.h
infback.o: zutil.h zlib.h zconf.h inftrees.h inflate.h inffast.h
inftrees.o: zutil.h zlib.h zconf.h inftrees.h
minigzip.o: zlib.h zconf.h
trees.o: deflate.h zutil.h zlib.h zconf.h trees.h
uncompr.o: zlib.h zconf.h
zutil.o: zutil.h zlib.h zconf.h
