# ESP-DSP Applications

This directory contains a range of applications for ESP-DSP library.

These applications are intended to demonstrate part of ESP-DSP functionality (e.g. initialization, execution) and to provide examples of fully working projects built using ESP-DSP component

See the [README.md](../README.md) file in the upper level directory for more information about ESP-DSP.

# Applications Layout

The applications are grouped into subdirectories by category. Each category directory contains one or more  projects:

* [ESP32-Azure IoT kit](./azure_board_apps/README.md) applications
    * [3d graphics](./azure_board_apps/apps/3d_graphics/README.md) application
    * [Kalman filter](./azure_board_apps/apps/kalman_filter/README.md) application

* [LyraT Board](./lyrat_board_app/README.md) application
* [ESP32-S3-BOX-Lite](./spectrum_box_lite/README.md) application


