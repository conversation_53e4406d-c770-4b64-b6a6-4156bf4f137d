#include "udp_client.h"
#include "audio_mem.h"
#include "audio_thread.h"

#include <esp_log.h>
#include <unistd.h>
#include <arpa/inet.h>
#include <sys/socket.h>
#include <netdb.h>

#define TAG "udp_client"

static void recv_task(void *arg);

struct udp_client_t
{
    int udp_fd;
    struct sockaddr_in server_addr;

    char recv_buff[128];

    audio_thread_t thread_recv;
    bool connected;
    udp_mesage_callback msg_cb;

    void *user_data;
};

udp_client_handle_t udp_client_create()
{
    udp_client_handle_t client = (udp_client_handle_t)audio_malloc(sizeof(struct udp_client_t));
    if (client == NULL)
        return NULL;

    client->udp_fd = -1;

    return client;
}

void udp_client_destroy(udp_client_handle_t handle)
{
    udp_client_disconnect(handle);

    if (handle)
    {
        audio_free(handle);
    }
    handle = NULL;
}

bool udp_client_connect(udp_client_handle_t handle, const char *host, int port)
{
    if (handle == NULL || host == NULL)
        return false;

    handle->udp_fd = socket(AF_INET, SOCK_DGRAM, IPPROTO_IP);
    if (handle->udp_fd < 0)
    {
        ESP_LOGE(TAG, "Failed to create socket");
        return false;
    }

    bzero(&handle->server_addr, sizeof(handle->server_addr));
    handle->server_addr.sin_family = AF_INET;
    handle->server_addr.sin_port = htons(port);
    handle->server_addr.sin_addr.s_addr = inet_addr(host);

    // host is domain
    struct hostent *server = gethostbyname(host);
    if (server == NULL)
    {
        ESP_LOGE(TAG, "Failed to get host by name");
        return false;
    }
    memcpy(&handle->server_addr.sin_addr, server->h_addr, server->h_length);

    // Set timeout
    struct timeval timeout;
    timeout.tv_sec = 10;
    timeout.tv_usec = 0;
    setsockopt(handle->udp_fd, SOL_SOCKET, SO_RCVTIMEO, &timeout, sizeof timeout);

    int ret = connect(handle->udp_fd, (struct sockaddr *)&handle->server_addr, sizeof(handle->server_addr));
    if (ret < 0)
    {
        ESP_LOGE(TAG, "Failed to connect to %s:%d", host, port);
        close(handle->udp_fd);
        handle->udp_fd = -1;
        return false;
    }

    handle->connected = true;

    audio_thread_create(handle->thread_recv, "udp_task", recv_task, handle, 4 * 1024, 5, true, 0);

    ESP_LOGI(TAG, "udp connected %d %s:%d", handle->udp_fd, host, port);

    return true;
}

void udp_client_disconnect(udp_client_handle_t handle)
{
    if (handle->udp_fd != -1)
    {
        close(handle->udp_fd);
        handle->udp_fd = -1;
    }
    handle->connected = false;
}

int udp_client_send(udp_client_handle_t handle, const void *data, int len)
{
    int ret = send(handle->udp_fd, data, len, 0);
    if (ret <= 0)
    {
        handle->connected = false;
        ESP_LOGE(TAG, "Send failed: ret=%d", ret);
    }
    return ret;
}

void udp_client_set_msg_callback(udp_client_handle_t handle, udp_mesage_callback cb)
{
    handle->msg_cb = cb;
}

void udp_client_set_user_data(udp_client_handle_t handle, void *user_data)
{
    handle->user_data = user_data;
}

static void recv_task(void *arg)
{
    udp_client_handle_t handle = (udp_client_handle_t)arg;

    struct sockaddr_storage source_addr; // Large enough for both IPv4 or IPv6
    socklen_t socklen = sizeof(source_addr);

    while (handle->connected)
    {
        // int ret = recv(handle->udp_fd, &handle->recv_buff, sizeof(handle->recv_buff), 0);
        int ret = recvfrom(handle->udp_fd, handle->recv_buff, sizeof(handle->recv_buff) - 1, 0, (struct sockaddr *)&source_addr, &socklen);
        ESP_LOGI(TAG, "udp recv %d", ret);
        if (ret <= 0)
        {
            break;
        }
        if (handle->msg_cb)
        {
            handle->recv_buff[ret] = 0;
            handle->msg_cb(handle->user_data, handle->recv_buff, ret);
        }
        vTaskDelay(1);
    }

    if (handle->thread_recv)
    {
        audio_thread_delete_task(handle->thread_recv);
        handle->thread_recv = NULL;
    }

    vTaskDelete(NULL);
}