# Requirements Document

## Introduction

本功能实现在Wave页面通过发送按钮将滑块设置的目标值通过CAN通信发送给电机控制器。系统将根据当前选择的控制模式（力矩环、速度环、位置环等）自动选择对应的CAN RPDO消息类型进行发送，为用户提供直观的电机控制界面。

## Requirements

### Requirement 1

**User Story:** 作为用户，我希望能够通过滑块设置目标值并点击发送按钮将其发送给电机，以便实现对电机的实时控制

#### Acceptance Criteria

1. WHEN 用户点击Wave_Page_btn_send按钮 THEN 系统 SHALL 获取当前滑块的数值
2. WHEN 用户点击发送按钮 THEN 系统 SHALL 获取当前下拉框选择的控制模式
3. WHEN 系统获取到滑块值和控制模式 THEN 系统 SHALL 通过对应的CAN RPDO消息发送目标值给电机
4. WHEN 发送成功 THEN 系统 SHALL 在日志中记录发送的目标值和模式类型

### Requirement 2

**User Story:** 作为用户，我希望系统能够根据当前选择的控制模式自动选择正确的CAN消息类型，以确保电机接收到正确格式的控制指令

#### Acceptance Criteria

1. WHEN 当前模式为力矩环（索引0） THEN 系统 SHALL 通过RPDO3发送转矩目标值
2. WHEN 当前模式为速度环（索引1） THEN 系统 SHALL 通过RPDO2发送速度目标值
3. WHEN 当前模式为位置环（索引2） THEN 系统 SHALL 通过RPDO1发送位置目标值
4. WHEN 当前模式为速度轨迹环（索引3） THEN 系统 SHALL 通过RPDO2发送速度目标值
5. WHEN 当前模式为位置轨迹环（索引4） THEN 系统 SHALL 通过RPDO1发送位置目标值

### Requirement 3

**User Story:** 作为用户，我希望在发送过程中能够获得适当的反馈，以便了解操作是否成功执行

#### Acceptance Criteria

1. WHEN CAN消息发送成功 THEN 系统 SHALL 在ESP_LOG中输出成功信息，包含发送的目标值和模式
2. WHEN CAN消息发送失败 THEN 系统 SHALL 在ESP_LOG中输出错误信息和失败原因
3. WHEN 获取滑块值或下拉框选择失败 THEN 系统 SHALL 在日志中记录错误信息
4. WHEN 发送按钮被点击 THEN 系统 SHALL 在日志中记录按钮点击事件

### Requirement 4

**User Story:** 作为开发者，我希望CAN发送功能能够复用现有的CANopen协议栈，以保持系统架构的一致性

#### Acceptance Criteria

1. WHEN 实现CAN发送功能 THEN 系统 SHALL 使用现有的twai_transmit函数进行消息发送
2. WHEN 构造RPDO消息 THEN 系统 SHALL 遵循现有的CANopen消息格式规范
3. WHEN 发送RPDO消息 THEN 系统 SHALL 使用正确的CAN ID计算方式（基础ID + 节点ID）
4. WHEN 打包数据到CAN消息 THEN 系统 SHALL 使用大端序格式，与现有接收逻辑保持一致