#include "ui_user_inc.h"

extern void ui_scr_can_connect_data_update(ui_notif_msg_t *msg);
extern void ui_scr_can_connect_status_update(ui_notif_msg_t *msg);

void ui_notif_scr_can_connect_task(ui_notif_msg_t *msg)
{
    switch ((int)msg->type)
    {
    case UI_NOTIF_CAN_DATA_UPDATE:
        ui_scr_can_connect_data_update(msg);
        break;
        
    case UI_NOTIF_CAN_STATUS_UPDATE:
        ui_scr_can_connect_status_update(msg);
        break;
        
    default:
        break;

    }
}

