/*
* Copyright 2025 NXP
* NXP Proprietary. This software is owned or controlled by NXP and may only be used strictly in
* accordance with the applicable license terms. By expressly accepting such terms or by downloading, installing,
* activating and/or otherwise using the software, you are agreeing that you have read, and that you agree to
* comply with and are bound by, such license terms.  If you do not agree to be bound by the applicable license
* terms, then you may not retain, install, activate or otherwise use the software.
*/

#include "lvgl.h"
#include <stdio.h>
#include "gui_guider.h"
#include "events_init.h"
#include "widgets_init.h"
#include "custom.h"



void setup_scr_Can_Connet_page(lv_ui *ui)
{
    //Write codes Can_Connet_page
    ui->Can_Connet_page = lv_obj_create(NULL);
    lv_obj_set_size(ui->Can_Connet_page, 320, 240);
    lv_obj_set_scrollbar_mode(ui->Can_Connet_page, LV_SCROLLBAR_MODE_OFF);

    //Write style for Can_Connet_page, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->Can_Connet_page, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->Can_Connet_page, lv_color_hex(0x242424), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->Can_Connet_page, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes Can_Connet_page_title_bar
    ui->Can_Connet_page_title_bar = lv_obj_create(ui->Can_Connet_page);
    lv_obj_set_pos(ui->Can_Connet_page_title_bar, 77, 5);
    lv_obj_set_size(ui->Can_Connet_page_title_bar, 151, 27);
    lv_obj_set_scrollbar_mode(ui->Can_Connet_page_title_bar, LV_SCROLLBAR_MODE_OFF);

    //Write style for Can_Connet_page_title_bar, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->Can_Connet_page_title_bar, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_opa(ui->Can_Connet_page_title_bar, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_color(ui->Can_Connet_page_title_bar, lv_color_hex(0x2195f6), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_side(ui->Can_Connet_page_title_bar, LV_BORDER_SIDE_FULL, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->Can_Connet_page_title_bar, 9, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->Can_Connet_page_title_bar, 36, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->Can_Connet_page_title_bar, lv_color_hex(0xcfcfcf), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->Can_Connet_page_title_bar, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->Can_Connet_page_title_bar, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->Can_Connet_page_title_bar, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->Can_Connet_page_title_bar, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->Can_Connet_page_title_bar, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->Can_Connet_page_title_bar, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes Can_Connet_page_label_state_true
    ui->Can_Connet_page_label_state_true = lv_label_create(ui->Can_Connet_page_title_bar);
    lv_label_set_text(ui->Can_Connet_page_label_state_true, "CAN总线未连接");
    lv_label_set_long_mode(ui->Can_Connet_page_label_state_true, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->Can_Connet_page_label_state_true, -4, 3);
    lv_obj_set_size(ui->Can_Connet_page_label_state_true, 154, 23);

    //Write style for Can_Connet_page_label_state_true, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->Can_Connet_page_label_state_true, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->Can_Connet_page_label_state_true, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->Can_Connet_page_label_state_true, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->Can_Connet_page_label_state_true, &lv_font_HarmonyOS_Sans_SC_Light_16, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->Can_Connet_page_label_state_true, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->Can_Connet_page_label_state_true, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->Can_Connet_page_label_state_true, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->Can_Connet_page_label_state_true, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->Can_Connet_page_label_state_true, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->Can_Connet_page_label_state_true, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->Can_Connet_page_label_state_true, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->Can_Connet_page_label_state_true, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->Can_Connet_page_label_state_true, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->Can_Connet_page_label_state_true, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes Can_Connet_page_control_meter
    ui->Can_Connet_page_control_meter = lv_obj_create(ui->Can_Connet_page);
    lv_obj_set_pos(ui->Can_Connet_page_control_meter, 4, 37);
    lv_obj_set_size(ui->Can_Connet_page_control_meter, 311, 130);
    lv_obj_set_scrollbar_mode(ui->Can_Connet_page_control_meter, LV_SCROLLBAR_MODE_OFF);

    //Write style for Can_Connet_page_control_meter, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->Can_Connet_page_control_meter, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->Can_Connet_page_control_meter, 9, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->Can_Connet_page_control_meter, 68, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->Can_Connet_page_control_meter, lv_color_hex(0xa257b2), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->Can_Connet_page_control_meter, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->Can_Connet_page_control_meter, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->Can_Connet_page_control_meter, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->Can_Connet_page_control_meter, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->Can_Connet_page_control_meter, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->Can_Connet_page_control_meter, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes Can_Connet_page_Speed
    ui->Can_Connet_page_Speed = lv_label_create(ui->Can_Connet_page_control_meter);
    lv_label_set_text(ui->Can_Connet_page_Speed, "1000");
    lv_label_set_long_mode(ui->Can_Connet_page_Speed, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->Can_Connet_page_Speed, 219, 0);
    lv_obj_set_size(ui->Can_Connet_page_Speed, 43, 14);

    //Write style for Can_Connet_page_Speed, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->Can_Connet_page_Speed, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->Can_Connet_page_Speed, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->Can_Connet_page_Speed, lv_color_hex(0x00ffd2), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->Can_Connet_page_Speed, &lv_font_HarmonyOS_Sans_SC_Medium_14, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->Can_Connet_page_Speed, 168, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->Can_Connet_page_Speed, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->Can_Connet_page_Speed, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->Can_Connet_page_Speed, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->Can_Connet_page_Speed, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->Can_Connet_page_Speed, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->Can_Connet_page_Speed, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->Can_Connet_page_Speed, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->Can_Connet_page_Speed, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->Can_Connet_page_Speed, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes Can_Connet_page_meter_speed
    ui->Can_Connet_page_meter_speed = lv_meter_create(ui->Can_Connet_page_control_meter);
    // add scale ui->Can_Connet_page_meter_speed_scale_0
    ui->Can_Connet_page_meter_speed_scale_0 = lv_meter_add_scale(ui->Can_Connet_page_meter_speed);
    lv_meter_set_scale_ticks(ui->Can_Connet_page_meter_speed, ui->Can_Connet_page_meter_speed_scale_0, 41, 3, 4, lv_color_hex(0xc8b8b8));
    lv_meter_set_scale_major_ticks(ui->Can_Connet_page_meter_speed, ui->Can_Connet_page_meter_speed_scale_0, 5, 2, 10, lv_color_hex(0x00a7c5), 3);
    lv_meter_set_scale_range(ui->Can_Connet_page_meter_speed, ui->Can_Connet_page_meter_speed_scale_0, 0, 5000, 300, 120);

    // add scale line for ui->Can_Connet_page_meter_speed_scale_0
    ui->Can_Connet_page_meter_speed_scale_0_scaleline_0 = lv_meter_add_scale_lines(ui->Can_Connet_page_meter_speed, ui->Can_Connet_page_meter_speed_scale_0, lv_color_hex(0x00ff47), lv_color_hex(0x01b51b), true, 0);
    lv_meter_set_indicator_start_value(ui->Can_Connet_page_meter_speed, ui->Can_Connet_page_meter_speed_scale_0_scaleline_0, 0);
    lv_meter_set_indicator_end_value(ui->Can_Connet_page_meter_speed, ui->Can_Connet_page_meter_speed_scale_0_scaleline_0, 2500);

    // add scale line for ui->Can_Connet_page_meter_speed_scale_0
    ui->Can_Connet_page_meter_speed_scale_0_scaleline_1 = lv_meter_add_scale_lines(ui->Can_Connet_page_meter_speed, ui->Can_Connet_page_meter_speed_scale_0, lv_color_hex(0xffff00), lv_color_hex(0xbc9c00), true, 0);
    lv_meter_set_indicator_start_value(ui->Can_Connet_page_meter_speed, ui->Can_Connet_page_meter_speed_scale_0_scaleline_1, 2500);
    lv_meter_set_indicator_end_value(ui->Can_Connet_page_meter_speed, ui->Can_Connet_page_meter_speed_scale_0_scaleline_1, 4000);

    // add scale line for ui->Can_Connet_page_meter_speed_scale_0
    ui->Can_Connet_page_meter_speed_scale_0_scaleline_2 = lv_meter_add_scale_lines(ui->Can_Connet_page_meter_speed, ui->Can_Connet_page_meter_speed_scale_0, lv_color_hex(0xd8c500), lv_color_hex(0xd80021), true, 0);
    lv_meter_set_indicator_start_value(ui->Can_Connet_page_meter_speed, ui->Can_Connet_page_meter_speed_scale_0_scaleline_2, 4000);
    lv_meter_set_indicator_end_value(ui->Can_Connet_page_meter_speed, ui->Can_Connet_page_meter_speed_scale_0_scaleline_2, 5000);

    // add needle images for ui->Can_Connet_page_meter_speed_scale_0.
    ui->Can_Connet_page_meter_speed_scale_0_ndline_0 = lv_meter_add_needle_img(ui->Can_Connet_page_meter_speed, ui->Can_Connet_page_meter_speed_scale_0, &_needle_alpha_210x210, 105, 105);
    lv_meter_set_indicator_value(ui->Can_Connet_page_meter_speed, ui->Can_Connet_page_meter_speed_scale_0_ndline_0, 300);
    lv_obj_set_pos(ui->Can_Connet_page_meter_speed, 178, 16);
    lv_obj_set_size(ui->Can_Connet_page_meter_speed, 126, 126);

    //Write style for Can_Connet_page_meter_speed, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->Can_Connet_page_meter_speed, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->Can_Connet_page_meter_speed, 100, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui->Can_Connet_page_meter_speed, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->Can_Connet_page_meter_speed, 1, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->Can_Connet_page_meter_speed, 5, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->Can_Connet_page_meter_speed, 4, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->Can_Connet_page_meter_speed, 5, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->Can_Connet_page_meter_speed, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write style for Can_Connet_page_meter_speed, Part: LV_PART_TICKS, State: LV_STATE_DEFAULT.
    lv_obj_set_style_text_color(ui->Can_Connet_page_meter_speed, lv_color_hex(0x3affe7), LV_PART_TICKS|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->Can_Connet_page_meter_speed, &lv_font_montserratMedium_10, LV_PART_TICKS|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->Can_Connet_page_meter_speed, 255, LV_PART_TICKS|LV_STATE_DEFAULT);

    //Write style for Can_Connet_page_meter_speed, Part: LV_PART_INDICATOR, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->Can_Connet_page_meter_speed, 255, LV_PART_INDICATOR|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->Can_Connet_page_meter_speed, lv_color_hex(0x000000), LV_PART_INDICATOR|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->Can_Connet_page_meter_speed, LV_GRAD_DIR_NONE, LV_PART_INDICATOR|LV_STATE_DEFAULT);

    //Write codes Can_Connet_page_label_title1
    ui->Can_Connet_page_label_title1 = lv_label_create(ui->Can_Connet_page_control_meter);
    lv_label_set_text(ui->Can_Connet_page_label_title1, "电机状态:");
    lv_label_set_long_mode(ui->Can_Connet_page_label_title1, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->Can_Connet_page_label_title1, -3, 2);
    lv_obj_set_size(ui->Can_Connet_page_label_title1, 68, 13);

    //Write style for Can_Connet_page_label_title1, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->Can_Connet_page_label_title1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->Can_Connet_page_label_title1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->Can_Connet_page_label_title1, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->Can_Connet_page_label_title1, &lv_font_HarmonyOS_Sans_SC_Medium_10, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->Can_Connet_page_label_title1, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->Can_Connet_page_label_title1, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->Can_Connet_page_label_title1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->Can_Connet_page_label_title1, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->Can_Connet_page_label_title1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->Can_Connet_page_label_title1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->Can_Connet_page_label_title1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->Can_Connet_page_label_title1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->Can_Connet_page_label_title1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->Can_Connet_page_label_title1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes Can_Connet_page_label_voltage
    ui->Can_Connet_page_label_voltage = lv_label_create(ui->Can_Connet_page_control_meter);
    lv_label_set_text(ui->Can_Connet_page_label_voltage, "电压:");
    lv_label_set_long_mode(ui->Can_Connet_page_label_voltage, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->Can_Connet_page_label_voltage, 6, 29);
    lv_obj_set_size(ui->Can_Connet_page_label_voltage, 58, 12);

    //Write style for Can_Connet_page_label_voltage, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->Can_Connet_page_label_voltage, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->Can_Connet_page_label_voltage, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->Can_Connet_page_label_voltage, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->Can_Connet_page_label_voltage, &lv_font_HarmonyOS_Sans_SC_Light_10, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->Can_Connet_page_label_voltage, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->Can_Connet_page_label_voltage, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->Can_Connet_page_label_voltage, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->Can_Connet_page_label_voltage, LV_TEXT_ALIGN_LEFT, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->Can_Connet_page_label_voltage, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->Can_Connet_page_label_voltage, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->Can_Connet_page_label_voltage, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->Can_Connet_page_label_voltage, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->Can_Connet_page_label_voltage, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->Can_Connet_page_label_voltage, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes Can_Connet_page_label_speed
    ui->Can_Connet_page_label_speed = lv_label_create(ui->Can_Connet_page_control_meter);
    lv_label_set_text(ui->Can_Connet_page_label_speed, "速度:");
    lv_label_set_long_mode(ui->Can_Connet_page_label_speed, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->Can_Connet_page_label_speed, 176, 2);
    lv_obj_set_size(ui->Can_Connet_page_label_speed, 61, 13);

    //Write style for Can_Connet_page_label_speed, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->Can_Connet_page_label_speed, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->Can_Connet_page_label_speed, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->Can_Connet_page_label_speed, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->Can_Connet_page_label_speed, &lv_font_HarmonyOS_Sans_SC_Light_10, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->Can_Connet_page_label_speed, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->Can_Connet_page_label_speed, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->Can_Connet_page_label_speed, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->Can_Connet_page_label_speed, LV_TEXT_ALIGN_LEFT, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->Can_Connet_page_label_speed, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->Can_Connet_page_label_speed, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->Can_Connet_page_label_speed, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->Can_Connet_page_label_speed, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->Can_Connet_page_label_speed, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->Can_Connet_page_label_speed, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes Can_Connet_page_label_current
    ui->Can_Connet_page_label_current = lv_label_create(ui->Can_Connet_page_control_meter);
    lv_label_set_text(ui->Can_Connet_page_label_current, "电流:");
    lv_label_set_long_mode(ui->Can_Connet_page_label_current, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->Can_Connet_page_label_current, 4, 109);
    lv_obj_set_size(ui->Can_Connet_page_label_current, 63, 12);

    //Write style for Can_Connet_page_label_current, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->Can_Connet_page_label_current, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->Can_Connet_page_label_current, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->Can_Connet_page_label_current, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->Can_Connet_page_label_current, &lv_font_HarmonyOS_Sans_SC_Medium_10, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->Can_Connet_page_label_current, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->Can_Connet_page_label_current, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->Can_Connet_page_label_current, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->Can_Connet_page_label_current, LV_TEXT_ALIGN_LEFT, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->Can_Connet_page_label_current, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->Can_Connet_page_label_current, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->Can_Connet_page_label_current, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->Can_Connet_page_label_current, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->Can_Connet_page_label_current, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->Can_Connet_page_label_current, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes Can_Connet_page_label_temper
    ui->Can_Connet_page_label_temper = lv_label_create(ui->Can_Connet_page_control_meter);
    lv_label_set_text(ui->Can_Connet_page_label_temper, "温度:");
    lv_label_set_long_mode(ui->Can_Connet_page_label_temper, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->Can_Connet_page_label_temper, 6, 57);
    lv_obj_set_size(ui->Can_Connet_page_label_temper, 57, 12);

    //Write style for Can_Connet_page_label_temper, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->Can_Connet_page_label_temper, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->Can_Connet_page_label_temper, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->Can_Connet_page_label_temper, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->Can_Connet_page_label_temper, &lv_font_HarmonyOS_Sans_SC_Medium_10, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->Can_Connet_page_label_temper, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->Can_Connet_page_label_temper, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->Can_Connet_page_label_temper, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->Can_Connet_page_label_temper, LV_TEXT_ALIGN_LEFT, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->Can_Connet_page_label_temper, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->Can_Connet_page_label_temper, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->Can_Connet_page_label_temper, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->Can_Connet_page_label_temper, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->Can_Connet_page_label_temper, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->Can_Connet_page_label_temper, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes Can_Connet_page_voltage
    ui->Can_Connet_page_voltage = lv_label_create(ui->Can_Connet_page_control_meter);
    lv_label_set_text(ui->Can_Connet_page_voltage, "0");
    lv_label_set_long_mode(ui->Can_Connet_page_voltage, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->Can_Connet_page_voltage, 58, 26);
    lv_obj_set_size(ui->Can_Connet_page_voltage, 95, 19);

    //Write style for Can_Connet_page_voltage, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->Can_Connet_page_voltage, 1, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_opa(ui->Can_Connet_page_voltage, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_color(ui->Can_Connet_page_voltage, lv_color_hex(0xe2e0e0), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_side(ui->Can_Connet_page_voltage, LV_BORDER_SIDE_FULL, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->Can_Connet_page_voltage, 3, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->Can_Connet_page_voltage, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->Can_Connet_page_voltage, &lv_font_HarmonyOS_Sans_SC_Medium_10, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->Can_Connet_page_voltage, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->Can_Connet_page_voltage, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->Can_Connet_page_voltage, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->Can_Connet_page_voltage, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->Can_Connet_page_voltage, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->Can_Connet_page_voltage, lv_color_hex(0x3D324A), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->Can_Connet_page_voltage, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->Can_Connet_page_voltage, 4, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->Can_Connet_page_voltage, 4, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->Can_Connet_page_voltage, 4, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->Can_Connet_page_voltage, 4, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->Can_Connet_page_voltage, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes Can_Connet_page_temper
    ui->Can_Connet_page_temper = lv_label_create(ui->Can_Connet_page_control_meter);
    lv_label_set_text(ui->Can_Connet_page_temper, "0");
    lv_label_set_long_mode(ui->Can_Connet_page_temper, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->Can_Connet_page_temper, 58, 51);
    lv_obj_set_size(ui->Can_Connet_page_temper, 95, 19);

    //Write style for Can_Connet_page_temper, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->Can_Connet_page_temper, 1, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_opa(ui->Can_Connet_page_temper, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_color(ui->Can_Connet_page_temper, lv_color_hex(0xe2e0e0), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_side(ui->Can_Connet_page_temper, LV_BORDER_SIDE_FULL, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->Can_Connet_page_temper, 3, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->Can_Connet_page_temper, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->Can_Connet_page_temper, &lv_font_HarmonyOS_Sans_SC_Medium_10, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->Can_Connet_page_temper, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->Can_Connet_page_temper, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->Can_Connet_page_temper, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->Can_Connet_page_temper, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->Can_Connet_page_temper, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->Can_Connet_page_temper, lv_color_hex(0x3D324A), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->Can_Connet_page_temper, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->Can_Connet_page_temper, 4, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->Can_Connet_page_temper, 4, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->Can_Connet_page_temper, 4, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->Can_Connet_page_temper, 4, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->Can_Connet_page_temper, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes Can_Connet_page_current
    ui->Can_Connet_page_current = lv_label_create(ui->Can_Connet_page_control_meter);
    lv_label_set_text(ui->Can_Connet_page_current, "0");
    lv_label_set_long_mode(ui->Can_Connet_page_current, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->Can_Connet_page_current, 58, 106);
    lv_obj_set_size(ui->Can_Connet_page_current, 95, 19);

    //Write style for Can_Connet_page_current, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->Can_Connet_page_current, 1, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_opa(ui->Can_Connet_page_current, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_color(ui->Can_Connet_page_current, lv_color_hex(0xe2e0e0), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_side(ui->Can_Connet_page_current, LV_BORDER_SIDE_FULL, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->Can_Connet_page_current, 3, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->Can_Connet_page_current, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->Can_Connet_page_current, &lv_font_HarmonyOS_Sans_SC_Medium_10, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->Can_Connet_page_current, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->Can_Connet_page_current, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->Can_Connet_page_current, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->Can_Connet_page_current, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->Can_Connet_page_current, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->Can_Connet_page_current, lv_color_hex(0x3D324A), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->Can_Connet_page_current, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->Can_Connet_page_current, 4, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->Can_Connet_page_current, 4, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->Can_Connet_page_current, 4, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->Can_Connet_page_current, 4, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->Can_Connet_page_current, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes Can_Connet_page_Position
    ui->Can_Connet_page_Position = lv_label_create(ui->Can_Connet_page_control_meter);
    lv_label_set_text(ui->Can_Connet_page_Position, "0");
    lv_label_set_long_mode(ui->Can_Connet_page_Position, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->Can_Connet_page_Position, 58, 80);
    lv_obj_set_size(ui->Can_Connet_page_Position, 95, 19);

    //Write style for Can_Connet_page_Position, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->Can_Connet_page_Position, 1, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_opa(ui->Can_Connet_page_Position, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_color(ui->Can_Connet_page_Position, lv_color_hex(0xe2e0e0), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_side(ui->Can_Connet_page_Position, LV_BORDER_SIDE_FULL, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->Can_Connet_page_Position, 3, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->Can_Connet_page_Position, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->Can_Connet_page_Position, &lv_font_HarmonyOS_Sans_SC_Medium_10, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->Can_Connet_page_Position, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->Can_Connet_page_Position, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->Can_Connet_page_Position, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->Can_Connet_page_Position, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->Can_Connet_page_Position, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->Can_Connet_page_Position, lv_color_hex(0x3D324A), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->Can_Connet_page_Position, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->Can_Connet_page_Position, 4, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->Can_Connet_page_Position, 4, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->Can_Connet_page_Position, 4, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->Can_Connet_page_Position, 4, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->Can_Connet_page_Position, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes Can_Connet_page_label_position
    ui->Can_Connet_page_label_position = lv_label_create(ui->Can_Connet_page_control_meter);
    lv_label_set_text(ui->Can_Connet_page_label_position, "位置:");
    lv_label_set_long_mode(ui->Can_Connet_page_label_position, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->Can_Connet_page_label_position, 5, 83);
    lv_obj_set_size(ui->Can_Connet_page_label_position, 61, 13);

    //Write style for Can_Connet_page_label_position, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->Can_Connet_page_label_position, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->Can_Connet_page_label_position, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->Can_Connet_page_label_position, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->Can_Connet_page_label_position, &lv_font_HarmonyOS_Sans_SC_Medium_10, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->Can_Connet_page_label_position, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->Can_Connet_page_label_position, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->Can_Connet_page_label_position, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->Can_Connet_page_label_position, LV_TEXT_ALIGN_LEFT, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->Can_Connet_page_label_position, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->Can_Connet_page_label_position, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->Can_Connet_page_label_position, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->Can_Connet_page_label_position, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->Can_Connet_page_label_position, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->Can_Connet_page_label_position, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes Can_Connet_page_state
    ui->Can_Connet_page_state = lv_label_create(ui->Can_Connet_page_control_meter);
    lv_label_set_text(ui->Can_Connet_page_state, "运行中");
    lv_label_set_long_mode(ui->Can_Connet_page_state, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->Can_Connet_page_state, 61, -1);
    lv_obj_set_size(ui->Can_Connet_page_state, 95, 19);

    //Write style for Can_Connet_page_state, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->Can_Connet_page_state, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->Can_Connet_page_state, 3, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->Can_Connet_page_state, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->Can_Connet_page_state, &lv_font_HarmonyOS_Sans_SC_Light_10, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->Can_Connet_page_state, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->Can_Connet_page_state, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->Can_Connet_page_state, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->Can_Connet_page_state, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->Can_Connet_page_state, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->Can_Connet_page_state, 4, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->Can_Connet_page_state, 4, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->Can_Connet_page_state, 4, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->Can_Connet_page_state, 4, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->Can_Connet_page_state, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes Can_Connet_page_back
    ui->Can_Connet_page_back = lv_imgbtn_create(ui->Can_Connet_page);
    lv_obj_add_flag(ui->Can_Connet_page_back, LV_OBJ_FLAG_CHECKABLE);
    lv_imgbtn_set_src(ui->Can_Connet_page_back, LV_IMGBTN_STATE_RELEASED, NULL, &_back01_ico_alpha_26x26, NULL);
    ui->Can_Connet_page_back_label = lv_label_create(ui->Can_Connet_page_back);
    lv_label_set_text(ui->Can_Connet_page_back_label, "");
    lv_label_set_long_mode(ui->Can_Connet_page_back_label, LV_LABEL_LONG_WRAP);
    lv_obj_align(ui->Can_Connet_page_back_label, LV_ALIGN_CENTER, 0, 0);
    lv_obj_set_style_pad_all(ui->Can_Connet_page_back, 0, LV_STATE_DEFAULT);
    lv_obj_set_pos(ui->Can_Connet_page_back, 4, 5);
    lv_obj_set_size(ui->Can_Connet_page_back, 26, 26);

    //Write style for Can_Connet_page_back, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_text_color(ui->Can_Connet_page_back, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->Can_Connet_page_back, &lv_font_montserratMedium_32, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->Can_Connet_page_back, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->Can_Connet_page_back, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->Can_Connet_page_back, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_clip_corner(ui->Can_Connet_page_back, true, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->Can_Connet_page_back, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write style for Can_Connet_page_back, Part: LV_PART_MAIN, State: LV_STATE_PRESSED.
    lv_obj_set_style_img_recolor_opa(ui->Can_Connet_page_back, 0, LV_PART_MAIN|LV_STATE_PRESSED);
    lv_obj_set_style_img_opa(ui->Can_Connet_page_back, 255, LV_PART_MAIN|LV_STATE_PRESSED);
    lv_obj_set_style_text_color(ui->Can_Connet_page_back, lv_color_hex(0xFF33FF), LV_PART_MAIN|LV_STATE_PRESSED);
    lv_obj_set_style_text_font(ui->Can_Connet_page_back, &lv_font_montserratMedium_10, LV_PART_MAIN|LV_STATE_PRESSED);
    lv_obj_set_style_text_opa(ui->Can_Connet_page_back, 255, LV_PART_MAIN|LV_STATE_PRESSED);
    lv_obj_set_style_shadow_width(ui->Can_Connet_page_back, 0, LV_PART_MAIN|LV_STATE_PRESSED);

    //Write style for Can_Connet_page_back, Part: LV_PART_MAIN, State: LV_STATE_CHECKED.
    lv_obj_set_style_img_recolor_opa(ui->Can_Connet_page_back, 0, LV_PART_MAIN|LV_STATE_CHECKED);
    lv_obj_set_style_img_opa(ui->Can_Connet_page_back, 255, LV_PART_MAIN|LV_STATE_CHECKED);
    lv_obj_set_style_text_color(ui->Can_Connet_page_back, lv_color_hex(0xFF33FF), LV_PART_MAIN|LV_STATE_CHECKED);
    lv_obj_set_style_text_font(ui->Can_Connet_page_back, &lv_font_montserratMedium_10, LV_PART_MAIN|LV_STATE_CHECKED);
    lv_obj_set_style_text_opa(ui->Can_Connet_page_back, 255, LV_PART_MAIN|LV_STATE_CHECKED);
    lv_obj_set_style_shadow_width(ui->Can_Connet_page_back, 0, LV_PART_MAIN|LV_STATE_CHECKED);

    //Write style for Can_Connet_page_back, Part: LV_PART_MAIN, State: LV_IMGBTN_STATE_RELEASED.
    lv_obj_set_style_img_recolor_opa(ui->Can_Connet_page_back, 0, LV_PART_MAIN|LV_IMGBTN_STATE_RELEASED);
    lv_obj_set_style_img_opa(ui->Can_Connet_page_back, 255, LV_PART_MAIN|LV_IMGBTN_STATE_RELEASED);

    //Write codes Can_Connet_page_line_1
    ui->Can_Connet_page_line_1 = lv_line_create(ui->Can_Connet_page);
    static lv_point_t Can_Connet_page_line_1[] = {{0, 0},{0, 130},};
    lv_line_set_points(ui->Can_Connet_page_line_1, Can_Connet_page_line_1, 2);
    lv_obj_set_pos(ui->Can_Connet_page_line_1, 171, 37);
    lv_obj_set_size(ui->Can_Connet_page_line_1, 2, 128);

    //Write style for Can_Connet_page_line_1, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_line_width(ui->Can_Connet_page_line_1, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_line_color(ui->Can_Connet_page_line_1, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_line_opa(ui->Can_Connet_page_line_1, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_line_rounded(ui->Can_Connet_page_line_1, true, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes Can_Connet_page_line_2
    ui->Can_Connet_page_line_2 = lv_line_create(ui->Can_Connet_page);
    static lv_point_t Can_Connet_page_line_2[] = {{0, 0},{180, 0},};
    lv_line_set_points(ui->Can_Connet_page_line_2, Can_Connet_page_line_2, 2);
    lv_obj_set_pos(ui->Can_Connet_page_line_2, 7, 56);
    lv_obj_set_size(ui->Can_Connet_page_line_2, 160, 1);

    //Write style for Can_Connet_page_line_2, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_line_width(ui->Can_Connet_page_line_2, 1, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_line_color(ui->Can_Connet_page_line_2, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_line_opa(ui->Can_Connet_page_line_2, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_line_rounded(ui->Can_Connet_page_line_2, true, LV_PART_MAIN|LV_STATE_DEFAULT);

    //The custom code of Can_Connet_page.


    //Update current screen layout.
    lv_obj_update_layout(ui->Can_Connet_page);

 
}
