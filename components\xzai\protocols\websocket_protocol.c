#include <string.h>
#include "websocket_protocol.h"
#include "audio_mem.h"
#include "esp_log.h"
#include "system_util.h"
#include <arpa/inet.h>

#define TAG "WS_PROTOCOL"

static void _parse_server_hello(ws_protocol_t *protocol, cJSON *root)
{
    cJSON *transport = cJSON_GetObjectItem(root, "transport");
    if (transport == NULL || strcmp(transport->valuestring, "websocket") != 0)
    {
        ESP_LOGE(TAG, "Unsupported transport: %s", transport->valuestring);
        return;
    }

    cJSON *audio_params = cJSON_GetObjectItem(root, "audio_params");
    if (audio_params != NULL)
    {
        cJSON *sample_rate = cJSON_GetObjectItem(audio_params, "sample_rate");
        if (sample_rate != NULL)
        {
            protocol->base.server_sample_rate = sample_rate->valueint;
        }
    }

    xEventGroupSetBits(protocol->event_group_handle, WEBSOCKET_SERVER_HELLO_EVENT);
}

static void _ws_client_on_connected(void *arg)
{
    ws_protocol_t *protocol = (ws_protocol_t *)arg;
}

static void _ws_client_on_disconnected(void *arg)
{
    ws_protocol_t *protocol = (ws_protocol_t *)arg;

    ESP_LOGI(TAG, "Websocket disconnected");
    if (protocol->base.audio_channel_closed_cb)
    {
        protocol->base.audio_channel_closed_cb(protocol->base.user_data);
    }
}

static void _ws_client_on_data(void *arg, const char *data, size_t len, bool binary)
{
    ws_protocol_t *protocol = (ws_protocol_t *)arg;

    if (binary)
    {
        if (protocol->base.incoming_audio_cb)
        {
            audio_stream_packet_t packet;
            if (PROTOCOL_VERSION == 3)
            {
            }
            else if (PROTOCOL_VERSION == 2)
            {
            }
            else
            {
                packet.timestamp = 0;
                packet.payload = data;
                packet.size = len;

                protocol->base.incoming_audio_cb(protocol->base.user_data, &packet);
            }
        }
    }
    else
    {
        // Parse JSON data
        cJSON *root = cJSON_Parse(data);
        cJSON *type = cJSON_GetObjectItem(root, "type");

        ESP_LOGD(TAG, "incoming json: %s", cJSON_Print(root));

        if (type != NULL)
        {
            if (strcmp(type->valuestring, "hello") == 0)
            {
                _parse_server_hello(protocol, root);
            }
            else
            {
                if (protocol->base.incoming_json_cb != NULL)
                {
                    protocol->base.incoming_json_cb(protocol->base.user_data, root);
                }
            }
        }
        else
        {
            ESP_LOGE(TAG, "Missing message type, data: %s", data);
        }
        cJSON_Delete(root);
    }
    protocol->base.last_incoming_time = time(NULL);
}

static void _ws_client_on_error(void *arg, int err)
{
    ws_protocol_t *protocol = (ws_protocol_t *)arg;
}

static void _start(struct protocol_base *base)
{
    ws_protocol_t *protocol = __containerof(base, ws_protocol_t, base);
}

static bool _open_audio_channel(struct protocol_base *base)
{
    ws_protocol_t *protocol = __containerof(base, ws_protocol_t, base);

    if (protocol->client)
    {
        ws_client_destroy(protocol->client);
        protocol->client = NULL;
    }

    protocol->base.error_occurred = false;

    char token[128] = {0};
    sprintf(token, "Bearer %s", protocol->ws_token);

    protocol->client = ws_client_create(protocol->ws_url);
    ws_client_set_user_data(protocol->client, protocol);

    ws_client_set_header(protocol->client, "Authorization", token);
    ws_client_set_header(protocol->client, "Protocol-Version", "1");
    ws_client_set_header(protocol->client, "Device-Id", get_mac_address());
    ws_client_set_header(protocol->client, "Client-Id", generate_uuid());

    ws_client_register_onconnected(protocol->client, _ws_client_on_connected);
    ws_client_register_ondisconnected(protocol->client, _ws_client_on_disconnected);
    ws_client_register_ondata(protocol->client, _ws_client_on_data);
    ws_client_register_onerror(protocol->client, _ws_client_on_error);

    if (!ws_client_connect(protocol->client, protocol->ws_url))
    {
        ESP_LOGE(TAG, "Failed to connect to websocket server");
        protocol->base.set_error(&protocol->base, "正在寻找可用服务");
        return false;
    }

    // Send hello message to describe the client
    // keys: message type, version, audio_params (format, sample_rate, channels)
    char message[256];
    sprintf(message, "{\"type\":\"hello\",\"version\":1,\"transport\":\"websocket\",\"audio_params\":{"
                     "\"format\":\"opus\",\"sample_rate\":16000,\"channels\":1,\"frame_duration\":%d}}",
            OPUS_FRAME_DURATION_MS);

    protocol->base.send_text(&protocol->base, message);

    // 等待服务器响应
    EventBits_t bits = xEventGroupWaitBits(protocol->event_group_handle, WEBSOCKET_SERVER_HELLO_EVENT, pdTRUE, pdFALSE, pdMS_TO_TICKS(10000));
    if (!(bits & WEBSOCKET_SERVER_HELLO_EVENT))
    {
        ESP_LOGE(TAG, "Failed to receive server hello");
        protocol->base.set_error(&protocol->base, "等待响应超时");
        return false;
    }

    if (protocol->base.audio_channel_opened_cb)
    {
        protocol->base.audio_channel_opened_cb(protocol->base.user_data);
    }

    return true;
}

static void _close_audio_channel(struct protocol_base *base)
{
    ws_protocol_t *protocol = __containerof(base, ws_protocol_t, base);

    if (protocol->client)
    {
        ws_client_destroy(protocol->client);
        protocol->client = NULL;
    }
}

static bool _is_audio_channel_opened(struct protocol_base *base)
{
    ws_protocol_t *protocol = __containerof(base, ws_protocol_t, base);
    return (protocol->client != NULL) && ws_client_is_connected(protocol->client) && !protocol->base.error_occurred && !protocol->base.is_timeout(&protocol->base);
}

static void _send_text(protocol_base_t *base, const char *text)
{
    // ws_protocol_t *protocol = (ws_protocol_t *)((char *)base - offsetof(ws_protocol_t, base));
    ws_protocol_t *protocol = __containerof(base, ws_protocol_t, base);

    if (protocol->client == NULL)
        return;

    ESP_LOGI(TAG, "send text: %s", text);

    if (!ws_client_send_string(protocol->client, text))
    {
        ESP_LOGE(TAG, "Failed to send text: %s", text);
        protocol->base.set_error(&protocol->base, "发送失败，请检查网络");
    }
}

static void _send_audio(struct protocol_base *base, audio_stream_packet_t *packet)
{
    ws_protocol_t *protocol = __containerof(base, ws_protocol_t, base);

    if (protocol->client == NULL)
        return;

    if (PROTOCOL_VERSION == 3)
    {
    }
    else if (PROTOCOL_VERSION == 2)
    {
    }
    else
    {
        ws_client_send_data(protocol->client, packet->payload, packet->size);
    }
}

ws_protocol_t *ws_protocol_create(ws_protocol_cfg_t *cfg)
{
    ws_protocol_t *protocol = (ws_protocol_t *)audio_calloc(1, sizeof(ws_protocol_t));

    if (protocol == NULL)
        goto PROTOCOL_CREATE_FAILED;

    protocol->ws_url = cfg->ws_url;
    protocol->ws_token = cfg->ws_token;

    protocol->event_group_handle = xEventGroupCreate();
    if (protocol->event_group_handle == NULL)
        goto PROTOCOL_CREATE_FAILED;

    // 初始化基类函数
    protocol_base_init(&protocol->base);

    protocol->base.send_text = _send_text;
    protocol->base.start = _start;
    protocol->base.open_audio_channel = _open_audio_channel;
    protocol->base.close_audio_channel = _close_audio_channel;
    protocol->base.send_audio = _send_audio;
    protocol->base.is_audio_channel_opened = _is_audio_channel_opened;

    return protocol;
PROTOCOL_CREATE_FAILED:
    ws_protocol_destroy(protocol);
    return NULL;
}

void ws_protocol_destroy(struct ws_protocol *protocol)
{
    if (protocol)
    {
        if (protocol->client)
        {
            ws_client_destroy(protocol->client);
        }

        if (protocol->event_group_handle)
        {
            vEventGroupDelete(protocol->event_group_handle);
        }

        memset(protocol, 0x0, sizeof(ws_protocol_t));
        audio_free(protocol);
        protocol = NULL;
    }
}