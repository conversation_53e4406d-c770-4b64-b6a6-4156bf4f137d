# Project:   zlib_1_03
# Patched for zlib 1.1.2 <EMAIL> 19980430
# test works out-of-the-box, installs `somewhere' on demand

# Toolflags:
CCflags = -c -depend !Depend -IC: -g -throwback  -DRISCOS  -fah
C++flags = -c -depend !Depend -IC: -throwback
Linkflags = -aif -c++ -o $@
ObjAsmflags = -throwback -NoCache -depend !Depend
CMHGflags =
LibFileflags = -c -l -o $@
Squeezeflags = -o $@

# change the line below to where _you_ want the library installed.
libdest = lib:zlib

# Final targets:
@.lib:   @.o.adler32 @.o.compress @.o.crc32 @.o.deflate @.o.gzio \
        @.o.infblock @.o.infcodes @.o.inffast @.o.inflate @.o.inftrees @.o.infutil @.o.trees \
        @.o.uncompr @.o.zutil
        LibFile $(LibFileflags) @.o.adler32 @.o.compress @.o.crc32 @.o.deflate \
        @.o.gzio @.o.infblock @.o.infcodes @.o.inffast @.o.inflate @.o.inftrees @.o.infutil \
        @.o.trees @.o.uncompr @.o.zutil
test:   @.minigzip @.example @.lib
	@copy @.lib @.libc  A~C~DF~L~N~P~Q~RS~TV
	@echo running tests: hang on.
	@/@.minigzip -f -9 libc
	@/@.minigzip -d libc-gz
	@/@.minigzip -f -1 libc
	@/@.minigzip -d libc-gz
	@/@.minigzip -h -9 libc
	@/@.minigzip -d libc-gz
	@/@.minigzip -h -1 libc
	@/@.minigzip -d libc-gz
	@/@.minigzip -9 libc
	@/@.minigzip -d libc-gz
	@/@.minigzip -1 libc
	@/@.minigzip -d libc-gz
	@diff @.lib @.libc
	@echo that should have reported '@.lib and @.libc identical' if you have diff.
	@/@.example @.fred @.fred
	@echo that will have given lots of hello!'s.

@.minigzip:   @.o.minigzip @.lib C:o.Stubs
        Link $(Linkflags) @.o.minigzip @.lib C:o.Stubs
@.example:   @.o.example @.lib C:o.Stubs
        Link $(Linkflags) @.o.example @.lib C:o.Stubs

install: @.lib
	cdir $(libdest)
	cdir $(libdest).h
	@copy @.h.zlib $(libdest).h.zlib A~C~DF~L~N~P~Q~RS~TV
	@copy @.h.zconf $(libdest).h.zconf A~C~DF~L~N~P~Q~RS~TV
	@copy @.lib $(libdest).lib  A~C~DF~L~N~P~Q~RS~TV
	@echo okay, installed zlib in $(libdest)

clean:; remove @.minigzip
	remove @.example
	remove @.libc
	-wipe @.o.* F~r~cV
	remove @.fred

# User-editable dependencies:
.c.o:
        cc $(ccflags) -o $@ $<

# Static dependencies:

# Dynamic dependencies:
o.example:	c.example
o.example:	h.zlib
o.example:	h.zconf
o.minigzip:	c.minigzip
o.minigzip:	h.zlib
o.minigzip:	h.zconf
o.adler32:	c.adler32
o.adler32:	h.zlib
o.adler32:	h.zconf
o.compress:	c.compress
o.compress:	h.zlib
o.compress:	h.zconf
o.crc32:	c.crc32
o.crc32:	h.zlib
o.crc32:	h.zconf
o.deflate:	c.deflate
o.deflate:	h.deflate
o.deflate:	h.zutil
o.deflate:	h.zlib
o.deflate:	h.zconf
o.gzio:	c.gzio
o.gzio:	h.zutil
o.gzio:	h.zlib
o.gzio:	h.zconf
o.infblock:	c.infblock
o.infblock:	h.zutil
o.infblock:	h.zlib
o.infblock:	h.zconf
o.infblock:	h.infblock
o.infblock:	h.inftrees
o.infblock:	h.infcodes
o.infblock:	h.infutil
o.infcodes:	c.infcodes
o.infcodes:	h.zutil
o.infcodes:	h.zlib
o.infcodes:	h.zconf
o.infcodes:	h.inftrees
o.infcodes:	h.infblock
o.infcodes:	h.infcodes
o.infcodes:	h.infutil
o.infcodes:	h.inffast
o.inffast:	c.inffast
o.inffast:	h.zutil
o.inffast:	h.zlib
o.inffast:	h.zconf
o.inffast:	h.inftrees
o.inffast:	h.infblock
o.inffast:	h.infcodes
o.inffast:	h.infutil
o.inffast:	h.inffast
o.inflate:	c.inflate
o.inflate:	h.zutil
o.inflate:	h.zlib
o.inflate:	h.zconf
o.inflate:	h.infblock
o.inftrees:	c.inftrees
o.inftrees:	h.zutil
o.inftrees:	h.zlib
o.inftrees:	h.zconf
o.inftrees:	h.inftrees
o.inftrees:	h.inffixed
o.infutil:	c.infutil
o.infutil:	h.zutil
o.infutil:	h.zlib
o.infutil:	h.zconf
o.infutil:	h.infblock
o.infutil:	h.inftrees
o.infutil:	h.infcodes
o.infutil:	h.infutil
o.trees:	c.trees
o.trees:	h.deflate
o.trees:	h.zutil
o.trees:	h.zlib
o.trees:	h.zconf
o.trees:	h.trees
o.uncompr:	c.uncompr
o.uncompr:	h.zlib
o.uncompr:	h.zconf
o.zutil:	c.zutil
o.zutil:	h.zutil
o.zutil:	h.zlib
o.zutil:	h.zconf
