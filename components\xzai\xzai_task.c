#include "xzai.h"
#include "hal_wifi.h"

#define TAG "xzai_task"

extern bool xzai_check_ver(void *arg);

void xzai_loop_task(void *arg)
{
    xzai_handle_t xzai = (xzai_handle_t)arg;
    EventGroupHandle_t event_group = xzai_get_event_group(xzai);
    QueueHandle_t task_queue = xzai_get_task_queue(xzai);

    xzai_task_exec task;

    while (xzai_is_running(xzai))
    {
        EventBits_t bits = xEventGroupWaitBits(event_group,
                                               SCHEDULE_EVENT | AUDIO_INPUT_READY_EVENT | AUDIO_OUTPUT_READY_EVENT,
                                               pdTRUE, pdFALSE, portMAX_DELAY);

        if (bits & AUDIO_INPUT_READY_EVENT)
        {
            xzai_input_audio(xzai);
        }
        else if (bits & AUDIO_OUTPUT_READY_EVENT)
        {
            xzai_output_audio(xzai);
        }

        if (bits & SCHEDULE_EVENT)
        {
            UBaseType_t queue_len = uxQueueMessagesWaiting(task_queue);
            for (int i = 0; i < queue_len; i++)
            {
                BaseType_t res = xQueueReceive(task_queue, &task, portMAX_DELAY);
                if (res == pdTRUE)
                {
                    task(xzai);
                }
            }
        }
    }

    ESP_LOGI(TAG, "exit the task of xzai loop");
    vTaskDelete(NULL);
}

void xzai_check_ver_task(void *arg)
{
    xzai_handle_t xzai = (xzai_handle_t)arg;

    const int MAX_RETRY = 10;
    int retry_count = 0;

    while (xzai_is_running(xzai))
    {
        // 等待WIFI连接成功
        if (hal_wifi_get_connect_state() == false)
        {
            vTaskDelay(pdMS_TO_TICKS(1000));
            continue;
        }

        if (!xzai_check_ver(xzai))
        {
            retry_count++;
            if (retry_count >= MAX_RETRY)
            {
                ESP_LOGE(TAG, "Too many retries, exit version check");
                break;
            }
            ESP_LOGW(TAG, "Check failed, retry in %d seconds (%d/%d)", 60, retry_count, MAX_RETRY);
            vTaskDelay(pdMS_TO_TICKS(60000));
            continue;
        }

        xzai_activation_t *activation_data = xzai_get_activation(xzai);
        if (activation_data->has_activation_code)
        {
            xzai_set_state(xzai, XZAI_STATE_ACTIVATING);

            for (int i = 0; i < 60; i++)
            {
                if (xzai_get_state(xzai) == XZAI_STATE_IDLE)
                {
                    break;
                }
                vTaskDelay(pdMS_TO_TICKS(1000));
            }
            continue;
        }

        xzai_set_state(xzai, XZAI_STATE_IDLE);
        // exit the loop if idle
        break;
    }

    ESP_LOGI(TAG, "exit the task of xzai version check");

    vTaskDelete(NULL);
}

void xzai_key_task(void *arg)
{
    // 暂时注释掉按键功能，因为缺少相关硬件抽象层
    xzai_handle_t xzai = (xzai_handle_t)arg;
    
    ESP_LOGI(TAG, "Key task disabled - no hardware abstraction layer");
    
    while (xzai_is_running(xzai))
    {
        vTaskDelay(pdMS_TO_TICKS(1000)); // 简单延时，避免占用CPU
    }

    ESP_LOGI(TAG, "exit the task of xzai key detect");
    vTaskDelete(NULL);
}