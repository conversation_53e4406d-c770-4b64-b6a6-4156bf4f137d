
#include <stdint.h>

#define REG_DLY 0xffff
#define REGLIST_TAIL 0xffff /* Array end token */

static const uint16_t bf20a6_default_init_regs[][2] = {
    {0xf2,0x01},
    {0x12,0x20},
    {0x3a,0x00},
    {0xe1,0x92},
    {0xe3,0x12},// PLL Control, important for framerate(choice: 0x02\0x12\0x22\0x32\0x82)
    {0xe0,0x00},
    {0x2a,0x98},
    {0xcd,0x17},
    {0xc0,0x10},
    {0xc6,0x1d},
    {0x10,0x35},
    {0xe2,0x09},
    {0xe4,0x72},
    {0xe5,0x22},
    {0xe6,0x24},
    {0xe7,0x64},
    {0xe8,0xa2}, // DVP:a2},  SPI:f2        VDDIO=1.8V,E8[2]=1},VDDIO=2.8V,E8[2]=0},
    {0x4a,0x00},
    {0x00,0x03},
    {0x1f,0x02},
    {0x22,0x02},
    {0x0c,0x31},

    {0x00,0x00},
    {0x60,0x81},
    {0x61,0x81},

    {0xa0,0x08},
    {0x01,0x1a},
    // {0x01,0x1a},
    // {0x01,0x1a},
    // {0x02,0x15},
    // {0x02,0x15},
    {0x02,0x15},
    {0x13,0x08},
    {0x8a,0x96},
    {0x8b,0x06},
    {0x87,0x18},


    {0x34,0x48}, // lens
    {0x35,0x40},
    {0x36,0x40},

    {0x71,0x44},
    {0x72,0x48},
    {0x74,0xa2},
    {0x75,0xa9},
    {0x78,0x12},
    {0x79,0xa0},
    {0x7a,0x94},
    {0x7c,0x97},
    {0x40,0x30},
    {0x41,0x30},
    {0x42,0x28},
    {0x43,0x1f},
    {0x44,0x1c},
    {0x45,0x16},
    {0x46,0x13},
    {0x47,0x10},
    {0x48,0x0D},
    {0x49,0x0C},
    {0x4B,0x0A},
    {0x4C,0x0B},
    {0x4E,0x09},
    {0x4F,0x08},
    {0x50,0x08},


    {0x5f,0x29},
    {0x23,0x33},
    {0xa1,0x10}, // AWB
    {0xa2,0x0d},
    {0xa3,0x30},
    {0xa4,0x06},
    {0xa5,0x22},
    {0xa6,0x56},
    {0xa7,0x18},
    {0xa8,0x1a},
    {0xa9,0x12},
    {0xaa,0x12},
    {0xab,0x16},
    {0xac,0xb1},
    {0xba,0x12},
    {0xbb,0x12},
    {0xad,0x12},
    {0xae,0x56},
    {0xaf,0x0a},
    {0x3b,0x30},
    {0x3c,0x12},
    {0x3d,0x22},
    {0x3e,0x3f},
    {0x3f,0x28},
    {0xb8,0xc3},
    {0xb9,0xa3},
    {0x39,0x47}, // pure color threshold
    {0x26,0x13},
    {0x27,0x16},
    {0x28,0x14},
    {0x29,0x18},
    {0xee,0x0d},

        
    {0x13,0x05},
    {0x24,0x3C},
    {0x81,0x20},
    {0x82,0x40},
    {0x83,0x30},
    {0x84,0x58},
    {0x85,0x30},
    {0x92,0x08},
    {0x86,0x80},
    {0x8a,0x96},
    {0x91,0xff},
    {0x94,0x62},
    {0x9a,0x18}, // outdoor threshold
    {0xf0,0x45}, // integral time control, important for framerate(choice: 0x46\0x45\0x44..)
    {0x51,0x17}, // color normal
    {0x52,0x03},
    {0x53,0x5F},
    {0x54,0x47},
    {0x55,0x66},
    {0x56,0x0F},
    {0x7e,0x14},
    {0x57,0x36}, // color
    {0x58,0x2A},
    {0x59,0xAA},
    {0x5a,0xA8},
    {0x5b,0x43},
    {0x5c,0x10},
    {0x5d,0x00},
    {0x7d,0x36},
    {0x5e,0x10},

    {0xd6,0x88}, // contrast
    {0xd5,0x20}, // bright
    {0xb0,0x84}, // low light ctrl in gray section
    {0xb5,0x08}, // the threshold of GLB_GAIN
    {0xb1,0xc8}, // saturation
    {0xb2,0xc0},
    {0xb3,0xd0},
    {0xb4,0xB0},

    {0x32,0x10},
    // {0x8a,0x00},
    // {0x8b,0x10},
    {0xa0,0x09},
    {0x00,0x03},
    {0x0b,0x02},
    {REGLIST_TAIL, 0x00},
};
