/*
* Copyright 2016 The Alatsi Project Authors (https://github.com/sorkintype/alatsi)
* This Font Software is licensed under the SIL Open Font License, Version 1.1.
* And is also available with a FAQ at: http://scripts.sil.org/OFL
*/
/*******************************************************************************
 * Size: 13 px
 * Bpp: 4
 * Opts: undefined
 ******************************************************************************/

#ifdef LV_LVGL_H_INCLUDE_SIMPLE
#include "lvgl.h"
#else
#include "lvgl.h"
#endif

#ifndef LV_FONT_MONTSERRATMEDIUM_13
#define LV_FONT_MONTSERRATMEDIUM_13 1
#endif

#if LV_FONT_MONTSERRATMEDIUM_13

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0xf, 0x70, 0xe6, 0xe, 0x60, 0xd5, 0xd, 0x40,
    0xc4, 0x3, 0x10, 0x83, 0xd, 0x60,

    /* U+0022 "\"" */
    0x2e, 0xd, 0x42, 0xd0, 0xc3, 0x2d, 0xc, 0x30,
    0x60, 0x61,

    /* U+0023 "#" */
    0x0, 0x1d, 0x0, 0xb3, 0x0, 0x3, 0xb0, 0xd,
    0x10, 0x3f, 0xff, 0xff, 0xff, 0xc0, 0x7, 0x70,
    0x1d, 0x0, 0x0, 0x95, 0x3, 0xb0, 0x0, 0xa,
    0x30, 0x49, 0x0, 0x9f, 0xff, 0xff, 0xff, 0x50,
    0xe, 0x0, 0x85, 0x0, 0x1, 0xd0, 0xb, 0x30,
    0x0,

    /* U+0024 "$" */
    0x0, 0x5, 0x80, 0x0, 0x0, 0x5, 0x80, 0x0,
    0x2, 0xbf, 0xfd, 0x80, 0xe, 0x97, 0x94, 0x90,
    0x3f, 0x15, 0x80, 0x0, 0xe, 0xb8, 0x80, 0x0,
    0x1, 0xaf, 0xfb, 0x30, 0x0, 0x5, 0xa9, 0xf4,
    0x0, 0x5, 0x80, 0xc8, 0x4c, 0x46, 0x96, 0xf4,
    0x7, 0xdf, 0xfd, 0x50, 0x0, 0x5, 0x80, 0x0,
    0x0, 0x2, 0x40, 0x0,

    /* U+0025 "%" */
    0xa, 0xbb, 0x10, 0x4, 0xb0, 0x5, 0x80, 0x58,
    0x0, 0xd1, 0x0, 0x75, 0x2, 0xa0, 0xa5, 0x0,
    0x4, 0xa0, 0x67, 0x5a, 0x0, 0x0, 0x8, 0xc9,
    0x2d, 0x2a, 0xc7, 0x0, 0x0, 0xb, 0x48, 0x60,
    0xa3, 0x0, 0x6, 0x90, 0xb1, 0x6, 0x70, 0x2,
    0xd0, 0x9, 0x40, 0x84, 0x0, 0xc3, 0x0, 0x1b,
    0xa9, 0x0,

    /* U+0026 "&" */
    0x0, 0x7e, 0xea, 0x0, 0x0, 0x2f, 0x10, 0xc5,
    0x0, 0x2, 0xf1, 0x1d, 0x30, 0x0, 0xa, 0xcd,
    0x70, 0x0, 0x2, 0xcd, 0xd1, 0x2, 0x1, 0xe5,
    0x9, 0xd1, 0xf2, 0x5d, 0x0, 0x9, 0xfc, 0x3,
    0xf6, 0x12, 0x8f, 0xd2, 0x5, 0xdf, 0xea, 0x28,
    0x60, 0x0, 0x0, 0x0, 0x0,

    /* U+0027 "'" */
    0x2e, 0x2d, 0x2d, 0x6,

    /* U+0028 "(" */
    0x7, 0xb0, 0xe4, 0x3f, 0x7, 0xb0, 0xa9, 0xb,
    0x80, 0xc7, 0xb, 0x80, 0xa9, 0x7, 0xb0, 0x3f,
    0x0, 0xe4, 0x7, 0xb0,

    /* U+0029 ")" */
    0x5d, 0x0, 0xe, 0x40, 0x9, 0x90, 0x5, 0xd0,
    0x3, 0xf0, 0x1, 0xf1, 0x1, 0xf2, 0x1, 0xf1,
    0x3, 0xf0, 0x5, 0xd0, 0x9, 0x90, 0xe, 0x40,
    0x5d, 0x0,

    /* U+002A "*" */
    0x0, 0xc0, 0x7, 0xad, 0x8a, 0xa, 0xfc, 0x8,
    0x8c, 0x6a, 0x0, 0xb0, 0x0,

    /* U+002B "+" */
    0x0, 0x8, 0x30, 0x0, 0x0, 0xc5, 0x0, 0x0,
    0xc, 0x50, 0x1, 0xee, 0xff, 0xea, 0x1, 0x1c,
    0x61, 0x10, 0x0, 0xc5, 0x0,

    /* U+002C "," */
    0x19, 0x14, 0xf4, 0xe, 0x3, 0xa0,

    /* U+002D "-" */
    0x4f, 0xff, 0x30, 0x22, 0x20,

    /* U+002E "." */
    0x2b, 0x13, 0xe3,

    /* U+002F "/" */
    0x0, 0x0, 0x17, 0x0, 0x0, 0x6, 0xb0, 0x0,
    0x0, 0xc5, 0x0, 0x0, 0x1f, 0x0, 0x0, 0x7,
    0xa0, 0x0, 0x0, 0xc5, 0x0, 0x0, 0x2f, 0x0,
    0x0, 0x7, 0xa0, 0x0, 0x0, 0xd4, 0x0, 0x0,
    0x2f, 0x0, 0x0, 0x8, 0x90, 0x0, 0x0, 0xd4,
    0x0, 0x0, 0x3e, 0x0, 0x0, 0x0,

    /* U+0030 "0" */
    0x0, 0x7d, 0xfc, 0x30, 0x0, 0x8f, 0x63, 0x9f,
    0x30, 0xf, 0x50, 0x0, 0xab, 0x4, 0xf0, 0x0,
    0x5, 0xf0, 0x5f, 0x0, 0x0, 0x4f, 0x4, 0xf0,
    0x0, 0x5, 0xf0, 0xf, 0x50, 0x0, 0xab, 0x0,
    0x8e, 0x63, 0x8f, 0x30, 0x0, 0x7d, 0xfc, 0x30,
    0x0,

    /* U+0031 "1" */
    0xef, 0xf7, 0x22, 0xd7, 0x0, 0xd7, 0x0, 0xd7,
    0x0, 0xd7, 0x0, 0xd7, 0x0, 0xd7, 0x0, 0xd7,
    0x0, 0xd7,

    /* U+0032 "2" */
    0x18, 0xef, 0xd7, 0x0, 0x8c, 0x53, 0x6f, 0x50,
    0x0, 0x0, 0xb, 0x90, 0x0, 0x0, 0xe, 0x60,
    0x0, 0x0, 0xac, 0x0, 0x0, 0xb, 0xc1, 0x0,
    0x0, 0xbc, 0x0, 0x0, 0x1c, 0xc2, 0x22, 0x20,
    0x8f, 0xff, 0xff, 0xf0,

    /* U+0033 "3" */
    0x8f, 0xff, 0xff, 0x71, 0x22, 0x29, 0xe1, 0x0,
    0x4, 0xf2, 0x0, 0x2, 0xf7, 0x0, 0x0, 0x4c,
    0xed, 0x20, 0x0, 0x0, 0xba, 0x0, 0x0, 0x8,
    0xca, 0x95, 0x36, 0xe7, 0x3a, 0xef, 0xd7, 0x0,

    /* U+0034 "4" */
    0x0, 0x0, 0x3f, 0x30, 0x0, 0x0, 0x1e, 0x60,
    0x0, 0x0, 0xb, 0xb0, 0x0, 0x0, 0x7, 0xe1,
    0x0, 0x0, 0x3, 0xf3, 0x6, 0xd0, 0x1, 0xe7,
    0x0, 0x6d, 0x0, 0x7f, 0xff, 0xff, 0xff, 0x81,
    0x22, 0x22, 0x8d, 0x21, 0x0, 0x0, 0x7, 0xd0,
    0x0,

    /* U+0035 "5" */
    0xb, 0xff, 0xff, 0x80, 0xc7, 0x22, 0x21, 0xe,
    0x40, 0x0, 0x0, 0xff, 0xfd, 0x80, 0x2, 0x23,
    0x6e, 0x80, 0x0, 0x0, 0x6e, 0x0, 0x0, 0x6,
    0xe9, 0xb5, 0x35, 0xe9, 0x29, 0xef, 0xe8, 0x0,

    /* U+0036 "6" */
    0x0, 0x5c, 0xfe, 0xb0, 0x6, 0xe6, 0x32, 0x40,
    0xf, 0x40, 0x0, 0x0, 0x4f, 0x4c, 0xec, 0x50,
    0x5f, 0xe5, 0x27, 0xf4, 0x4f, 0x50, 0x0, 0xb9,
    0x1f, 0x50, 0x0, 0xb9, 0x9, 0xe4, 0x16, 0xf3,
    0x0, 0x8e, 0xfd, 0x50,

    /* U+0037 "7" */
    0x9f, 0xff, 0xff, 0xf3, 0x9b, 0x22, 0x27, 0xe0,
    0x77, 0x0, 0xd, 0x80, 0x0, 0x0, 0x4f, 0x10,
    0x0, 0x0, 0xb9, 0x0, 0x0, 0x3, 0xf2, 0x0,
    0x0, 0xa, 0xb0, 0x0, 0x0, 0x2f, 0x40, 0x0,
    0x0, 0x9c, 0x0, 0x0,

    /* U+0038 "8" */
    0x2, 0xaf, 0xfd, 0x50, 0xd, 0xb2, 0x17, 0xf3,
    0x1f, 0x30, 0x0, 0xd6, 0xd, 0xa1, 0x5, 0xf3,
    0x4, 0xff, 0xff, 0x90, 0x2f, 0x71, 0x3, 0xe7,
    0x6e, 0x0, 0x0, 0x9c, 0x2f, 0x82, 0x14, 0xe8,
    0x4, 0xcf, 0xfd, 0x70,

    /* U+0039 "9" */
    0x6, 0xdf, 0xe8, 0x0, 0x5f, 0x51, 0x3c, 0x90,
    0x9a, 0x0, 0x4, 0xf1, 0x7e, 0x30, 0x1b, 0xf4,
    0x9, 0xff, 0xf7, 0xf5, 0x0, 0x1, 0x1, 0xf4,
    0x0, 0x0, 0x7, 0xf0, 0x5, 0x33, 0x7f, 0x60,
    0xb, 0xff, 0xc5, 0x0,

    /* U+003A ":" */
    0x3e, 0x22, 0xb2, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xb1, 0x3e, 0x30,

    /* U+003B ";" */
    0x3e, 0x22, 0xb2, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x91, 0x4f, 0x40, 0xe0, 0x3a, 0x0,

    /* U+003C "<" */
    0x0, 0x0, 0x0, 0x30, 0x0, 0x29, 0xe8, 0x6,
    0xcd, 0x71, 0x1, 0xfa, 0x10, 0x0, 0x3, 0xae,
    0xa3, 0x0, 0x0, 0x16, 0xda, 0x0, 0x0, 0x0,
    0x10,

    /* U+003D "=" */
    0x2f, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xee, 0xee, 0xea, 0x1, 0x11,
    0x11, 0x10,

    /* U+003E ">" */
    0x3, 0x0, 0x0, 0x1, 0xdc, 0x60, 0x0, 0x0,
    0x4a, 0xe9, 0x30, 0x0, 0x4, 0xea, 0x0, 0x6c,
    0xd7, 0x11, 0xea, 0x40, 0x0, 0x1, 0x0, 0x0,
    0x0,

    /* U+003F "?" */
    0x19, 0xef, 0xd7, 0x9, 0xc4, 0x26, 0xf5, 0x0,
    0x0, 0xd, 0x70, 0x0, 0x4, 0xf2, 0x0, 0x5,
    0xf4, 0x0, 0x0, 0xe7, 0x0, 0x0, 0x1, 0x0,
    0x0, 0x0, 0x82, 0x0, 0x0, 0x1e, 0x40, 0x0,

    /* U+0040 "@" */
    0x0, 0x2, 0x9d, 0xdd, 0xc6, 0x0, 0x0, 0x6,
    0xd5, 0x0, 0x1, 0x8d, 0x20, 0x4, 0xc0, 0x4d,
    0xfd, 0x6f, 0x3c, 0x0, 0xd2, 0x2f, 0x61, 0x3d,
    0xf0, 0x86, 0x2c, 0x9, 0xa0, 0x0, 0x5f, 0x3,
    0xa5, 0xa0, 0xb7, 0x0, 0x2, 0xf0, 0x1c, 0x5a,
    0x9, 0xa0, 0x0, 0x5f, 0x2, 0xb2, 0xc0, 0x2f,
    0x60, 0x3d, 0xf2, 0x87, 0xd, 0x20, 0x4d, 0xfd,
    0x39, 0xfa, 0x0, 0x4c, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6d, 0x50, 0x0, 0x42, 0x0, 0x0,
    0x0, 0x39, 0xde, 0xeb, 0x30, 0x0,

    /* U+0041 "A" */
    0x0, 0x0, 0x2f, 0x90, 0x0, 0x0, 0x0, 0x9,
    0xdf, 0x10, 0x0, 0x0, 0x1, 0xf4, 0xc8, 0x0,
    0x0, 0x0, 0x7d, 0x5, 0xf0, 0x0, 0x0, 0xe,
    0x70, 0xe, 0x70, 0x0, 0x6, 0xf1, 0x0, 0x8e,
    0x0, 0x0, 0xdf, 0xee, 0xef, 0xf6, 0x0, 0x5f,
    0x31, 0x11, 0x1a, 0xd0, 0xc, 0x90, 0x0, 0x0,
    0x2f, 0x40,

    /* U+0042 "B" */
    0xaf, 0xff, 0xfd, 0x80, 0xa, 0xb1, 0x11, 0x4e,
    0x70, 0xaa, 0x0, 0x0, 0xaa, 0xa, 0xa0, 0x0,
    0x2e, 0x60, 0xaf, 0xff, 0xff, 0xd1, 0xa, 0xb2,
    0x22, 0x3b, 0xd0, 0xaa, 0x0, 0x0, 0x2f, 0x2a,
    0xb1, 0x11, 0x29, 0xe0, 0xaf, 0xff, 0xfe, 0xb3,
    0x0,

    /* U+0043 "C" */
    0x0, 0x29, 0xef, 0xe8, 0x10, 0x3f, 0xb5, 0x35,
    0xca, 0xd, 0xa0, 0x0, 0x0, 0x3, 0xf1, 0x0,
    0x0, 0x0, 0x5f, 0x0, 0x0, 0x0, 0x3, 0xf1,
    0x0, 0x0, 0x0, 0xd, 0x90, 0x0, 0x0, 0x0,
    0x3f, 0xb5, 0x35, 0xca, 0x0, 0x29, 0xef, 0xe8,
    0x10,

    /* U+0044 "D" */
    0xaf, 0xff, 0xfd, 0x80, 0x0, 0xab, 0x22, 0x25,
    0xdd, 0x10, 0xaa, 0x0, 0x0, 0xd, 0xa0, 0xaa,
    0x0, 0x0, 0x5, 0xf0, 0xaa, 0x0, 0x0, 0x3,
    0xf1, 0xaa, 0x0, 0x0, 0x5, 0xf0, 0xaa, 0x0,
    0x0, 0xc, 0xa0, 0xab, 0x22, 0x25, 0xdd, 0x10,
    0xaf, 0xff, 0xfd, 0x80, 0x0,

    /* U+0045 "E" */
    0xaf, 0xff, 0xff, 0xca, 0xb2, 0x22, 0x21, 0xaa,
    0x0, 0x0, 0xa, 0xa0, 0x0, 0x0, 0xaf, 0xff,
    0xff, 0x3a, 0xb2, 0x22, 0x20, 0xaa, 0x0, 0x0,
    0xa, 0xb2, 0x22, 0x22, 0xaf, 0xff, 0xff, 0xf0,

    /* U+0046 "F" */
    0xaf, 0xff, 0xff, 0xca, 0xb2, 0x22, 0x21, 0xaa,
    0x0, 0x0, 0xa, 0xa0, 0x0, 0x0, 0xaf, 0xff,
    0xff, 0x3a, 0xb2, 0x22, 0x20, 0xaa, 0x0, 0x0,
    0xa, 0xa0, 0x0, 0x0, 0xaa, 0x0, 0x0, 0x0,

    /* U+0047 "G" */
    0x0, 0x29, 0xef, 0xe9, 0x10, 0x3f, 0xb5, 0x35,
    0xbb, 0xd, 0xa0, 0x0, 0x0, 0x3, 0xf1, 0x0,
    0x0, 0x0, 0x5f, 0x0, 0x0, 0x2, 0x83, 0xf1,
    0x0, 0x0, 0x5e, 0xd, 0xa0, 0x0, 0x5, 0xe0,
    0x3f, 0xb5, 0x34, 0xbe, 0x0, 0x29, 0xef, 0xe9,
    0x10,

    /* U+0048 "H" */
    0xaa, 0x0, 0x0, 0x1f, 0x3a, 0xa0, 0x0, 0x1,
    0xf3, 0xaa, 0x0, 0x0, 0x1f, 0x3a, 0xa0, 0x0,
    0x1, 0xf3, 0xaf, 0xff, 0xff, 0xff, 0x3a, 0xb2,
    0x22, 0x23, 0xf3, 0xaa, 0x0, 0x0, 0x1f, 0x3a,
    0xa0, 0x0, 0x1, 0xf3, 0xaa, 0x0, 0x0, 0x1f,
    0x30,

    /* U+0049 "I" */
    0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
    0xaa,

    /* U+004A "J" */
    0x3, 0xff, 0xff, 0x60, 0x2, 0x22, 0xe6, 0x0,
    0x0, 0xe, 0x60, 0x0, 0x0, 0xe6, 0x0, 0x0,
    0xe, 0x60, 0x0, 0x0, 0xe6, 0x0, 0x0, 0xf,
    0x40, 0xd6, 0x28, 0xf1, 0x4, 0xdf, 0xd5, 0x0,

    /* U+004B "K" */
    0xaa, 0x0, 0x1, 0xc9, 0xa, 0xa0, 0x1, 0xca,
    0x0, 0xaa, 0x0, 0xca, 0x0, 0xa, 0xa0, 0xcb,
    0x0, 0x0, 0xab, 0xcf, 0x90, 0x0, 0xa, 0xfc,
    0x4f, 0x60, 0x0, 0xad, 0x0, 0x5f, 0x40, 0xa,
    0xa0, 0x0, 0x7e, 0x20, 0xaa, 0x0, 0x0, 0x9d,
    0x10,

    /* U+004C "L" */
    0xaa, 0x0, 0x0, 0xa, 0xa0, 0x0, 0x0, 0xaa,
    0x0, 0x0, 0xa, 0xa0, 0x0, 0x0, 0xaa, 0x0,
    0x0, 0xa, 0xa0, 0x0, 0x0, 0xaa, 0x0, 0x0,
    0xa, 0xb2, 0x22, 0x21, 0xaf, 0xff, 0xff, 0x90,

    /* U+004D "M" */
    0xab, 0x0, 0x0, 0x0, 0x5f, 0xa, 0xf5, 0x0,
    0x0, 0xe, 0xf0, 0xae, 0xe0, 0x0, 0x8, 0xef,
    0xa, 0x9c, 0x80, 0x2, 0xf6, 0xf0, 0xa9, 0x2f,
    0x20, 0xb8, 0x3f, 0xa, 0x90, 0x8b, 0x4e, 0x3,
    0xf0, 0xa9, 0x0, 0xee, 0x50, 0x3f, 0xa, 0x90,
    0x5, 0xb0, 0x3, 0xf0, 0xa9, 0x0, 0x0, 0x0,
    0x3f, 0x0,

    /* U+004E "N" */
    0xac, 0x0, 0x0, 0x1f, 0x3a, 0xfa, 0x0, 0x1,
    0xf3, 0xad, 0xf7, 0x0, 0x1f, 0x3a, 0xa5, 0xf4,
    0x1, 0xf3, 0xaa, 0x8, 0xf2, 0x1f, 0x3a, 0xa0,
    0xb, 0xd2, 0xf3, 0xaa, 0x0, 0xd, 0xcf, 0x3a,
    0xa0, 0x0, 0x2f, 0xf3, 0xaa, 0x0, 0x0, 0x4f,
    0x30,

    /* U+004F "O" */
    0x0, 0x19, 0xef, 0xd9, 0x10, 0x0, 0x3f, 0xb5,
    0x35, 0xce, 0x20, 0xd, 0xa0, 0x0, 0x0, 0xbc,
    0x3, 0xf1, 0x0, 0x0, 0x2, 0xf2, 0x5f, 0x0,
    0x0, 0x0, 0xf, 0x43, 0xf1, 0x0, 0x0, 0x3,
    0xf2, 0xd, 0x90, 0x0, 0x0, 0xbc, 0x0, 0x3f,
    0xb4, 0x35, 0xce, 0x20, 0x0, 0x29, 0xef, 0xe9,
    0x10, 0x0,

    /* U+0050 "P" */
    0xaf, 0xff, 0xeb, 0x40, 0xab, 0x22, 0x38, 0xf4,
    0xaa, 0x0, 0x0, 0xba, 0xaa, 0x0, 0x0, 0xaa,
    0xaa, 0x0, 0x5, 0xf5, 0xaf, 0xff, 0xfe, 0x60,
    0xab, 0x22, 0x10, 0x0, 0xaa, 0x0, 0x0, 0x0,
    0xaa, 0x0, 0x0, 0x0,

    /* U+0051 "Q" */
    0x0, 0x19, 0xef, 0xd9, 0x10, 0x0, 0x3f, 0xb5,
    0x35, 0xce, 0x20, 0xd, 0xa0, 0x0, 0x0, 0xbc,
    0x3, 0xf1, 0x0, 0x0, 0x3, 0xf2, 0x5f, 0x0,
    0x0, 0x0, 0xf, 0x43, 0xf1, 0x0, 0x0, 0x2,
    0xf2, 0xe, 0x90, 0x0, 0x0, 0xad, 0x0, 0x4f,
    0xa4, 0x24, 0xbf, 0x30, 0x0, 0x2a, 0xef, 0xfa,
    0x20, 0x0, 0x0, 0x0, 0x6f, 0x50, 0x56, 0x0,
    0x0, 0x0, 0x5e, 0xfd, 0x30, 0x0, 0x0, 0x0,
    0x1, 0x0,

    /* U+0052 "R" */
    0xaf, 0xff, 0xeb, 0x40, 0xab, 0x22, 0x38, 0xf4,
    0xaa, 0x0, 0x0, 0xba, 0xaa, 0x0, 0x0, 0xaa,
    0xaa, 0x0, 0x5, 0xf5, 0xaf, 0xff, 0xff, 0x60,
    0xab, 0x22, 0x5f, 0x20, 0xaa, 0x0, 0x9, 0xc0,
    0xaa, 0x0, 0x0, 0xd8,

    /* U+0053 "S" */
    0x2, 0xbe, 0xfd, 0x70, 0xe, 0xa3, 0x25, 0x90,
    0x3f, 0x10, 0x0, 0x0, 0xe, 0xb3, 0x0, 0x0,
    0x1, 0x9e, 0xfb, 0x40, 0x0, 0x0, 0x28, 0xf4,
    0x0, 0x0, 0x0, 0xc8, 0x4c, 0x52, 0x26, 0xf4,
    0x7, 0xcf, 0xfc, 0x50,

    /* U+0054 "T" */
    0xff, 0xff, 0xff, 0xf9, 0x22, 0x2d, 0x82, 0x21,
    0x0, 0xd, 0x70, 0x0, 0x0, 0xd, 0x70, 0x0,
    0x0, 0xd, 0x70, 0x0, 0x0, 0xd, 0x70, 0x0,
    0x0, 0xd, 0x70, 0x0, 0x0, 0xd, 0x70, 0x0,
    0x0, 0xd, 0x70, 0x0,

    /* U+0055 "U" */
    0xb9, 0x0, 0x0, 0x4f, 0xb9, 0x0, 0x0, 0x4f,
    0xb9, 0x0, 0x0, 0x4f, 0xb9, 0x0, 0x0, 0x4f,
    0xb9, 0x0, 0x0, 0x4f, 0xaa, 0x0, 0x0, 0x5f,
    0x8e, 0x0, 0x0, 0x9c, 0x1f, 0xb4, 0x38, 0xf5,
    0x2, 0xbf, 0xfc, 0x40,

    /* U+0056 "V" */
    0xc, 0x90, 0x0, 0x0, 0x4f, 0x10, 0x5f, 0x10,
    0x0, 0xb, 0x90, 0x0, 0xe7, 0x0, 0x2, 0xf2,
    0x0, 0x7, 0xe0, 0x0, 0x9b, 0x0, 0x0, 0x1f,
    0x60, 0x1f, 0x40, 0x0, 0x0, 0x9d, 0x7, 0xd0,
    0x0, 0x0, 0x2, 0xf4, 0xe6, 0x0, 0x0, 0x0,
    0xa, 0xee, 0x0, 0x0, 0x0, 0x0, 0x3f, 0x80,
    0x0, 0x0,

    /* U+0057 "W" */
    0x6e, 0x0, 0x0, 0x6f, 0x20, 0x0, 0x3f, 0x11,
    0xf4, 0x0, 0xc, 0xf7, 0x0, 0x8, 0xb0, 0xb,
    0x90, 0x1, 0xf8, 0xd0, 0x0, 0xd6, 0x0, 0x6e,
    0x0, 0x7b, 0x1f, 0x20, 0x3f, 0x10, 0x1, 0xf4,
    0xc, 0x60, 0xb8, 0x8, 0xb0, 0x0, 0xb, 0x92,
    0xf1, 0x6, 0xd0, 0xe5, 0x0, 0x0, 0x6e, 0x8b,
    0x0, 0x1f, 0x6f, 0x0, 0x0, 0x1, 0xff, 0x50,
    0x0, 0xbf, 0xb0, 0x0, 0x0, 0xb, 0xf0, 0x0,
    0x5, 0xf5, 0x0, 0x0,

    /* U+0058 "X" */
    0x4f, 0x30, 0x0, 0x7e, 0x10, 0x8e, 0x10, 0x3f,
    0x30, 0x0, 0xcb, 0xd, 0x70, 0x0, 0x1, 0xed,
    0xc0, 0x0, 0x0, 0x9, 0xf5, 0x0, 0x0, 0x3,
    0xfa, 0xe1, 0x0, 0x1, 0xe7, 0xb, 0xb0, 0x0,
    0xbb, 0x0, 0x1e, 0x70, 0x7e, 0x10, 0x0, 0x4f,
    0x30,

    /* U+0059 "Y" */
    0xb, 0xa0, 0x0, 0x2, 0xf2, 0x2, 0xf4, 0x0,
    0xb, 0x80, 0x0, 0x8d, 0x0, 0x5e, 0x0, 0x0,
    0xd, 0x71, 0xe5, 0x0, 0x0, 0x4, 0xfb, 0xb0,
    0x0, 0x0, 0x0, 0xaf, 0x20, 0x0, 0x0, 0x0,
    0x7d, 0x0, 0x0, 0x0, 0x0, 0x7d, 0x0, 0x0,
    0x0, 0x0, 0x7d, 0x0, 0x0,

    /* U+005A "Z" */
    0x5f, 0xff, 0xff, 0xff, 0x0, 0x22, 0x22, 0x4f,
    0x80, 0x0, 0x0, 0xc, 0xa0, 0x0, 0x0, 0xa,
    0xd0, 0x0, 0x0, 0x7, 0xe2, 0x0, 0x0, 0x4,
    0xf3, 0x0, 0x0, 0x2, 0xf6, 0x0, 0x0, 0x1,
    0xdb, 0x22, 0x22, 0x20, 0x6f, 0xff, 0xff, 0xff,
    0x20,

    /* U+005B "[" */
    0xaf, 0xf1, 0xaa, 0x0, 0xa9, 0x0, 0xa9, 0x0,
    0xa9, 0x0, 0xa9, 0x0, 0xa9, 0x0, 0xa9, 0x0,
    0xa9, 0x0, 0xa9, 0x0, 0xa9, 0x0, 0xa9, 0x0,
    0xaf, 0xf1,

    /* U+005C "\\" */
    0x35, 0x0, 0x0, 0x2f, 0x0, 0x0, 0xd, 0x50,
    0x0, 0x7, 0xa0, 0x0, 0x2, 0xf0, 0x0, 0x0,
    0xc5, 0x0, 0x0, 0x7b, 0x0, 0x0, 0x1f, 0x10,
    0x0, 0xc, 0x60, 0x0, 0x6, 0xb0, 0x0, 0x1,
    0xf1, 0x0, 0x0, 0xb6, 0x0, 0x0, 0x6c,

    /* U+005D "]" */
    0xcf, 0xf0, 0x5f, 0x4, 0xf0, 0x4f, 0x4, 0xf0,
    0x4f, 0x4, 0xf0, 0x4f, 0x4, 0xf0, 0x4f, 0x4,
    0xf0, 0x4f, 0xcf, 0xf0,

    /* U+005E "^" */
    0x0, 0x6, 0x30, 0x0, 0x2, 0xeb, 0x0, 0x0,
    0x96, 0xc2, 0x0, 0xe, 0x6, 0x90, 0x6, 0x90,
    0xe, 0x0, 0xd2, 0x0, 0x96,

    /* U+005F "_" */
    0xdd, 0xdd, 0xdd, 0x60,

    /* U+0060 "`" */
    0x17, 0x30, 0x3, 0xc5,

    /* U+0061 "a" */
    0x7, 0xdf, 0xe8, 0x0, 0x94, 0x15, 0xf5, 0x0,
    0x0, 0xa, 0x90, 0x7d, 0xee, 0xfa, 0x3f, 0x20,
    0x9, 0xa3, 0xf2, 0x2, 0xea, 0x7, 0xed, 0xbb,
    0xa0,

    /* U+0062 "b" */
    0xd7, 0x0, 0x0, 0x0, 0xd7, 0x0, 0x0, 0x0,
    0xd7, 0x0, 0x0, 0x0, 0xd8, 0xbf, 0xe9, 0x0,
    0xdf, 0x82, 0x3d, 0xb0, 0xda, 0x0, 0x2, 0xf2,
    0xd7, 0x0, 0x0, 0xf4, 0xda, 0x0, 0x2, 0xf2,
    0xdf, 0x82, 0x3d, 0xb0, 0xd7, 0xaf, 0xe9, 0x0,

    /* U+0063 "c" */
    0x1, 0x9e, 0xfb, 0x30, 0xcc, 0x32, 0x8b, 0x4f,
    0x10, 0x0, 0x6, 0xd0, 0x0, 0x0, 0x4f, 0x10,
    0x0, 0x0, 0xcc, 0x32, 0x9b, 0x1, 0x9e, 0xfb,
    0x20,

    /* U+0064 "d" */
    0x0, 0x0, 0x0, 0x9b, 0x0, 0x0, 0x0, 0x9b,
    0x0, 0x0, 0x0, 0x9b, 0x1, 0xaf, 0xfa, 0x9b,
    0xd, 0xc3, 0x29, 0xfb, 0x4f, 0x10, 0x0, 0xcb,
    0x6d, 0x0, 0x0, 0x9b, 0x4f, 0x0, 0x0, 0xcb,
    0xd, 0xb2, 0x18, 0xfb, 0x1, 0xaf, 0xfa, 0x9b,

    /* U+0065 "e" */
    0x1, 0xae, 0xfa, 0x10, 0xd, 0xa2, 0x2a, 0xc0,
    0x4e, 0x0, 0x0, 0xe3, 0x6f, 0xee, 0xee, 0xf5,
    0x4f, 0x0, 0x0, 0x0, 0xd, 0xb3, 0x26, 0x90,
    0x1, 0x9e, 0xfc, 0x30,

    /* U+0066 "f" */
    0x0, 0xbf, 0xc0, 0x7, 0xd1, 0x20, 0xa, 0x90,
    0x0, 0xdf, 0xff, 0x90, 0xa, 0x90, 0x0, 0xa,
    0x90, 0x0, 0xa, 0x90, 0x0, 0xa, 0x90, 0x0,
    0xa, 0x90, 0x0, 0xa, 0x90, 0x0,

    /* U+0067 "g" */
    0x1, 0xae, 0xfa, 0x8c, 0xd, 0xc3, 0x28, 0xfc,
    0x4f, 0x10, 0x0, 0xbc, 0x6d, 0x0, 0x0, 0x7c,
    0x4f, 0x10, 0x0, 0xbc, 0xd, 0xc3, 0x29, 0xfc,
    0x1, 0xae, 0xfa, 0x8b, 0x0, 0x0, 0x0, 0xa9,
    0xc, 0x73, 0x27, 0xf3, 0x5, 0xcf, 0xfc, 0x50,

    /* U+0068 "h" */
    0xd7, 0x0, 0x0, 0xd, 0x70, 0x0, 0x0, 0xd7,
    0x0, 0x0, 0xd, 0x8b, 0xfe, 0x80, 0xdf, 0x62,
    0x5f, 0x5d, 0x90, 0x0, 0xaa, 0xd7, 0x0, 0x8,
    0xbd, 0x70, 0x0, 0x8b, 0xd7, 0x0, 0x8, 0xbd,
    0x70, 0x0, 0x8b,

    /* U+0069 "i" */
    0xd, 0x70, 0x73, 0x0, 0x0, 0xd7, 0xd, 0x70,
    0xd7, 0xd, 0x70, 0xd7, 0xd, 0x70, 0xd7,

    /* U+006A "j" */
    0x0, 0xc, 0x80, 0x0, 0x64, 0x0, 0x0, 0x0,
    0x0, 0xc8, 0x0, 0xc, 0x80, 0x0, 0xc8, 0x0,
    0xc, 0x80, 0x0, 0xc8, 0x0, 0xc, 0x80, 0x0,
    0xc8, 0x0, 0xc, 0x70, 0x32, 0xe5, 0xd, 0xfa,
    0x0,

    /* U+006B "k" */
    0xd7, 0x0, 0x0, 0xd, 0x70, 0x0, 0x0, 0xd7,
    0x0, 0x0, 0xd, 0x70, 0x5, 0xf3, 0xd7, 0x6,
    0xf3, 0xd, 0x78, 0xf3, 0x0, 0xdd, 0xff, 0x40,
    0xd, 0xe2, 0x8e, 0x10, 0xd7, 0x0, 0xbc, 0xd,
    0x70, 0x1, 0xd9,

    /* U+006C "l" */
    0xd7, 0xd7, 0xd7, 0xd7, 0xd7, 0xd7, 0xd7, 0xd7,
    0xd7, 0xd7,

    /* U+006D "m" */
    0xd8, 0xbf, 0xe7, 0x3c, 0xfe, 0x70, 0xdf, 0x51,
    0x6f, 0xf6, 0x15, 0xf4, 0xd9, 0x0, 0xc, 0xa0,
    0x0, 0xb8, 0xd7, 0x0, 0xb, 0x80, 0x0, 0xa9,
    0xd7, 0x0, 0xb, 0x80, 0x0, 0xaa, 0xd7, 0x0,
    0xb, 0x80, 0x0, 0xaa, 0xd7, 0x0, 0xb, 0x80,
    0x0, 0xaa,

    /* U+006E "n" */
    0xd8, 0xbf, 0xe8, 0xd, 0xf5, 0x14, 0xf5, 0xd9,
    0x0, 0x9, 0xad, 0x70, 0x0, 0x8b, 0xd7, 0x0,
    0x8, 0xbd, 0x70, 0x0, 0x8b, 0xd7, 0x0, 0x8,
    0xb0,

    /* U+006F "o" */
    0x1, 0x9e, 0xfb, 0x30, 0xd, 0xc3, 0x29, 0xf2,
    0x4f, 0x10, 0x0, 0xc8, 0x6d, 0x0, 0x0, 0x9a,
    0x4f, 0x10, 0x0, 0xc8, 0xc, 0xc3, 0x29, 0xf1,
    0x1, 0x9e, 0xfb, 0x20,

    /* U+0070 "p" */
    0xd7, 0xbf, 0xe9, 0x0, 0xdf, 0x71, 0x3c, 0xb0,
    0xd9, 0x0, 0x2, 0xf2, 0xd7, 0x0, 0x0, 0xf4,
    0xda, 0x0, 0x2, 0xf2, 0xdf, 0x82, 0x4d, 0xb0,
    0xd8, 0xaf, 0xe9, 0x0, 0xd7, 0x0, 0x0, 0x0,
    0xd7, 0x0, 0x0, 0x0, 0xd7, 0x0, 0x0, 0x0,

    /* U+0071 "q" */
    0x1, 0xaf, 0xfa, 0x9b, 0xd, 0xc3, 0x29, 0xfb,
    0x4f, 0x10, 0x0, 0xcb, 0x6d, 0x0, 0x0, 0x9b,
    0x4f, 0x10, 0x0, 0xcb, 0xd, 0xc3, 0x29, 0xfb,
    0x1, 0xaf, 0xf9, 0x9b, 0x0, 0x0, 0x0, 0x9b,
    0x0, 0x0, 0x0, 0x9b, 0x0, 0x0, 0x0, 0x9b,

    /* U+0072 "r" */
    0xd7, 0xbd, 0xdf, 0x83, 0xda, 0x0, 0xd7, 0x0,
    0xd7, 0x0, 0xd7, 0x0, 0xd7, 0x0,

    /* U+0073 "s" */
    0x7, 0xef, 0xd7, 0x5, 0xe3, 0x14, 0x40, 0x6e,
    0x30, 0x0, 0x0, 0x8e, 0xfc, 0x40, 0x0, 0x1,
    0x8f, 0x14, 0x72, 0x17, 0xf0, 0x4b, 0xef, 0xc4,
    0x0,

    /* U+0074 "t" */
    0x5, 0x40, 0x0, 0xa, 0x90, 0x0, 0xdf, 0xff,
    0x90, 0xa, 0x90, 0x0, 0xa, 0x90, 0x0, 0xa,
    0x90, 0x0, 0xa, 0x90, 0x0, 0x8, 0xd1, 0x20,
    0x1, 0xbf, 0xc0,

    /* U+0075 "u" */
    0xe5, 0x0, 0xa, 0x9e, 0x50, 0x0, 0xa9, 0xe5,
    0x0, 0xa, 0x9e, 0x50, 0x0, 0xa9, 0xc7, 0x0,
    0xc, 0x98, 0xd3, 0x17, 0xf9, 0x9, 0xef, 0xaa,
    0x90,

    /* U+0076 "v" */
    0xd, 0x70, 0x0, 0x2f, 0x10, 0x6e, 0x0, 0x9,
    0xa0, 0x0, 0xe5, 0x1, 0xf3, 0x0, 0x8, 0xc0,
    0x7c, 0x0, 0x0, 0x1f, 0x2d, 0x50, 0x0, 0x0,
    0xad, 0xe0, 0x0, 0x0, 0x3, 0xf7, 0x0, 0x0,

    /* U+0077 "w" */
    0xb7, 0x0, 0xd, 0x90, 0x0, 0xb6, 0x6c, 0x0,
    0x3f, 0xe0, 0x1, 0xf1, 0xf, 0x20, 0x98, 0xd4,
    0x6, 0xb0, 0xa, 0x80, 0xe2, 0x7a, 0xc, 0x50,
    0x4, 0xd5, 0xc0, 0x1f, 0x3e, 0x0, 0x0, 0xee,
    0x60, 0xb, 0xd9, 0x0, 0x0, 0x8f, 0x0, 0x5,
    0xf3, 0x0,

    /* U+0078 "x" */
    0x5e, 0x20, 0xd, 0x70, 0x8c, 0xa, 0xb0, 0x0,
    0xcc, 0xe1, 0x0, 0x4, 0xf7, 0x0, 0x0, 0xda,
    0xe2, 0x0, 0xab, 0x8, 0xc0, 0x6e, 0x10, 0xc,
    0x90,

    /* U+0079 "y" */
    0xd, 0x70, 0x0, 0x2f, 0x10, 0x6e, 0x0, 0x8,
    0xa0, 0x0, 0xf4, 0x0, 0xe3, 0x0, 0x9, 0xb0,
    0x5c, 0x0, 0x0, 0x2f, 0x2c, 0x60, 0x0, 0x0,
    0xbb, 0xe0, 0x0, 0x0, 0x5, 0xf8, 0x0, 0x0,
    0x0, 0x1f, 0x20, 0x0, 0x5, 0x2a, 0xa0, 0x0,
    0x1, 0xcf, 0xb1, 0x0, 0x0,

    /* U+007A "z" */
    0x6f, 0xff, 0xff, 0x20, 0x0, 0xc, 0xa0, 0x0,
    0x9, 0xd0, 0x0, 0x5, 0xe2, 0x0, 0x3, 0xf4,
    0x0, 0x1, 0xd8, 0x0, 0x0, 0x7f, 0xff, 0xff,
    0x40,

    /* U+007B "{" */
    0x0, 0x9f, 0x50, 0x2f, 0x40, 0x3, 0xf0, 0x0,
    0x3f, 0x0, 0x3, 0xf0, 0x0, 0x4f, 0x0, 0x3f,
    0xa0, 0x0, 0x6f, 0x0, 0x3, 0xf0, 0x0, 0x3f,
    0x0, 0x3, 0xf0, 0x0, 0x2f, 0x40, 0x0, 0x9f,
    0x50,

    /* U+007C "|" */
    0xa8, 0xa8, 0xa8, 0xa8, 0xa8, 0xa8, 0xa8, 0xa8,
    0xa8, 0xa8, 0xa8, 0xa8, 0xa8,

    /* U+007D "}" */
    0xbd, 0x30, 0xa, 0xb0, 0x7, 0xc0, 0x7, 0xc0,
    0x7, 0xc0, 0x6, 0xd0, 0x2, 0xfc, 0x6, 0xe1,
    0x7, 0xc0, 0x7, 0xc0, 0x7, 0xc0, 0xa, 0xb0,
    0xbe, 0x30,

    /* U+007E "~" */
    0xa, 0xea, 0x23, 0xb2, 0xb0, 0x6d, 0xd3,

    /* U+F001 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x16, 0xaf, 0xf0, 0x0, 0x0,
    0x3, 0x7c, 0xff, 0xff, 0xf0, 0x0, 0x6, 0xef,
    0xff, 0xff, 0xff, 0xf0, 0x0, 0xb, 0xff, 0xff,
    0xfe, 0xac, 0xf0, 0x0, 0xb, 0xff, 0xd8, 0x30,
    0x9, 0xf0, 0x0, 0xb, 0xf1, 0x0, 0x0, 0x9,
    0xf0, 0x0, 0xb, 0xe0, 0x0, 0x0, 0x9, 0xf0,
    0x0, 0xb, 0xe0, 0x0, 0x0, 0x9, 0xf0, 0x0,
    0xb, 0xe0, 0x0, 0x3d, 0xff, 0xf0, 0x3, 0x6c,
    0xe0, 0x0, 0xcf, 0xff, 0xf0, 0xaf, 0xff, 0xe0,
    0x0, 0x5f, 0xff, 0x80, 0xdf, 0xff, 0xd0, 0x0,
    0x0, 0x21, 0x0, 0x2a, 0xda, 0x20, 0x0, 0x0,
    0x0, 0x0,

    /* U+F008 "" */
    0x80, 0x9c, 0xcc, 0xcc, 0xcc, 0x40, 0x8f, 0xdf,
    0xb7, 0x77, 0x77, 0xfe, 0xdf, 0xd0, 0xc7, 0x0,
    0x0, 0xd, 0x50, 0xde, 0x8e, 0x70, 0x0, 0x0,
    0xdb, 0x8e, 0xe5, 0xdb, 0x77, 0x77, 0x7f, 0x95,
    0xed, 0x1c, 0xeb, 0xbb, 0xbb, 0xf6, 0x1d, 0xfc,
    0xf7, 0x0, 0x0, 0xd, 0xdc, 0xfd, 0xc, 0x70,
    0x0, 0x0, 0xd5, 0xd, 0xfa, 0xe9, 0x33, 0x33,
    0x3e, 0xca, 0xfc, 0x3d, 0xff, 0xff, 0xff, 0xf8,
    0x3c,

    /* U+F00B "" */
    0x35, 0x52, 0x5, 0x55, 0x55, 0x55, 0x3f, 0xff,
    0xc6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x6f,
    0xff, 0xff, 0xff, 0xfc, 0xee, 0x94, 0xee, 0xee,
    0xee, 0xec, 0x34, 0x42, 0x4, 0x44, 0x44, 0x44,
    0x3f, 0xff, 0xc6, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0x6f, 0xff, 0xff, 0xff, 0xfc, 0xee, 0x94,
    0xee, 0xee, 0xee, 0xec, 0x34, 0x42, 0x4, 0x44,
    0x44, 0x44, 0x3f, 0xff, 0xc6, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x6f, 0xff, 0xff, 0xff, 0xfd,
    0xff, 0xa4, 0xff, 0xff, 0xff, 0xfd,

    /* U+F00C "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x29, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x2e, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x2e, 0xff, 0x90, 0x50, 0x0, 0x0, 0x2e,
    0xff, 0x90, 0xaf, 0xc0, 0x0, 0x2e, 0xff, 0x90,
    0xc, 0xff, 0xc0, 0x2e, 0xff, 0x90, 0x0, 0x1c,
    0xff, 0xce, 0xff, 0x90, 0x0, 0x0, 0x1c, 0xff,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x1c, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x0, 0xb, 0x90, 0x0, 0x0,
    0x0,

    /* U+F00D "" */
    0x0, 0x0, 0x0, 0x0, 0x9, 0xf5, 0x0, 0x6,
    0xf8, 0xdf, 0xf5, 0x6, 0xff, 0xc2, 0xef, 0xf9,
    0xff, 0xd1, 0x2, 0xef, 0xff, 0xd1, 0x0, 0x8,
    0xff, 0xf7, 0x0, 0x6, 0xff, 0xff, 0xf5, 0x6,
    0xff, 0xe4, 0xef, 0xf5, 0xef, 0xe1, 0x2, 0xef,
    0xd4, 0xa1, 0x0, 0x2, 0xb4,

    /* U+F011 "" */
    0x0, 0x0, 0x2, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x20, 0xbf, 0x40, 0x30, 0x0, 0x1, 0xcf, 0x1b,
    0xf4, 0x7f, 0x80, 0x0, 0xbf, 0xd0, 0xbf, 0x45,
    0xff, 0x40, 0x4f, 0xe1, 0xb, 0xf4, 0x7, 0xfd,
    0x9, 0xf8, 0x0, 0xbf, 0x40, 0xe, 0xf2, 0xcf,
    0x40, 0xb, 0xf4, 0x0, 0xbf, 0x4b, 0xf4, 0x0,
    0x9e, 0x30, 0xb, 0xf4, 0x9f, 0x80, 0x0, 0x0,
    0x0, 0xff, 0x23, 0xff, 0x20, 0x0, 0x0, 0x8f,
    0xc0, 0xa, 0xfe, 0x40, 0x1, 0x8f, 0xf4, 0x0,
    0xc, 0xff, 0xed, 0xff, 0xf6, 0x0, 0x0, 0x7,
    0xef, 0xff, 0xc3, 0x0, 0x0, 0x0, 0x0, 0x12,
    0x10, 0x0, 0x0,

    /* U+F013 "" */
    0x0, 0x0, 0x0, 0x21, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x10, 0x3f,
    0xff, 0x70, 0x11, 0x0, 0x7f, 0xbf, 0xff, 0xff,
    0xce, 0xb0, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x53, 0xff, 0xff, 0x82, 0x5f, 0xff, 0xf7, 0x3,
    0xff, 0xc0, 0x0, 0x7f, 0xf8, 0x0, 0x3f, 0xfa,
    0x0, 0x6, 0xff, 0x70, 0x2c, 0xff, 0xf4, 0x1,
    0xdf, 0xfe, 0x42, 0xff, 0xff, 0xfe, 0xff, 0xff,
    0xf6, 0xa, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x0,
    0x5, 0x7, 0xff, 0xfa, 0x13, 0x20, 0x0, 0x0,
    0xf, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x46,
    0x50, 0x0, 0x0,

    /* U+F015 "" */
    0x0, 0x0, 0x0, 0x25, 0x0, 0x36, 0x20, 0x0,
    0x0, 0x0, 0x4f, 0xfc, 0x1a, 0xf6, 0x0, 0x0,
    0x0, 0x7f, 0xe9, 0xfe, 0xcf, 0x60, 0x0, 0x0,
    0xaf, 0xc3, 0x64, 0xff, 0xf6, 0x0, 0x1, 0xcf,
    0xa3, 0xef, 0xa3, 0xdf, 0x90, 0x3, 0xef, 0x75,
    0xff, 0xff, 0xc3, 0xcf, 0xb0, 0xdf, 0x48, 0xff,
    0xff, 0xff, 0xe3, 0x9f, 0x72, 0x27, 0xff, 0xff,
    0xff, 0xff, 0xf2, 0x50, 0x0, 0x9f, 0xff, 0xcb,
    0xef, 0xff, 0x30, 0x0, 0x9, 0xff, 0xf1, 0x7,
    0xff, 0xf3, 0x0, 0x0, 0x9f, 0xff, 0x10, 0x7f,
    0xff, 0x30, 0x0, 0x7, 0xff, 0xe0, 0x6, 0xff,
    0xf2, 0x0,

    /* U+F019 "" */
    0x0, 0x0, 0x4, 0x54, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff,
    0x20, 0x0, 0x0, 0x0, 0x47, 0x8f, 0xff, 0x87,
    0x40, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xff, 0x90,
    0x0, 0x0, 0xa, 0xff, 0xff, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xff, 0xa0, 0x0, 0x0, 0x46,
    0x66, 0x3a, 0xfa, 0x36, 0x66, 0x50, 0xff, 0xff,
    0xf4, 0x54, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xff,
    0xcf, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xff, 0xff,
    0xf3, 0xb8, 0xf2, 0x9b, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xa0,

    /* U+F01C "" */
    0x0, 0x8, 0xaa, 0xaa, 0xaa, 0xa4, 0x0, 0x0,
    0x8, 0xff, 0xff, 0xff, 0xff, 0xf2, 0x0, 0x3,
    0xfb, 0x0, 0x0, 0x0, 0x2f, 0xd0, 0x0, 0xdf,
    0x10, 0x0, 0x0, 0x0, 0x6f, 0x80, 0x8f, 0x60,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0x3f, 0xfe, 0xee,
    0x40, 0x0, 0x9e, 0xee, 0xf9, 0xff, 0xff, 0xfc,
    0x44, 0x5f, 0xff, 0xff, 0xaf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfa, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x9a, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf5,

    /* U+F021 "" */
    0x0, 0x0, 0x0, 0x10, 0x0, 0x7, 0x90, 0x0,
    0x6d, 0xff, 0xfb, 0x40, 0xcf, 0x0, 0xbf, 0xfd,
    0xbd, 0xff, 0xac, 0xf0, 0xbf, 0xd3, 0x0, 0x3,
    0xcf, 0xff, 0x4f, 0xd1, 0x0, 0x7, 0xaa, 0xff,
    0xf9, 0xf5, 0x0, 0x0, 0xaf, 0xff, 0xff, 0x1,
    0x0, 0x0, 0x0, 0x11, 0x11, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xfa, 0x0,
    0x0, 0x4f, 0x9f, 0xff, 0xab, 0x70, 0x0, 0xd,
    0xf4, 0xff, 0xfb, 0x20, 0x0, 0x3c, 0xfb, 0xf,
    0xcb, 0xff, 0xca, 0xcf, 0xfc, 0x10, 0xfc, 0x5,
    0xcf, 0xff, 0xd7, 0x0, 0x9, 0x70, 0x0, 0x12,
    0x10, 0x0, 0x0,

    /* U+F026 "" */
    0x0, 0x0, 0x5, 0x20, 0x0, 0x6, 0xf7, 0x1,
    0x16, 0xff, 0x8f, 0xff, 0xff, 0xf8, 0xff, 0xff,
    0xff, 0x8f, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff,
    0x8a, 0xcc, 0xef, 0xf8, 0x0, 0x2, 0xef, 0x80,
    0x0, 0x2, 0xe7, 0x0, 0x0, 0x1, 0x0,

    /* U+F027 "" */
    0x0, 0x0, 0x4, 0x20, 0x0, 0x0, 0x0, 0x6f,
    0x70, 0x0, 0x1, 0x16, 0xff, 0x80, 0x0, 0xef,
    0xff, 0xff, 0x82, 0xb1, 0xff, 0xff, 0xff, 0x80,
    0xc9, 0xff, 0xff, 0xff, 0x80, 0x9b, 0xff, 0xff,
    0xff, 0x82, 0xf3, 0xbd, 0xdf, 0xff, 0x80, 0x10,
    0x0, 0x3, 0xef, 0x80, 0x0, 0x0, 0x0, 0x3e,
    0x70, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0,

    /* U+F028 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xc2, 0x0, 0x0,
    0x0, 0x5, 0x20, 0x0, 0xb, 0xe1, 0x0, 0x0,
    0x6, 0xf7, 0x0, 0xb7, 0xc, 0xb0, 0x1, 0x16,
    0xff, 0x80, 0x5, 0xf5, 0x2f, 0x3f, 0xff, 0xff,
    0xf8, 0x2c, 0x17, 0xd0, 0xb8, 0xff, 0xff, 0xff,
    0x80, 0xba, 0x2f, 0x18, 0xbf, 0xff, 0xff, 0xf8,
    0x9, 0xb1, 0xf2, 0x7b, 0xff, 0xff, 0xff, 0x82,
    0xf3, 0x5e, 0xa, 0x9a, 0xcc, 0xef, 0xf8, 0x0,
    0x2e, 0x80, 0xe4, 0x0, 0x2, 0xef, 0x80, 0xc,
    0xa0, 0x8d, 0x0, 0x0, 0x2, 0xe7, 0x0, 0x10,
    0x7f, 0x30, 0x0, 0x0, 0x1, 0x0, 0x0, 0x4f,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x10,
    0x0,

    /* U+F03E "" */
    0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xf9,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0x80, 0xd, 0xff, 0xf8,
    0xaf, 0xff, 0xff, 0x9b, 0xff, 0xf8, 0x0, 0xaf,
    0xff, 0xff, 0x6d, 0xf8, 0x0, 0x0, 0xcf, 0xff,
    0x40, 0x17, 0x0, 0x0, 0xa, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xfd, 0xaa, 0xaa, 0xaa,
    0xaa, 0xad, 0xfa, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfa,

    /* U+F043 "" */
    0x0, 0x0, 0x40, 0x0, 0x0, 0x0, 0x6f, 0x50,
    0x0, 0x0, 0xc, 0xfb, 0x0, 0x0, 0x3, 0xff,
    0xf3, 0x0, 0x0, 0xdf, 0xff, 0xc0, 0x0, 0x8f,
    0xff, 0xff, 0x70, 0x2f, 0xff, 0xff, 0xff, 0x2a,
    0xff, 0xff, 0xff, 0xf9, 0xef, 0xef, 0xff, 0xff,
    0xdf, 0xb7, 0xff, 0xff, 0xfe, 0xce, 0x2d, 0xff,
    0xff, 0xb5, 0xfc, 0x36, 0xff, 0xf3, 0x7, 0xff,
    0xff, 0xf6, 0x0, 0x2, 0x89, 0x72, 0x0,

    /* U+F048 "" */
    0x6, 0x40, 0x0, 0x2, 0x42, 0xfa, 0x0, 0x3,
    0xef, 0x2f, 0xa0, 0x4, 0xff, 0xf2, 0xfa, 0x5,
    0xff, 0xff, 0x2f, 0xa6, 0xff, 0xff, 0xf2, 0xfe,
    0xff, 0xff, 0xff, 0x2f, 0xff, 0xff, 0xff, 0xf2,
    0xfc, 0xdf, 0xff, 0xff, 0x2f, 0xa1, 0xdf, 0xff,
    0xf2, 0xfa, 0x1, 0xcf, 0xff, 0x2f, 0xa0, 0x0,
    0xbf, 0xf2, 0xf9, 0x0, 0x0, 0x9c, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F04B "" */
    0x24, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xc3,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0x91, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0x70, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xfd, 0x40, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xfa, 0x10, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0x50, 0xff, 0xff,
    0xff, 0xff, 0x80, 0x0, 0xff, 0xff, 0xff, 0xb2,
    0x0, 0x0, 0xff, 0xff, 0xd4, 0x0, 0x0, 0x0,
    0xff, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x48, 0x10,
    0x0, 0x0, 0x0, 0x0,

    /* U+F04C "" */
    0x26, 0x66, 0x10, 0x16, 0x66, 0x30, 0xef, 0xff,
    0xc0, 0xaf, 0xff, 0xf1, 0xff, 0xff, 0xe0, 0xbf,
    0xff, 0xf2, 0xff, 0xff, 0xe0, 0xbf, 0xff, 0xf2,
    0xff, 0xff, 0xe0, 0xbf, 0xff, 0xf2, 0xff, 0xff,
    0xe0, 0xbf, 0xff, 0xf2, 0xff, 0xff, 0xe0, 0xbf,
    0xff, 0xf2, 0xff, 0xff, 0xe0, 0xbf, 0xff, 0xf2,
    0xff, 0xff, 0xe0, 0xbf, 0xff, 0xf2, 0xff, 0xff,
    0xe0, 0xbf, 0xff, 0xf2, 0xff, 0xff, 0xd0, 0xbf,
    0xff, 0xf2, 0xaf, 0xff, 0x80, 0x6e, 0xff, 0xc0,

    /* U+F04D "" */
    0x26, 0x66, 0x66, 0x66, 0x66, 0x50, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf6, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf6, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xd1,

    /* U+F051 "" */
    0x5, 0x0, 0x0, 0x6, 0x45, 0xfb, 0x0, 0x1,
    0xfc, 0x6f, 0xfc, 0x10, 0x1f, 0xc6, 0xff, 0xfd,
    0x11, 0xfc, 0x6f, 0xff, 0xfd, 0x3f, 0xc6, 0xff,
    0xff, 0xff, 0xfc, 0x6f, 0xff, 0xff, 0xff, 0xc6,
    0xff, 0xff, 0xfa, 0xfc, 0x6f, 0xff, 0xf7, 0x1f,
    0xc6, 0xff, 0xf6, 0x1, 0xfc, 0x6f, 0xf5, 0x0,
    0x1f, 0xc3, 0xe4, 0x0, 0x0, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F052 "" */
    0x0, 0x0, 0x0, 0x52, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xe2, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xff, 0xd1, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xff,
    0xd1, 0x0, 0x0, 0x6f, 0xff, 0xff, 0xff, 0xc0,
    0x0, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xb0, 0xe,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x40, 0x8e, 0xee,
    0xee, 0xee, 0xee, 0xc1, 0x2, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xe3,

    /* U+F053 "" */
    0x0, 0x0, 0x0, 0x10, 0x0, 0x0, 0xb, 0xf2,
    0x0, 0x0, 0xbf, 0xf2, 0x0, 0xb, 0xff, 0x30,
    0x0, 0xbf, 0xf3, 0x0, 0xb, 0xff, 0x30, 0x0,
    0x2f, 0xf9, 0x0, 0x0, 0x6, 0xff, 0x80, 0x0,
    0x0, 0x6f, 0xf8, 0x0, 0x0, 0x6, 0xff, 0x80,
    0x0, 0x0, 0x6f, 0xf4, 0x0, 0x0, 0x5, 0xb0,

    /* U+F054 "" */
    0x1, 0x0, 0x0, 0x0, 0x1e, 0xc0, 0x0, 0x0,
    0x1e, 0xfc, 0x0, 0x0, 0x2, 0xef, 0xc0, 0x0,
    0x0, 0x2e, 0xfc, 0x0, 0x0, 0x2, 0xef, 0xc0,
    0x0, 0x0, 0x7f, 0xf4, 0x0, 0x6, 0xff, 0x70,
    0x0, 0x6f, 0xf7, 0x0, 0x6, 0xff, 0x70, 0x0,
    0x2f, 0xf7, 0x0, 0x0, 0xa, 0x70, 0x0, 0x0,

    /* U+F067 "" */
    0x0, 0x0, 0x17, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xe0, 0x0, 0x0, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xf3, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0x36, 0x66, 0xbf, 0xf6, 0x66, 0x50,
    0x0, 0x0, 0x8f, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x4d, 0x90, 0x0, 0x0,

    /* U+F068 "" */
    0x1, 0x11, 0x11, 0x11, 0x11, 0x0, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xf3, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0x35, 0x55, 0x55, 0x55, 0x55, 0x40,

    /* U+F06E "" */
    0x0, 0x0, 0x5a, 0xcd, 0xc8, 0x20, 0x0, 0x0,
    0x2, 0xdf, 0xd7, 0x69, 0xff, 0x90, 0x0, 0x4,
    0xff, 0xa0, 0x1, 0x2, 0xef, 0xc1, 0x2, 0xff,
    0xf1, 0x2, 0xfc, 0x16, 0xff, 0xb0, 0xcf, 0xfb,
    0x5, 0xaf, 0xf9, 0x1f, 0xff, 0x6d, 0xff, 0xb1,
    0xff, 0xff, 0xb1, 0xff, 0xf7, 0x4f, 0xfe, 0xb,
    0xff, 0xf5, 0x5f, 0xfc, 0x0, 0x6f, 0xf9, 0x8,
    0xa5, 0x1d, 0xfd, 0x20, 0x0, 0x5e, 0xfb, 0x53,
    0x6e, 0xfb, 0x10, 0x0, 0x0, 0x7, 0xce, 0xfe,
    0xa4, 0x0, 0x0,

    /* U+F070 "" */
    0x32, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3e, 0xf9, 0x5, 0xac, 0xdb, 0x71, 0x0,
    0x0, 0x0, 0x1b, 0xff, 0xfd, 0x76, 0xaf, 0xf7,
    0x0, 0x0, 0x0, 0x8, 0xff, 0x31, 0x30, 0x4f,
    0xfa, 0x0, 0x0, 0x32, 0x4, 0xef, 0xbf, 0xc0,
    0x9f, 0xf8, 0x0, 0xe, 0xf5, 0x2, 0xcf, 0xff,
    0x64, 0xff, 0xf3, 0x1, 0xff, 0xf6, 0x0, 0x9f,
    0xf8, 0x4f, 0xff, 0x40, 0x6, 0xff, 0xc0, 0x0,
    0x6f, 0xeb, 0xff, 0xa0, 0x0, 0x9, 0xff, 0x60,
    0x0, 0x3e, 0xff, 0xc0, 0x0, 0x0, 0x7, 0xff,
    0xa4, 0x30, 0x1b, 0xfb, 0x10, 0x0, 0x0, 0x2,
    0x8d, 0xff, 0x80, 0x8, 0xfe, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xef, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x70,

    /* U+F071 "" */
    0x0, 0x0, 0x0, 0x1, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xf5, 0x1a, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xf4, 0xa, 0xff, 0x60, 0x0,
    0x0, 0x5, 0xff, 0xf5, 0xa, 0xff, 0xe1, 0x0,
    0x0, 0xe, 0xff, 0xf6, 0xb, 0xff, 0xf9, 0x0,
    0x0, 0x8f, 0xff, 0xfd, 0x8f, 0xff, 0xff, 0x20,
    0x2, 0xff, 0xff, 0xf3, 0x9, 0xff, 0xff, 0xb0,
    0xa, 0xff, 0xff, 0xf7, 0x1c, 0xff, 0xff, 0xf4,
    0xe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0x4, 0x9a, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0x91,

    /* U+F074 "" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xa0, 0xef, 0xfa, 0x0,
    0x5, 0xff, 0xff, 0xaf, 0xff, 0xf9, 0x4, 0xff,
    0xff, 0xfb, 0x11, 0x3f, 0xa4, 0xff, 0x84, 0xfc,
    0x0, 0x0, 0x33, 0xff, 0x90, 0x19, 0x0, 0x0,
    0x2, 0xef, 0xa2, 0x1, 0x70, 0x0, 0x2, 0xef,
    0xa4, 0xf6, 0x3f, 0xa0, 0xef, 0xff, 0xb0, 0x5f,
    0xff, 0xff, 0xaf, 0xff, 0xc0, 0x0, 0x6f, 0xff,
    0xfb, 0x11, 0x10, 0x0, 0x0, 0x14, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x19, 0x0,

    /* U+F077 "" */
    0x0, 0x0, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xa0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xfa,
    0x0, 0x0, 0x0, 0x5f, 0xfa, 0xff, 0xa0, 0x0,
    0x5, 0xff, 0x90, 0x3f, 0xfa, 0x0, 0x4f, 0xf9,
    0x0, 0x3, 0xff, 0xa0, 0xbf, 0x90, 0x0, 0x0,
    0x3f, 0xf1, 0x15, 0x0, 0x0, 0x0, 0x3, 0x30,

    /* U+F078 "" */
    0x1, 0x0, 0x0, 0x0, 0x0, 0x10, 0x9f, 0x50,
    0x0, 0x0, 0x1d, 0xe1, 0x8f, 0xf5, 0x0, 0x1,
    0xdf, 0xd0, 0x8, 0xff, 0x50, 0x1d, 0xfd, 0x10,
    0x0, 0x8f, 0xf6, 0xdf, 0xd1, 0x0, 0x0, 0x8,
    0xff, 0xfd, 0x10, 0x0, 0x0, 0x0, 0x8f, 0xd1,
    0x0, 0x0, 0x0, 0x0, 0x5, 0x10, 0x0, 0x0,

    /* U+F079 "" */
    0x0, 0x7d, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xfd, 0x18, 0xff, 0xff, 0xff, 0x80,
    0x0, 0x6f, 0xff, 0xfd, 0x4a, 0xaa, 0xaa, 0xfb,
    0x0, 0xc, 0xda, 0xf7, 0xf4, 0x0, 0x0, 0xe,
    0xb0, 0x0, 0x0, 0x9f, 0x11, 0x0, 0x0, 0x0,
    0xeb, 0x0, 0x0, 0x9, 0xf1, 0x0, 0x0, 0x0,
    0xe, 0xb0, 0x0, 0x0, 0x9f, 0x10, 0x0, 0x2,
    0xe6, 0xeb, 0x9d, 0x0, 0x9, 0xf9, 0x99, 0x99,
    0x4d, 0xff, 0xff, 0xa0, 0x0, 0x7f, 0xff, 0xff,
    0xfb, 0x1d, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1d, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F07B "" */
    0x6b, 0xcc, 0xc7, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xff, 0xf8, 0x22, 0x22, 0x10, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfa, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfa,

    /* U+F093 "" */
    0x0, 0x0, 0x0, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xff, 0x80, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xff,
    0xff, 0x70, 0x0, 0x0, 0x69, 0x9f, 0xff, 0x99,
    0x60, 0x0, 0x0, 0x0, 0x2f, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xff, 0x20, 0x0, 0x0, 0x46,
    0x66, 0x2f, 0xff, 0x26, 0x66, 0x50, 0xff, 0xff,
    0x46, 0x76, 0x4f, 0xff, 0xf2, 0xff, 0xff, 0xfc,
    0xbc, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xff, 0xff,
    0xf3, 0xb8, 0xf2, 0x9b, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xa0,

    /* U+F095 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x33, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0xe, 0xff, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0x80,
    0x0, 0x1, 0x50, 0x0, 0x4f, 0xfd, 0x0, 0x3,
    0xaf, 0xf8, 0x7, 0xff, 0xf2, 0x0, 0xe, 0xff,
    0xff, 0xef, 0xfe, 0x30, 0x0, 0xb, 0xff, 0xff,
    0xff, 0xc2, 0x0, 0x0, 0x8, 0xff, 0xff, 0xd5,
    0x0, 0x0, 0x0, 0x2, 0x98, 0x62, 0x0, 0x0,
    0x0, 0x0,

    /* U+F0C4 "" */
    0x5, 0x84, 0x0, 0x0, 0x1, 0x0, 0x8f, 0xff,
    0x60, 0x0, 0xaf, 0xf3, 0xec, 0x2e, 0xc0, 0x1c,
    0xff, 0xa0, 0xde, 0x9f, 0xd1, 0xdf, 0xfa, 0x0,
    0x3e, 0xff, 0xff, 0xff, 0xa0, 0x0, 0x0, 0x2d,
    0xff, 0xfa, 0x0, 0x0, 0x0, 0x1c, 0xff, 0xf7,
    0x0, 0x0, 0x2d, 0xff, 0xff, 0xff, 0x60, 0x0,
    0xcf, 0xaf, 0xd2, 0xef, 0xf6, 0x0, 0xfb, 0xd,
    0xd0, 0x2e, 0xff, 0x60, 0xbf, 0xef, 0x80, 0x2,
    0xef, 0xf3, 0x19, 0xc8, 0x0, 0x0, 0x16, 0x40,

    /* U+F0C5 "" */
    0x0, 0x2, 0x66, 0x66, 0x12, 0x0, 0x0, 0xb,
    0xff, 0xff, 0x3f, 0x40, 0x0, 0xc, 0xff, 0xff,
    0x3f, 0xf2, 0xdf, 0x6c, 0xff, 0xff, 0x53, 0x31,
    0xff, 0x7c, 0xff, 0xff, 0xff, 0xf6, 0xff, 0x7c,
    0xff, 0xff, 0xff, 0xf6, 0xff, 0x7c, 0xff, 0xff,
    0xff, 0xf6, 0xff, 0x7c, 0xff, 0xff, 0xff, 0xf6,
    0xff, 0x7c, 0xff, 0xff, 0xff, 0xf6, 0xff, 0x7c,
    0xff, 0xff, 0xff, 0xf6, 0xff, 0x7b, 0xff, 0xff,
    0xff, 0xf5, 0xff, 0xc2, 0x33, 0x33, 0x33, 0x20,
    0xff, 0xff, 0xff, 0xff, 0x20, 0x0, 0x8a, 0xaa,
    0xaa, 0xa9, 0x0, 0x0,

    /* U+F0C7 "" */
    0x49, 0x99, 0x99, 0x99, 0x60, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0x0, 0xfa, 0x0, 0x0, 0x1,
    0xff, 0x80, 0xfa, 0x0, 0x0, 0x0, 0xef, 0xf3,
    0xfa, 0x0, 0x0, 0x0, 0xef, 0xf5, 0xfe, 0xaa,
    0xaa, 0xaa, 0xff, 0xf5, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0xff, 0xff, 0x80, 0x4f, 0xff, 0xf5,
    0xff, 0xff, 0x10, 0xc, 0xff, 0xf5, 0xff, 0xff,
    0x50, 0x1e, 0xff, 0xf5, 0xff, 0xff, 0xfb, 0xef,
    0xff, 0xf5, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xd1,

    /* U+F0C9 "" */
    0xcd, 0xdd, 0xdd, 0xdd, 0xdd, 0xd4, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbc, 0xcc, 0xcc, 0xcc, 0xcc, 0xc3, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbc, 0xcc, 0xcc, 0xcc, 0xcc, 0xc3, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F0E0 "" */
    0x6b, 0xcc, 0xcc, 0xcc, 0xcc, 0xcb, 0x6f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x95, 0x6f, 0xff, 0xff, 0xff,
    0xff, 0x65, 0xf9, 0x3d, 0xff, 0xff, 0xfd, 0x39,
    0xff, 0xfd, 0x39, 0xff, 0xf9, 0x3d, 0xff, 0xff,
    0xff, 0x65, 0xa4, 0x6f, 0xff, 0xff, 0xff, 0xff,
    0xb7, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfa, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfa,

    /* U+F0E7 "" */
    0x0, 0x55, 0x55, 0x20, 0x0, 0x3, 0xff, 0xff,
    0x90, 0x0, 0x5, 0xff, 0xff, 0x40, 0x0, 0x8,
    0xff, 0xff, 0x0, 0x0, 0xa, 0xff, 0xfc, 0x44,
    0x40, 0xc, 0xff, 0xff, 0xff, 0xf0, 0xe, 0xff,
    0xff, 0xff, 0x80, 0xd, 0xff, 0xff, 0xfe, 0x0,
    0x0, 0x0, 0x8f, 0xf6, 0x0, 0x0, 0x0, 0xbf,
    0xc0, 0x0, 0x0, 0x0, 0xff, 0x30, 0x0, 0x0,
    0x3, 0xfa, 0x0, 0x0, 0x0, 0x7, 0xf1, 0x0,
    0x0, 0x0, 0x3, 0x50, 0x0, 0x0,

    /* U+F0EA "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x35, 0x6e,
    0xf7, 0x54, 0x0, 0x0, 0xff, 0xf9, 0x8f, 0xff,
    0x10, 0x0, 0xff, 0xff, 0xff, 0xff, 0x10, 0x0,
    0xff, 0xfa, 0x24, 0x44, 0x1, 0x0, 0xff, 0xf4,
    0xef, 0xff, 0x3e, 0x20, 0xff, 0xf4, 0xff, 0xff,
    0x3f, 0xe2, 0xff, 0xf4, 0xff, 0xff, 0x43, 0x31,
    0xff, 0xf4, 0xff, 0xff, 0xff, 0xf6, 0xff, 0xf4,
    0xff, 0xff, 0xff, 0xf6, 0xff, 0xf4, 0xff, 0xff,
    0xff, 0xf6, 0x23, 0x30, 0xff, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x0,
    0x7a, 0xaa, 0xaa, 0xa2,

    /* U+F0F3 "" */
    0x0, 0x0, 0x3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0x70, 0x0, 0x0, 0x0, 0x7, 0xef, 0xfa,
    0x10, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xd0, 0x0,
    0x1, 0xff, 0xff, 0xff, 0xf7, 0x0, 0x4, 0xff,
    0xff, 0xff, 0xfb, 0x0, 0x7, 0xff, 0xff, 0xff,
    0xfd, 0x0, 0x9, 0xff, 0xff, 0xff, 0xff, 0x0,
    0xd, 0xff, 0xff, 0xff, 0xff, 0x30, 0x7f, 0xff,
    0xff, 0xff, 0xff, 0xd0, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0x13, 0x33, 0x33, 0x33, 0x33, 0x20,
    0x0, 0x0, 0xcf, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x29, 0x50, 0x0, 0x0,

    /* U+F11C "" */
    0x5a, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0x2f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0xfa,
    0xd, 0x1, 0xb0, 0xd0, 0xa2, 0xf, 0xaf, 0xeb,
    0xfb, 0xcf, 0xbf, 0xbe, 0xcb, 0xfa, 0xff, 0xe1,
    0x98, 0x1c, 0x1c, 0x15, 0xff, 0xaf, 0xfd, 0x8,
    0x70, 0xb0, 0xb0, 0x3f, 0xfa, 0xff, 0xdf, 0xdd,
    0xdd, 0xdd, 0xfd, 0xdf, 0xaf, 0xa0, 0xd0, 0x0,
    0x0, 0xa, 0x20, 0xfa, 0xfe, 0xaf, 0xaa, 0xaa,
    0xaa, 0xeb, 0xaf, 0x9a, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf5,

    /* U+F124 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x41, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xef, 0xd0, 0x0,
    0x0, 0x0, 0x1, 0x8f, 0xff, 0xfc, 0x0, 0x0,
    0x0, 0x29, 0xff, 0xff, 0xff, 0x50, 0x0, 0x3,
    0xbf, 0xff, 0xff, 0xff, 0xe0, 0x0, 0x3c, 0xff,
    0xff, 0xff, 0xff, 0xf7, 0x0, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0x0, 0x0, 0x9e, 0xff, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x76, 0x0, 0x0,
    0x0,

    /* U+F15B "" */
    0x34, 0x44, 0x43, 0x10, 0x0, 0xff, 0xff, 0xfb,
    0x8a, 0x0, 0xff, 0xff, 0xfb, 0x8f, 0xa0, 0xff,
    0xff, 0xfb, 0x7f, 0xf8, 0xff, 0xff, 0xfc, 0x11,
    0x11, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0xff, 0xff, 0xff,
    0xff, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0xbd, 0xdd, 0xdd, 0xdd, 0xd8,

    /* U+F1EB "" */
    0x0, 0x0, 0x0, 0x35, 0x54, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x6c, 0xff, 0xff, 0xff, 0xd8, 0x10,
    0x0, 0x4, 0xef, 0xff, 0xdb, 0xbc, 0xff, 0xff,
    0x70, 0x8, 0xff, 0xd6, 0x10, 0x0, 0x0, 0x4b,
    0xff, 0xb0, 0xbf, 0x70, 0x0, 0x13, 0x32, 0x0,
    0x5, 0xed, 0x10, 0x30, 0x6, 0xdf, 0xff, 0xfe,
    0x80, 0x1, 0x10, 0x0, 0xb, 0xff, 0xfc, 0xce,
    0xff, 0xd2, 0x0, 0x0, 0x0, 0xbe, 0x60, 0x0,
    0x4, 0xde, 0x10, 0x0, 0x0, 0x0, 0x10, 0x3,
    0x40, 0x0, 0x10, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xac, 0x30, 0x0, 0x0, 0x0,

    /* U+F240 "" */
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd2,
    0xf, 0xea, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xcf,
    0x90, 0xfa, 0x48, 0x88, 0x88, 0x88, 0x88, 0x65,
    0xff, 0x1f, 0xa9, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x1a, 0xf1, 0xfa, 0x9f, 0xff, 0xff, 0xff, 0xff,
    0xd1, 0xaf, 0x1f, 0xa5, 0x99, 0x99, 0x99, 0x99,
    0x98, 0x5f, 0xf1, 0xfd, 0x99, 0x99, 0x99, 0x99,
    0x99, 0x9b, 0xfa, 0xb, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F241 "" */
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd2,
    0xf, 0xea, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xcf,
    0x90, 0xfa, 0x48, 0x88, 0x88, 0x88, 0x30, 0x5,
    0xff, 0x1f, 0xa9, 0xff, 0xff, 0xff, 0xf7, 0x0,
    0x1a, 0xf1, 0xfa, 0x9f, 0xff, 0xff, 0xff, 0x70,
    0x1, 0xaf, 0x1f, 0xa5, 0x99, 0x99, 0x99, 0x94,
    0x0, 0x5f, 0xf1, 0xfd, 0x99, 0x99, 0x99, 0x99,
    0x99, 0x9b, 0xfa, 0xb, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F242 "" */
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd2,
    0xf, 0xea, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xcf,
    0x90, 0xfa, 0x48, 0x88, 0x88, 0x0, 0x0, 0x5,
    0xff, 0x1f, 0xa9, 0xff, 0xff, 0xf0, 0x0, 0x0,
    0x1a, 0xf1, 0xfa, 0x9f, 0xff, 0xff, 0x0, 0x0,
    0x1, 0xaf, 0x1f, 0xa5, 0x99, 0x99, 0x90, 0x0,
    0x0, 0x5f, 0xf1, 0xfd, 0x99, 0x99, 0x99, 0x99,
    0x99, 0x9b, 0xfa, 0xb, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F243 "" */
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd2,
    0xf, 0xea, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xcf,
    0x90, 0xfa, 0x48, 0x85, 0x0, 0x0, 0x0, 0x5,
    0xff, 0x1f, 0xa9, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x1a, 0xf1, 0xfa, 0x9f, 0xfa, 0x0, 0x0, 0x0,
    0x1, 0xaf, 0x1f, 0xa5, 0x99, 0x60, 0x0, 0x0,
    0x0, 0x5f, 0xf1, 0xfd, 0x99, 0x99, 0x99, 0x99,
    0x99, 0x9b, 0xfa, 0xb, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F244 "" */
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd2,
    0xf, 0xea, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xcf,
    0x90, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0x1f, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1a, 0xf1, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xaf, 0x1f, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xf1, 0xfd, 0x99, 0x99, 0x99, 0x99,
    0x99, 0x9b, 0xfa, 0xb, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F287 "" */
    0x0, 0x0, 0x0, 0x0, 0x5c, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xef, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xd0, 0x5c, 0x50, 0x0,
    0x0, 0x0, 0x64, 0x0, 0xa5, 0x0, 0x0, 0x0,
    0x10, 0x0, 0xbf, 0xf6, 0x4e, 0x21, 0x11, 0x11,
    0x17, 0xd3, 0xf, 0xff, 0xec, 0xce, 0xec, 0xcc,
    0xcc, 0xef, 0xe1, 0x7f, 0xe3, 0x0, 0x2d, 0x0,
    0x0, 0x6, 0x80, 0x0, 0x10, 0x0, 0x0, 0x97,
    0xa, 0xa6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcc, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F293 "" */
    0x0, 0x0, 0x1, 0x10, 0x0, 0x0, 0x1, 0xaf,
    0xff, 0xe8, 0x0, 0x0, 0xdf, 0xf8, 0xcf, 0xf8,
    0x0, 0x7f, 0xff, 0x81, 0xdf, 0xf1, 0xc, 0xfa,
    0xf8, 0x52, 0xdf, 0x60, 0xff, 0x64, 0x78, 0x3a,
    0xf8, 0xf, 0xff, 0x50, 0x8, 0xff, 0xa1, 0xff,
    0xff, 0x12, 0xff, 0xfa, 0xf, 0xff, 0x41, 0x15,
    0xff, 0xa0, 0xef, 0x46, 0x88, 0x48, 0xf8, 0xb,
    0xfc, 0xf8, 0x42, 0xef, 0x50, 0x5f, 0xff, 0x82,
    0xef, 0xf1, 0x0, 0xaf, 0xfa, 0xef, 0xf6, 0x0,
    0x0, 0x5a, 0xdc, 0xa4, 0x0,

    /* U+F2ED "" */
    0x0, 0x1, 0x56, 0x63, 0x0, 0x0, 0x78, 0x8b,
    0xff, 0xfe, 0x88, 0x82, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0x12, 0x22, 0x22, 0x22, 0x22, 0x20,
    0x3f, 0xff, 0xff, 0xff, 0xff, 0x90, 0x3f, 0xfc,
    0xfc, 0xfe, 0xdf, 0x90, 0x3f, 0xd5, 0xf4, 0xeb,
    0x7f, 0x90, 0x3f, 0xd5, 0xf4, 0xeb, 0x7f, 0x90,
    0x3f, 0xd5, 0xf4, 0xeb, 0x7f, 0x90, 0x3f, 0xd5,
    0xf4, 0xeb, 0x7f, 0x90, 0x3f, 0xd5, 0xf4, 0xeb,
    0x7f, 0x90, 0x3f, 0xe6, 0xf5, 0xec, 0x8f, 0x90,
    0x2f, 0xff, 0xff, 0xff, 0xff, 0x80, 0x7, 0x9a,
    0xaa, 0xaa, 0xa9, 0x10,

    /* U+F304 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x14, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xef, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xff, 0xf9, 0x0, 0x0, 0x0,
    0x3, 0xc3, 0xdf, 0xff, 0x0, 0x0, 0x0, 0x3e,
    0xfd, 0x3d, 0xf5, 0x0, 0x0, 0x3, 0xef, 0xff,
    0xd3, 0x50, 0x0, 0x0, 0x3e, 0xff, 0xff, 0xf5,
    0x0, 0x0, 0x3, 0xef, 0xff, 0xff, 0x60, 0x0,
    0x0, 0x3e, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x3,
    0xef, 0xff, 0xff, 0x60, 0x0, 0x0, 0xb, 0xff,
    0xff, 0xf6, 0x0, 0x0, 0x0, 0xd, 0xff, 0xff,
    0x60, 0x0, 0x0, 0x0, 0xf, 0xff, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x8, 0x86, 0x30, 0x0, 0x0,
    0x0, 0x0,

    /* U+F55A "" */
    0x0, 0x0, 0x8b, 0xcc, 0xcc, 0xcc, 0xcc, 0xb6,
    0x0, 0x0, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf2, 0x0, 0xbf, 0xff, 0xf8, 0xbf, 0xf6, 0xef,
    0xff, 0x40, 0xbf, 0xff, 0xff, 0x10, 0xa4, 0x9,
    0xff, 0xf4, 0xaf, 0xff, 0xff, 0xfd, 0x10, 0x7,
    0xff, 0xff, 0x4d, 0xff, 0xff, 0xff, 0xf4, 0x0,
    0xbf, 0xff, 0xf4, 0x1d, 0xff, 0xff, 0xf4, 0x6,
    0x10, 0xbf, 0xff, 0x40, 0x1d, 0xff, 0xff, 0x47,
    0xfd, 0x2b, 0xff, 0xf4, 0x0, 0x1d, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x30, 0x0, 0x1c, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xa0,

    /* U+F7C2 "" */
    0x0, 0x8, 0x99, 0x99, 0x70, 0x0, 0xbf, 0xff,
    0xff, 0xf8, 0xa, 0xd0, 0xd0, 0xd0, 0xeb, 0x9f,
    0xd0, 0xd0, 0xd0, 0xeb, 0xff, 0xd1, 0xd1, 0xd1,
    0xeb, 0xff, 0xff, 0xff, 0xff, 0xfb, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0xff, 0xff, 0xff, 0xff, 0xfb, 0xff, 0xff, 0xff,
    0xff, 0xfb, 0xff, 0xff, 0xff, 0xff, 0xfb, 0xff,
    0xff, 0xff, 0xff, 0xfb, 0xef, 0xff, 0xff, 0xff,
    0xfa, 0x5c, 0xdd, 0xdd, 0xdd, 0xb2,

    /* U+F8A2 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb0, 0x0,
    0x3, 0x0, 0x0, 0x0, 0xb, 0xf0, 0x0, 0x9f,
    0x10, 0x0, 0x0, 0xf, 0xf0, 0xa, 0xff, 0x21,
    0x11, 0x11, 0x1f, 0xf0, 0xaf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0x9f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xd0, 0x8, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 56, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 56, .box_w = 3, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 14, .adv_w = 81, .box_w = 5, .box_h = 4, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 24, .adv_w = 146, .box_w = 9, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 65, .adv_w = 129, .box_w = 8, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 117, .adv_w = 175, .box_w = 11, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 167, .adv_w = 143, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 212, .adv_w = 44, .box_w = 2, .box_h = 4, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 216, .adv_w = 70, .box_w = 3, .box_h = 13, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 236, .adv_w = 70, .box_w = 4, .box_h = 13, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 262, .adv_w = 83, .box_w = 5, .box_h = 5, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 275, .adv_w = 121, .box_w = 7, .box_h = 6, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 296, .adv_w = 47, .box_w = 3, .box_h = 4, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 302, .adv_w = 80, .box_w = 5, .box_h = 2, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 307, .adv_w = 47, .box_w = 3, .box_h = 2, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 310, .adv_w = 73, .box_w = 7, .box_h = 13, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 356, .adv_w = 139, .box_w = 9, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 397, .adv_w = 77, .box_w = 4, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 415, .adv_w = 119, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 451, .adv_w = 119, .box_w = 7, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 483, .adv_w = 139, .box_w = 9, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 524, .adv_w = 119, .box_w = 7, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 556, .adv_w = 128, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 592, .adv_w = 124, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 628, .adv_w = 134, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 664, .adv_w = 128, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 700, .adv_w = 47, .box_w = 3, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 711, .adv_w = 47, .box_w = 3, .box_h = 9, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 725, .adv_w = 121, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 750, .adv_w = 121, .box_w = 7, .box_h = 5, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 768, .adv_w = 121, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 793, .adv_w = 119, .box_w = 7, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 825, .adv_w = 215, .box_w = 13, .box_h = 12, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 903, .adv_w = 152, .box_w = 11, .box_h = 9, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 953, .adv_w = 157, .box_w = 9, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 994, .adv_w = 150, .box_w = 9, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1035, .adv_w = 172, .box_w = 10, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1080, .adv_w = 139, .box_w = 7, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1112, .adv_w = 132, .box_w = 7, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1144, .adv_w = 161, .box_w = 9, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1185, .adv_w = 169, .box_w = 9, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1226, .adv_w = 64, .box_w = 2, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1235, .adv_w = 107, .box_w = 7, .box_h = 9, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 1267, .adv_w = 150, .box_w = 9, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1308, .adv_w = 124, .box_w = 7, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1340, .adv_w = 199, .box_w = 11, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1390, .adv_w = 169, .box_w = 9, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1431, .adv_w = 175, .box_w = 11, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1481, .adv_w = 150, .box_w = 8, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1517, .adv_w = 175, .box_w = 11, .box_h = 12, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 1583, .adv_w = 151, .box_w = 8, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1619, .adv_w = 129, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1655, .adv_w = 122, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1691, .adv_w = 165, .box_w = 8, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1727, .adv_w = 148, .box_w = 11, .box_h = 9, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 1777, .adv_w = 234, .box_w = 15, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1845, .adv_w = 140, .box_w = 9, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1886, .adv_w = 135, .box_w = 10, .box_h = 9, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 1931, .adv_w = 137, .box_w = 9, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1972, .adv_w = 69, .box_w = 4, .box_h = 13, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 1998, .adv_w = 73, .box_w = 6, .box_h = 13, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 2037, .adv_w = 69, .box_w = 3, .box_h = 13, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 2057, .adv_w = 121, .box_w = 7, .box_h = 6, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 2078, .adv_w = 104, .box_w = 7, .box_h = 1, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2082, .adv_w = 125, .box_w = 4, .box_h = 2, .ofs_x = 1, .ofs_y = 8},
    {.bitmap_index = 2086, .adv_w = 124, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2111, .adv_w = 142, .box_w = 8, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2151, .adv_w = 119, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2176, .adv_w = 142, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2216, .adv_w = 127, .box_w = 8, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2244, .adv_w = 73, .box_w = 6, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2274, .adv_w = 144, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 2314, .adv_w = 142, .box_w = 7, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2349, .adv_w = 58, .box_w = 3, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2364, .adv_w = 59, .box_w = 5, .box_h = 13, .ofs_x = -2, .ofs_y = -3},
    {.bitmap_index = 2397, .adv_w = 128, .box_w = 7, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2432, .adv_w = 58, .box_w = 2, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2442, .adv_w = 220, .box_w = 12, .box_h = 7, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2484, .adv_w = 142, .box_w = 7, .box_h = 7, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2509, .adv_w = 132, .box_w = 8, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2537, .adv_w = 142, .box_w = 8, .box_h = 10, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 2577, .adv_w = 142, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 2617, .adv_w = 85, .box_w = 4, .box_h = 7, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2631, .adv_w = 104, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2656, .adv_w = 86, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2683, .adv_w = 141, .box_w = 7, .box_h = 7, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2708, .adv_w = 116, .box_w = 9, .box_h = 7, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 2740, .adv_w = 187, .box_w = 12, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2782, .adv_w = 115, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2807, .adv_w = 116, .box_w = 9, .box_h = 10, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 2852, .adv_w = 108, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2877, .adv_w = 73, .box_w = 5, .box_h = 13, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 2910, .adv_w = 62, .box_w = 2, .box_h = 13, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 2923, .adv_w = 73, .box_w = 4, .box_h = 13, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 2949, .adv_w = 121, .box_w = 7, .box_h = 2, .ofs_x = 0, .ofs_y = 4},
    {.bitmap_index = 2956, .adv_w = 208, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3054, .adv_w = 208, .box_w = 13, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3119, .adv_w = 208, .box_w = 13, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3197, .adv_w = 208, .box_w = 13, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3262, .adv_w = 143, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3307, .adv_w = 208, .box_w = 13, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3398, .adv_w = 208, .box_w = 13, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3489, .adv_w = 234, .box_w = 15, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3579, .adv_w = 208, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3677, .adv_w = 234, .box_w = 15, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3752, .adv_w = 208, .box_w = 13, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3843, .adv_w = 104, .box_w = 7, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3882, .adv_w = 156, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3937, .adv_w = 234, .box_w = 15, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4042, .adv_w = 208, .box_w = 13, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4107, .adv_w = 143, .box_w = 9, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4170, .adv_w = 182, .box_w = 9, .box_h = 13, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 4229, .adv_w = 182, .box_w = 12, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4313, .adv_w = 182, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4385, .adv_w = 182, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4457, .adv_w = 182, .box_w = 9, .box_h = 13, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 4516, .adv_w = 182, .box_w = 13, .box_h = 12, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 4594, .adv_w = 130, .box_w = 8, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4642, .adv_w = 130, .box_w = 8, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4690, .adv_w = 182, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4762, .adv_w = 182, .box_w = 12, .box_h = 4, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 4786, .adv_w = 234, .box_w = 15, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4861, .adv_w = 260, .box_w = 17, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4980, .adv_w = 234, .box_w = 16, .box_h = 14, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 5092, .adv_w = 208, .box_w = 13, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5170, .adv_w = 182, .box_w = 12, .box_h = 8, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 5218, .adv_w = 182, .box_w = 12, .box_h = 8, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 5266, .adv_w = 260, .box_w = 17, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5360, .adv_w = 208, .box_w = 13, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5425, .adv_w = 208, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5523, .adv_w = 208, .box_w = 14, .box_h = 14, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 5621, .adv_w = 182, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5693, .adv_w = 182, .box_w = 12, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5777, .adv_w = 182, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5849, .adv_w = 182, .box_w = 12, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5915, .adv_w = 208, .box_w = 13, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5980, .adv_w = 130, .box_w = 10, .box_h = 14, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 6050, .adv_w = 182, .box_w = 12, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6134, .adv_w = 182, .box_w = 12, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6218, .adv_w = 234, .box_w = 15, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6293, .adv_w = 208, .box_w = 15, .box_h = 14, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 6398, .adv_w = 156, .box_w = 10, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6468, .adv_w = 260, .box_w = 17, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 6570, .adv_w = 260, .box_w = 17, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6647, .adv_w = 260, .box_w = 17, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6724, .adv_w = 260, .box_w = 17, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6801, .adv_w = 260, .box_w = 17, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6878, .adv_w = 260, .box_w = 17, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6955, .adv_w = 260, .box_w = 17, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 7049, .adv_w = 182, .box_w = 11, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7126, .adv_w = 182, .box_w = 12, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7210, .adv_w = 208, .box_w = 14, .box_h = 14, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 7308, .adv_w = 260, .box_w = 17, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7393, .adv_w = 156, .box_w = 10, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7463, .adv_w = 209, .box_w = 14, .box_h = 9, .ofs_x = 0, .ofs_y = 0}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_1[] = {
    0x0, 0x7, 0xa, 0xb, 0xc, 0x10, 0x12, 0x14,
    0x18, 0x1b, 0x20, 0x25, 0x26, 0x27, 0x3d, 0x42,
    0x47, 0x4a, 0x4b, 0x4c, 0x50, 0x51, 0x52, 0x53,
    0x66, 0x67, 0x6d, 0x6f, 0x70, 0x73, 0x76, 0x77,
    0x78, 0x7a, 0x92, 0x94, 0xc3, 0xc4, 0xc6, 0xc8,
    0xdf, 0xe6, 0xe9, 0xf2, 0x11b, 0x123, 0x15a, 0x1ea,
    0x23f, 0x240, 0x241, 0x242, 0x243, 0x286, 0x292, 0x2ec,
    0x303, 0x559, 0x7c1, 0x8a1
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 61441, .range_length = 2210, .glyph_id_start = 96,
        .unicode_list = unicode_list_1, .glyph_id_ofs_list = NULL, .list_length = 60, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/


/*Map glyph_ids to kern left classes*/
static const uint8_t kern_left_class_mapping[] =
{
    0, 0, 1, 2, 0, 3, 4, 5,
    2, 6, 7, 8, 9, 10, 9, 10,
    11, 12, 0, 13, 14, 15, 16, 17,
    18, 19, 12, 20, 20, 0, 0, 0,
    21, 22, 23, 24, 25, 22, 26, 27,
    28, 29, 29, 30, 31, 32, 29, 29,
    22, 33, 34, 35, 3, 36, 30, 37,
    37, 38, 39, 40, 41, 42, 43, 0,
    44, 0, 45, 46, 47, 48, 49, 50,
    51, 45, 52, 52, 53, 48, 45, 45,
    46, 46, 54, 55, 56, 57, 51, 58,
    58, 59, 58, 60, 41, 0, 0, 9,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0
};

/*Map glyph_ids to kern right classes*/
static const uint8_t kern_right_class_mapping[] =
{
    0, 0, 1, 2, 0, 3, 4, 5,
    2, 6, 7, 8, 9, 10, 9, 10,
    11, 12, 13, 14, 15, 16, 17, 12,
    18, 19, 20, 21, 21, 0, 0, 0,
    22, 23, 24, 25, 23, 25, 25, 25,
    23, 25, 25, 26, 25, 25, 25, 25,
    23, 25, 23, 25, 3, 27, 28, 29,
    29, 30, 31, 32, 33, 34, 35, 0,
    36, 0, 37, 38, 39, 39, 39, 0,
    39, 38, 40, 41, 38, 38, 42, 42,
    39, 42, 39, 42, 43, 44, 45, 46,
    46, 47, 46, 48, 0, 0, 35, 9,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0
};

/*Kern values between classes*/
static const int8_t kern_class_values[] =
{
    0, 1, 0, 0, 0, 0, 0, 0,
    0, 1, 0, 0, 2, 0, 0, 0,
    0, 1, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 1, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    1, 9, 0, 6, -5, 0, 0, 0,
    0, -11, -12, 1, 10, 5, 4, -8,
    1, 10, 1, 9, 2, 7, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 12, 2, -1, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -6, 0, 0, 0, 0, 0, -4,
    4, 4, 0, 0, -2, 0, -1, 2,
    0, -2, 0, -2, -1, -4, 0, 0,
    0, 0, -2, 0, 0, -3, -3, 0,
    0, -2, 0, -4, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -2, -2, 0,
    0, -6, 0, -25, 0, 0, -4, 0,
    4, 6, 0, 0, -4, 2, 2, 7,
    4, -4, 4, 0, 0, -12, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -8, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -2, -10, 0, -8, -1, 0, 0, 0,
    0, 0, 8, 0, -6, -2, -1, 1,
    0, -4, 0, 0, -1, -15, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -17, -2, 8, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 7, 0, 2, 0, 0, -4,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 8, 2, 1, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -8, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    1, 4, 2, 6, -2, 0, 0, 4,
    -2, -7, -28, 1, 6, 4, 0, -3,
    0, 7, 0, 7, 0, 7, 0, -19,
    0, -2, 6, 0, 7, -2, 4, 2,
    0, 0, 1, -2, 0, 0, -4, 17,
    0, 17, 0, 6, 0, 9, 3, 4,
    0, 0, 0, -8, 0, 0, 0, 0,
    1, -1, 0, 1, -4, -3, -4, 1,
    0, -2, 0, 0, 0, -8, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -14, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    1, -11, 0, -13, 0, 0, 0, 0,
    -1, 0, 21, -2, -3, 2, 2, -2,
    0, -3, 2, 0, 0, -11, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -20, 0, 2, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 12, 0, 0, -8, 0, 7, 0,
    -14, -20, -14, -4, 6, 0, 0, -14,
    0, 2, -5, 0, -3, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 5, 6, -25, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 1, 0, 0, 0, 0, 0, 1,
    1, -2, -4, 0, -1, -1, -2, 0,
    0, -1, 0, 0, 0, -4, 0, -2,
    0, -5, -4, 0, -5, -7, -7, -4,
    0, -4, 0, -4, 0, 0, 0, 0,
    -2, 0, 0, 2, 0, 1, -2, 0,
    0, 0, 0, 2, -1, 0, 0, 0,
    -1, 2, 2, -1, 0, 0, 0, -4,
    0, -1, 0, 0, 0, 0, 0, 1,
    0, 3, -1, 0, -2, 0, -4, 0,
    0, -1, 0, 6, 0, 0, -2, 0,
    0, 0, 0, 0, -1, 1, -1, -1,
    0, -2, 0, -2, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -1, -1, 0,
    -2, -2, 0, 0, 0, 0, 0, 1,
    0, 0, -1, 0, -2, -2, -2, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -1, 0, 0, 0, 0, -1, -3, 0,
    0, -6, -1, -6, 4, 0, 0, -4,
    2, 4, 6, 0, -5, -1, -2, 0,
    -1, -10, 2, -1, 1, -11, 2, 0,
    0, 1, -11, 0, -11, -2, -18, -1,
    0, -10, 0, 4, 6, 0, 3, 0,
    0, 0, 0, 0, 0, -4, -3, 0,
    0, 0, 0, -2, 0, 0, 0, -2,
    0, 0, 0, 0, 0, -1, -1, 0,
    -1, -3, 0, 0, 0, 0, 0, 0,
    0, -2, -2, 0, -1, -2, -2, 0,
    0, -2, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -2, -2, 0,
    0, -1, 0, -4, 2, 0, 0, -2,
    1, 2, 2, 0, 0, 0, 0, 0,
    0, -1, 0, 0, 0, 0, 0, 1,
    0, 0, -2, 0, -2, -1, -2, 0,
    0, 0, 0, 0, 0, 0, 2, 0,
    -2, 0, 0, 0, 0, -2, -3, 0,
    0, 6, -1, 1, -7, 0, 0, 6,
    -10, -11, -9, -4, 2, 0, -2, -14,
    -4, 0, -4, 0, -4, 3, -4, -13,
    0, -6, 0, 0, 1, -1, 2, -1,
    0, 2, 0, -6, -8, 0, -10, -5,
    -4, -5, -6, -2, -6, 0, -4, -6,
    0, 1, 0, -2, 0, 0, 0, 1,
    0, 2, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -2, 0, -1,
    0, -1, -2, 0, -4, -5, -5, -1,
    0, -6, 0, 0, 0, 0, 0, 0,
    -2, 0, 0, 0, 0, 1, -1, 0,
    0, 2, 0, 0, 0, 0, 0, 0,
    0, 0, 10, 0, 0, 0, 0, 0,
    0, 1, 0, 0, 0, -2, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -4, 0, 2, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -1, 0, 0, 0, -4, 0, 0, 0,
    0, -10, -6, 0, 0, 0, -3, -10,
    0, 0, -2, 2, 0, -6, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -3, 0, 0, -4, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -4, 0, 0, 0, 0, 2, 0,
    1, -4, -4, 0, -2, -2, -2, 0,
    0, 0, 0, 0, 0, -6, 0, -2,
    0, -3, -2, 0, -5, -5, -6, -2,
    0, -4, 0, -6, 0, 0, 0, 0,
    17, 0, 0, 1, 0, 0, -3, 0,
    0, -9, 0, 0, 0, 0, 0, -19,
    -4, 7, 6, -2, -9, 0, 2, -3,
    0, -10, -1, -3, 2, -15, -2, 3,
    0, 3, -7, -3, -8, -7, -9, 0,
    0, -12, 0, 12, 0, 0, -1, 0,
    0, 0, -1, -1, -2, -6, -7, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 1, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -2, 0, -1, -2, -3, 0,
    0, -4, 0, -2, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -1, 0, -4, 0, 0, 4,
    -1, 3, 0, -5, 2, -1, -1, -5,
    -2, 0, -3, -2, -1, 0, -3, -4,
    0, 0, -2, -1, -1, -4, -2, 0,
    0, -2, 0, 2, -1, 0, -5, 0,
    0, 0, -4, 0, -4, 0, -4, -4,
    0, 0, 0, 0, 0, 0, 0, 0,
    -4, 2, 0, -3, 0, -1, -2, -6,
    -1, -1, -1, -1, -1, -2, -1, 0,
    0, 0, 0, 0, -2, -2, -2, 0,
    0, 0, 0, 2, -1, 0, -1, 0,
    0, 0, -1, -2, -1, -2, -2, -2,
    2, 8, -1, 0, -6, 0, -1, 4,
    0, -2, -9, -3, 3, 0, 0, -10,
    -4, 2, -4, 1, 0, -1, -2, -7,
    0, -3, 1, 0, 0, -4, 0, 0,
    0, 2, 2, -4, -4, 0, -4, -2,
    -3, -2, -2, 0, -4, 1, -4, -4,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 2, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -4, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -1, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -2, -2, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -3,
    0, 0, -3, 0, 0, -2, -2, 0,
    0, 0, 0, -2, 0, 0, 0, 0,
    -1, 0, 0, 0, 0, 0, -1, 0,
    0, 0, -3, 0, -4, 0, 0, 0,
    -7, 0, 1, -5, 4, 0, -1, -10,
    0, 0, -5, -2, 0, -8, -5, -6,
    0, 0, -9, -2, -8, -8, -10, 0,
    -5, 0, 2, 14, -3, 0, -5, -2,
    -1, -2, -4, -6, -4, -8, -9, -5,
    0, 0, -1, 0, 1, 0, 0, -15,
    -2, 6, 5, -5, -8, 0, 1, -6,
    0, -10, -1, -2, 4, -19, -3, 1,
    0, 0, -14, -2, -11, -2, -15, 0,
    0, -15, 0, 12, 1, 0, -1, 0,
    0, 0, 0, -1, -1, -8, -1, 0,
    0, 0, 0, 0, -7, 0, -2, 0,
    -1, -6, -10, 0, 0, -1, -3, -6,
    -2, 0, -1, 0, 0, 0, 0, -9,
    -2, -7, -7, -2, -4, -5, -2, -4,
    0, -4, -2, -7, -3, 0, -2, -4,
    -2, -4, 0, 1, 0, -1, -7, 0,
    0, -4, 0, 0, 0, 0, 2, 0,
    1, -4, 9, 0, -2, -2, -2, 0,
    0, 0, 0, 0, 0, -6, 0, -2,
    0, -3, -2, 0, -5, -5, -6, -2,
    0, -4, 2, 8, 0, 0, 0, 0,
    17, 0, 0, 1, 0, 0, -3, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -1, -4,
    0, 0, 0, 0, 0, -1, 0, 0,
    0, -2, -2, 0, 0, -4, -2, 0,
    0, -4, 0, 4, -1, 0, 0, 0,
    0, 0, 0, 1, 0, 0, 0, 0,
    4, 2, -2, 0, -7, -3, 0, 6,
    -7, -7, -4, -4, 8, 4, 2, -18,
    -1, 4, -2, 0, -2, 2, -2, -7,
    0, -2, 2, -3, -2, -6, -2, 0,
    0, 6, 4, 0, -6, 0, -11, -3,
    6, -3, -8, 1, -3, -7, -7, -2,
    2, 0, -3, 0, -6, 0, 2, 7,
    -5, -8, -8, -5, 6, 0, 1, -15,
    -2, 2, -4, -1, -5, 0, -5, -8,
    -3, -3, -2, 0, 0, -5, -4, -2,
    0, 6, 5, -2, -11, 0, -11, -3,
    0, -7, -12, -1, -7, -4, -7, -6,
    0, 0, -3, 0, -4, -2, 0, -2,
    -4, 0, 4, -7, 2, 0, 0, -11,
    0, -2, -5, -4, -1, -6, -5, -7,
    -5, 0, -6, -2, -5, -4, -6, -2,
    0, 0, 1, 10, -4, 0, -6, -2,
    0, -2, -4, -5, -6, -6, -8, -3,
    4, 0, -3, 0, -10, -2, 1, 4,
    -7, -8, -4, -7, 7, -2, 1, -19,
    -4, 4, -5, -4, -8, 0, -6, -9,
    -2, -2, -2, -2, -4, -6, -1, 0,
    0, 6, 6, -1, -14, 0, -12, -5,
    5, -8, -14, -4, -7, -9, -10, -7,
    0, 0, 0, 0, -2, 0, 0, 2,
    -2, 4, 1, -4, 4, 0, 0, -6,
    -1, 0, -1, 0, 1, 1, -2, 0,
    0, 0, 0, 0, 0, -2, 0, 0,
    0, 0, 2, 6, 0, 0, -2, 0,
    0, 0, 0, -1, -1, -2, 0, 0,
    1, 2, 0, 0, 0, 0, 2, 0,
    -2, 0, 8, 0, 4, 1, 1, -3,
    0, 4, 0, 0, 0, 2, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 6, 0, 6, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -12, 0, -2, 4, 0, 6, 0,
    0, 21, 2, -4, -4, 2, 2, -1,
    1, -10, 0, 0, 10, -12, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -14, 8, 29, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -3, 0, 0, -4, -2, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -1, 0, -6, 0, 0, 1, 0,
    0, 2, 27, -4, -2, 7, 6, -6,
    2, 0, 0, 2, 2, -3, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -27, 6, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -6, 0, 0, 0, -6,
    0, 0, 0, 0, -5, -1, 0, 0,
    0, -5, 0, -2, 0, -10, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -14, 0, 0, 0, 0, 1, 0,
    0, 0, 0, 0, 0, -2, 0, 0,
    0, -3, 0, -6, 0, 0, 0, -4,
    2, -2, 0, 0, -6, -2, -5, 0,
    0, -6, 0, -2, 0, -10, 0, -2,
    0, 0, -17, -4, -8, -2, -7, 0,
    0, -14, 0, -6, -1, 0, 0, 0,
    0, 0, 0, 0, 0, -3, -4, -2,
    0, 0, 0, 0, -5, 0, -5, 3,
    -2, 4, 0, -1, -5, -1, -4, -4,
    0, -2, -1, -1, 1, -6, -1, 0,
    0, 0, -18, -2, -3, 0, -5, 0,
    -1, -10, -2, 0, 0, -1, -2, 0,
    0, 0, 0, 1, 0, -1, -4, -1,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 3, 0, 0, 0, 0,
    0, -5, 0, -1, 0, 0, 0, -4,
    2, 0, 0, 0, -6, -2, -4, 0,
    0, -6, 0, -2, 0, -10, 0, 0,
    0, 0, -20, 0, -4, -8, -10, 0,
    0, -14, 0, -1, -3, 0, 0, 0,
    0, 0, 0, 0, 0, -2, -3, -1,
    1, 0, 0, 4, -3, 0, 6, 10,
    -2, -2, -6, 2, 10, 4, 5, -6,
    2, 9, 2, 6, 5, 6, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 13, 10, -4, -2, 0, -2, 17,
    9, 17, 0, 0, 0, 2, 0, 0,
    0, 0, -3, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -1, 0, 0, 0,
    0, 0, 0, 0, 0, 3, 0, 0,
    0, 0, -17, -2, -2, -9, -10, 0,
    0, -14, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -3, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -1, 0, 0, 0,
    0, 0, 0, 0, 0, 3, 0, 0,
    0, 0, -17, -2, -2, -9, -10, 0,
    0, -8, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -2, 0, 0, 0,
    -5, 2, 0, -2, 2, 4, 2, -6,
    0, 0, -2, 2, 0, 2, 0, 0,
    0, 0, -5, 0, -2, -1, -4, 0,
    -2, -8, 0, 13, -2, 0, -5, -1,
    0, -1, -4, 0, -2, -6, -4, -2,
    0, 0, -3, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -1, 0, 0, 0,
    0, 0, 0, 0, 0, 3, 0, 0,
    0, 0, -17, -2, -2, -9, -10, 0,
    0, -14, 0, 0, 0, 0, 0, 0,
    10, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -3, 0, -7, -2, -2, 6,
    -2, -2, -8, 1, -1, 1, -1, -6,
    0, 5, 0, 2, 1, 2, -5, -8,
    -2, 0, -8, -4, -6, -9, -8, 0,
    -3, -4, -2, -3, -2, -1, -2, -1,
    0, -1, -1, 3, 0, 3, -1, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -1, -2, -2, 0,
    0, -6, 0, -1, 0, -4, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -12, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -2, -2, 0,
    0, 0, 0, 0, -2, 0, 0, -4,
    -2, 2, 0, -4, -4, -1, 0, -6,
    -1, -5, -1, -2, 0, -4, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -14, 0, 7, 0, 0, -4, 0,
    0, 0, 0, -3, 0, -2, 0, 0,
    0, 0, -1, 0, -5, 0, 0, 9,
    -3, -7, -6, 1, 2, 2, 0, -6,
    1, 3, 1, 6, 1, 7, -1, -6,
    0, 0, -8, 0, 0, -6, -6, 0,
    0, -4, 0, -3, -4, 0, -3, 0,
    -3, 0, -1, 3, 0, -2, -6, -2,
    0, 0, -2, 0, -4, 0, 0, 3,
    -5, 0, 2, -2, 2, 0, 0, -7,
    0, -1, -1, 0, -2, 2, -2, 0,
    0, 0, -9, -2, -5, 0, -6, 0,
    0, -10, 0, 8, -2, 0, -4, 0,
    1, 0, -2, 0, -2, -6, 0, -2,
    0, 0, 0, 0, -1, 0, 0, 2,
    -3, 1, 0, 0, -2, -1, 0, -2,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -13, 0, 5, 0, 0, -2, 0,
    0, 0, 0, 0, 0, -2, -2, 0
};


/*Collect the kern class' data in one place*/
static const lv_font_fmt_txt_kern_classes_t kern_classes =
{
    .class_pair_values   = kern_class_values,
    .left_class_mapping  = kern_left_class_mapping,
    .right_class_mapping = kern_right_class_mapping,
    .left_class_cnt      = 60,
    .right_class_cnt     = 48,
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LVGL_VERSION_MAJOR == 8
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
#endif

#if LVGL_VERSION_MAJOR >= 8
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_classes,
    .kern_scale = 16,
    .cmap_num = 2,
    .bpp = 4,
    .kern_classes = 1,
    .bitmap_format = 0,
#if LVGL_VERSION_MAJOR == 8
    .cache = &cache
#endif
};



/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LVGL_VERSION_MAJOR >= 8
const lv_font_t lv_font_montserratMedium_13 = {
#else
lv_font_t lv_font_montserratMedium_13 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 13,          /*The maximum line height required by the font*/
    .base_line = 1,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -1,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc,          /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
#if LV_VERSION_CHECK(8, 2, 0) || LVGL_VERSION_MAJOR >= 9
    .fallback = NULL,
#endif
    .user_data = NULL,
};



#endif /*#if LV_FONT_MONTSERRATMEDIUM_13*/

