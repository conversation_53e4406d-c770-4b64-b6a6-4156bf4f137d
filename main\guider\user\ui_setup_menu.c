#include "events_init.h"
#include <stdio.h>
#include "lvgl.h"
#include "esp_log.h"
#include "ui_user_inc.h"

#if LV_USE_GUIDER_SIMULATOR && LV_USE_FREEMASTER
#include "freemaster_client.h"
#endif

#define MENU_TAG "MENU_EVENTS"


// 通用的返回主菜单事件处理函数
void back_to_menu_event_handler(lv_event_t *e)
{
    lv_event_code_t code = lv_event_get_code(e);
    switch (code) {
    case LV_EVENT_CLICKED:
    {      
        if (guider_ui.Menu_page == NULL) {
            ESP_LOGE("EVENTS", "Menu_page is NULL, initializing");
            ui_scr_t *menu_scr = ui_scr_get(UI_SCR_ID_MENU_PAGE);
            if (menu_scr && menu_scr->setup_handle) {
                menu_scr->setup_handle(&guider_ui);
            } else {
                ESP_LOGE("EVENTS", "Menu_page setup_handle is NULL, cannot proceed");
                return;
            }
        }        
        ui_scr_goto(UI_SCR_ID_MENU_PAGE, false);
        break;
    }
    default:
        break;
    }
}

// Wave按钮事件处理
static void Menu_page_btn_wave_event_handler(lv_event_t *e)
{
    lv_event_code_t code = lv_event_get_code(e);
    switch (code) {
    case LV_EVENT_CLICKED:
    {
        ESP_LOGI(MENU_TAG, "Wave button clicked");
        ui_scr_goto(UI_SCR_ID_WAVE_PAGE, true);
        break;
    }
    default:
        break;
    }
}

// AI按钮事件处理
static void Menu_page_btn_ai_event_handler(lv_event_t *e)
{
    lv_event_code_t code = lv_event_get_code(e);
    switch (code) {
    case LV_EVENT_CLICKED:
    {
        ESP_LOGI(MENU_TAG, "AI button clicked");
        ui_scr_goto(UI_SCR_ID_AI_PAGE, true);
        break;
    }
    default:
        break;
    }
}

// PID按钮事件处理
static void Menu_page_btn_pid_event_handler(lv_event_t *e)
{
    lv_event_code_t code = lv_event_get_code(e);
    switch (code) {
    case LV_EVENT_CLICKED:
    {
        ESP_LOGI(MENU_TAG, "PID button clicked");
        ui_scr_goto(UI_SCR_ID_PID_PAGE, true);
        break;
    }
    default:
        break;
    }
}

// Setting按钮事件处理
static void Menu_page_btn_setting_event_handler(lv_event_t *e)
{
    lv_event_code_t code = lv_event_get_code(e);
    switch (code) {
    case LV_EVENT_CLICKED:
    {
        ESP_LOGI(MENU_TAG, "Setting button clicked");
        ui_scr_goto(UI_SCR_ID_SETTINGS_PAGE, true);
        break;
    }
    default:
        break;
    }
}

// 蓝牙容器事件处理
static void Menu_page_cont_3_event_handler(lv_event_t *e)
{
    lv_event_code_t code = lv_event_get_code(e);
    switch (code) {
    case LV_EVENT_CLICKED:
    {
        ESP_LOGI(MENU_TAG, "BT container clicked");
        ui_scr_goto(UI_SCR_ID_BT_PAGE, true);
        break;
    }
    default:
        break;
    }
}

// 蓝牙按钮事件处理
static void Menu_page_btn_bt_event_handler(lv_event_t *e)
{
    lv_event_code_t code = lv_event_get_code(e);
    switch (code) {
    case LV_EVENT_CLICKED:
    {
        ESP_LOGI(MENU_TAG, "BT button clicked");
        ui_scr_goto(UI_SCR_ID_BT_PAGE, true);
        break;
    }
    default:
        break;
    }
}

// CAN按钮事件处理
static void Menu_page_btn_can_event_handler(lv_event_t *e)
{
    lv_event_code_t code = lv_event_get_code(e);
    switch (code) {
    case LV_EVENT_CLICKED:
    {
        ESP_LOGI(MENU_TAG, "CAN button clicked");
        ui_scr_goto(UI_SCR_ID_CAN_CONNET_PAGE, true);
        break;
    }
    default:
        break;
    }
}

// WIFI按钮事件处理
static void Menu_page_btn_wifi_event_handler(lv_event_t *e)
{
    lv_event_code_t code = lv_event_get_code(e);
    switch (code) {
    case LV_EVENT_CLICKED:
    {
        ESP_LOGI(MENU_TAG, "WIFI button clicked");
        ui_scr_goto(UI_SCR_ID_WIFI_PAGE, true);
        break;
    }
    default:
        break;
    }
}

// 设备按钮事件处理
static void Menu_page_btn_device_event_handler(lv_event_t *e)
{
    lv_event_code_t code = lv_event_get_code(e);
    switch (code) {
    case LV_EVENT_CLICKED:
    {
        ESP_LOGI(MENU_TAG, "Device button clicked");
        ui_scr_goto(UI_SCR_ID_DEVOCE_INFO_PAGE, true);
        break;
    }
    default:
        break;
    }
}

// 更新按钮事件处理
static void Menu_page_btn_update_event_handler(lv_event_t *e)
{
    lv_event_code_t code = lv_event_get_code(e);
    switch (code) {
    case LV_EVENT_CLICKED:
    {
        ESP_LOGI(MENU_TAG, "Update button clicked");
        ui_scr_goto(UI_SCR_ID_UPDATE_PAGE, true);
        break;
    }
    default:
        break;
    }
}

// 菜单页面初始化
void ui_setup_menu(void) {
    ESP_LOGI(MENU_TAG, "设置菜单页面");
    
    // 获取菜单页面
    ui_scr_t *menu_scr = ui_scr_get(UI_SCR_ID_MENU_PAGE);
    if (menu_scr == NULL) {
        ESP_LOGE(MENU_TAG, "Menu page not found in screen manager");
        return;
    }
    
    // 初始化菜单页面
    if (guider_ui.Menu_page == NULL && menu_scr->setup_handle) {
        menu_scr->setup_handle(&guider_ui);
    }
    
    // 使用ui_scr_goto函数切换到菜单页面
    ui_scr_goto(UI_SCR_ID_MENU_PAGE, true);
}

void events_init_Menu_page(lv_ui *ui)
{
    ESP_LOGI(MENU_TAG, "Initializing menu page events");
    
    // Wave页面
    lv_obj_add_event_cb(ui->Menu_page_btn_wave, Menu_page_btn_wave_event_handler, LV_EVENT_ALL, ui);
    
    // AI页面
    lv_obj_add_event_cb(ui->Menu_page_btn_ai, Menu_page_btn_ai_event_handler, LV_EVENT_ALL, ui);
    
    // PID页面
    lv_obj_add_event_cb(ui->Menu_page_btn_pid, Menu_page_btn_pid_event_handler, LV_EVENT_ALL, ui);
    
    // 设置页面
    lv_obj_add_event_cb(ui->Menu_page_btn_setting, Menu_page_btn_setting_event_handler, LV_EVENT_ALL, ui);
    
    // 蓝牙设置
    lv_obj_add_event_cb(ui->Menu_page_cont_3, Menu_page_cont_3_event_handler, LV_EVENT_ALL, ui);
    lv_obj_add_event_cb(ui->Menu_page_btn_bt, Menu_page_btn_bt_event_handler, LV_EVENT_ALL, ui);
    
    // CAN设置
    lv_obj_add_event_cb(ui->Menu_page_btn_can, Menu_page_btn_can_event_handler, LV_EVENT_ALL, ui);
    
    // WIFI设置
    lv_obj_add_event_cb(ui->Menu_page_btn_wifi, Menu_page_btn_wifi_event_handler, LV_EVENT_ALL, ui);
    
    // 设备信息
    lv_obj_add_event_cb(ui->Menu_page_btn_device, Menu_page_btn_device_event_handler, LV_EVENT_ALL, ui);
    
    // 更新页面
    lv_obj_add_event_cb(ui->Menu_page_btn_update, Menu_page_btn_update_event_handler, LV_EVENT_ALL, ui);
    
    ESP_LOGI(MENU_TAG, "Menu page events initialization complete");
}