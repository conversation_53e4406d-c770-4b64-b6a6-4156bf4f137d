/*******************************************************************************
 * Size: 14 px
 * Bpp: 4
 * Opts: undefined
 ******************************************************************************/

#ifdef LV_LVGL_H_INCLUDE_SIMPLE
#include "lvgl.h"
#else
#include "lvgl.h"
#endif

#ifndef LV_FONT_HARMONYOS_SANS_SC_REGULAR_14
#define LV_FONT_HARMONYOS_SANS_SC_REGULAR_14 1
#endif

#if LV_FONT_HARMONYOS_SANS_SC_REGULAR_14

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0xf, 0x30, 0xf3, 0xe, 0x30, 0xe2, 0xe, 0x20,
    0xd2, 0xd, 0x10, 0xd1, 0x4, 0x0, 0xa2, 0xd,
    0x40,

    /* U+0022 "\"" */
    0x29, 0xb, 0x3, 0xc0, 0xf0, 0x2c, 0xf, 0x2,
    0xc0, 0xf0,

    /* U+0023 "#" */
    0x0, 0x9, 0x80, 0x3e, 0x0, 0x0, 0xd4, 0x7,
    0xa0, 0x0, 0xf, 0x10, 0xa7, 0x0, 0xdf, 0xff,
    0xff, 0xfd, 0x2, 0x9b, 0x24, 0xf3, 0x20, 0xb,
    0x60, 0x5c, 0x0, 0x0, 0xe2, 0x9, 0x80, 0xc,
    0xff, 0xff, 0xff, 0xe0, 0x28, 0xc3, 0x3f, 0x42,
    0x0, 0x98, 0x3, 0xe0, 0x0, 0xd, 0x40, 0x7a,
    0x0, 0x0,

    /* U+0024 "$" */
    0x0, 0x7, 0xb0, 0x0, 0x0, 0x7, 0xb0, 0x0,
    0x2, 0xcf, 0xfd, 0x30, 0xd, 0x98, 0xb9, 0xe1,
    0x2f, 0x7, 0xb0, 0xa1, 0x2f, 0x37, 0xb0, 0x0,
    0xa, 0xec, 0xb0, 0x0, 0x0, 0x7e, 0xfa, 0x10,
    0x0, 0x7, 0xdc, 0xe1, 0x2, 0x7, 0xb0, 0xe6,
    0x7c, 0x7, 0xb0, 0xc6, 0x1e, 0xb9, 0xc8, 0xf1,
    0x2, 0xaf, 0xfc, 0x30, 0x0, 0x7, 0xb0, 0x0,
    0x0, 0x7, 0xb0, 0x0,

    /* U+0025 "%" */
    0xb, 0xfb, 0x10, 0x7, 0xc0, 0x8, 0xc2, 0xb9,
    0x1, 0xf4, 0x0, 0xb7, 0x5, 0xc0, 0x9b, 0x0,
    0x9, 0xb0, 0x9a, 0x2f, 0x20, 0x0, 0x1d, 0xfd,
    0x2b, 0x90, 0x0, 0x0, 0x1, 0x4, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0xc7, 0x3c, 0xe8, 0x0, 0x0,
    0x5e, 0xd, 0x93, 0xe4, 0x0, 0xe, 0x50, 0xf2,
    0xa, 0x70, 0x7, 0xc0, 0xd, 0x60, 0xd5, 0x1,
    0xf4, 0x0, 0x2d, 0xf8, 0x0,

    /* U+0026 "&" */
    0x0, 0x2b, 0xfd, 0x40, 0x0, 0x0, 0xc, 0xb3,
    0x9e, 0x0, 0x0, 0x0, 0xf4, 0x1, 0xf2, 0x0,
    0x0, 0xe, 0x60, 0x5f, 0x10, 0x0, 0x0, 0x9e,
    0x6f, 0x60, 0x0, 0x0, 0x4, 0xff, 0x40, 0x28,
    0x0, 0x5, 0xf8, 0xf7, 0x5, 0xc0, 0x1, 0xf6,
    0x5, 0xf5, 0x99, 0x0, 0x4f, 0x0, 0x7, 0xfe,
    0x30, 0x3, 0xf1, 0x0, 0xc, 0xf1, 0x0, 0xd,
    0xa2, 0x17, 0xfd, 0xc0, 0x0, 0x1a, 0xff, 0xb2,
    0x1e, 0xa0,

    /* U+0027 "'" */
    0x29, 0x3c, 0x2c, 0x2c,

    /* U+0028 "(" */
    0x0, 0xd, 0x30, 0x9, 0x90, 0x2, 0xf2, 0x0,
    0x7c, 0x0, 0xc, 0x70, 0x0, 0xe5, 0x0, 0xf,
    0x40, 0x0, 0xf4, 0x0, 0xe, 0x50, 0x0, 0xc7,
    0x0, 0x7, 0xb0, 0x0, 0x2f, 0x20, 0x0, 0xa9,
    0x0, 0x1, 0xd3,

    /* U+0029 ")" */
    0x6b, 0x0, 0xc, 0x70, 0x4, 0xe0, 0x0, 0xe4,
    0x0, 0xa8, 0x0, 0x8b, 0x0, 0x6c, 0x0, 0x6c,
    0x0, 0x8b, 0x0, 0xa9, 0x0, 0xe4, 0x4, 0xe0,
    0xc, 0x70, 0x6b, 0x0,

    /* U+002A "*" */
    0x0, 0x77, 0x0, 0x4b, 0x88, 0xb4, 0x6, 0xff,
    0x60, 0x2c, 0xdd, 0xc3, 0x15, 0x77, 0x52, 0x0,
    0x44, 0x0,

    /* U+002B "+" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0x80, 0x0,
    0x0, 0x8, 0x80, 0x0, 0x0, 0x8, 0x80, 0x0,
    0x4f, 0xff, 0xff, 0xf4, 0x2, 0x29, 0x92, 0x20,
    0x0, 0x8, 0x80, 0x0, 0x0, 0x8, 0x80, 0x0,

    /* U+002C "," */
    0x8, 0x40, 0xda, 0x4, 0x81, 0xc1, 0x0, 0x0,

    /* U+002D "-" */
    0x1f, 0xff, 0xfe, 0x2, 0x22, 0x22,

    /* U+002E "." */
    0xa, 0x20, 0xe3,

    /* U+002F "/" */
    0x0, 0x2, 0xf1, 0x0, 0x7, 0xb0, 0x0, 0xd,
    0x60, 0x0, 0x2f, 0x0, 0x0, 0x8a, 0x0, 0x0,
    0xd5, 0x0, 0x3, 0xf0, 0x0, 0x9, 0xa0, 0x0,
    0xe, 0x40, 0x0, 0x4e, 0x0, 0x0, 0x99, 0x0,
    0x0,

    /* U+0030 "0" */
    0x0, 0x8e, 0xe8, 0x0, 0x7, 0xe5, 0x5e, 0x70,
    0xe, 0x60, 0x6, 0xe0, 0x2f, 0x20, 0x2, 0xf2,
    0x4f, 0x0, 0x0, 0xf4, 0x5f, 0x0, 0x0, 0xf5,
    0x4f, 0x0, 0x0, 0xf4, 0x2f, 0x20, 0x2, 0xf2,
    0xe, 0x60, 0x6, 0xe0, 0x8, 0xe5, 0x5e, 0x70,
    0x0, 0x9e, 0xe8, 0x0,

    /* U+0031 "1" */
    0x0, 0x7f, 0x53, 0xde, 0xf5, 0x99, 0xf, 0x50,
    0x0, 0xf5, 0x0, 0xf, 0x50, 0x0, 0xf5, 0x0,
    0xf, 0x50, 0x0, 0xf5, 0x0, 0xf, 0x50, 0x0,
    0xf5, 0x0, 0xf, 0x50,

    /* U+0032 "2" */
    0x0, 0x9e, 0xf9, 0x0, 0xb, 0xd4, 0x5e, 0x80,
    0x1e, 0x10, 0x7, 0xe0, 0x0, 0x0, 0x6, 0xe0,
    0x0, 0x0, 0xb, 0xa0, 0x0, 0x0, 0x5f, 0x20,
    0x0, 0x3, 0xf6, 0x0, 0x0, 0x2e, 0x90, 0x0,
    0x0, 0xdb, 0x0, 0x0, 0xb, 0xe3, 0x33, 0x30,
    0x4f, 0xff, 0xff, 0xf3,

    /* U+0033 "3" */
    0x1, 0xaf, 0xea, 0x10, 0xd, 0xb4, 0x5e, 0x90,
    0x7, 0x0, 0x8, 0xd0, 0x0, 0x0, 0x3d, 0x80,
    0x0, 0x1f, 0xfc, 0x0, 0x0, 0x3, 0x6e, 0xa0,
    0x0, 0x0, 0x4, 0xf1, 0x1, 0x0, 0x2, 0xf3,
    0x2e, 0x10, 0x5, 0xf1, 0xd, 0xc5, 0x5e, 0xa0,
    0x1, 0xae, 0xe9, 0x0,

    /* U+0034 "4" */
    0x0, 0x0, 0xd7, 0x0, 0x0, 0x6, 0xe0, 0x0,
    0x0, 0xe, 0x60, 0x0, 0x0, 0x7d, 0x0, 0x0,
    0x1, 0xf4, 0xa, 0x30, 0x8, 0xc0, 0xf, 0x40,
    0x1f, 0x30, 0xf, 0x40, 0x7f, 0xff, 0xff, 0xfc,
    0x12, 0x22, 0x2f, 0x62, 0x0, 0x0, 0xf, 0x40,
    0x0, 0x0, 0xf, 0x40,

    /* U+0035 "5" */
    0x3, 0xff, 0xff, 0xb0, 0x4, 0xe2, 0x22, 0x10,
    0x6, 0xc0, 0x0, 0x0, 0x8, 0xa0, 0x0, 0x0,
    0xa, 0xee, 0xf9, 0x0, 0xc, 0xb4, 0x5e, 0xa0,
    0x1, 0x0, 0x5, 0xf1, 0x0, 0x0, 0x2, 0xf2,
    0x6, 0x10, 0x5, 0xf0, 0xb, 0xc4, 0x5e, 0x90,
    0x1, 0xaf, 0xe8, 0x0,

    /* U+0036 "6" */
    0x0, 0x0, 0xd8, 0x0, 0x0, 0x7, 0xe0, 0x0,
    0x0, 0x1f, 0x50, 0x0, 0x0, 0x9b, 0x0, 0x0,
    0x3, 0xff, 0xfb, 0x10, 0xb, 0xe5, 0x4c, 0xd0,
    0x1f, 0x40, 0x2, 0xf4, 0x3f, 0x20, 0x0, 0xf6,
    0x1f, 0x40, 0x2, 0xf3, 0xa, 0xe5, 0x4c, 0xc0,
    0x0, 0x9e, 0xfa, 0x10,

    /* U+0037 "7" */
    0x3f, 0xff, 0xff, 0xf2, 0x2, 0x22, 0x28, 0xe0,
    0x0, 0x0, 0xc, 0x80, 0x0, 0x0, 0x2f, 0x20,
    0x0, 0x0, 0x8c, 0x0, 0x0, 0x0, 0xe6, 0x0,
    0x0, 0x5, 0xf0, 0x0, 0x0, 0xb, 0xa0, 0x0,
    0x0, 0x1f, 0x40, 0x0, 0x0, 0x7e, 0x0, 0x0,
    0x0, 0xd8, 0x0, 0x0,

    /* U+0038 "8" */
    0x1, 0xaf, 0xfa, 0x10, 0xb, 0xd4, 0x4d, 0xa0,
    0xf, 0x60, 0x5, 0xe0, 0xb, 0xb1, 0x1b, 0xa0,
    0x1, 0xef, 0xfe, 0x10, 0xa, 0xd4, 0x4d, 0xa0,
    0x2f, 0x30, 0x3, 0xf2, 0x4f, 0x0, 0x0, 0xf4,
    0x3f, 0x30, 0x3, 0xf2, 0xc, 0xd4, 0x4d, 0xb0,
    0x1, 0xaf, 0xfa, 0x10,

    /* U+0039 "9" */
    0x1, 0xaf, 0xe9, 0x0, 0xc, 0xd4, 0x5e, 0xa0,
    0x3f, 0x20, 0x5, 0xf1, 0x5f, 0x0, 0x2, 0xf3,
    0x4f, 0x10, 0x4, 0xf1, 0xe, 0xb1, 0x2c, 0xb0,
    0x3, 0xef, 0xff, 0x30, 0x0, 0x2, 0xa9, 0x0,
    0x0, 0x3, 0xf1, 0x0, 0x0, 0xd, 0x70, 0x0,
    0x0, 0x7d, 0x0, 0x0,

    /* U+003A ":" */
    0xd6, 0x83, 0x0, 0x0, 0x0, 0x0, 0x94, 0xc6,

    /* U+003B ";" */
    0xa, 0x80, 0x75, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0x50, 0xbb, 0x2, 0x90, 0xc2, 0x0,
    0x0,

    /* U+003C "<" */
    0x0, 0x0, 0x2, 0xa3, 0x0, 0x4, 0xbf, 0xa1,
    0x5, 0xcf, 0x91, 0x0, 0x4f, 0xc1, 0x0, 0x0,
    0x7, 0xee, 0x70, 0x0, 0x0, 0x5, 0xdf, 0x81,
    0x0, 0x0, 0x4, 0xb4, 0x0, 0x0, 0x0, 0x0,

    /* U+003D "=" */
    0x4f, 0xff, 0xff, 0xf4, 0x2, 0x22, 0x22, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xff, 0xf4,
    0x2, 0x22, 0x22, 0x20,

    /* U+003E ">" */
    0x4a, 0x20, 0x0, 0x0, 0x1a, 0xfb, 0x40, 0x0,
    0x0, 0x29, 0xfc, 0x50, 0x0, 0x0, 0x1d, 0xf4,
    0x0, 0x7, 0xee, 0x60, 0x18, 0xfc, 0x50, 0x0,
    0x4b, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+003F "?" */
    0x0, 0x0, 0x0, 0x0, 0x8, 0xef, 0xa1, 0x9,
    0xd4, 0x5e, 0x90, 0xa2, 0x0, 0x7d, 0x0, 0x0,
    0x9, 0xb0, 0x0, 0x5, 0xf2, 0x0, 0x3, 0xf4,
    0x0, 0x0, 0xb8, 0x0, 0x0, 0xd, 0x30, 0x0,
    0x0, 0x72, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x75, 0x0, 0x0, 0xa, 0x80, 0x0,

    /* U+0040 "@" */
    0x0, 0x0, 0x6c, 0xee, 0xc7, 0x10, 0x0, 0x0,
    0x2d, 0xc6, 0x33, 0x5b, 0xe3, 0x0, 0x1, 0xe8,
    0x0, 0x0, 0x0, 0x7e, 0x10, 0x9, 0xa0, 0x1a,
    0xfd, 0x6d, 0xb, 0x80, 0xe, 0x30, 0xbc, 0x46,
    0xed, 0x4, 0xe0, 0x2e, 0x1, 0xf3, 0x0, 0x8d,
    0x1, 0xf0, 0x4d, 0x3, 0xf0, 0x0, 0x5d, 0x0,
    0xf1, 0x2e, 0x1, 0xf2, 0x0, 0x7d, 0x1, 0xf0,
    0xf, 0x10, 0xca, 0x2, 0xdf, 0x17, 0xb0, 0xa,
    0x80, 0x2d, 0xff, 0x4a, 0xff, 0x30, 0x2, 0xf4,
    0x0, 0x10, 0x0, 0x10, 0x0, 0x0, 0x5f, 0x92,
    0x0, 0x39, 0x0, 0x0, 0x0, 0x2, 0xaf, 0xff,
    0xf9, 0x10, 0x0, 0x0, 0x0, 0x0, 0x11, 0x0,
    0x0, 0x0,

    /* U+0041 "A" */
    0x0, 0x2, 0xf4, 0x0, 0x0, 0x0, 0x8, 0xfa,
    0x0, 0x0, 0x0, 0xd, 0x9f, 0x0, 0x0, 0x0,
    0x3f, 0xe, 0x60, 0x0, 0x0, 0x9a, 0x9, 0xc0,
    0x0, 0x0, 0xe5, 0x4, 0xf2, 0x0, 0x4, 0xf0,
    0x0, 0xe8, 0x0, 0xa, 0xff, 0xff, 0xfe, 0x0,
    0xf, 0x63, 0x33, 0x5f, 0x40, 0x5e, 0x0, 0x0,
    0xc, 0xa0, 0xb8, 0x0, 0x0, 0x6, 0xf0,

    /* U+0042 "B" */
    0xcf, 0xff, 0xd7, 0x0, 0xc9, 0x33, 0x7f, 0x80,
    0xc7, 0x0, 0x7, 0xe0, 0xc7, 0x0, 0x6, 0xf0,
    0xc7, 0x0, 0x3d, 0xa0, 0xcf, 0xff, 0xff, 0x30,
    0xc8, 0x22, 0x3a, 0xf2, 0xc7, 0x0, 0x0, 0xd8,
    0xc7, 0x0, 0x0, 0xd8, 0xc8, 0x22, 0x4a, 0xf3,
    0xcf, 0xff, 0xec, 0x40,

    /* U+0043 "C" */
    0x0, 0x8, 0xdf, 0xc5, 0x0, 0xc, 0xd6, 0x48,
    0xf5, 0x9, 0xd0, 0x0, 0x6, 0x50, 0xf5, 0x0,
    0x0, 0x0, 0x3f, 0x10, 0x0, 0x0, 0x4, 0xf0,
    0x0, 0x0, 0x0, 0x3f, 0x10, 0x0, 0x0, 0x0,
    0xf5, 0x0, 0x0, 0x0, 0x9, 0xd0, 0x0, 0x6,
    0x50, 0xc, 0xd6, 0x48, 0xf5, 0x0, 0x8, 0xef,
    0xc5, 0x0,

    /* U+0044 "D" */
    0xcf, 0xff, 0xc6, 0x0, 0xc, 0x82, 0x37, 0xeb,
    0x0, 0xc7, 0x0, 0x1, 0xe8, 0xc, 0x70, 0x0,
    0x6, 0xe0, 0xc7, 0x0, 0x0, 0x2f, 0x2c, 0x70,
    0x0, 0x0, 0xf4, 0xc7, 0x0, 0x0, 0x2f, 0x2c,
    0x70, 0x0, 0x6, 0xe0, 0xc7, 0x0, 0x1, 0xe8,
    0xc, 0x93, 0x37, 0xeb, 0x0, 0xcf, 0xff, 0xc6,
    0x0, 0x0,

    /* U+0045 "E" */
    0xcf, 0xff, 0xff, 0x8c, 0x93, 0x33, 0x31, 0xc7,
    0x0, 0x0, 0xc, 0x70, 0x0, 0x0, 0xc7, 0x0,
    0x0, 0xc, 0xff, 0xff, 0xf0, 0xc9, 0x33, 0x32,
    0xc, 0x70, 0x0, 0x0, 0xc7, 0x0, 0x0, 0xc,
    0x93, 0x33, 0x31, 0xcf, 0xff, 0xff, 0xa0,

    /* U+0046 "F" */
    0xcf, 0xff, 0xff, 0x8c, 0x93, 0x33, 0x31, 0xc7,
    0x0, 0x0, 0xc, 0x70, 0x0, 0x0, 0xc7, 0x0,
    0x0, 0xc, 0xff, 0xff, 0xf0, 0xc9, 0x33, 0x32,
    0xc, 0x70, 0x0, 0x0, 0xc7, 0x0, 0x0, 0xc,
    0x70, 0x0, 0x0, 0xc7, 0x0, 0x0, 0x0,

    /* U+0047 "G" */
    0x0, 0x7, 0xdf, 0xe8, 0x0, 0x0, 0xcd, 0x64,
    0x6d, 0xb0, 0x8, 0xe1, 0x0, 0x1, 0x50, 0xf,
    0x60, 0x0, 0x0, 0x0, 0x3f, 0x10, 0x0, 0x0,
    0x0, 0x4f, 0x0, 0x4, 0xff, 0xf2, 0x3f, 0x10,
    0x0, 0x34, 0xf2, 0xf, 0x60, 0x0, 0x1, 0xf2,
    0x8, 0xe1, 0x0, 0x1, 0xf2, 0x0, 0xce, 0x74,
    0x5c, 0xe0, 0x0, 0x7, 0xdf, 0xe9, 0x10,

    /* U+0048 "H" */
    0xc7, 0x0, 0x0, 0xf, 0x3c, 0x70, 0x0, 0x0,
    0xf3, 0xc7, 0x0, 0x0, 0xf, 0x3c, 0x70, 0x0,
    0x0, 0xf3, 0xc7, 0x0, 0x0, 0xf, 0x3c, 0xff,
    0xff, 0xff, 0xf3, 0xc9, 0x33, 0x33, 0x3f, 0x3c,
    0x70, 0x0, 0x0, 0xf3, 0xc7, 0x0, 0x0, 0xf,
    0x3c, 0x70, 0x0, 0x0, 0xf3, 0xc7, 0x0, 0x0,
    0xf, 0x30,

    /* U+0049 "I" */
    0xc7, 0xc7, 0xc7, 0xc7, 0xc7, 0xc7, 0xc7, 0xc7,
    0xc7, 0xc7, 0xc7,

    /* U+004A "J" */
    0x0, 0x0, 0xf4, 0x0, 0x0, 0xf4, 0x0, 0x0,
    0xf4, 0x0, 0x0, 0xf4, 0x0, 0x0, 0xf4, 0x0,
    0x0, 0xf4, 0x0, 0x0, 0xf4, 0x0, 0x0, 0xf4,
    0x40, 0x1, 0xf2, 0xda, 0x4a, 0xd0, 0x2c, 0xfc,
    0x30,

    /* U+004B "K" */
    0xc7, 0x0, 0x1, 0xda, 0xc, 0x70, 0x0, 0xcb,
    0x0, 0xc7, 0x0, 0xad, 0x0, 0xc, 0x70, 0x8e,
    0x10, 0x0, 0xc7, 0x6f, 0x20, 0x0, 0xc, 0xbf,
    0xf8, 0x0, 0x0, 0xcf, 0x55, 0xf3, 0x0, 0xc,
    0x90, 0xa, 0xe1, 0x0, 0xc7, 0x0, 0xd, 0xa0,
    0xc, 0x70, 0x0, 0x3f, 0x60, 0xc7, 0x0, 0x0,
    0x7f, 0x20,

    /* U+004C "L" */
    0xc7, 0x0, 0x0, 0xc, 0x70, 0x0, 0x0, 0xc7,
    0x0, 0x0, 0xc, 0x70, 0x0, 0x0, 0xc7, 0x0,
    0x0, 0xc, 0x70, 0x0, 0x0, 0xc7, 0x0, 0x0,
    0xc, 0x70, 0x0, 0x0, 0xc7, 0x0, 0x0, 0xc,
    0x93, 0x33, 0x31, 0xcf, 0xff, 0xff, 0x80,

    /* U+004D "M" */
    0xc9, 0x0, 0x0, 0x0, 0x6e, 0xcf, 0x30, 0x0,
    0x1, 0xee, 0xcf, 0xc0, 0x0, 0x9, 0xfe, 0xc8,
    0xf5, 0x0, 0x2f, 0x8e, 0xc6, 0x7e, 0x0, 0xb8,
    0x5e, 0xc6, 0xd, 0x75, 0xe0, 0x5e, 0xc6, 0x4,
    0xfe, 0x60, 0x5e, 0xc6, 0x0, 0xbc, 0x0, 0x5e,
    0xc6, 0x0, 0x1, 0x0, 0x5e, 0xc6, 0x0, 0x0,
    0x0, 0x5e, 0xc6, 0x0, 0x0, 0x0, 0x5e,

    /* U+004E "N" */
    0xc9, 0x0, 0x0, 0xf, 0x2c, 0xf4, 0x0, 0x0,
    0xf2, 0xcd, 0xd0, 0x0, 0xf, 0x2c, 0x6d, 0x90,
    0x0, 0xf2, 0xc6, 0x3f, 0x40, 0xf, 0x2c, 0x60,
    0x8d, 0x0, 0xf2, 0xc6, 0x0, 0xd9, 0xf, 0x2c,
    0x60, 0x3, 0xf4, 0xf2, 0xc6, 0x0, 0x8, 0xef,
    0x2c, 0x60, 0x0, 0xd, 0xf2, 0xc6, 0x0, 0x0,
    0x3f, 0x20,

    /* U+004F "O" */
    0x0, 0x19, 0xef, 0xc6, 0x0, 0x1, 0xdd, 0x54,
    0x7f, 0x90, 0x9, 0xd0, 0x0, 0x3, 0xf4, 0xf,
    0x50, 0x0, 0x0, 0xaa, 0x3f, 0x10, 0x0, 0x0,
    0x6d, 0x4f, 0x0, 0x0, 0x0, 0x5f, 0x3f, 0x10,
    0x0, 0x0, 0x7d, 0xf, 0x50, 0x0, 0x0, 0xba,
    0x9, 0xd0, 0x0, 0x4, 0xf4, 0x1, 0xdd, 0x64,
    0x7f, 0x90, 0x0, 0x19, 0xef, 0xd6, 0x0,

    /* U+0050 "P" */
    0xcf, 0xff, 0xc5, 0xc, 0x82, 0x38, 0xf5, 0xc7,
    0x0, 0x9, 0xcc, 0x70, 0x0, 0x6e, 0xc7, 0x0,
    0x8, 0xdc, 0x70, 0x5, 0xf7, 0xcf, 0xff, 0xf8,
    0xc, 0x82, 0x20, 0x0, 0xc7, 0x0, 0x0, 0xc,
    0x70, 0x0, 0x0, 0xc7, 0x0, 0x0, 0x0,

    /* U+0051 "Q" */
    0x0, 0x19, 0xef, 0xc6, 0x0, 0x0, 0x1d, 0xd5,
    0x47, 0xf9, 0x0, 0x9, 0xd0, 0x0, 0x3, 0xf4,
    0x0, 0xf5, 0x0, 0x0, 0xa, 0xa0, 0x3f, 0x10,
    0x0, 0x0, 0x6d, 0x4, 0xf0, 0x0, 0x0, 0x5,
    0xf0, 0x3f, 0x10, 0x0, 0x0, 0x7d, 0x0, 0xf5,
    0x0, 0x0, 0xb, 0xa0, 0x9, 0xd0, 0x0, 0x4,
    0xf4, 0x0, 0x1d, 0xd6, 0x47, 0xf8, 0x0, 0x0,
    0x19, 0xef, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xdc, 0x10, 0x0, 0x0, 0x0, 0x0, 0xae, 0x30,

    /* U+0052 "R" */
    0xcf, 0xff, 0xd6, 0x0, 0xc8, 0x23, 0x7f, 0x70,
    0xc7, 0x0, 0x7, 0xe0, 0xc7, 0x0, 0x6, 0xe0,
    0xc7, 0x0, 0x4e, 0x90, 0xcf, 0xff, 0xf9, 0x0,
    0xc8, 0x26, 0xf2, 0x0, 0xc7, 0x0, 0xac, 0x0,
    0xc7, 0x0, 0x1f, 0x60, 0xc7, 0x0, 0x7, 0xf1,
    0xc7, 0x0, 0x0, 0xda,

    /* U+0053 "S" */
    0x2, 0xaf, 0xfb, 0x30, 0xe, 0xc4, 0x4a, 0xf2,
    0x4f, 0x10, 0x0, 0x92, 0x3f, 0x30, 0x0, 0x0,
    0xb, 0xf8, 0x30, 0x0, 0x0, 0x5b, 0xfd, 0x50,
    0x0, 0x0, 0x19, 0xf4, 0x1, 0x0, 0x0, 0xc9,
    0x9b, 0x0, 0x0, 0xc9, 0x2e, 0xb5, 0x49, 0xf3,
    0x2, 0xae, 0xfc, 0x40,

    /* U+0054 "T" */
    0xdf, 0xff, 0xff, 0xfc, 0x23, 0x3b, 0xa3, 0x32,
    0x0, 0xa, 0x90, 0x0, 0x0, 0xa, 0x90, 0x0,
    0x0, 0xa, 0x90, 0x0, 0x0, 0xa, 0x90, 0x0,
    0x0, 0xa, 0x90, 0x0, 0x0, 0xa, 0x90, 0x0,
    0x0, 0xa, 0x90, 0x0, 0x0, 0xa, 0x90, 0x0,
    0x0, 0xa, 0x90, 0x0,

    /* U+0055 "U" */
    0xe5, 0x0, 0x0, 0x2f, 0x1e, 0x50, 0x0, 0x2,
    0xf1, 0xe5, 0x0, 0x0, 0x2f, 0x1e, 0x50, 0x0,
    0x2, 0xf1, 0xe5, 0x0, 0x0, 0x2f, 0x1e, 0x50,
    0x0, 0x2, 0xf1, 0xd6, 0x0, 0x0, 0x2f, 0x1c,
    0x80, 0x0, 0x4, 0xf0, 0x8e, 0x0, 0x0, 0xab,
    0x1, 0xec, 0x54, 0xaf, 0x30, 0x2, 0xbf, 0xfc,
    0x30, 0x0,

    /* U+0056 "V" */
    0xba, 0x0, 0x0, 0x5, 0xe0, 0x5f, 0x0, 0x0,
    0xa, 0x90, 0xf, 0x50, 0x0, 0xf, 0x30, 0x9,
    0xb0, 0x0, 0x5d, 0x0, 0x3, 0xf1, 0x0, 0xa8,
    0x0, 0x0, 0xd6, 0x0, 0xf2, 0x0, 0x0, 0x7c,
    0x5, 0xc0, 0x0, 0x0, 0x2f, 0x2a, 0x70, 0x0,
    0x0, 0xc, 0x8f, 0x10, 0x0, 0x0, 0x6, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0xf6, 0x0, 0x0,

    /* U+0057 "W" */
    0xb9, 0x0, 0x0, 0xd7, 0x0, 0x0, 0xe3, 0x6d,
    0x0, 0x1, 0xfc, 0x0, 0x3, 0xf0, 0x2f, 0x20,
    0x5, 0xef, 0x10, 0x7, 0xa0, 0xd, 0x70, 0xa,
    0x8e, 0x50, 0xb, 0x60, 0x8, 0xb0, 0xe, 0x39,
    0xa0, 0xf, 0x10, 0x3, 0xf0, 0x2e, 0x5, 0xe0,
    0x4d, 0x0, 0x0, 0xe4, 0x6a, 0x0, 0xf3, 0x88,
    0x0, 0x0, 0xa9, 0xb5, 0x0, 0xb8, 0xc4, 0x0,
    0x0, 0x5e, 0xe1, 0x0, 0x6d, 0xf0, 0x0, 0x0,
    0xf, 0xc0, 0x0, 0x2f, 0xb0, 0x0, 0x0, 0xb,
    0x80, 0x0, 0xd, 0x60, 0x0,

    /* U+0058 "X" */
    0x6f, 0x20, 0x0, 0xc, 0x90, 0xb, 0xc0, 0x0,
    0x7e, 0x0, 0x1, 0xf6, 0x2, 0xf4, 0x0, 0x0,
    0x6f, 0x2c, 0x90, 0x0, 0x0, 0xb, 0xee, 0x0,
    0x0, 0x0, 0x4, 0xf7, 0x0, 0x0, 0x0, 0xc,
    0xde, 0x10, 0x0, 0x0, 0x7d, 0xc, 0xb0, 0x0,
    0x2, 0xf3, 0x2, 0xf5, 0x0, 0xc, 0x90, 0x0,
    0x7e, 0x10, 0x7e, 0x0, 0x0, 0xc, 0xb0,

    /* U+0059 "Y" */
    0x9d, 0x0, 0x0, 0x2f, 0x41, 0xf7, 0x0, 0xa,
    0xb0, 0x7, 0xf1, 0x3, 0xf2, 0x0, 0xd, 0x90,
    0xb9, 0x0, 0x0, 0x4f, 0x7f, 0x10, 0x0, 0x0,
    0xbf, 0x70, 0x0, 0x0, 0x4, 0xf0, 0x0, 0x0,
    0x0, 0x3f, 0x0, 0x0, 0x0, 0x3, 0xf0, 0x0,
    0x0, 0x0, 0x3f, 0x0, 0x0, 0x0, 0x3, 0xf0,
    0x0, 0x0,

    /* U+005A "Z" */
    0x3f, 0xff, 0xff, 0xf8, 0x3, 0x33, 0x38, 0xf1,
    0x0, 0x0, 0xe, 0x70, 0x0, 0x0, 0x8d, 0x0,
    0x0, 0x2, 0xf4, 0x0, 0x0, 0xb, 0xa0, 0x0,
    0x0, 0x5f, 0x10, 0x0, 0x0, 0xe7, 0x0, 0x0,
    0x8, 0xd0, 0x0, 0x0, 0x2f, 0x73, 0x33, 0x32,
    0x9f, 0xff, 0xff, 0xfc,

    /* U+005B "[" */
    0xcf, 0xf8, 0xc8, 0x21, 0xc6, 0x0, 0xc6, 0x0,
    0xc6, 0x0, 0xc6, 0x0, 0xc6, 0x0, 0xc6, 0x0,
    0xc6, 0x0, 0xc6, 0x0, 0xc6, 0x0, 0xc6, 0x0,
    0xc6, 0x0, 0xcf, 0xf8, 0x22, 0x21,

    /* U+005C "\\" */
    0xa9, 0x0, 0x0, 0x4e, 0x0, 0x0, 0xe, 0x40,
    0x0, 0x9, 0xa0, 0x0, 0x3, 0xf0, 0x0, 0x0,
    0xd5, 0x0, 0x0, 0x8a, 0x0, 0x0, 0x2f, 0x0,
    0x0, 0xd, 0x60, 0x0, 0x7, 0xb0, 0x0, 0x2,
    0xf1,

    /* U+005D "]" */
    0xbf, 0xf9, 0x12, 0xa9, 0x0, 0x99, 0x0, 0x99,
    0x0, 0x99, 0x0, 0x99, 0x0, 0x99, 0x0, 0x99,
    0x0, 0x99, 0x0, 0x99, 0x0, 0x99, 0x0, 0x99,
    0x0, 0x99, 0xbf, 0xf9, 0x12, 0x21,

    /* U+005E "^" */
    0x0, 0x27, 0x0, 0x0, 0xa, 0xf4, 0x0, 0x1,
    0xfa, 0xb0, 0x0, 0x8b, 0x1f, 0x10, 0xe, 0x40,
    0xa8, 0x5, 0xd0, 0x3, 0xe0,

    /* U+005F "_" */
    0xff, 0xff, 0xfc, 0x22, 0x22, 0x21,

    /* U+0060 "`" */
    0x7, 0x20, 0x8, 0xa0, 0x0, 0xe2,

    /* U+0061 "a" */
    0x1, 0xaf, 0xe7, 0x0, 0xba, 0x35, 0xf4, 0x1,
    0x0, 0x9, 0x90, 0x2a, 0xed, 0xda, 0xe, 0x92,
    0x3a, 0xa3, 0xf0, 0x0, 0x9a, 0x1f, 0x50, 0x4f,
    0xa0, 0x5d, 0xec, 0x8a,

    /* U+0062 "b" */
    0xe4, 0x0, 0x0, 0xe, 0x40, 0x0, 0x0, 0xe4,
    0x0, 0x0, 0xe, 0x5b, 0xfd, 0x40, 0xee, 0x84,
    0x9f, 0x2e, 0x90, 0x0, 0xb9, 0xe5, 0x0, 0x7,
    0xce, 0x40, 0x0, 0x7c, 0xe9, 0x0, 0xb, 0x9e,
    0xe7, 0x38, 0xf2, 0xe4, 0xbf, 0xd4, 0x0,

    /* U+0063 "c" */
    0x0, 0x8e, 0xfa, 0x0, 0x9e, 0x54, 0xd7, 0x1f,
    0x40, 0x0, 0x4, 0xf0, 0x0, 0x0, 0x4f, 0x0,
    0x0, 0x1, 0xf4, 0x0, 0x0, 0x9, 0xe5, 0x4d,
    0x70, 0x8, 0xef, 0xa0,

    /* U+0064 "d" */
    0x0, 0x0, 0x0, 0xc6, 0x0, 0x0, 0x0, 0xc6,
    0x0, 0x0, 0x0, 0xc6, 0x0, 0x9f, 0xe6, 0xc6,
    0xa, 0xd5, 0x5d, 0xf6, 0x1f, 0x30, 0x2, 0xf6,
    0x4f, 0x0, 0x0, 0xd6, 0x4f, 0x0, 0x0, 0xd6,
    0x1f, 0x20, 0x1, 0xf6, 0xa, 0xc2, 0x2b, 0xf6,
    0x0, 0x9f, 0xe7, 0xa6,

    /* U+0065 "e" */
    0x0, 0x8e, 0xfa, 0x10, 0x9, 0xd4, 0x4c, 0xb0,
    0x1f, 0x20, 0x3, 0xf1, 0x4f, 0xee, 0xee, 0xf2,
    0x4f, 0x21, 0x11, 0x10, 0x1f, 0x40, 0x0, 0x0,
    0x9, 0xe5, 0x3a, 0xb0, 0x0, 0x8e, 0xfb, 0x20,

    /* U+0066 "f" */
    0x0, 0x7e, 0xe1, 0x2, 0xf5, 0x40, 0x6, 0xc0,
    0x0, 0x7, 0xb0, 0x0, 0xbf, 0xff, 0xa0, 0x18,
    0xc1, 0x0, 0x7, 0xb0, 0x0, 0x7, 0xb0, 0x0,
    0x7, 0xb0, 0x0, 0x7, 0xb0, 0x0, 0x7, 0xb0,
    0x0, 0x7, 0xb0, 0x0,

    /* U+0067 "g" */
    0x0, 0x9f, 0xe6, 0xa6, 0xa, 0xd5, 0x5d, 0xe6,
    0x1f, 0x30, 0x1, 0xf6, 0x4f, 0x0, 0x0, 0xd6,
    0x4f, 0x0, 0x0, 0xd6, 0x1f, 0x30, 0x1, 0xf6,
    0xa, 0xd5, 0x4d, 0xf6, 0x0, 0x9f, 0xe6, 0xc6,
    0x0, 0x0, 0x0, 0xf3, 0xc, 0xa4, 0x4b, 0xd0,
    0x2, 0xbf, 0xea, 0x10,

    /* U+0068 "h" */
    0xe4, 0x0, 0x0, 0xe, 0x40, 0x0, 0x0, 0xe4,
    0x0, 0x0, 0xe, 0x6b, 0xfc, 0x20, 0xee, 0x74,
    0xcc, 0xe, 0x70, 0x3, 0xf1, 0xe4, 0x0, 0xf,
    0x2e, 0x40, 0x0, 0xf2, 0xe4, 0x0, 0xf, 0x2e,
    0x40, 0x0, 0xf2, 0xe4, 0x0, 0xf, 0x20,

    /* U+0069 "i" */
    0xe, 0x40, 0xa2, 0x0, 0x0, 0xe4, 0xe, 0x40,
    0xe4, 0xe, 0x40, 0xe4, 0xe, 0x40, 0xe4, 0xe,
    0x40,

    /* U+006A "j" */
    0x0, 0xe, 0x40, 0x0, 0xa2, 0x0, 0x0, 0x0,
    0x0, 0xe4, 0x0, 0xe, 0x40, 0x0, 0xe4, 0x0,
    0xe, 0x40, 0x0, 0xe4, 0x0, 0xe, 0x40, 0x0,
    0xe4, 0x0, 0xe, 0x40, 0x0, 0xf3, 0x15, 0x7f,
    0x5, 0xee, 0x50,

    /* U+006B "k" */
    0xe4, 0x0, 0x0, 0xe, 0x40, 0x0, 0x0, 0xe4,
    0x0, 0x0, 0xe, 0x40, 0xc, 0xa0, 0xe4, 0x9,
    0xc0, 0xe, 0x46, 0xe1, 0x0, 0xe7, 0xf8, 0x0,
    0xe, 0xfa, 0xf2, 0x0, 0xe9, 0xb, 0xc0, 0xe,
    0x40, 0x2f, 0x60, 0xe4, 0x0, 0x7f, 0x10,

    /* U+006C "l" */
    0xe4, 0xe4, 0xe4, 0xe4, 0xe4, 0xe4, 0xe4, 0xe4,
    0xe4, 0xe4, 0xe4,

    /* U+006D "m" */
    0xe6, 0xdf, 0xa1, 0xaf, 0xc1, 0xed, 0x33, 0xfd,
    0x61, 0xba, 0xe6, 0x0, 0xac, 0x0, 0x4e, 0xe4,
    0x0, 0x8a, 0x0, 0x3f, 0xe4, 0x0, 0x8a, 0x0,
    0x3f, 0xe4, 0x0, 0x8a, 0x0, 0x3f, 0xe4, 0x0,
    0x8a, 0x0, 0x3f, 0xe4, 0x0, 0x8a, 0x0, 0x3f,

    /* U+006E "n" */
    0xe5, 0xcf, 0xc2, 0xe, 0xe4, 0x1a, 0xc0, 0xe6,
    0x0, 0x2f, 0x1e, 0x40, 0x0, 0xf2, 0xe4, 0x0,
    0xf, 0x2e, 0x40, 0x0, 0xf2, 0xe4, 0x0, 0xf,
    0x2e, 0x40, 0x0, 0xf2,

    /* U+006F "o" */
    0x0, 0x7e, 0xfa, 0x10, 0x8, 0xe5, 0x4b, 0xc0,
    0x1f, 0x30, 0x0, 0xe5, 0x4f, 0x0, 0x0, 0xb8,
    0x4f, 0x0, 0x0, 0xb8, 0x1f, 0x30, 0x0, 0xe5,
    0x9, 0xe5, 0x4b, 0xc0, 0x0, 0x7e, 0xfa, 0x10,

    /* U+0070 "p" */
    0xe4, 0xcf, 0xd4, 0xe, 0xe4, 0x5, 0xf2, 0xe8,
    0x0, 0xa, 0x9e, 0x40, 0x0, 0x7c, 0xe5, 0x0,
    0x7, 0xce, 0x90, 0x0, 0xb9, 0xee, 0x73, 0x8f,
    0x2e, 0x5b, 0xfd, 0x40, 0xe4, 0x0, 0x0, 0xe,
    0x40, 0x0, 0x0, 0xe4, 0x0, 0x0, 0x0,

    /* U+0071 "q" */
    0x0, 0x9f, 0xe6, 0xa6, 0xa, 0xd5, 0x4c, 0xe6,
    0x1f, 0x30, 0x1, 0xf6, 0x4f, 0x0, 0x0, 0xd6,
    0x4f, 0x0, 0x0, 0xd6, 0x1f, 0x30, 0x1, 0xf6,
    0xa, 0xd5, 0x4c, 0xf6, 0x0, 0x9f, 0xe6, 0xc6,
    0x0, 0x0, 0x0, 0xc6, 0x0, 0x0, 0x0, 0xc6,
    0x0, 0x0, 0x0, 0xc6,

    /* U+0072 "r" */
    0xe6, 0xdf, 0x1e, 0xe3, 0x10, 0xe7, 0x0, 0xe,
    0x40, 0x0, 0xe4, 0x0, 0xe, 0x40, 0x0, 0xe4,
    0x0, 0xe, 0x40, 0x0,

    /* U+0073 "s" */
    0x5, 0xdf, 0xb2, 0x2f, 0x53, 0xaa, 0x4f, 0x10,
    0x0, 0xb, 0xe9, 0x40, 0x0, 0x38, 0xf8, 0x2,
    0x0, 0x5f, 0x6e, 0x53, 0xac, 0x7, 0xef, 0xb2,

    /* U+0074 "t" */
    0xa, 0x80, 0x0, 0xa, 0x80, 0x0, 0xdf, 0xff,
    0xa0, 0x1a, 0x91, 0x10, 0xa, 0x80, 0x0, 0xa,
    0x80, 0x0, 0xa, 0x80, 0x0, 0x9, 0x90, 0x0,
    0x7, 0xe4, 0x40, 0x0, 0xbf, 0xc0,

    /* U+0075 "u" */
    0xf, 0x20, 0x2, 0xf0, 0xf, 0x20, 0x2, 0xf0,
    0xf, 0x20, 0x2, 0xf0, 0xf, 0x20, 0x2, 0xf0,
    0xf, 0x20, 0x2, 0xf0, 0xe, 0x40, 0x4, 0xf0,
    0xa, 0xb1, 0x3d, 0xf0, 0x1, 0xbf, 0xd4, 0xf0,

    /* U+0076 "v" */
    0xaa, 0x0, 0x5, 0xd0, 0x4f, 0x10, 0xa, 0x80,
    0xe, 0x60, 0xf, 0x20, 0x8, 0xb0, 0x5c, 0x0,
    0x2, 0xf1, 0xa6, 0x0, 0x0, 0xc8, 0xf1, 0x0,
    0x0, 0x6f, 0xa0, 0x0, 0x0, 0xf, 0x50, 0x0,

    /* U+0077 "w" */
    0xb8, 0x0, 0x1f, 0x20, 0x8, 0x95, 0xd0, 0x6,
    0xf7, 0x0, 0xc4, 0xf, 0x20, 0xac, 0xc0, 0x1e,
    0x0, 0xb7, 0xe, 0x2f, 0x16, 0xa0, 0x5, 0xd4,
    0xb0, 0xc6, 0xa5, 0x0, 0xf, 0xa6, 0x6, 0xbe,
    0x0, 0x0, 0xbf, 0x10, 0x1f, 0xa0, 0x0, 0x5,
    0xc0, 0x0, 0xc5, 0x0,

    /* U+0078 "x" */
    0x8e, 0x10, 0xd, 0x70, 0xd9, 0x8, 0xc0, 0x3,
    0xf7, 0xf2, 0x0, 0x8, 0xf7, 0x0, 0x0, 0x8f,
    0x80, 0x0, 0x3f, 0x7f, 0x20, 0xd, 0x80, 0x9c,
    0x8, 0xd0, 0x1, 0xe7,

    /* U+0079 "y" */
    0xba, 0x0, 0x4, 0xe0, 0x5f, 0x0, 0xa, 0x80,
    0xe, 0x60, 0xf, 0x20, 0x8, 0xc0, 0x5c, 0x0,
    0x1, 0xf2, 0xb6, 0x0, 0x0, 0xba, 0xf0, 0x0,
    0x0, 0x4f, 0xa0, 0x0, 0x0, 0xf, 0x40, 0x0,
    0x0, 0x4d, 0x0, 0x0, 0x34, 0xd6, 0x0, 0x0,
    0xbf, 0xa0, 0x0, 0x0,

    /* U+007A "z" */
    0x4f, 0xff, 0xff, 0x0, 0x11, 0x1e, 0x70, 0x0,
    0x8, 0xd0, 0x0, 0x2, 0xf3, 0x0, 0x0, 0xc9,
    0x0, 0x0, 0x6e, 0x10, 0x0, 0x1e, 0x71, 0x11,
    0x8, 0xff, 0xff, 0xf2,

    /* U+007B "{" */
    0x0, 0x3c, 0xd0, 0xb, 0xb2, 0x0, 0xe5, 0x0,
    0xf, 0x40, 0x0, 0xf4, 0x0, 0xf, 0x40, 0x6,
    0xf1, 0x7, 0xf6, 0x0, 0x29, 0xf1, 0x0, 0xf,
    0x40, 0x0, 0xf4, 0x0, 0xf, 0x40, 0x0, 0xd8,
    0x0, 0x6, 0xfd, 0x0, 0x0, 0x20,

    /* U+007C "|" */
    0x4d, 0x4d, 0x4d, 0x4d, 0x4d, 0x4d, 0x4d, 0x4d,
    0x4d, 0x4d, 0x4d, 0x4d, 0x4d, 0x4d,

    /* U+007D "}" */
    0xad, 0x40, 0x2, 0x9e, 0x0, 0x2, 0xf1, 0x0,
    0x2f, 0x20, 0x1, 0xf2, 0x0, 0x1f, 0x20, 0x0,
    0xd9, 0x10, 0x4, 0xf9, 0x0, 0xdb, 0x30, 0x1f,
    0x20, 0x2, 0xf2, 0x0, 0x2f, 0x10, 0x6, 0xf0,
    0xb, 0xf8, 0x0, 0x10, 0x0, 0x0,

    /* U+007E "~" */
    0x4, 0xea, 0x0, 0xf1, 0xe, 0x6b, 0x83, 0xe0,
    0x1f, 0x0, 0xdf, 0x60, 0x2, 0x0, 0x1, 0x0,

    /* U+63A5 "接" */
    0x0, 0x0, 0x0, 0x0, 0x10, 0x0, 0x0, 0x0,
    0xc4, 0x0, 0x1, 0xf2, 0x0, 0x0, 0x0, 0xc4,
    0x2, 0x22, 0xe8, 0x22, 0x10, 0x0, 0xc4, 0xd,
    0xfe, 0xdd, 0xfd, 0x80, 0x6e, 0xfe, 0x70, 0x88,
    0x0, 0xf1, 0x0, 0x1, 0xc5, 0x13, 0x7c, 0x35,
    0xe3, 0x30, 0x0, 0xc4, 0x3c, 0xcc, 0xcc, 0xcc,
    0xc0, 0x3, 0xdd, 0x50, 0xc, 0x40, 0x0, 0x0,
    0x9c, 0xe4, 0x8f, 0xff, 0xff, 0xff, 0xf0, 0x0,
    0xc4, 0x1, 0xd5, 0x11, 0xd5, 0x10, 0x0, 0xc4,
    0x5, 0xf8, 0x15, 0xd0, 0x0, 0x0, 0xc4, 0x0,
    0x3b, 0xff, 0x50, 0x0, 0x1, 0xd4, 0x1, 0x6c,
    0xeb, 0xf8, 0x10, 0x3f, 0xe1, 0x8f, 0xc6, 0x0,
    0x2a, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+7EDC "络" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3b, 0x0, 0x9, 0x90, 0x0, 0x0, 0x0, 0xb7,
    0x0, 0x2f, 0xed, 0xdd, 0x20, 0x3, 0xe0, 0x86,
    0xba, 0x33, 0x6e, 0x0, 0xb, 0x61, 0xf9, 0xde,
    0x21, 0xd6, 0x0, 0x7f, 0xce, 0x97, 0x27, 0xdd,
    0xa0, 0x0, 0x47, 0x8f, 0x10, 0x5, 0xff, 0x70,
    0x0, 0x0, 0xd7, 0x5, 0xce, 0x53, 0xce, 0x91,
    0x8, 0xd3, 0x5c, 0x71, 0x0, 0x4, 0x90, 0x4f,
    0xfd, 0x80, 0xef, 0xff, 0xfe, 0x0, 0x3, 0x0,
    0x0, 0xe2, 0x0, 0x2e, 0x0, 0x3, 0x69, 0xc4,
    0xe2, 0x0, 0x2e, 0x0, 0x7c, 0x96, 0x20, 0xef,
    0xff, 0xfe, 0x0, 0x0, 0x0, 0x0, 0xe2, 0x0,
    0x2e, 0x0,

    /* U+7F51 "网" */
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xf3, 0xc6, 0x11,
    0x11, 0x11, 0x11, 0xe3, 0xc5, 0x60, 0x3b, 0x0,
    0x56, 0xe3, 0xc5, 0xc5, 0x97, 0xc1, 0xb4, 0xe3,
    0xc5, 0x4d, 0xe2, 0x9a, 0xe0, 0xe3, 0xc5, 0xc,
    0xc0, 0x1f, 0x90, 0xe3, 0xc5, 0xc, 0xd0, 0xe,
    0x90, 0xe3, 0xc5, 0x4e, 0xd5, 0x7c, 0xf1, 0xe3,
    0xc5, 0xd6, 0x46, 0xe2, 0x98, 0xe3, 0xc9, 0xc0,
    0xb, 0x80, 0x25, 0xe3, 0xc5, 0x0, 0x3, 0x0,
    0x12, 0xf3, 0xc5, 0x0, 0x0, 0x2, 0xff, 0xc0,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+8FDE "连" */
    0x0, 0x30, 0x0, 0x4, 0xb0, 0x0, 0x0, 0x3,
    0xf3, 0x5, 0x5b, 0xb5, 0x55, 0x51, 0x0, 0x6e,
    0x3b, 0xcf, 0xbb, 0xbb, 0xb3, 0x0, 0x7, 0x20,
    0x8a, 0x16, 0x0, 0x0, 0x0, 0x0, 0x1, 0xf3,
    0x3e, 0x0, 0x0, 0xf, 0xfd, 0x7, 0xfb, 0xcf,
    0xbb, 0xa0, 0x2, 0x4e, 0x2, 0x55, 0x7e, 0x55,
    0x40, 0x0, 0x2e, 0x2, 0x22, 0x5e, 0x22, 0x20,
    0x0, 0x2e, 0x2e, 0xee, 0xff, 0xee, 0xe6, 0x0,
    0x3f, 0x0, 0x0, 0x3e, 0x0, 0x0, 0x0, 0xbe,
    0x90, 0x0, 0x3e, 0x0, 0x0, 0x9, 0xc1, 0xcb,
    0x42, 0x11, 0x0, 0x0, 0xd, 0x20, 0x8, 0xdf,
    0xff, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x11, 0x0,

    /* U+F001 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0x7b, 0xfb, 0x0,
    0x0, 0x0, 0x4, 0x9d, 0xff, 0xff, 0xd0, 0x0,
    0x3, 0xaf, 0xff, 0xff, 0xff, 0xfd, 0x0, 0x0,
    0xaf, 0xff, 0xff, 0xff, 0xdf, 0xd0, 0x0, 0xa,
    0xff, 0xff, 0xb6, 0x10, 0xed, 0x0, 0x0, 0xaf,
    0x94, 0x0, 0x0, 0xe, 0xd0, 0x0, 0xa, 0xf1,
    0x0, 0x0, 0x0, 0xed, 0x0, 0x0, 0xaf, 0x10,
    0x0, 0x0, 0xe, 0xd0, 0x0, 0xa, 0xf1, 0x0,
    0x0, 0x45, 0xfd, 0x0, 0x0, 0xaf, 0x10, 0x1,
    0xef, 0xff, 0xd0, 0x17, 0x9d, 0xf1, 0x0, 0x5f,
    0xff, 0xfc, 0xe, 0xff, 0xff, 0x10, 0x0, 0xaf,
    0xfd, 0x31, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x1,
    0x0, 0x3, 0xbd, 0xa3, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F008 "" */
    0x50, 0x18, 0x88, 0x88, 0x88, 0x84, 0x5, 0xfa,
    0xbf, 0xdd, 0xdd, 0xdd, 0xfd, 0xaf, 0xe4, 0x7f,
    0x10, 0x0, 0x0, 0xca, 0x4e, 0xe0, 0x4f, 0x10,
    0x0, 0x0, 0xc8, 0xe, 0xfe, 0xef, 0x10, 0x0,
    0x0, 0xcf, 0xef, 0xe0, 0x3f, 0xee, 0xee, 0xee,
    0xf8, 0xe, 0xf6, 0x8f, 0x76, 0x66, 0x66, 0xeb,
    0x6f, 0xf8, 0xaf, 0x10, 0x0, 0x0, 0xcc, 0x8f,
    0xe0, 0x3f, 0x10, 0x0, 0x0, 0xc8, 0xe, 0xfc,
    0xdf, 0x65, 0x55, 0x55, 0xee, 0xcf, 0xc2, 0x5f,
    0xff, 0xff, 0xff, 0xf9, 0x2c,

    /* U+F00B "" */
    0x57, 0x75, 0x5, 0x77, 0x77, 0x77, 0x75, 0xff,
    0xff, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x2f, 0xff, 0xff, 0xff, 0xff, 0xef, 0xff, 0xe,
    0xff, 0xff, 0xff, 0xfe, 0x1, 0x10, 0x0, 0x11,
    0x11, 0x11, 0x10, 0xef, 0xfe, 0xe, 0xff, 0xff,
    0xff, 0xfe, 0xff, 0xff, 0x2f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x1f, 0xff, 0xff, 0xff, 0xff,
    0x68, 0x87, 0x7, 0x88, 0x88, 0x88, 0x86, 0x68,
    0x87, 0x7, 0x88, 0x88, 0x88, 0x86, 0xff, 0xff,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x2f,
    0xff, 0xff, 0xff, 0xff, 0xdf, 0xfd, 0xd, 0xff,
    0xff, 0xff, 0xfd,

    /* U+F00C "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1d, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xdf, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x1d, 0xff, 0xe2, 0x2d, 0x60, 0x0, 0x1,
    0xdf, 0xfe, 0x20, 0xdf, 0xf6, 0x0, 0x1d, 0xff,
    0xe2, 0x0, 0x8f, 0xff, 0x61, 0xdf, 0xfe, 0x20,
    0x0, 0x8, 0xff, 0xfe, 0xff, 0xe2, 0x0, 0x0,
    0x0, 0x8f, 0xff, 0xfe, 0x20, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xe2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7d, 0x20, 0x0, 0x0, 0x0,

    /* U+F00D "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0x60, 0x0,
    0xb, 0xe2, 0xef, 0xf6, 0x0, 0xbf, 0xf8, 0x4f,
    0xff, 0x6b, 0xff, 0xd1, 0x4, 0xff, 0xff, 0xfd,
    0x10, 0x0, 0x5f, 0xff, 0xe1, 0x0, 0x0, 0xbf,
    0xff, 0xf6, 0x0, 0xb, 0xff, 0xdf, 0xff, 0x60,
    0xbf, 0xfd, 0x14, 0xff, 0xf5, 0xcf, 0xd1, 0x0,
    0x4f, 0xf6, 0x17, 0x10, 0x0, 0x3, 0x60,

    /* U+F011 "" */
    0x0, 0x0, 0x0, 0x11, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0x21, 0xff, 0x12, 0xf7, 0x0, 0x6, 0xff, 0x61,
    0xff, 0x16, 0xff, 0x60, 0x1f, 0xf9, 0x1, 0xff,
    0x10, 0x9f, 0xf1, 0x6f, 0xe0, 0x1, 0xff, 0x10,
    0xe, 0xf6, 0xaf, 0x80, 0x1, 0xff, 0x10, 0x8,
    0xfa, 0xcf, 0x60, 0x1, 0xff, 0x10, 0x6, 0xfc,
    0xaf, 0x80, 0x0, 0xaa, 0x0, 0x8, 0xfb, 0x7f,
    0xd0, 0x0, 0x0, 0x0, 0xd, 0xf7, 0x1f, 0xf8,
    0x0, 0x0, 0x0, 0x8f, 0xf1, 0x7, 0xff, 0x91,
    0x0, 0x2a, 0xff, 0x70, 0x0, 0x9f, 0xff, 0xee,
    0xff, 0xf9, 0x0, 0x0, 0x5, 0xcf, 0xff, 0xfd,
    0x50, 0x0, 0x0, 0x0, 0x2, 0x44, 0x20, 0x0,
    0x0,

    /* U+F013 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0xc0, 0x0, 0x0, 0x3, 0xd6, 0xdf,
    0xff, 0xfd, 0x6d, 0x30, 0xe, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xe0, 0x5f, 0xff, 0xff, 0xaa, 0xff,
    0xff, 0xf5, 0x1a, 0xff, 0xf4, 0x0, 0x4f, 0xff,
    0xa1, 0x3, 0xff, 0xd0, 0x0, 0xd, 0xff, 0x30,
    0x4, 0xff, 0xf0, 0x0, 0xf, 0xff, 0x40, 0x4f,
    0xff, 0xfb, 0x22, 0xbf, 0xff, 0xf4, 0x2f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf2, 0x9, 0xfe, 0xff,
    0xff, 0xff, 0xef, 0x90, 0x0, 0x50, 0x5e, 0xff,
    0xe5, 0x5, 0x0, 0x0, 0x0, 0xc, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0x77, 0x40, 0x0,
    0x0,

    /* U+F015 "" */
    0x0, 0x0, 0x0, 0x3, 0x10, 0x3, 0x41, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xf5, 0xd, 0xf5, 0x0,
    0x0, 0x0, 0x1b, 0xfd, 0xff, 0x8d, 0xf5, 0x0,
    0x0, 0x2, 0xdf, 0xb1, 0x2d, 0xff, 0xf5, 0x0,
    0x0, 0x4f, 0xf8, 0x3e, 0xc2, 0xbf, 0xf5, 0x0,
    0x7, 0xff, 0x55, 0xff, 0xfe, 0x39, 0xfe, 0x40,
    0x9f, 0xe3, 0x8f, 0xff, 0xff, 0xf5, 0x6f, 0xf6,
    0xac, 0x2a, 0xff, 0xff, 0xff, 0xff, 0x73, 0xe6,
    0x0, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xf1, 0x0,
    0x0, 0x6f, 0xff, 0xd7, 0x7f, 0xff, 0xf2, 0x0,
    0x0, 0x6f, 0xff, 0x90, 0xd, 0xff, 0xf2, 0x0,
    0x0, 0x6f, 0xff, 0x90, 0xd, 0xff, 0xf2, 0x0,
    0x0, 0x4f, 0xff, 0x70, 0xb, 0xff, 0xe1, 0x0,

    /* U+F019 "" */
    0x0, 0x0, 0x0, 0x33, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0xc,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xf8,
    0x0, 0x0, 0x2e, 0xff, 0xff, 0xff, 0xe2, 0x0,
    0x0, 0x2, 0xef, 0xff, 0xfe, 0x20, 0x0, 0x0,
    0x0, 0x2d, 0xff, 0xe2, 0x0, 0x0, 0x79, 0x99,
    0x82, 0xde, 0x28, 0x99, 0x97, 0xff, 0xff, 0xfb,
    0x22, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0xb3, 0xcf, 0xac, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc,
    0xca,

    /* U+F01C "" */
    0x0, 0x6, 0xbb, 0xbb, 0xbb, 0xba, 0x30, 0x0,
    0x0, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xe1, 0x0,
    0x0, 0xef, 0x30, 0x0, 0x0, 0x6, 0xfb, 0x0,
    0x9, 0xf8, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x50,
    0x4f, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xe1,
    0xdf, 0x84, 0x42, 0x0, 0x0, 0x34, 0x4b, 0xf9,
    0xff, 0xff, 0xfd, 0x0, 0x1, 0xff, 0xff, 0xfb,
    0xff, 0xff, 0xff, 0x98, 0x8b, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,

    /* U+F021 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x33, 0x0,
    0x1, 0x8d, 0xff, 0xc6, 0x0, 0xef, 0x0, 0x4e,
    0xff, 0xff, 0xff, 0xe4, 0xdf, 0x4, 0xff, 0xb3,
    0x0, 0x4c, 0xff, 0xff, 0xe, 0xf9, 0x0, 0x0,
    0x0, 0x8f, 0xff, 0x6f, 0xc0, 0x0, 0x1, 0xff,
    0xff, 0xff, 0x8e, 0x50, 0x0, 0x1, 0xde, 0xee,
    0xed, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x22, 0x22, 0x22, 0x0, 0x0, 0x0, 0x21, 0xff,
    0xff, 0xff, 0x10, 0x0, 0x8, 0xf8, 0xff, 0xfb,
    0xbc, 0x10, 0x0, 0x1e, 0xf4, 0xff, 0xfc, 0x10,
    0x0, 0x1, 0xdf, 0xc0, 0xfe, 0xef, 0xe8, 0x44,
    0x8e, 0xfe, 0x10, 0xfe, 0x1a, 0xff, 0xff, 0xff,
    0xc1, 0x0, 0xfd, 0x0, 0x28, 0xbb, 0x94, 0x0,
    0x0,

    /* U+F026 "" */
    0x0, 0x0, 0x2, 0x70, 0x0, 0x2, 0xef, 0x0,
    0x2, 0xef, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x34, 0x47, 0xff, 0xf0,
    0x0, 0x5, 0xff, 0x0, 0x0, 0x5, 0xc0, 0x0,
    0x0, 0x0,

    /* U+F027 "" */
    0x0, 0x0, 0x2, 0x70, 0x0, 0x0, 0x0, 0x2,
    0xef, 0x0, 0x0, 0x0, 0x2, 0xef, 0xf0, 0x0,
    0xd, 0xff, 0xff, 0xff, 0x2, 0x20, 0xff, 0xff,
    0xff, 0xf0, 0x8e, 0x1f, 0xff, 0xff, 0xff, 0x0,
    0xe7, 0xff, 0xff, 0xff, 0xf0, 0x3f, 0x5f, 0xff,
    0xff, 0xff, 0x8, 0x90, 0x34, 0x47, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x5, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F028 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0x70, 0x0,
    0x0, 0x0, 0x2, 0x70, 0x0, 0x5, 0xfa, 0x0,
    0x0, 0x0, 0x2e, 0xf0, 0x0, 0x81, 0x4f, 0x60,
    0x0, 0x2, 0xef, 0xf0, 0x1, 0xdd, 0x7, 0xf0,
    0xdf, 0xff, 0xff, 0xf0, 0x32, 0x1e, 0x80, 0xf6,
    0xff, 0xff, 0xff, 0xf0, 0x8e, 0x27, 0xe0, 0xb9,
    0xff, 0xff, 0xff, 0xf0, 0xe, 0x73, 0xf1, 0x9b,
    0xff, 0xff, 0xff, 0xf0, 0x3f, 0x54, 0xf0, 0x9a,
    0xff, 0xff, 0xff, 0xf0, 0x89, 0xa, 0xc0, 0xd8,
    0x34, 0x47, 0xff, 0xf0, 0x0, 0x7f, 0x43, 0xf3,
    0x0, 0x0, 0x5f, 0xf0, 0x2, 0xf6, 0xc, 0xb0,
    0x0, 0x0, 0x5, 0xc0, 0x0, 0x0, 0xbf, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xe3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x10, 0x0,

    /* U+F03E "" */
    0x37, 0x88, 0x88, 0x88, 0x88, 0x88, 0x73, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xfe, 0x32,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x0, 0x7f,
    0xff, 0xfd, 0xff, 0xff, 0xfd, 0x10, 0xcf, 0xff,
    0xa0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x0,
    0x7, 0xff, 0xff, 0xf3, 0x5f, 0xa0, 0x0, 0x0,
    0xcf, 0xff, 0x30, 0x3, 0x0, 0x0, 0x0, 0xcf,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff,
    0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xff, 0xaf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf9,

    /* U+F043 "" */
    0x0, 0x0, 0x20, 0x0, 0x0, 0x0, 0x1, 0xfa,
    0x0, 0x0, 0x0, 0x6, 0xff, 0x10, 0x0, 0x0,
    0xd, 0xff, 0x70, 0x0, 0x0, 0x6f, 0xff, 0xf1,
    0x0, 0x1, 0xef, 0xff, 0xfa, 0x0, 0xb, 0xff,
    0xff, 0xff, 0x60, 0x5f, 0xff, 0xff, 0xff, 0xe0,
    0xcf, 0xff, 0xff, 0xff, 0xf6, 0xfe, 0xbf, 0xff,
    0xff, 0xf9, 0xfd, 0x4f, 0xff, 0xff, 0xf9, 0xbf,
    0x49, 0xff, 0xff, 0xf5, 0x3f, 0xe5, 0x2e, 0xff,
    0xd0, 0x6, 0xff, 0xff, 0xfd, 0x20, 0x0, 0x28,
    0xba, 0x60, 0x0,

    /* U+F048 "" */
    0x4, 0x30, 0x0, 0x0, 0x31, 0x1f, 0xe0, 0x0,
    0x6, 0xf9, 0x1f, 0xe0, 0x0, 0x7f, 0xfa, 0x1f,
    0xe0, 0x9, 0xff, 0xfa, 0x1f, 0xe0, 0xaf, 0xff,
    0xfa, 0x1f, 0xeb, 0xff, 0xff, 0xfa, 0x1f, 0xff,
    0xff, 0xff, 0xfa, 0x1f, 0xff, 0xff, 0xff, 0xfa,
    0x1f, 0xe6, 0xff, 0xff, 0xfa, 0x1f, 0xe0, 0x5f,
    0xff, 0xfa, 0x1f, 0xe0, 0x4, 0xff, 0xfa, 0x1f,
    0xe0, 0x0, 0x3e, 0xfa, 0xf, 0xd0, 0x0, 0x2,
    0xd7, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F04B "" */
    0x2, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xfb,
    0x20, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0x90,
    0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xe6, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xfc, 0x30, 0x0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0x91, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe6, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0xf, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xb2, 0x0, 0xf,
    0xff, 0xff, 0xfd, 0x40, 0x0, 0x0, 0xff, 0xff,
    0xf7, 0x0, 0x0, 0x0, 0xf, 0xff, 0xa1, 0x0,
    0x0, 0x0, 0x0, 0x6a, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F04C "" */
    0x14, 0x44, 0x20, 0x1, 0x44, 0x42, 0xd, 0xff,
    0xff, 0x10, 0xdf, 0xff, 0xf1, 0xff, 0xff, 0xf3,
    0xf, 0xff, 0xff, 0x3f, 0xff, 0xff, 0x40, 0xff,
    0xff, 0xf4, 0xff, 0xff, 0xf4, 0xf, 0xff, 0xff,
    0x4f, 0xff, 0xff, 0x40, 0xff, 0xff, 0xf4, 0xff,
    0xff, 0xf4, 0xf, 0xff, 0xff, 0x4f, 0xff, 0xff,
    0x40, 0xff, 0xff, 0xf4, 0xff, 0xff, 0xf4, 0xf,
    0xff, 0xff, 0x4f, 0xff, 0xff, 0x40, 0xff, 0xff,
    0xf4, 0xff, 0xff, 0xf4, 0xf, 0xff, 0xff, 0x4f,
    0xff, 0xff, 0x30, 0xff, 0xff, 0xf3, 0x9f, 0xff,
    0xc0, 0x9, 0xff, 0xfc, 0x0,

    /* U+F04D "" */
    0x14, 0x44, 0x44, 0x44, 0x44, 0x42, 0xd, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x3f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x4f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x4f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0x9f, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0x0,

    /* U+F051 "" */
    0x2, 0x10, 0x0, 0x0, 0x42, 0xf, 0xe2, 0x0,
    0x3, 0xfb, 0xf, 0xfe, 0x30, 0x4, 0xfb, 0xf,
    0xff, 0xf4, 0x4, 0xfb, 0xf, 0xff, 0xff, 0x54,
    0xfb, 0xf, 0xff, 0xff, 0xfa, 0xfb, 0xf, 0xff,
    0xff, 0xff, 0xfb, 0xf, 0xff, 0xff, 0xff, 0xfb,
    0xf, 0xff, 0xff, 0xd6, 0xfb, 0xf, 0xff, 0xfd,
    0x14, 0xfb, 0xf, 0xff, 0xc1, 0x4, 0xfb, 0xf,
    0xfb, 0x0, 0x4, 0xfb, 0xc, 0xa0, 0x0, 0x3,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F052 "" */
    0x0, 0x0, 0x0, 0x12, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x2e, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x1, 0xef,
    0xff, 0xff, 0x40, 0x0, 0x0, 0x1d, 0xff, 0xff,
    0xff, 0xf3, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xff,
    0xfe, 0x20, 0xa, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe0, 0xe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2,
    0x3, 0x99, 0x99, 0x99, 0x99, 0x99, 0x50, 0x5,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x70, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf3, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0xb, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xd1,

    /* U+F053 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0x90, 0x0, 0x0, 0x3f, 0xfc, 0x0, 0x0, 0x3f,
    0xfd, 0x10, 0x0, 0x3f, 0xfd, 0x10, 0x0, 0x3f,
    0xfd, 0x10, 0x0, 0x1f, 0xfd, 0x10, 0x0, 0x0,
    0xcf, 0xf4, 0x0, 0x0, 0x0, 0xcf, 0xf4, 0x0,
    0x0, 0x0, 0xcf, 0xf4, 0x0, 0x0, 0x0, 0xcf,
    0xf4, 0x0, 0x0, 0x0, 0xcf, 0xe0, 0x0, 0x0,
    0x0, 0xa4, 0x0,

    /* U+F054 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcd, 0x10, 0x0,
    0x0, 0x1f, 0xfd, 0x10, 0x0, 0x0, 0x3f, 0xfd,
    0x10, 0x0, 0x0, 0x3f, 0xfd, 0x10, 0x0, 0x0,
    0x3f, 0xfd, 0x10, 0x0, 0x0, 0x3f, 0xfd, 0x0,
    0x0, 0x7, 0xff, 0x90, 0x0, 0x7, 0xff, 0x90,
    0x0, 0x7, 0xff, 0x90, 0x0, 0x7, 0xff, 0x90,
    0x0, 0x2, 0xff, 0x90, 0x0, 0x0, 0x7, 0x80,
    0x0, 0x0, 0x0,

    /* U+F067 "" */
    0x0, 0x0, 0x4, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xf7, 0x0, 0x0,
    0x6, 0x99, 0x9a, 0xff, 0xc9, 0x99, 0x80, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x3d, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf2, 0x1, 0x11, 0x3f, 0xf7,
    0x11, 0x10, 0x0, 0x0, 0x3, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xd3, 0x0, 0x0, 0x0,

    /* U+F068 "" */
    0x69, 0x99, 0x99, 0x99, 0x99, 0x98, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf3, 0xdf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F06E "" */
    0x0, 0x0, 0x1, 0x56, 0x64, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xbf, 0xfe, 0xef, 0xf9, 0x10, 0x0,
    0x0, 0x7f, 0xfa, 0x10, 0x3, 0xdf, 0xe4, 0x0,
    0x8, 0xff, 0xa0, 0x9, 0xb4, 0x1e, 0xff, 0x50,
    0x4f, 0xff, 0x20, 0xb, 0xff, 0x26, 0xff, 0xe1,
    0xef, 0xff, 0x9, 0xcf, 0xff, 0x63, 0xff, 0xfa,
    0xbf, 0xff, 0x9, 0xff, 0xff, 0x54, 0xff, 0xf6,
    0x1e, 0xff, 0x51, 0xdf, 0xfb, 0x9, 0xff, 0xb0,
    0x3, 0xef, 0xe2, 0x4, 0x30, 0x5f, 0xfc, 0x10,
    0x0, 0x2c, 0xff, 0x95, 0x6a, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x49, 0xdf, 0xfd, 0x92, 0x0, 0x0,

    /* U+F070 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcd, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xf5, 0x0, 0x14, 0x66, 0x40,
    0x0, 0x0, 0x0, 0x4, 0xef, 0xac, 0xff, 0xef,
    0xff, 0x91, 0x0, 0x0, 0x0, 0x1c, 0xff, 0xa1,
    0x0, 0x4d, 0xfe, 0x30, 0x0, 0x0, 0x0, 0x9f,
    0xf5, 0xab, 0x31, 0xef, 0xf4, 0x0, 0x7, 0xb1,
    0x5, 0xff, 0xff, 0xe1, 0x7f, 0xfe, 0x10, 0xf,
    0xfe, 0x30, 0x2d, 0xff, 0xf5, 0x4f, 0xff, 0x90,
    0xc, 0xff, 0xe0, 0x0, 0xaf, 0xf6, 0x5f, 0xff,
    0x60, 0x2, 0xff, 0xf4, 0x0, 0x6, 0xff, 0xef,
    0xfb, 0x0, 0x0, 0x4f, 0xfd, 0x10, 0x0, 0x3e,
    0xff, 0xc0, 0x0, 0x0, 0x2, 0xdf, 0xe8, 0x54,
    0x1, 0xbf, 0xe3, 0x0, 0x0, 0x0, 0x5, 0xae,
    0xff, 0x60, 0x7, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4e, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xa1,

    /* U+F071 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3e, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xff, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xfc, 0xcf,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xfb, 0x0,
    0xff, 0xd0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xc0,
    0xf, 0xff, 0x70, 0x0, 0x0, 0x4, 0xff, 0xfd,
    0x1, 0xff, 0xff, 0x10, 0x0, 0x0, 0xdf, 0xff,
    0xe0, 0x2f, 0xff, 0xfa, 0x0, 0x0, 0x7f, 0xff,
    0xff, 0x9b, 0xff, 0xff, 0xf3, 0x0, 0x1f, 0xff,
    0xff, 0xb0, 0xe, 0xff, 0xff, 0xc0, 0xa, 0xff,
    0xff, 0xfe, 0x24, 0xff, 0xff, 0xff, 0x60, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x6,
    0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcb, 0x30,

    /* U+F074 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x36, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0x80, 0xdd, 0xdb,
    0x0, 0x0, 0x8d, 0xef, 0xf8, 0xff, 0xff, 0xb0,
    0x7, 0xff, 0xff, 0xfd, 0x55, 0x6f, 0xf4, 0x6f,
    0xf8, 0xaf, 0xe2, 0x0, 0x5, 0x74, 0xff, 0x90,
    0x7e, 0x20, 0x0, 0x0, 0x3f, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xef, 0xc2, 0x50, 0x4a, 0x0,
    0x1, 0x2e, 0xfd, 0x1d, 0xf4, 0x8f, 0xb0, 0xff,
    0xff, 0xe1, 0xb, 0xff, 0xff, 0xfb, 0xff, 0xfe,
    0x20, 0x0, 0xcf, 0xff, 0xfb, 0x12, 0x21, 0x0,
    0x0, 0x2, 0x9f, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5b, 0x0,

    /* U+F077 "" */
    0x0, 0x0, 0x7, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xff, 0xb0, 0x0, 0x0, 0x8, 0xff, 0x95, 0xff,
    0xb0, 0x0, 0x8, 0xff, 0x90, 0x5, 0xff, 0xb0,
    0x7, 0xff, 0x90, 0x0, 0x5, 0xff, 0xb0, 0x9f,
    0x90, 0x0, 0x0, 0x5, 0xfd, 0x0, 0x40, 0x0,
    0x0, 0x0, 0x3, 0x10,

    /* U+F078 "" */
    0x4c, 0x20, 0x0, 0x0, 0x0, 0xb6, 0xb, 0xfe,
    0x20, 0x0, 0x0, 0xcf, 0xf0, 0x2e, 0xfe, 0x20,
    0x0, 0xcf, 0xf4, 0x0, 0x2e, 0xfe, 0x20, 0xcf,
    0xf4, 0x0, 0x0, 0x2e, 0xfe, 0xcf, 0xf4, 0x0,
    0x0, 0x0, 0x2e, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x2e, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x13, 0x0, 0x0, 0x0,

    /* U+F079 "" */
    0x0, 0x8, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xf3, 0x8, 0xbb, 0xbb, 0xbb,
    0x90, 0x0, 0xb, 0xff, 0xff, 0x39, 0xff, 0xff,
    0xff, 0xf1, 0x0, 0x8f, 0xcf, 0xcf, 0xf0, 0x0,
    0x0, 0xa, 0xf1, 0x0, 0x38, 0x2f, 0x94, 0x80,
    0x0, 0x0, 0xa, 0xf1, 0x0, 0x0, 0x2f, 0x90,
    0x0, 0x0, 0x0, 0xa, 0xf1, 0x0, 0x0, 0x2f,
    0x90, 0x0, 0x0, 0x3, 0xa, 0xf1, 0x30, 0x0,
    0x2f, 0x90, 0x0, 0x0, 0x1f, 0xcb, 0xf8, 0xf8,
    0x0, 0x2f, 0xeb, 0xbb, 0xbb, 0x39, 0xff, 0xff,
    0xe2, 0x0, 0x1f, 0xff, 0xff, 0xff, 0xb0, 0x9f,
    0xfd, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xd1, 0x0,

    /* U+F07B "" */
    0x37, 0x88, 0x87, 0x0, 0x0, 0x0, 0x0, 0xef,
    0xff, 0xff, 0xa0, 0x0, 0x0, 0x0, 0xff, 0xff,
    0xff, 0xfd, 0xcc, 0xcc, 0xb6, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xaf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf9,

    /* U+F093 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xdd, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x1d, 0xff, 0xd1, 0x0, 0x0, 0x0, 0x1, 0xdf,
    0xff, 0xfd, 0x10, 0x0, 0x0, 0x1d, 0xff, 0xff,
    0xff, 0xd1, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xff,
    0xf9, 0x0, 0x0, 0x1, 0x1c, 0xff, 0xc1, 0x10,
    0x0, 0x0, 0x0, 0xc, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xff, 0xc0, 0x0, 0x0, 0x79, 0x99,
    0x3b, 0xff, 0xb3, 0x99, 0x97, 0xff, 0xff, 0xb2,
    0x44, 0x2b, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xdd,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0xb3, 0xcf, 0xac, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc,
    0xca,

    /* U+F095 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xc7, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xff, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xff, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xef, 0xf3, 0x0, 0x0, 0x4a, 0x30, 0x2,
    0xdf, 0xf8, 0x0, 0x5, 0xdf, 0xfe, 0x15, 0xef,
    0xfb, 0x0, 0x0, 0xef, 0xff, 0xff, 0xff, 0xfa,
    0x0, 0x0, 0xb, 0xff, 0xff, 0xff, 0xf7, 0x0,
    0x0, 0x0, 0x7f, 0xff, 0xff, 0xa2, 0x0, 0x0,
    0x0, 0x2, 0xba, 0x85, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F0C4 "" */
    0x4, 0x86, 0x0, 0x0, 0x0, 0x10, 0x6, 0xff,
    0xfa, 0x0, 0x2, 0xdf, 0xd1, 0xef, 0x3c, 0xf1,
    0x1, 0xdf, 0xfa, 0xe, 0xe0, 0xaf, 0x21, 0xdf,
    0xfa, 0x0, 0x9f, 0xef, 0xf6, 0xdf, 0xfa, 0x0,
    0x0, 0x8d, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x0,
    0x6, 0xff, 0xfd, 0x0, 0x0, 0x0, 0x48, 0xef,
    0xff, 0xf6, 0x0, 0x0, 0x6f, 0xff, 0xfb, 0xff,
    0xf6, 0x0, 0xe, 0xf3, 0xcf, 0x23, 0xff, 0xf6,
    0x0, 0xee, 0xa, 0xf2, 0x4, 0xff, 0xf6, 0x9,
    0xfe, 0xfc, 0x0, 0x4, 0xff, 0xf1, 0x8, 0xda,
    0x10, 0x0, 0x2, 0x62, 0x0,

    /* U+F0C5 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xff, 0xf9, 0x86, 0x0, 0x0, 0x8, 0xff,
    0xff, 0x98, 0xf6, 0x8, 0xa6, 0x8f, 0xff, 0xf9,
    0x59, 0x90, 0xff, 0xa8, 0xff, 0xff, 0xfc, 0xcc,
    0xf, 0xfa, 0x8f, 0xff, 0xff, 0xff, 0xf1, 0xff,
    0xa8, 0xff, 0xff, 0xff, 0xff, 0x1f, 0xfa, 0x8f,
    0xff, 0xff, 0xff, 0xf1, 0xff, 0xa8, 0xff, 0xff,
    0xff, 0xff, 0x1f, 0xfa, 0x8f, 0xff, 0xff, 0xff,
    0xf1, 0xff, 0xa8, 0xff, 0xff, 0xff, 0xff, 0x1f,
    0xfa, 0x7f, 0xff, 0xff, 0xff, 0xf0, 0xff, 0xe3,
    0x12, 0x22, 0x22, 0x21, 0xf, 0xff, 0xff, 0xff,
    0xf9, 0x0, 0x0, 0xac, 0xcc, 0xcc, 0xcb, 0x50,
    0x0, 0x0,

    /* U+F0C7 "" */
    0x49, 0x99, 0x99, 0x99, 0x95, 0x0, 0xe, 0xff,
    0xff, 0xff, 0xff, 0xf6, 0x0, 0xfd, 0x22, 0x22,
    0x22, 0x4f, 0xf6, 0xf, 0xc0, 0x0, 0x0, 0x1,
    0xff, 0xf3, 0xfc, 0x0, 0x0, 0x0, 0x1f, 0xff,
    0x6f, 0xc0, 0x0, 0x0, 0x2, 0xff, 0xf6, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x6f, 0xff, 0xff,
    0xdc, 0xff, 0xff, 0xf6, 0xff, 0xff, 0xb0, 0x5,
    0xff, 0xff, 0x6f, 0xff, 0xf6, 0x0, 0xf, 0xff,
    0xf6, 0xff, 0xff, 0xc0, 0x6, 0xff, 0xff, 0x6f,
    0xff, 0xff, 0xed, 0xff, 0xff, 0xf6, 0x9f, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0x10,

    /* U+F0C9 "" */
    0xcd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0x2f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf3, 0x12, 0x22, 0x22,
    0x22, 0x22, 0x22, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0xde,
    0xee, 0xee, 0xee, 0xee, 0xee, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xee, 0xee, 0xee, 0xee, 0xee,
    0xe2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F0E0 "" */
    0x37, 0x88, 0x88, 0x88, 0x88, 0x88, 0x73, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0x1c, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xc1, 0xd2, 0x8f, 0xff, 0xff,
    0xff, 0xf8, 0x2d, 0xff, 0x64, 0xef, 0xff, 0xfe,
    0x45, 0xff, 0xff, 0xfa, 0x2b, 0xff, 0xb2, 0xaf,
    0xff, 0xff, 0xff, 0xd3, 0x55, 0x3d, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xbb, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xaf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf9,

    /* U+F0E7 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xff,
    0xf0, 0x0, 0x4, 0xff, 0xff, 0xd0, 0x0, 0x6,
    0xff, 0xff, 0x80, 0x0, 0x8, 0xff, 0xff, 0x30,
    0x0, 0xa, 0xff, 0xff, 0xaa, 0xa6, 0xc, 0xff,
    0xff, 0xff, 0xf8, 0xe, 0xff, 0xff, 0xff, 0xe1,
    0xb, 0xdd, 0xdf, 0xff, 0x60, 0x0, 0x0, 0x4f,
    0xfd, 0x0, 0x0, 0x0, 0x7f, 0xf3, 0x0, 0x0,
    0x0, 0xbf, 0xa0, 0x0, 0x0, 0x0, 0xff, 0x10,
    0x0, 0x0, 0x3, 0xf8, 0x0, 0x0, 0x0, 0x3,
    0xc0, 0x0, 0x0,

    /* U+F0EA "" */
    0x0, 0x0, 0x20, 0x0, 0x0, 0x0, 0x4, 0x55,
    0xef, 0xb5, 0x52, 0x0, 0x0, 0xff, 0xfd, 0x1f,
    0xff, 0xb0, 0x0, 0xf, 0xff, 0xff, 0xff, 0xfc,
    0x0, 0x0, 0xff, 0xff, 0x53, 0x33, 0x20, 0x0,
    0xf, 0xff, 0x97, 0xff, 0xfb, 0x57, 0x0, 0xff,
    0xf8, 0xaf, 0xff, 0xc6, 0xf8, 0xf, 0xff, 0x8a,
    0xff, 0xfc, 0x4a, 0xa1, 0xff, 0xf8, 0xaf, 0xff,
    0xe3, 0x22, 0xf, 0xff, 0x8a, 0xff, 0xff, 0xff,
    0xf4, 0xff, 0xf8, 0xaf, 0xff, 0xff, 0xff, 0x4f,
    0xff, 0x8a, 0xff, 0xff, 0xff, 0xf4, 0x35, 0x52,
    0xaf, 0xff, 0xff, 0xff, 0x40, 0x0, 0xa, 0xff,
    0xff, 0xff, 0xf4, 0x0, 0x0, 0x7f, 0xff, 0xff,
    0xfe, 0x20,

    /* U+F0F3 "" */
    0x0, 0x0, 0x1, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0x0, 0x0, 0x0, 0x0, 0x1, 0x8f,
    0xfa, 0x30, 0x0, 0x0, 0x2, 0xef, 0xff, 0xff,
    0x50, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xff, 0x10,
    0x0, 0x1f, 0xff, 0xff, 0xff, 0xf5, 0x0, 0x3,
    0xff, 0xff, 0xff, 0xff, 0x70, 0x0, 0x5f, 0xff,
    0xff, 0xff, 0xf9, 0x0, 0x8, 0xff, 0xff, 0xff,
    0xff, 0xc0, 0x0, 0xdf, 0xff, 0xff, 0xff, 0xff,
    0x20, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xe,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0x2, 0x22,
    0x22, 0x22, 0x22, 0x21, 0x0, 0x0, 0x8, 0xff,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xa2, 0x0,
    0x0, 0x0,

    /* U+F11C "" */
    0x5b, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xa3,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0xfc, 0xc, 0x30, 0xe1, 0x1d, 0xd, 0x11, 0xfc,
    0xfc, 0xb, 0x30, 0xe0, 0x1d, 0xd, 0x10, 0xfc,
    0xff, 0xfe, 0xff, 0xef, 0xfe, 0xfe, 0xef, 0xfc,
    0xff, 0xf1, 0x5a, 0x8, 0x70, 0xa0, 0x5f, 0xfc,
    0xff, 0xf3, 0x7b, 0x29, 0x92, 0xc2, 0x7f, 0xfc,
    0xff, 0xbf, 0xcb, 0xbb, 0xbb, 0xbf, 0xcb, 0xfc,
    0xfc, 0xb, 0x20, 0x0, 0x0, 0xd, 0x0, 0xfc,
    0xff, 0xcf, 0xcc, 0xcc, 0xcc, 0xcf, 0xcc, 0xfb,
    0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,

    /* U+F124 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xdf, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xef, 0xff, 0xd0,
    0x0, 0x0, 0x0, 0x18, 0xff, 0xff, 0xff, 0x70,
    0x0, 0x0, 0x29, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x0, 0x3b, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0x0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x0,
    0x4, 0x9a, 0xaa, 0xaf, 0xff, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x0, 0xe, 0xff, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xb3, 0x0, 0x0, 0x0,

    /* U+F15B "" */
    0x35, 0x55, 0x55, 0x2, 0x0, 0xf, 0xff, 0xff,
    0xf2, 0xf4, 0x0, 0xff, 0xff, 0xff, 0x2f, 0xf4,
    0xf, 0xff, 0xff, 0xf2, 0xff, 0xf3, 0xff, 0xff,
    0xff, 0x32, 0x22, 0x1f, 0xff, 0xff, 0xff, 0xff,
    0xf7, 0xff, 0xff, 0xff, 0xff, 0xff, 0x8f, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0x8f,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0x8a, 0xaa, 0xaa,
    0xaa, 0xaa, 0x30,

    /* U+F1EB "" */
    0x0, 0x0, 0x0, 0x24, 0x55, 0x31, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xaf, 0xff, 0xff, 0xff, 0xc7,
    0x0, 0x0, 0x2, 0xbf, 0xff, 0xfe, 0xde, 0xff,
    0xff, 0xf6, 0x0, 0x5f, 0xff, 0xb5, 0x10, 0x0,
    0x3, 0x8e, 0xff, 0xb0, 0xdf, 0xd3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xf5, 0x18, 0x0, 0x5,
    0xae, 0xfe, 0xc8, 0x10, 0x4, 0x60, 0x0, 0x2,
    0xdf, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0xc, 0xff, 0x95, 0x34, 0x7d, 0xff, 0x40, 0x0,
    0x0, 0x2, 0xa2, 0x0, 0x0, 0x0, 0x77, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0x96, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xda, 0x0, 0x0, 0x0, 0x0,

    /* U+F240 "" */
    0x5b, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xba,
    0x20, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x90, 0xfc, 0x12, 0x22, 0x22, 0x22, 0x22,
    0x22, 0xf, 0xf7, 0xfc, 0x5f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x2c, 0xfa, 0xfc, 0x5f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x21, 0xfa, 0xfc, 0x5f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x27, 0xfa, 0xfc, 0x26,
    0x66, 0x66, 0x66, 0x66, 0x66, 0x1f, 0xfa, 0xfe,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbf, 0xb1,
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F241 "" */
    0x5b, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xba,
    0x20, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x90, 0xfc, 0x12, 0x22, 0x22, 0x22, 0x21,
    0x0, 0xf, 0xf7, 0xfc, 0x5f, 0xff, 0xff, 0xff,
    0xf8, 0x0, 0xc, 0xfa, 0xfc, 0x5f, 0xff, 0xff,
    0xff, 0xf8, 0x0, 0x1, 0xfa, 0xfc, 0x5f, 0xff,
    0xff, 0xff, 0xf8, 0x0, 0x7, 0xfa, 0xfc, 0x26,
    0x66, 0x66, 0x66, 0x63, 0x0, 0xf, 0xfa, 0xfe,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbf, 0xb1,
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F242 "" */
    0x5b, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xba,
    0x20, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x90, 0xfc, 0x12, 0x22, 0x22, 0x10, 0x0,
    0x0, 0xf, 0xf7, 0xfc, 0x5f, 0xff, 0xff, 0xd0,
    0x0, 0x0, 0xc, 0xfa, 0xfc, 0x5f, 0xff, 0xff,
    0xd0, 0x0, 0x0, 0x1, 0xfa, 0xfc, 0x5f, 0xff,
    0xff, 0xd0, 0x0, 0x0, 0x7, 0xfa, 0xfc, 0x26,
    0x66, 0x66, 0x50, 0x0, 0x0, 0xf, 0xfa, 0xfe,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbf, 0xb1,
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F243 "" */
    0x5b, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xba,
    0x20, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x90, 0xfc, 0x12, 0x22, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xf7, 0xfc, 0x5f, 0xff, 0x30, 0x0,
    0x0, 0x0, 0xc, 0xfa, 0xfc, 0x5f, 0xff, 0x30,
    0x0, 0x0, 0x0, 0x1, 0xfa, 0xfc, 0x5f, 0xff,
    0x30, 0x0, 0x0, 0x0, 0x7, 0xfa, 0xfc, 0x26,
    0x66, 0x10, 0x0, 0x0, 0x0, 0xf, 0xfa, 0xfe,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbf, 0xb1,
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F244 "" */
    0x5b, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xba,
    0x20, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x90, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xf7, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xfa, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xfa, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xfa, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfa, 0xfe,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbf, 0xb1,
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F287 "" */
    0x0, 0x0, 0x0, 0x0, 0x7, 0xb2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xdf, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa9, 0x3d, 0xf5,
    0x0, 0x0, 0x0, 0x4, 0x40, 0x2, 0xe0, 0x0,
    0x10, 0x0, 0x0, 0x0, 0xaf, 0xf8, 0xb, 0x60,
    0x0, 0x0, 0x0, 0x6c, 0x30, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0xaf, 0xf9,
    0x0, 0xc, 0x50, 0x0, 0x0, 0x6d, 0x40, 0x5,
    0x50, 0x0, 0x4, 0xc0, 0x0, 0x0, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc4, 0x3e, 0xe8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2e, 0xef, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F293 "" */
    0x0, 0x0, 0x1, 0x10, 0x0, 0x0, 0x0, 0x7,
    0xef, 0xff, 0xb3, 0x0, 0x0, 0xaf, 0xfd, 0x8f,
    0xff, 0x20, 0x4, 0xff, 0xfd, 0x9, 0xff, 0xb0,
    0xa, 0xfe, 0xfd, 0x12, 0xaf, 0xf0, 0xe, 0xf5,
    0x5d, 0x2c, 0xe, 0xf3, 0xf, 0xff, 0x33, 0x12,
    0x9f, 0xf5, 0xf, 0xff, 0xf3, 0x7, 0xff, 0xf6,
    0xf, 0xff, 0xe2, 0x6, 0xff, 0xf6, 0xf, 0xfe,
    0x24, 0x13, 0x7f, 0xf5, 0xd, 0xf5, 0x7d, 0x2c,
    0xd, 0xf3, 0xa, 0xff, 0xfd, 0x11, 0xbf, 0xf0,
    0x3, 0xff, 0xfe, 0xb, 0xff, 0xa0, 0x0, 0x7f,
    0xfe, 0xbf, 0xfe, 0x10, 0x0, 0x3, 0xac, 0xdc,
    0x81, 0x0,

    /* U+F2ED "" */
    0x0, 0x0, 0x34, 0x43, 0x0, 0x0, 0x5, 0x66,
    0x7f, 0xff, 0xf9, 0x66, 0x50, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x35, 0x66, 0x66, 0x66, 0x66,
    0x66, 0x50, 0x1c, 0xcc, 0xcc, 0xcc, 0xcc, 0xc4,
    0x2, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60, 0x2f,
    0xf3, 0xfb, 0x7f, 0x6d, 0xf6, 0x2, 0xff, 0x2f,
    0xb7, 0xf5, 0xdf, 0x60, 0x2f, 0xf2, 0xfb, 0x7f,
    0x5d, 0xf6, 0x2, 0xff, 0x2f, 0xb7, 0xf5, 0xdf,
    0x60, 0x2f, 0xf2, 0xfb, 0x7f, 0x5d, 0xf6, 0x2,
    0xff, 0x2f, 0xb7, 0xf5, 0xdf, 0x60, 0x2f, 0xf3,
    0xfb, 0x7f, 0x6d, 0xf6, 0x1, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x50, 0x7, 0xbc, 0xcc, 0xcc, 0xcc,
    0x90, 0x0,

    /* U+F304 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xff, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x4, 0x39, 0xff, 0xfe, 0x0, 0x0,
    0x0, 0x4, 0xff, 0x39, 0xff, 0xa0, 0x0, 0x0,
    0x4, 0xff, 0xff, 0x39, 0xb0, 0x0, 0x0, 0x4,
    0xff, 0xff, 0xff, 0x20, 0x0, 0x0, 0x4, 0xff,
    0xff, 0xff, 0xb0, 0x0, 0x0, 0x4, 0xff, 0xff,
    0xff, 0xb0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xff,
    0xb0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0xd, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xa8, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F55A "" */
    0x0, 0x0, 0x17, 0x88, 0x88, 0x88, 0x88, 0x87,
    0x40, 0x0, 0x2, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0x0, 0x3e, 0xff, 0xff, 0xcf, 0xff,
    0xcf, 0xff, 0xf7, 0x3, 0xef, 0xff, 0xf9, 0x8,
    0xf8, 0x9, 0xff, 0xf8, 0x3e, 0xff, 0xff, 0xfe,
    0x20, 0x40, 0x2e, 0xff, 0xf8, 0xdf, 0xff, 0xff,
    0xff, 0xe1, 0x1, 0xef, 0xff, 0xf8, 0x9f, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x8f, 0xff, 0xf8, 0x9,
    0xff, 0xff, 0xf9, 0x2, 0xc2, 0x9, 0xff, 0xf8,
    0x0, 0x9f, 0xff, 0xfe, 0x4e, 0xfe, 0x4e, 0xff,
    0xf8, 0x0, 0x9, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf7, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xc1,

    /* U+F7C2 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xef,
    0xff, 0xff, 0xe2, 0x3, 0xfb, 0xfb, 0xce, 0xbf,
    0xa4, 0xff, 0x1d, 0x3, 0xa1, 0xfa, 0xff, 0xf1,
    0xd0, 0x3a, 0x1f, 0xaf, 0xff, 0xff, 0xff, 0xff,
    0xfa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xaf, 0xff,
    0xff, 0xff, 0xff, 0xfa, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xfa, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xaf, 0xff, 0xff, 0xff,
    0xff, 0xfa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xad,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0x29, 0xaa, 0xaa,
    0xaa, 0xa8, 0x0,

    /* U+F8A2 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xf1, 0x0,
    0x8, 0x20, 0x0, 0x0, 0x1, 0xff, 0x10, 0xb,
    0xf7, 0x0, 0x0, 0x0, 0x2f, 0xf1, 0xc, 0xff,
    0x94, 0x44, 0x44, 0x45, 0xff, 0x1b, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0x8f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0x0, 0x7f, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 60, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 53, .box_w = 3, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 17, .adv_w = 77, .box_w = 5, .box_h = 4, .ofs_x = 0, .ofs_y = 8},
    {.bitmap_index = 27, .adv_w = 144, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 77, .adv_w = 128, .box_w = 8, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 137, .adv_w = 173, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 198, .adv_w = 158, .box_w = 11, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 264, .adv_w = 42, .box_w = 2, .box_h = 4, .ofs_x = 0, .ofs_y = 8},
    {.bitmap_index = 268, .adv_w = 77, .box_w = 5, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 303, .adv_w = 77, .box_w = 4, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 331, .adv_w = 96, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 349, .adv_w = 128, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 381, .adv_w = 54, .box_w = 3, .box_h = 5, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 389, .adv_w = 109, .box_w = 6, .box_h = 2, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 395, .adv_w = 52, .box_w = 3, .box_h = 2, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 398, .adv_w = 87, .box_w = 6, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 431, .adv_w = 128, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 475, .adv_w = 128, .box_w = 5, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 503, .adv_w = 128, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 547, .adv_w = 128, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 591, .adv_w = 128, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 635, .adv_w = 128, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 679, .adv_w = 128, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 723, .adv_w = 128, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 767, .adv_w = 128, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 811, .adv_w = 128, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 855, .adv_w = 57, .box_w = 2, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 863, .adv_w = 59, .box_w = 3, .box_h = 11, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 880, .adv_w = 128, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 912, .adv_w = 128, .box_w = 8, .box_h = 5, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 932, .adv_w = 128, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 964, .adv_w = 97, .box_w = 7, .box_h = 13, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 1010, .adv_w = 218, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 1108, .adv_w = 149, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1163, .adv_w = 146, .box_w = 8, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1207, .adv_w = 146, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1257, .adv_w = 160, .box_w = 9, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1307, .adv_w = 134, .box_w = 7, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1346, .adv_w = 127, .box_w = 7, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1385, .adv_w = 157, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1440, .adv_w = 167, .box_w = 9, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1490, .adv_w = 59, .box_w = 2, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1501, .adv_w = 102, .box_w = 6, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1534, .adv_w = 152, .box_w = 9, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1584, .adv_w = 127, .box_w = 7, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1623, .adv_w = 194, .box_w = 10, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1678, .adv_w = 166, .box_w = 9, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1728, .adv_w = 171, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1783, .adv_w = 133, .box_w = 7, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1822, .adv_w = 171, .box_w = 11, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1894, .adv_w = 144, .box_w = 8, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1938, .adv_w = 128, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1982, .adv_w = 127, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2026, .adv_w = 164, .box_w = 9, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2076, .adv_w = 147, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2131, .adv_w = 216, .box_w = 14, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2208, .adv_w = 147, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2263, .adv_w = 138, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2313, .adv_w = 129, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2357, .adv_w = 77, .box_w = 4, .box_h = 15, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 2387, .adv_w = 87, .box_w = 6, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2420, .adv_w = 77, .box_w = 4, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2450, .adv_w = 106, .box_w = 7, .box_h = 6, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 2471, .adv_w = 93, .box_w = 6, .box_h = 2, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 2477, .adv_w = 68, .box_w = 4, .box_h = 3, .ofs_x = 0, .ofs_y = 9},
    {.bitmap_index = 2483, .adv_w = 122, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2511, .adv_w = 136, .box_w = 7, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2550, .adv_w = 110, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2578, .adv_w = 136, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2622, .adv_w = 123, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2654, .adv_w = 77, .box_w = 6, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2690, .adv_w = 136, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 2734, .adv_w = 130, .box_w = 7, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2773, .adv_w = 54, .box_w = 3, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2790, .adv_w = 54, .box_w = 5, .box_h = 14, .ofs_x = -2, .ofs_y = -3},
    {.bitmap_index = 2825, .adv_w = 117, .box_w = 7, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2864, .adv_w = 54, .box_w = 2, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2875, .adv_w = 191, .box_w = 10, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2915, .adv_w = 130, .box_w = 7, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2943, .adv_w = 132, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2975, .adv_w = 136, .box_w = 7, .box_h = 11, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 3014, .adv_w = 136, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 3058, .adv_w = 84, .box_w = 5, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3078, .adv_w = 102, .box_w = 6, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3102, .adv_w = 82, .box_w = 6, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3132, .adv_w = 130, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3164, .adv_w = 115, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3196, .adv_w = 174, .box_w = 11, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3240, .adv_w = 111, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3268, .adv_w = 116, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 3312, .adv_w = 106, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3340, .adv_w = 83, .box_w = 5, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3378, .adv_w = 41, .box_w = 2, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3392, .adv_w = 83, .box_w = 5, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3430, .adv_w = 128, .box_w = 8, .box_h = 4, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 3446, .adv_w = 224, .box_w = 14, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3551, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3649, .adv_w = 224, .box_w = 12, .box_h = 13, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 3727, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3825, .adv_w = 224, .box_w = 15, .box_h = 15, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 3938, .adv_w = 224, .box_w = 14, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4015, .adv_w = 224, .box_w = 14, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4106, .adv_w = 224, .box_w = 14, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4183, .adv_w = 154, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4238, .adv_w = 224, .box_w = 14, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4343, .adv_w = 224, .box_w = 14, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4448, .adv_w = 252, .box_w = 16, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4552, .adv_w = 224, .box_w = 14, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4657, .adv_w = 252, .box_w = 16, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4745, .adv_w = 224, .box_w = 14, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4850, .adv_w = 112, .box_w = 7, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4892, .adv_w = 168, .box_w = 11, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4958, .adv_w = 252, .box_w = 16, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5070, .adv_w = 224, .box_w = 14, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5147, .adv_w = 154, .box_w = 10, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5222, .adv_w = 196, .box_w = 10, .box_h = 14, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 5292, .adv_w = 196, .box_w = 13, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5390, .adv_w = 196, .box_w = 13, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5475, .adv_w = 196, .box_w = 13, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5560, .adv_w = 196, .box_w = 10, .box_h = 14, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 5630, .adv_w = 196, .box_w = 14, .box_h = 13, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 5721, .adv_w = 140, .box_w = 9, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5780, .adv_w = 140, .box_w = 9, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5839, .adv_w = 196, .box_w = 13, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5924, .adv_w = 196, .box_w = 13, .box_h = 4, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 5950, .adv_w = 252, .box_w = 16, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6038, .adv_w = 280, .box_w = 18, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6173, .adv_w = 252, .box_w = 17, .box_h = 15, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 6301, .adv_w = 224, .box_w = 14, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 6392, .adv_w = 196, .box_w = 13, .box_h = 8, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 6444, .adv_w = 196, .box_w = 13, .box_h = 8, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 6496, .adv_w = 280, .box_w = 18, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6595, .adv_w = 224, .box_w = 14, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6672, .adv_w = 224, .box_w = 14, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6777, .adv_w = 224, .box_w = 15, .box_h = 15, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 6890, .adv_w = 196, .box_w = 13, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 6975, .adv_w = 196, .box_w = 13, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7073, .adv_w = 196, .box_w = 13, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 7158, .adv_w = 196, .box_w = 13, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 7236, .adv_w = 224, .box_w = 14, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7313, .adv_w = 140, .box_w = 10, .box_h = 15, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 7388, .adv_w = 196, .box_w = 13, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7486, .adv_w = 196, .box_w = 13, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7584, .adv_w = 252, .box_w = 16, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7672, .adv_w = 224, .box_w = 16, .box_h = 15, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 7792, .adv_w = 168, .box_w = 11, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7875, .adv_w = 280, .box_w = 18, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 7992, .adv_w = 280, .box_w = 18, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8082, .adv_w = 280, .box_w = 18, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8172, .adv_w = 280, .box_w = 18, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8262, .adv_w = 280, .box_w = 18, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8352, .adv_w = 280, .box_w = 18, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8442, .adv_w = 280, .box_w = 18, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 8550, .adv_w = 196, .box_w = 12, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 8640, .adv_w = 196, .box_w = 13, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 8738, .adv_w = 224, .box_w = 15, .box_h = 15, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 8851, .adv_w = 280, .box_w = 18, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8950, .adv_w = 168, .box_w = 11, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 9033, .adv_w = 225, .box_w = 15, .box_h = 10, .ofs_x = 0, .ofs_y = 0}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_1[] = {
    0x0, 0x1b37, 0x1bac, 0x2c39, 0x8c5c, 0x8c63, 0x8c66, 0x8c67,
    0x8c68, 0x8c6c, 0x8c6e, 0x8c70, 0x8c74, 0x8c77, 0x8c7c, 0x8c81,
    0x8c82, 0x8c83, 0x8c99, 0x8c9e, 0x8ca3, 0x8ca6, 0x8ca7, 0x8ca8,
    0x8cac, 0x8cad, 0x8cae, 0x8caf, 0x8cc2, 0x8cc3, 0x8cc9, 0x8ccb,
    0x8ccc, 0x8ccf, 0x8cd2, 0x8cd3, 0x8cd4, 0x8cd6, 0x8cee, 0x8cf0,
    0x8d1f, 0x8d20, 0x8d22, 0x8d24, 0x8d3b, 0x8d42, 0x8d45, 0x8d4e,
    0x8d77, 0x8d7f, 0x8db6, 0x8e46, 0x8e9b, 0x8e9c, 0x8e9d, 0x8e9e,
    0x8e9f, 0x8ee2, 0x8eee, 0x8f48, 0x8f5f, 0x91b5, 0x941d, 0x94fd
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 25509, .range_length = 38142, .glyph_id_start = 96,
        .unicode_list = unicode_list_1, .glyph_id_ofs_list = NULL, .list_length = 64, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/


/*Map glyph_ids to kern left classes*/
static const uint8_t kern_left_class_mapping[] =
{
    0, 0, 0, 1, 0, 0, 0, 0,
    1, 2, 3, 0, 4, 0, 4, 0,
    5, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 4,
    6, 7, 8, 9, 10, 7, 11, 12,
    13, 14, 14, 15, 16, 17, 14, 14,
    7, 18, 0, 19, 20, 21, 15, 5,
    22, 23, 24, 25, 2, 8, 3, 0,
    0, 0, 26, 27, 28, 29, 30, 31,
    32, 26, 0, 33, 34, 29, 26, 26,
    27, 27, 0, 35, 36, 37, 32, 38,
    38, 39, 38, 40, 2, 0, 3, 4,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0
};

/*Map glyph_ids to kern right classes*/
static const uint8_t kern_right_class_mapping[] =
{
    0, 1, 0, 2, 0, 0, 0, 0,
    2, 0, 3, 0, 4, 5, 4, 5,
    6, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 4, 0, 0,
    7, 8, 6, 9, 8, 9, 9, 9,
    8, 9, 9, 10, 9, 9, 9, 9,
    8, 9, 8, 9, 11, 12, 13, 14,
    15, 16, 17, 18, 0, 14, 3, 0,
    5, 0, 19, 20, 21, 21, 21, 22,
    21, 20, 0, 23, 20, 20, 24, 24,
    21, 0, 21, 24, 25, 26, 27, 28,
    28, 29, 28, 30, 0, 0, 3, 4,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0
};

/*Kern values between classes*/
static const int8_t kern_class_values[] =
{
    0, 0, 0, 0, 0, 0, 3, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 4, 0, 4, 4, 2,
    0, 3, 0, 0, 15, 0, 0, 5,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 7, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -12, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -9, 4, 4, -9, -30, -19, 5, -7,
    0, -26, -1, 4, 0, 0, 0, 0,
    0, 0, -15, 0, -15, -4, 0, -9,
    -12, 0, -9, -9, -11, -11, 0, 0,
    0, -7, -21, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -6, 0, 0,
    -9, -7, 0, 0, 0, -9, 0, -7,
    0, -7, -4, -7, -11, -6, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -9, -26, 0, -13, 7, 0,
    -15, -7, 0, 0, 0, -18, -3, -19,
    -14, 0, -24, 4, 0, 0, -2, 0,
    0, 0, 0, 0, 0, -8, 0, 0,
    0, -2, 0, 0, 0, -3, 0, 0,
    0, 3, 0, -8, 0, -9, -2, 0,
    -12, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 2,
    0, -7, 6, 0, 7, -3, 0, 0,
    0, 1, 0, 0, 0, 0, 1, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -5, 0, 0, 0, 0, 0, 0,
    2, 0, 2, -2, 0, 3, 0, 0,
    0, -2, 0, 0, -3, 0, -2, 0,
    -3, -4, 0, 0, -2, -2, -2, -4,
    -2, 0, -2, 6, 0, 1, -29, -12,
    9, -1, 0, -32, 0, 4, 0, 0,
    0, 0, 0, 0, -9, 0, -7, -2,
    0, -5, 0, -3, 0, -4, -9, -6,
    0, 0, 0, 0, 5, 0, 2, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 1, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -6, -3, 0, 0, 0, -7, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -9, 0, 0, -25, 4, 0,
    0, -13, -3, 0, -2, 0, -5, 0,
    0, 0, 0, 0, -6, 0, -7, -9,
    0, -3, -3, -7, -9, -14, -7, 0,
    -11, -22, 0, -20, 6, 0, -16, -12,
    0, 4, -2, -29, -10, -33, -24, 0,
    -39, 0, -2, 0, -5, -4, 0, 0,
    0, -6, -7, -21, 0, 0, -2, 2,
    0, 3, -32, -18, 3, 0, 0, -36,
    0, 0, 0, 0, 0, -4, 0, -7,
    -7, 0, -7, 0, 0, 0, 0, 0,
    0, 2, 0, 0, 0, -2, 0, -2,
    10, 0, -1, -2, 0, 0, 2, -2,
    -3, -6, -3, 0, -10, 0, 0, 0,
    -3, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    2, 0, 0, 0, 5, 0, 0, -3,
    0, 0, -4, 1, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -7, 7, 0, -18, -25, -18, 7, -7,
    0, -31, 0, 4, 0, 4, 4, 0,
    0, 0, -26, 0, -25, -11, 0, -20,
    -25, -8, -19, -23, -23, -19, -2, 4,
    0, -4, -17, -14, 0, -4, 0, -15,
    0, 4, 0, 0, 0, 0, 0, 0,
    -17, 0, -13, -3, 0, -9, -9, 0,
    -6, -4, -7, -7, 0, 0, 4, -20,
    2, 0, 3, -7, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -5, 0,
    -7, 0, 0, -4, -4, -6, -6, -13,
    0, 0, -7, 3, 4, -16, -30, -24,
    2, -11, 0, -30, -4, 0, 0, 0,
    0, 0, 0, 0, -24, 0, -22, -11,
    0, -18, -20, -6, -16, -15, -13, -16,
    0, 0, 2, -11, 4, 0, 2, -7,
    0, 0, -3, 0, 0, 0, 0, 0,
    0, 0, -1, 0, -3, 0, 0, 0,
    0, 0, 0, -7, 0, 0, 0, -9,
    0, 0, 0, 0, -6, 0, 0, 0,
    0, -19, 0, -15, -14, -2, -21, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -2, 0, 0, 0, -11, 0, -2,
    -7, 0, -9, 0, 0, 0, 0, -25,
    0, -15, -13, -7, -22, 0, -2, 0,
    0, -2, 0, 0, 0, -1, 0, -4,
    -5, -5, 0, 1, 0, 4, 5, 0,
    -2, 0, 0, 0, 0, -17, 0, -10,
    -7, 4, -16, 0, 0, 0, -1, 2,
    0, 0, 0, 5, 0, 0, 2, 3,
    0, 0, 3, 0, 0, 0, 2, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 4, 0, 0, -5, 0, 0, 0,
    0, -15, 0, -12, -10, -2, -18, 0,
    0, 0, 0, 0, 0, 0, 3, 0,
    0, 1, -1, 0, 0, 9, 0, -2,
    -17, 0, 9, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -6, 0,
    -6, 0, 0, 0, 0, 3, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -5, 0, 0, 0, 0, -20, 0, -9,
    -9, 0, -18, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 3, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 7, 0,
    0, 0, 0, 0, 0, 0, -4, 0,
    0, -5, 6, 0, -10, 0, 0, 0,
    0, -21, 0, -13, -12, 0, -18, 0,
    -7, 0, -5, 0, 0, 0, -3, 0,
    -1, 0, 0, 0, 0, 4, 0, 2,
    -23, -8, -6, 0, 0, -24, 0, 0,
    0, -8, 0, -9, -15, 0, -10, 0,
    -7, 0, 0, 0, -1, 6, 0, 0,
    0, 0, 0, -7, 0, 0, 0, 0,
    -6, 0, 0, 0, 0, -23, 0, -15,
    -11, 0, -22, 0, 0, 0, 0, 0,
    0, 0, 2, 0, 0, -1, 0, 2,
    0, -2, 2, 0, 6, 0, -5, 0,
    0, 0, 0, -16, 0, -9, 0, 0,
    -14, 0, 0, 0, -2, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -20, -8, -6, 0, 0, -17,
    0, -23, 0, -9, -4, -13, -15, 0,
    -4, 0, -4, 0, 0, 0, -1, 0,
    0, 0, 0, 0, 0, 0, 0, -4,
    2, 0, -10, 0, 0, 0, 0, -23,
    0, -11, -7, 0, -13, 0, -3, 0,
    -5, 0, 0, 0, 0, 3, 0, 0,
    0, 0, 0, 0, 0, 0, 2, 0,
    -6, 0, 0, 0, 0, -23, 0, -12,
    -6, 0, -16, 0, -4, 0, -5, 0,
    0, 0, 0, 0, 0, 0, 0, 0
};


/*Collect the kern class' data in one place*/
static const lv_font_fmt_txt_kern_classes_t kern_classes =
{
    .class_pair_values   = kern_class_values,
    .left_class_mapping  = kern_left_class_mapping,
    .right_class_mapping = kern_right_class_mapping,
    .left_class_cnt      = 40,
    .right_class_cnt     = 30,
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LVGL_VERSION_MAJOR == 8
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
#endif

#if LVGL_VERSION_MAJOR >= 8
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_classes,
    .kern_scale = 16,
    .cmap_num = 2,
    .bpp = 4,
    .kern_classes = 1,
    .bitmap_format = 0,
#if LVGL_VERSION_MAJOR == 8
    .cache = &cache
#endif
};



/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LVGL_VERSION_MAJOR >= 8
const lv_font_t lv_font_HarmonyOS_Sans_SC_Regular_14 = {
#else
lv_font_t lv_font_HarmonyOS_Sans_SC_Regular_14 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 14,          /*The maximum line height required by the font*/
    .base_line = 2,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -1,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc,          /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
#if LV_VERSION_CHECK(8, 2, 0) || LVGL_VERSION_MAJOR >= 9
    .fallback = NULL,
#endif
    .user_data = NULL,
};



#endif /*#if LV_FONT_HARMONYOS_SANS_SC_REGULAR_14*/

