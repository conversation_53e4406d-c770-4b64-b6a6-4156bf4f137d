#include <stdio.h>
#include "device_bsp.h"
#include "esp_log.h"
#include "nvs_flash.h"
#include "esp_err.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"

#include "guider/generated/gui_guider.h"
#include "guider/custom/custom.h"
#include "esp_lvgl_port.h"
#include "gui_port.h"
#include "ui_user_inc.h"
#include "esp_psram.h"

#include "hal_wifi.h"
static const char *TAG = "MAIN";

void app_main(void)
{   
    esp_err_t err;
    err = nvs_flash_init();
    if (err == ESP_ERR_NVS_NO_FREE_PAGES)
    {
        // NVS partition was truncated and needs to be erased
        // Retry nvs_flash_init
        ESP_ERROR_CHECK(nvs_flash_erase());
        err = nvs_flash_init();
    }
    bsp_i2c_init();  // I2C初始化
    pca9557_init();  // IO扩展芯片初始化

    // hal_wifi_init();
    // hal_wifi_start();

    // 初始化UI通知服务
    gui_port_init(DEF_UI_SCR_START);

    esp_err_t ret;
    ret = canopen_init(1, 10);  // 节点ID=1, TPDO周期=10ms
    // 启动CANopen通信
    ret = canopen_start();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to start CANopen: %s", esp_err_to_name(ret));
        return;
        }
        
    ESP_LOGI(TAG, "CAN-UI Display System initialized successfully");
    ui_notif_service_init();
}
