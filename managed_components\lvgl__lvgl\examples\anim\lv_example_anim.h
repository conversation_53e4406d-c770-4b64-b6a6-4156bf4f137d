/**
 * @file lv_example_anim.h
 *
 */

#ifndef LV_EXAMPLE_ANIM_H
#define LV_EXAMPLE_ANIM_H

#ifdef __cplusplus
extern "C" {
#endif

/*********************
 *      INCLUDES
 *********************/

/*********************
 *      DEFINES
 *********************/

/**********************
 *      TYPEDEFS
 **********************/

/**********************
 * GLOBAL PROTOTYPES
 **********************/
void lv_example_anim_1(void);
void lv_example_anim_2(void);
void lv_example_anim_3(void);
void lv_example_anim_timeline_1(void);

/**********************
 *      MACROS
 **********************/

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif /*LV_EXAMPLE_ANIM_H*/
