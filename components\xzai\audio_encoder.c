#include <string.h>
#include "audio_encoder.h"
#include "audio_mem.h"
#include "audio_thread.h"
#include "opus.h"
#include "res_binary.h"
#include "esp_log.h"

#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/semphr.h"
#include "freertos/queue.h"

#define TAG "audio_encoder"

#define MAX_OPUS_PACKET_SIZE 1500
#define AUDIO_ENCODER_QUEUE_SIZE 64

typedef struct
{
    xQueueHandle q;
    StaticQueue_t *q_buff;
    uint8_t *q_storage;
} queue_data_t;

typedef struct
{
    int16_t data[MAX_OPUS_PACKET_SIZE];
    size_t len;
} recv_pack_t;

typedef struct
{
    uint8_t data[MAX_OPUS_PACKET_SIZE];
    size_t len;
} send_pack_t;

struct audio_encoder
{
    struct OpusEncoder *enc;

    int sample_rate;
    int channels;
    int bits;
    int duration_ms;

    int frame_size;

    queue_data_t recv_queue; // 接收队列
    queue_data_t send_queue; // 发送队列

    volatile bool run_flag;

    audio_encoder_callback callback;

    void *user_data;
};

static void queue_free(queue_data_t *queue)
{
    if (queue->q)
    {
        vQueueDelete(queue->q);
        queue->q = NULL;
    }

    if (queue->q_buff)
    {
        audio_free(queue->q_buff);
        queue->q_buff = NULL;
    }

    if (queue->q_storage)
    {
        audio_free(queue->q_storage);
        queue->q_storage = NULL;
    }
}

static esp_err_t queue_init(queue_data_t *queue, int item_size)
{
    queue->q_buff = (StaticQueue_t *)audio_calloc(1, sizeof(StaticQueue_t));
    queue->q_storage = (uint8_t *)audio_calloc(1, AUDIO_ENCODER_QUEUE_SIZE * item_size);

    if (!queue->q_buff || !queue->q_storage)
    {
        ESP_LOGE(TAG, "Failed to allocate queue");
        return ESP_FAIL;
    }

    queue->q = xQueueCreateStatic(AUDIO_ENCODER_QUEUE_SIZE, item_size,
                                  queue->q_storage, queue->q_buff);
    if (queue->q == NULL)
    {
        ESP_LOGE(TAG, "Failed to create queue");
        return ESP_FAIL;
    }

    return ESP_OK;
}

static void encoder_recv_task(void *arg)
{
    audio_encoder_t encoder = (audio_encoder_t)arg;
    recv_pack_t pack_pcm = {0};
    send_pack_t pack_opus = {0};

    encoder->run_flag = true;
    while (encoder->run_flag)
    {
        if (xQueueReceive(encoder->recv_queue.q, &pack_pcm, portMAX_DELAY) == pdTRUE)
        {
            memset(pack_opus.data, 0x0, sizeof(pack_opus.data));

            if (pack_pcm.len != encoder->frame_size)
                continue;

            // 编码数据
            int ret = opus_encode(encoder->enc, pack_pcm.data, encoder->frame_size, pack_opus.data, MAX_OPUS_PACKET_SIZE);
            if (ret < 0)
            {
                ESP_LOGE(TAG, "Failed to encode audio, error code: %ld", ret);
                continue;
            }
            pack_opus.len = ret;

            // 发送到队列
            if (xQueueSend(encoder->send_queue.q, &pack_opus, 0) != pdTRUE)
            {
                ESP_LOGE(TAG, "send to queue send failed");
            }
        }
    }

    ESP_LOGI(TAG, "encoder recv task exit");
    vTaskDelete(NULL);
}

static void encoder_send_task(void *arg)
{
    audio_encoder_t encoder = (audio_encoder_t)arg;
    send_pack_t pack_opus = {0};

    encoder->run_flag = true;
    while (encoder->run_flag)
    {
        if (xQueueReceive(encoder->send_queue.q, &pack_opus, portMAX_DELAY) == pdTRUE)
        {
            // 发送数据
            // ESP_LOGI(TAG, "opus send %d, %d", pack_opus.data[0], pack_opus.len);
            if (encoder->callback && (pack_opus.len < MAX_OPUS_PACKET_SIZE))
            {
                encoder->callback(encoder->user_data, pack_opus.data, pack_opus.len);
            }
        }
    }

    ESP_LOGI(TAG, "encoder send task exit");
    vTaskDelete(NULL);
}

audio_encoder_t audio_encoder_create(audio_encoder_cfg_t *cfg)
{
    audio_encoder_t encoder = (audio_encoder_t)audio_calloc(1, sizeof(struct audio_encoder));
    if (encoder == NULL)
        goto AUDIO_ENCODER_FAILED;

    encoder->sample_rate = cfg->sample_rate;
    encoder->channels = cfg->channels;
    encoder->bits = cfg->bits;
    encoder->duration_ms = cfg->duration_ms;

    encoder->frame_size = encoder->sample_rate / 1000 * encoder->channels * encoder->duration_ms;

    int error;
    encoder->enc = opus_encoder_create(encoder->sample_rate, encoder->channels, OPUS_APPLICATION_VOIP, &error);
    if (encoder->enc == NULL)
    {
        ESP_LOGE(TAG, "Failed to create audio encoder, error code: %d", error);
        goto AUDIO_ENCODER_FAILED;
    }

    // Default DTX enabled
    audio_encoder_set_dtx(encoder, true);
    // Complexity 5 almost uses up all CPU of ESP32C3
    audio_encoder_set_complexity(encoder, 3);

    opus_encoder_ctl(encoder->enc, OPUS_RESET_STATE);

    // 使用静态队列
    if (queue_init(&encoder->recv_queue, sizeof(recv_pack_t)) != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to create recv queue");
        goto AUDIO_ENCODER_FAILED;
    }

    if (queue_init(&encoder->send_queue, sizeof(send_pack_t)) != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to create send queue");
        goto AUDIO_ENCODER_FAILED;
    }

    // 创建接收任务
    audio_thread_create(NULL, "encoder_recv", encoder_recv_task, encoder, 16 * 1024, 5, true, 0);
    // 创建编码发送任务
    audio_thread_create(NULL, "encoder_send", encoder_send_task, encoder, 16 * 1024, 5, true, 0);

    return encoder;
AUDIO_ENCODER_FAILED:
    audio_encoder_destroy(encoder);
    return NULL;
}

void audio_encoder_destroy(audio_encoder_t encoder)
{
    if (encoder)
    {
        encoder->run_flag = false;

        // 清空队列
        xQueueReset(encoder->recv_queue.q);
        xQueueReset(encoder->send_queue.q);

        queue_free(&encoder->recv_queue);
        queue_free(&encoder->send_queue);

        if (encoder->enc)
        {
            opus_encoder_destroy(encoder->enc);
            encoder->enc = NULL;
        }

        encoder->callback = NULL;
        encoder->user_data = NULL;

        audio_free(encoder);
        encoder = NULL;
    }
}

void audio_encoder_send_data(audio_encoder_t encoder, int16_t *pcm, int len)
{
    recv_pack_t pack_pcm;

    pack_pcm.len = len;
    memcpy(pack_pcm.data, pcm, pack_pcm.len * sizeof(int16_t));

    if (xQueueSend(encoder->recv_queue.q, &pack_pcm, 0) != pdTRUE)
    {
        ESP_LOGE(TAG, "send to queue recv failed");
    }
}

void audio_encoder_reset(audio_encoder_t encoder)
{
    if (encoder->enc != NULL)
    {
        // opus_encoder_ctl(encoder->enc, OPUS_RESET_STATE);
        // 清空队列
        xQueueReset(encoder->recv_queue.q);
        xQueueReset(encoder->send_queue.q);
    }
}

void audio_encoder_set_dtx(audio_encoder_t encoder, bool enable)
{
    if (encoder->enc != NULL)
    {
        opus_encoder_ctl(encoder->enc, OPUS_SET_DTX(enable ? 1 : 0));
    }
}

void audio_encoder_set_complexity(audio_encoder_t encoder, int complexity)
{
    if (encoder->enc != NULL)
    {
        opus_encoder_ctl(encoder->enc, OPUS_SET_COMPLEXITY(complexity));
    }
}

void audio_encoder_set_callback(audio_encoder_t encoder, audio_encoder_callback cb)
{
    encoder->callback = cb;
}

void audio_encoder_set_user_data(audio_encoder_t encoder, void *user_data)
{
    encoder->user_data = user_data;
}