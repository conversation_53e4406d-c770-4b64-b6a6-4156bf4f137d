menu "HAL Wifi configuration"
    config WIFI_SSID
        string "SSID (network name) for the example to connect to."
        default "yui"
    config WIFI_PASSWD
        string "WiFi password (WPA or WPA2) for the example to use. Can be left blank if the network has no security set."
        default "88888888"
    config WIFI_SCAN_LIST_SIZE
        int "Wifi scan list size"
        range 0 30
        default 20
    config WIFI_ENABLE
        bool "Enable wifi"
        default y
    config WIFI_LOAD_FROM_STORE
        bool "Load wifi information from nvs configuration"
        default n
endmenu