# AI 功能组件使用指南

## 概述
这是一个简化的AI功能组件，专门为您的ESP32项目适配。由于原始组件依赖了很多外部音频库，我们创建了一个基础版本，可以作为AI功能的框架。

## 当前功能状态

### ✅ 已实现的功能
- 基础任务框架（xzai_task.c）
- WiFi状态检查
- 事件驱动架构
- 基础的状态管理
- 回调函数支持

### ⚠️ 暂时禁用的功能
- 语音识别（需要音频库支持）
- 语音合成（需要音频库支持）
- 按键检测（需要硬件抽象层）
- MQTT协议（需要额外配置）
- WebSocket协议（需要额外配置）

## 使用方法

### 1. 基础集成
在您的main.c中，AI功能已经通过以下方式集成：

```c
#include "xzai_example.h"

void app_main(void)
{
    // ... 其他初始化代码 ...
    
    // 初始化AI功能
    xzai_example_init();
    xzai_example_start_task();
    
    // ... 其他代码 ...
}
```

### 2. 自定义回调函数
您可以在 `xzai_example.c` 中自定义各种回调函数：

```c
// 通知回调函数
static void xzai_notif_callback_example(xzai_handle_t xzai, xzai_notif_msg_t msg)
{
    switch (msg) {
        case XZAI_NOTIF_AI_INIT_BEGIN:
            ESP_LOGI(TAG, "AI 初始化开始");
            break;
        case XZAI_NOTIF_WAKE_UP:
            ESP_LOGI(TAG, "AI 被唤醒");
            break;
        // 添加更多处理逻辑...
    }
}

// 按键回调函数（当按键功能可用时）
static void xzai_key_callback_example(xzai_handle_t xzai, bool state)
{
    if (state) {
        ESP_LOGI(TAG, "开始语音识别");
        // xzai_start_listening(xzai);
    } else {
        ESP_LOGI(TAG, "停止语音识别");
        // xzai_stop_listening(xzai);
    }
}
```

### 3. 状态管理
AI组件支持多种状态：

- `XZAI_STATE_UNKNOWN` - 未知状态
- `XZAI_STATE_STARTING` - 启动中
- `XZAI_STATE_IDLE` - 空闲状态
- `XZAI_STATE_CONNECTING` - 连接中
- `XZAI_STATE_LISTENING` - 聆听中
- `XZAI_STATE_SPEAKING` - 说话中

### 4. 事件系统
组件使用事件驱动架构：

- `SCHEDULE_EVENT` - 调度事件
- `AUDIO_INPUT_READY_EVENT` - 音频输入就绪
- `AUDIO_OUTPUT_READY_EVENT` - 音频输出就绪

## 扩展功能

### 添加音频支持
要启用语音功能，您需要：

1. 添加音频库依赖到 `components/xzai/CMakeLists.txt`
2. 取消注释相关的音频文件（voice.c, audio_player.c等）
3. 配置音频硬件（麦克风、扬声器）

### 添加网络通信
要启用网络AI功能：

1. 配置MQTT或WebSocket连接参数
2. 取消注释协议相关文件
3. 添加相应的网络库依赖

### 添加硬件按键
要启用按键控制：

1. 实现硬件按键抽象层
2. 取消注释按键检测代码
3. 配置按键GPIO

## 配置选项

您可以在 `xzai_example.c` 中修改以下配置：

```c
xzai_cfg_t xzai_cfg = {
    .recorder = NULL,           // 录音器句柄
    .voice_model_path = NULL,   // 语音模型路径
    .speak_volume = 50,         // 音量（0-100）
};
```

## WiFi 热点功能

项目已配置为同时支持AP（热点）和STA（客户端）模式：
- 热点SSID: "FlipClock-AP"
- 热点密码: "12345678"
- IP地址: ***********

手机现在可以搜索到并连接这个热点。

## 调试信息

在串口监视器中，您可以看到以下日志：
- AI任务启动信息
- WiFi连接状态
- 按键事件（如果启用）
- 状态变化通知

## 下一步开发建议

1. **添加简单的音频播放功能** - 使用现有的音频播放器组件
2. **实现基础的网络通信** - 连接到AI服务
3. **添加用户界面控制** - 通过LVGL界面控制AI功能
4. **集成CAN总线数据** - 将CAN数据作为AI的输入

## 故障排除

如果遇到编译错误：
1. 确保所有依赖的头文件都存在
2. 检查 CMakeLists.txt 中的组件依赖
3. 查看串口日志中的错误信息

如果功能不工作：
1. 检查WiFi连接状态
2. 查看AI任务是否正常启动
3. 确认回调函数是否被正确调用
