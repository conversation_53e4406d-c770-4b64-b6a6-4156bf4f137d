#include "events_init.h"
#include <stdio.h>
#include "lvgl.h"
#include "ui_user_inc.h"
#include "esp_log.h"
#if LV_USE_GUIDER_SIMULATOR && LV_USE_FREEMASTER
#include "freemaster_client.h"
#endif

#define TAG "ui_setup_can_connet"

void events_init_Can_Connet_page (lv_ui *ui)
{
    lv_obj_add_event_cb(ui->Can_Connet_page_back, back_to_menu_event_handler, LV_EVENT_ALL, ui);
}

/**
 * @brief 更新CAN数据显示
 */
void ui_scr_can_connect_data_update(ui_notif_msg_t *msg)
{
    if (!msg || !msg->user_data) {
        return;
    }
    
    can_display_data_t *can_data = (can_display_data_t *)msg->user_data;
    
    if (!can_data->valid) {
        return;
    }
    
    lv_ui *ui = &guider_ui;
    //打印can_data和内存地址
    // ESP_LOGI(TAG, "Recv data - addr: %p, voltage=%ld, current=%ld, temperature=%ld, speed=%ld, position=%ld",
    //          can_data, can_data->voltage, can_data->current, can_data->temperature, can_data->speed, can_data->position);
    
    // 更新电压显示
    if (ui->Can_Connet_page_voltage) {
        lv_label_set_text_fmt(ui->Can_Connet_page_voltage, "%ld", can_data->voltage);
    }
    
    // 更新电流显示
    if (ui->Can_Connet_page_current) {
        lv_label_set_text_fmt(ui->Can_Connet_page_current, "%ld", can_data->current);
    }
    
    // 更新温度显示
    if (ui->Can_Connet_page_temper) {
        lv_label_set_text_fmt(ui->Can_Connet_page_temper, "%ld", can_data->temperature);
    }
    
    // 更新速度显示
    if (ui->Can_Connet_page_Speed) {
        lv_label_set_text_fmt(ui->Can_Connet_page_Speed, "%ld", can_data->speed);
    }
    
    // 更新位置显示
    if (ui->Can_Connet_page_label_position) {
        lv_label_set_text_fmt(ui->Can_Connet_page_label_position, "%ld", can_data->position);
    }
}

/**
 * @brief 更新CAN状态显示
 */
void ui_scr_can_connect_status_update(ui_notif_msg_t *msg)
{
    if (!msg || !msg->user_data) {
        return;
    }
    
    can_status_data_t *can_status = (can_status_data_t *)msg->user_data;
    lv_ui *ui = &guider_ui;
    
    // 更新连接状态，可以修改标题栏颜色或显示文本
    if (ui->Can_Connet_page_label_state_true) {
        if (can_status->connected) {
            lv_label_set_text(ui->Can_Connet_page_label_state_true, "CAN connected");
        } else {
            lv_label_set_text(ui->Can_Connet_page_label_state_true, "CAN disconnected");
        }
    }
    
    // 更新错误计数 (可以在某个地方显示)
    // 这里可以根据需要添加更多的状态显示逻辑
}