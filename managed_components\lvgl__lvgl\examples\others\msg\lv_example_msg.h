/**
 * @file lv_example_msg.h
 *
 */

#ifndef LV_EXAMPLE_MSG_H
#define LV_EXAMPLE_MSG_H

#ifdef __cplusplus
extern "C" {
#endif

/*********************
 *      INCLUDES
 *********************/

/*********************
 *      DEFINES
 *********************/

/**********************
 *      TYPEDEFS
 **********************/

/**********************
 * GLOBAL PROTOTYPES
 **********************/
void lv_example_msg_1(void);
void lv_example_msg_2(void);
void lv_example_msg_3(void);

/**********************
 *      MACROS
 **********************/

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif /*LV_EXAMPLE_MSG_H*/
