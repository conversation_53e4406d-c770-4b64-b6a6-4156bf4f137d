/*******************************************************************************
 * Size: 20 px
 * Bpp: 4
 * Opts: undefined
 ******************************************************************************/

#ifdef LV_LVGL_H_INCLUDE_SIMPLE
#include "lvgl.h"
#else
#include "lvgl.h"
#endif

#ifndef LV_FONT_HARMONYOS_SANS_SC_MEDIUM_20
#define LV_FONT_HARMONYOS_SANS_SC_MEDIUM_20 1
#endif

#if LV_FONT_HARMONYOS_SANS_SC_MEDIUM_20

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0xaf, 0x79, 0xf7, 0x9f, 0x68, 0xf6, 0x8f, 0x67,
    0xf5, 0x7f, 0x57, 0xf4, 0x6f, 0x46, 0xf3, 0x5f,
    0x31, 0x40, 0x4a, 0x3c, 0xfb, 0x7f, 0x60,

    /* U+0022 "\"" */
    0x66, 0x2, 0x73, 0xee, 0x6, 0xf6, 0xee, 0x5,
    0xf6, 0xde, 0x5, 0xf6, 0xdd, 0x5, 0xf5, 0xcd,
    0x4, 0xf5,

    /* U+0023 "#" */
    0x0, 0x0, 0x3f, 0xb0, 0x4, 0xf9, 0x0, 0x0,
    0x7, 0xf6, 0x0, 0x9f, 0x50, 0x0, 0x0, 0xcf,
    0x20, 0xd, 0xf1, 0x0, 0x5e, 0xef, 0xfe, 0xee,
    0xff, 0xed, 0x6, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x0, 0x6f, 0x70, 0x8, 0xf5, 0x0, 0x0,
    0xa, 0xf3, 0x0, 0xbf, 0x10, 0x0, 0x0, 0xef,
    0x0, 0xf, 0xe0, 0x0, 0x0, 0x1f, 0xc0, 0x3,
    0xfa, 0x0, 0xb, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x80, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x0,
    0xc, 0xf1, 0x0, 0xef, 0x0, 0x0, 0x0, 0xfe,
    0x0, 0x1f, 0xc0, 0x0, 0x0, 0x3f, 0xb0, 0x5,
    0xf9, 0x0, 0x0, 0x6, 0xf7, 0x0, 0x8f, 0x60,
    0x0, 0x0,

    /* U+0024 "$" */
    0x0, 0x0, 0xf, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xf3, 0x0,
    0x0, 0x4, 0xef, 0xff, 0xfc, 0x20, 0x4, 0xff,
    0xdf, 0xff, 0xfe, 0x10, 0xcf, 0x80, 0xfe, 0x1c,
    0xf9, 0xf, 0xf1, 0xf, 0xe0, 0x2c, 0x30, 0xef,
    0x30, 0xfe, 0x0, 0x0, 0xa, 0xfe, 0x4f, 0xe0,
    0x0, 0x0, 0x1d, 0xff, 0xfe, 0x10, 0x0, 0x0,
    0x8, 0xef, 0xff, 0xa1, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xe2, 0x0, 0x0, 0xf, 0xe2, 0xdf, 0xa0,
    0x63, 0x0, 0xfe, 0x3, 0xfe, 0x5f, 0xc0, 0xf,
    0xe0, 0x2f, 0xe0, 0xef, 0xa1, 0xfe, 0xb, 0xfa,
    0x4, 0xff, 0xff, 0xff, 0xff, 0x20, 0x2, 0xbf,
    0xff, 0xfc, 0x20, 0x0, 0x0, 0x1f, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xe0, 0x0, 0x0,

    /* U+0025 "%" */
    0x3, 0xcf, 0xe7, 0x0, 0x0, 0x1e, 0xf2, 0x0,
    0x1f, 0xfc, 0xef, 0x70, 0x0, 0x9f, 0x80, 0x0,
    0x7f, 0x70, 0x1e, 0xe0, 0x2, 0xfe, 0x10, 0x0,
    0x9f, 0x30, 0xc, 0xf0, 0xc, 0xf6, 0x0, 0x0,
    0x6f, 0xa0, 0x4f, 0xd0, 0x5f, 0xc0, 0x0, 0x0,
    0xd, 0xff, 0xff, 0x50, 0xef, 0x30, 0x0, 0x0,
    0x1, 0x8b, 0xa3, 0x8, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xf1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0x70, 0x3a, 0xb7, 0x0,
    0x0, 0x0, 0x4, 0xfd, 0x5, 0xff, 0xff, 0xc0,
    0x0, 0x0, 0xd, 0xf5, 0xe, 0xf4, 0x1b, 0xf6,
    0x0, 0x0, 0x6f, 0xb0, 0x1f, 0xc0, 0x4, 0xf8,
    0x0, 0x1, 0xef, 0x20, 0xf, 0xe0, 0x6, 0xf7,
    0x0, 0x9, 0xf9, 0x0, 0x8, 0xfc, 0x9f, 0xe1,
    0x0, 0x2f, 0xe1, 0x0, 0x0, 0x8e, 0xfb, 0x20,

    /* U+0026 "&" */
    0x0, 0x0, 0x6d, 0xfe, 0xa1, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xff, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x1f, 0xf5, 0x2, 0xef, 0x60, 0x0, 0x0, 0x4,
    0xfe, 0x0, 0xa, 0xf8, 0x0, 0x0, 0x0, 0x3f,
    0xf0, 0x0, 0xdf, 0x60, 0x0, 0x0, 0x0, 0xdf,
    0x92, 0xcf, 0xd0, 0x0, 0x0, 0x0, 0x3, 0xff,
    0xff, 0xb1, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0x80, 0x0, 0xef, 0x20, 0x0, 0xbf, 0xea, 0xff,
    0x20, 0x1f, 0xe0, 0x0, 0x9f, 0xd1, 0xa, 0xfe,
    0x15, 0xfb, 0x0, 0xf, 0xf4, 0x0, 0xc, 0xfd,
    0xbf, 0x50, 0x1, 0xff, 0x10, 0x0, 0x1d, 0xff,
    0xe0, 0x0, 0xf, 0xf3, 0x0, 0x0, 0x2f, 0xfb,
    0x0, 0x0, 0xbf, 0xc1, 0x0, 0x2c, 0xff, 0xf8,
    0x0, 0x2, 0xef, 0xfc, 0xcf, 0xfc, 0x5f, 0xf6,
    0x0, 0x1, 0x9d, 0xfe, 0xb5, 0x0, 0x6f, 0xf4,

    /* U+0027 "'" */
    0x66, 0xee, 0xee, 0xde, 0xdd, 0xcd,

    /* U+0028 "(" */
    0x0, 0x0, 0x22, 0x0, 0xb, 0xf5, 0x0, 0x7f,
    0xa0, 0x1, 0xff, 0x10, 0x8, 0xfa, 0x0, 0xe,
    0xf3, 0x0, 0x2f, 0xf0, 0x0, 0x6f, 0xb0, 0x0,
    0x8f, 0x90, 0x0, 0xaf, 0x80, 0x0, 0xaf, 0x70,
    0x0, 0x9f, 0x80, 0x0, 0x8f, 0x90, 0x0, 0x6f,
    0xb0, 0x0, 0x2f, 0xf0, 0x0, 0xd, 0xf4, 0x0,
    0x6, 0xfb, 0x0, 0x0, 0xef, 0x20, 0x0, 0x5f,
    0xc0, 0x0, 0x9, 0xf7,

    /* U+0029 ")" */
    0x12, 0x0, 0x0, 0x3f, 0xc0, 0x0, 0x8, 0xf9,
    0x0, 0x0, 0xef, 0x20, 0x0, 0x7f, 0x90, 0x0,
    0x1f, 0xf0, 0x0, 0xd, 0xf4, 0x0, 0x9, 0xf8,
    0x0, 0x7, 0xfa, 0x0, 0x5, 0xfb, 0x0, 0x5,
    0xfc, 0x0, 0x6, 0xfb, 0x0, 0x7, 0xfa, 0x0,
    0x9, 0xf8, 0x0, 0xd, 0xf3, 0x0, 0x2f, 0xf0,
    0x0, 0x9f, 0x80, 0x1, 0xff, 0x10, 0xa, 0xf7,
    0x0, 0x5f, 0xa0, 0x0,

    /* U+002A "*" */
    0x0, 0x6, 0xf3, 0x0, 0x0, 0x72, 0x5f, 0x13,
    0x50, 0x2f, 0xf9, 0xf8, 0xfe, 0x0, 0x2a, 0xff,
    0xf8, 0x10, 0x3, 0xbf, 0xff, 0x91, 0x2, 0xfe,
    0x8f, 0x7f, 0xe0, 0x6, 0x15, 0xf1, 0x25, 0x0,
    0x0, 0x5e, 0x20, 0x0,

    /* U+002B "+" */
    0x0, 0x0, 0x1f, 0xb0, 0x0, 0x0, 0x0, 0x2,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xb0, 0x0,
    0x0, 0x0, 0x2, 0xfb, 0x0, 0x0, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xa0, 0xee, 0xee, 0xff, 0xee,
    0xe9, 0x0, 0x0, 0x2f, 0xb0, 0x0, 0x0, 0x0,
    0x2, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xb0,
    0x0, 0x0, 0x0, 0x2, 0xfb, 0x0, 0x0,

    /* U+002C "," */
    0x3b, 0x50, 0xaf, 0xe0, 0x5f, 0xf0, 0x6, 0xe0,
    0x2d, 0x70, 0xd8, 0x0, 0x0, 0x0,

    /* U+002D "-" */
    0x9c, 0xcc, 0xcc, 0xc7, 0xcf, 0xff, 0xff, 0xf9,

    /* U+002E "." */
    0x5b, 0x3d, 0xfa, 0x8f, 0x50,

    /* U+002F "/" */
    0x0, 0x0, 0x6, 0xf9, 0x0, 0x0, 0xc, 0xf3,
    0x0, 0x0, 0x2f, 0xd0, 0x0, 0x0, 0x8f, 0x80,
    0x0, 0x0, 0xdf, 0x20, 0x0, 0x3, 0xfc, 0x0,
    0x0, 0x9, 0xf6, 0x0, 0x0, 0xf, 0xf1, 0x0,
    0x0, 0x5f, 0xa0, 0x0, 0x0, 0xbf, 0x40, 0x0,
    0x1, 0xfe, 0x0, 0x0, 0x7, 0xf9, 0x0, 0x0,
    0xc, 0xf3, 0x0, 0x0, 0x2f, 0xd0, 0x0, 0x0,
    0x8f, 0x70, 0x0, 0x0,

    /* U+0030 "0" */
    0x0, 0x5, 0xcf, 0xea, 0x20, 0x0, 0x7, 0xff,
    0xff, 0xfe, 0x20, 0x2, 0xff, 0x81, 0x2d, 0xfb,
    0x0, 0x8f, 0xd0, 0x0, 0x3f, 0xf2, 0xc, 0xf7,
    0x0, 0x0, 0xef, 0x60, 0xff, 0x50, 0x0, 0xb,
    0xf9, 0xf, 0xf3, 0x0, 0x0, 0xaf, 0xa1, 0xff,
    0x30, 0x0, 0x9, 0xfb, 0xf, 0xf3, 0x0, 0x0,
    0xaf, 0xa0, 0xff, 0x50, 0x0, 0xb, 0xf9, 0xc,
    0xf7, 0x0, 0x0, 0xef, 0x60, 0x8f, 0xd0, 0x0,
    0x3f, 0xf2, 0x2, 0xff, 0x81, 0x2d, 0xfb, 0x0,
    0x7, 0xff, 0xff, 0xfe, 0x20, 0x0, 0x5, 0xcf,
    0xea, 0x20, 0x0,

    /* U+0031 "1" */
    0x0, 0x0, 0x9f, 0xe0, 0x6, 0xef, 0xfe, 0xc,
    0xff, 0xdf, 0xe1, 0xfc, 0x34, 0xfe, 0x16, 0x0,
    0x4f, 0xe0, 0x0, 0x4, 0xfe, 0x0, 0x0, 0x4f,
    0xe0, 0x0, 0x4, 0xfe, 0x0, 0x0, 0x4f, 0xe0,
    0x0, 0x4, 0xfe, 0x0, 0x0, 0x4f, 0xe0, 0x0,
    0x4, 0xfe, 0x0, 0x0, 0x4f, 0xe0, 0x0, 0x4,
    0xfe, 0x0, 0x0, 0x4f, 0xe0,

    /* U+0032 "2" */
    0x0, 0x6c, 0xfe, 0xa2, 0x0, 0xb, 0xff, 0xff,
    0xff, 0x30, 0x7f, 0xe5, 0x13, 0xdf, 0xc0, 0xbf,
    0x40, 0x0, 0x5f, 0xf0, 0x5, 0x0, 0x0, 0x2f,
    0xf1, 0x0, 0x0, 0x0, 0x5f, 0xf0, 0x0, 0x0,
    0x0, 0xdf, 0x90, 0x0, 0x0, 0x9, 0xfe, 0x10,
    0x0, 0x0, 0x7f, 0xf4, 0x0, 0x0, 0x5, 0xff,
    0x60, 0x0, 0x0, 0x4f, 0xf7, 0x0, 0x0, 0x3,
    0xff, 0x90, 0x0, 0x0, 0x2e, 0xfa, 0x0, 0x0,
    0x0, 0xdf, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff,
    0xff, 0xff, 0xf9,

    /* U+0033 "3" */
    0x0, 0x18, 0xdf, 0xea, 0x30, 0x0, 0x1d, 0xff,
    0xff, 0xff, 0x30, 0x9, 0xfd, 0x30, 0x3d, 0xfc,
    0x0, 0x7e, 0x10, 0x0, 0x5f, 0xf0, 0x0, 0x0,
    0x0, 0x6, 0xfe, 0x0, 0x0, 0x0, 0x15, 0xef,
    0x80, 0x0, 0x0, 0xcf, 0xff, 0x80, 0x0, 0x0,
    0xc, 0xff, 0xfe, 0x30, 0x0, 0x0, 0x0, 0x3b,
    0xff, 0x10, 0x0, 0x0, 0x0, 0xe, 0xf6, 0x0,
    0x20, 0x0, 0x0, 0xcf, 0x80, 0xbe, 0x10, 0x0,
    0xe, 0xf6, 0xb, 0xfd, 0x40, 0x2b, 0xff, 0x10,
    0x2d, 0xff, 0xff, 0xff, 0x60, 0x0, 0x18, 0xdf,
    0xeb, 0x30, 0x0,

    /* U+0034 "4" */
    0x0, 0x0, 0x2, 0xff, 0x20, 0x0, 0x0, 0x0,
    0xb, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0x90, 0x0, 0x0,
    0x0, 0x3, 0xff, 0x10, 0x0, 0x0, 0x0, 0xc,
    0xf8, 0x2, 0x30, 0x0, 0x0, 0x4f, 0xf1, 0xd,
    0xf4, 0x0, 0x0, 0xcf, 0x70, 0xd, 0xf4, 0x0,
    0x5, 0xfe, 0x0, 0xd, 0xf4, 0x0, 0xd, 0xf7,
    0x0, 0xd, 0xf4, 0x0, 0x3f, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0x4e, 0xee, 0xee, 0xef, 0xfe, 0xe4,
    0x0, 0x0, 0x0, 0xd, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xf4, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xf4, 0x0,

    /* U+0035 "5" */
    0x6, 0xff, 0xff, 0xff, 0xd0, 0x8, 0xff, 0xff,
    0xff, 0xc0, 0xa, 0xf6, 0x0, 0x0, 0x0, 0xc,
    0xf4, 0x0, 0x0, 0x0, 0xe, 0xf2, 0x0, 0x0,
    0x0, 0xf, 0xfb, 0xee, 0xb3, 0x0, 0x2f, 0xff,
    0xff, 0xff, 0x40, 0x4f, 0xe4, 0x3, 0xcf, 0xe0,
    0x3, 0x20, 0x0, 0x2f, 0xf4, 0x0, 0x0, 0x0,
    0xd, 0xf7, 0x0, 0x0, 0x0, 0xd, 0xf6, 0x2b,
    0x50, 0x0, 0x2f, 0xf4, 0x6f, 0xe4, 0x2, 0xcf,
    0xd0, 0xb, 0xff, 0xff, 0xff, 0x30, 0x0, 0x7d,
    0xfe, 0x92, 0x0,

    /* U+0036 "6" */
    0x0, 0x0, 0x2f, 0xf3, 0x0, 0x0, 0x0, 0xbf,
    0x90, 0x0, 0x0, 0x5, 0xfd, 0x0, 0x0, 0x0,
    0xe, 0xf3, 0x0, 0x0, 0x0, 0x9f, 0x80, 0x0,
    0x0, 0x2, 0xff, 0xde, 0xc5, 0x0, 0xb, 0xff,
    0xff, 0xff, 0x80, 0x4f, 0xf7, 0x11, 0x9f, 0xf3,
    0xaf, 0xa0, 0x0, 0xd, 0xf9, 0xef, 0x60, 0x0,
    0x9, 0xfc, 0xef, 0x60, 0x0, 0x8, 0xfb, 0xbf,
    0xa0, 0x0, 0xd, 0xf9, 0x5f, 0xf7, 0x12, 0xaf,
    0xf2, 0xa, 0xff, 0xff, 0xff, 0x60, 0x0, 0x6c,
    0xfe, 0xb4, 0x0,

    /* U+0037 "7" */
    0xef, 0xff, 0xff, 0xff, 0xf7, 0xde, 0xee, 0xee,
    0xef, 0xf6, 0x0, 0x0, 0x0, 0x3f, 0xf0, 0x0,
    0x0, 0x0, 0x9f, 0xa0, 0x0, 0x0, 0x0, 0xff,
    0x30, 0x0, 0x0, 0x6, 0xfd, 0x0, 0x0, 0x0,
    0xc, 0xf7, 0x0, 0x0, 0x0, 0x3f, 0xf1, 0x0,
    0x0, 0x0, 0x9f, 0xa0, 0x0, 0x0, 0x0, 0xff,
    0x40, 0x0, 0x0, 0x6, 0xfe, 0x0, 0x0, 0x0,
    0xd, 0xf7, 0x0, 0x0, 0x0, 0x3f, 0xf1, 0x0,
    0x0, 0x0, 0xaf, 0xb0, 0x0, 0x0, 0x1, 0xff,
    0x50, 0x0, 0x0,

    /* U+0038 "8" */
    0x0, 0x6, 0xcf, 0xeb, 0x30, 0x0, 0xa, 0xff,
    0xff, 0xff, 0x40, 0x4, 0xff, 0x50, 0xa, 0xfd,
    0x0, 0x8f, 0xb0, 0x0, 0x1f, 0xf1, 0x7, 0xfc,
    0x0, 0x1, 0xff, 0x0, 0x2f, 0xf6, 0x2, 0xbf,
    0xb0, 0x0, 0x4e, 0xff, 0xff, 0xc1, 0x0, 0x8,
    0xff, 0xff, 0xfe, 0x30, 0x7, 0xfe, 0x50, 0x19,
    0xff, 0x10, 0xef, 0x60, 0x0, 0xc, 0xf7, 0xf,
    0xf3, 0x0, 0x0, 0x9f, 0xa0, 0xff, 0x60, 0x0,
    0xc, 0xf9, 0xa, 0xfe, 0x50, 0x18, 0xff, 0x40,
    0x1d, 0xff, 0xff, 0xff, 0x90, 0x0, 0x8, 0xdf,
    0xec, 0x50, 0x0,

    /* U+0039 "9" */
    0x0, 0x8, 0xdf, 0xea, 0x20, 0x0, 0x1d, 0xff,
    0xff, 0xff, 0x40, 0xa, 0xfe, 0x40, 0x2a, 0xfe,
    0x0, 0xff, 0x50, 0x0, 0xe, 0xf5, 0x2f, 0xf2,
    0x0, 0x0, 0xbf, 0x81, 0xff, 0x50, 0x0, 0xe,
    0xf8, 0xb, 0xfe, 0x40, 0x1a, 0xff, 0x40, 0x2e,
    0xff, 0xff, 0xff, 0xd0, 0x0, 0x19, 0xef, 0xef,
    0xf5, 0x0, 0x0, 0x0, 0xc, 0xfc, 0x0, 0x0,
    0x0, 0x6, 0xff, 0x20, 0x0, 0x0, 0x0, 0xef,
    0x80, 0x0, 0x0, 0x0, 0x8f, 0xe0, 0x0, 0x0,
    0x0, 0x1f, 0xf5, 0x0, 0x0, 0x0, 0xa, 0xfb,
    0x0, 0x0, 0x0,

    /* U+003A ":" */
    0x4f, 0x99, 0xfe, 0x2b, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2b, 0x59, 0xfe, 0x4f,
    0x90,

    /* U+003B ";" */
    0x2e, 0xb0, 0x6f, 0xf1, 0x1a, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1b, 0x70, 0x7f, 0xf1, 0x3e, 0xf2, 0x3, 0xf0,
    0x1c, 0x90, 0xaa, 0x0, 0x0, 0x0,

    /* U+003C "<" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0x40, 0x0, 0x0,
    0x0, 0x3a, 0xfa, 0x0, 0x0, 0x5, 0xcf, 0xff,
    0x60, 0x1, 0x7e, 0xff, 0xd7, 0x0, 0xa, 0xff,
    0xfb, 0x40, 0x0, 0x0, 0xff, 0xf5, 0x0, 0x0,
    0x0, 0xa, 0xff, 0xfb, 0x40, 0x0, 0x0, 0x1,
    0x8e, 0xff, 0xd6, 0x0, 0x0, 0x0, 0x5, 0xcf,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x3a, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x40,

    /* U+003D "=" */
    0xf, 0xff, 0xff, 0xff, 0xff, 0xa0, 0xee, 0xee,
    0xee, 0xee, 0xe8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xee,
    0xee, 0xee, 0xee, 0x80, 0xff, 0xff, 0xff, 0xff,
    0xfa,

    /* U+003E ">" */
    0x5, 0x0, 0x0, 0x0, 0x0, 0x0, 0xfe, 0x71,
    0x0, 0x0, 0x0, 0xb, 0xff, 0xf9, 0x20, 0x0,
    0x0, 0x2, 0x9f, 0xff, 0xb5, 0x0, 0x0, 0x0,
    0x17, 0xef, 0xfe, 0x50, 0x0, 0x0, 0x0, 0xaf,
    0xfa, 0x0, 0x0, 0x17, 0xef, 0xfe, 0x50, 0x2,
    0x9f, 0xff, 0xc5, 0x0, 0xb, 0xff, 0xfa, 0x30,
    0x0, 0x0, 0xfe, 0x71, 0x0, 0x0, 0x0, 0x5,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+003F "?" */
    0x0, 0x18, 0xdf, 0xea, 0x20, 0x2, 0xef, 0xff,
    0xff, 0xf2, 0xc, 0xfb, 0x31, 0x4e, 0xfa, 0x8,
    0xd0, 0x0, 0x8, 0xfc, 0x0, 0x0, 0x0, 0xb,
    0xf8, 0x0, 0x0, 0x0, 0x8f, 0xd0, 0x0, 0x0,
    0xa, 0xfb, 0x10, 0x0, 0x0, 0x8f, 0xb0, 0x0,
    0x0, 0x0, 0xef, 0x10, 0x0, 0x0, 0x0, 0xee,
    0x0, 0x0, 0x0, 0x0, 0x67, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5a, 0x10,
    0x0, 0x0, 0x0, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x9e, 0x40, 0x0,

    /* U+0040 "@" */
    0x0, 0x0, 0x0, 0x15, 0x9b, 0xba, 0x72, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xff,
    0xfb, 0x10, 0x0, 0x0, 0x2, 0xcf, 0xd7, 0x31,
    0x2, 0x6c, 0xfe, 0x40, 0x0, 0x0, 0xdf, 0x90,
    0x0, 0x0, 0x0, 0x6, 0xff, 0x20, 0x0, 0xaf,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x6, 0xfc, 0x0,
    0x3f, 0xc0, 0x0, 0x5d, 0xfd, 0x55, 0xc2, 0xb,
    0xf3, 0x9, 0xf5, 0x0, 0x7f, 0xff, 0xff, 0xdf,
    0x30, 0x5f, 0x80, 0xdf, 0x0, 0x1f, 0xf5, 0x1,
    0xaf, 0xf3, 0x0, 0xfc, 0xf, 0xd0, 0x6, 0xfa,
    0x0, 0x0, 0xef, 0x30, 0xf, 0xd0, 0xfb, 0x0,
    0x8f, 0x60, 0x0, 0xc, 0xf3, 0x0, 0xde, 0xf,
    0xc0, 0x8, 0xf7, 0x0, 0x0, 0xcf, 0x30, 0xe,
    0xc0, 0xee, 0x0, 0x5f, 0xa0, 0x0, 0xe, 0xf3,
    0x1, 0xfa, 0xa, 0xf3, 0x0, 0xef, 0x61, 0x2b,
    0xff, 0x91, 0x9f, 0x50, 0x5f, 0x90, 0x4, 0xff,
    0xff, 0xf4, 0xef, 0xff, 0xc0, 0x0, 0xdf, 0x40,
    0x2, 0x9b, 0x92, 0x2, 0x9b, 0x80, 0x0, 0x3,
    0xfe, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0x93, 0x0, 0x0, 0x2a, 0x40,
    0x0, 0x0, 0x0, 0x3, 0xdf, 0xfe, 0xdc, 0xef,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5a, 0xef,
    0xfe, 0xb5, 0x0, 0x0, 0x0,

    /* U+0041 "A" */
    0x0, 0x0, 0x6, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0x8f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0xef, 0x1b,
    0xf6, 0x0, 0x0, 0x0, 0x4, 0xfb, 0x5, 0xfc,
    0x0, 0x0, 0x0, 0xa, 0xf5, 0x0, 0xff, 0x20,
    0x0, 0x0, 0x1f, 0xf0, 0x0, 0x9f, 0x90, 0x0,
    0x0, 0x6f, 0x90, 0x0, 0x3f, 0xe0, 0x0, 0x0,
    0xcf, 0xff, 0xff, 0xff, 0xf5, 0x0, 0x2, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0x0, 0x8, 0xf8, 0x0,
    0x0, 0x1, 0xff, 0x20, 0xe, 0xf2, 0x0, 0x0,
    0x0, 0xbf, 0x80, 0x4f, 0xd0, 0x0, 0x0, 0x0,
    0x5f, 0xe0, 0xaf, 0x70, 0x0, 0x0, 0x0, 0xf,
    0xf5,

    /* U+0042 "B" */
    0x5f, 0xff, 0xff, 0xeb, 0x50, 0x0, 0x5f, 0xff,
    0xff, 0xff, 0xfa, 0x0, 0x5f, 0xc0, 0x0, 0x18,
    0xff, 0x50, 0x5f, 0xc0, 0x0, 0x0, 0xbf, 0x90,
    0x5f, 0xc0, 0x0, 0x0, 0xbf, 0x90, 0x5f, 0xc0,
    0x0, 0x7, 0xff, 0x40, 0x5f, 0xfd, 0xde, 0xff,
    0xf7, 0x0, 0x5f, 0xff, 0xff, 0xff, 0xfc, 0x20,
    0x5f, 0xc0, 0x0, 0x14, 0xcf, 0xe0, 0x5f, 0xc0,
    0x0, 0x0, 0x1f, 0xf5, 0x5f, 0xc0, 0x0, 0x0,
    0xd, 0xf7, 0x5f, 0xc0, 0x0, 0x0, 0xf, 0xf6,
    0x5f, 0xc0, 0x0, 0x3, 0xcf, 0xf1, 0x5f, 0xff,
    0xff, 0xff, 0xff, 0x60, 0x5f, 0xff, 0xff, 0xfe,
    0xa3, 0x0,

    /* U+0043 "C" */
    0x0, 0x0, 0x29, 0xdf, 0xeb, 0x40, 0x0, 0x0,
    0x6f, 0xff, 0xff, 0xff, 0x90, 0x0, 0x6f, 0xfa,
    0x31, 0x28, 0xff, 0x60, 0x1f, 0xf8, 0x0, 0x0,
    0x6, 0xe5, 0x8, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0x80, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0x30,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xf8, 0x0, 0x0, 0x6, 0xe4, 0x0, 0x6f,
    0xfa, 0x31, 0x28, 0xff, 0x60, 0x0, 0x6f, 0xff,
    0xff, 0xff, 0x90, 0x0, 0x0, 0x29, 0xdf, 0xeb,
    0x40, 0x0,

    /* U+0044 "D" */
    0x5f, 0xff, 0xff, 0xd9, 0x30, 0x0, 0x5, 0xff,
    0xee, 0xff, 0xff, 0xa0, 0x0, 0x5f, 0xd0, 0x0,
    0x28, 0xff, 0xb0, 0x5, 0xfd, 0x0, 0x0, 0x3,
    0xff, 0x70, 0x5f, 0xd0, 0x0, 0x0, 0x7, 0xfe,
    0x5, 0xfd, 0x0, 0x0, 0x0, 0xf, 0xf4, 0x5f,
    0xd0, 0x0, 0x0, 0x0, 0xdf, 0x65, 0xfd, 0x0,
    0x0, 0x0, 0xc, 0xf7, 0x5f, 0xd0, 0x0, 0x0,
    0x0, 0xdf, 0x65, 0xfd, 0x0, 0x0, 0x0, 0x1f,
    0xf4, 0x5f, 0xd0, 0x0, 0x0, 0x7, 0xfe, 0x5,
    0xfd, 0x0, 0x0, 0x3, 0xff, 0x70, 0x5f, 0xd0,
    0x0, 0x38, 0xff, 0xb0, 0x5, 0xff, 0xff, 0xff,
    0xff, 0xa0, 0x0, 0x5f, 0xff, 0xff, 0xd9, 0x30,
    0x0, 0x0,

    /* U+0045 "E" */
    0x5f, 0xff, 0xff, 0xff, 0xfd, 0x5, 0xff, 0xff,
    0xff, 0xff, 0xd0, 0x5f, 0xd0, 0x0, 0x0, 0x0,
    0x5, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xd0,
    0x0, 0x0, 0x0, 0x5, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xff, 0xff, 0xff, 0xf1, 0x5, 0xff,
    0xff, 0xff, 0xff, 0x10, 0x5f, 0xd0, 0x0, 0x0,
    0x0, 0x5, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xd0, 0x0, 0x0, 0x0, 0x5, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xd0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0x5f, 0xff, 0xff,
    0xff, 0xff, 0x10,

    /* U+0046 "F" */
    0x5f, 0xff, 0xff, 0xff, 0xfd, 0x5f, 0xff, 0xff,
    0xff, 0xfd, 0x5f, 0xd0, 0x0, 0x0, 0x0, 0x5f,
    0xd0, 0x0, 0x0, 0x0, 0x5f, 0xd0, 0x0, 0x0,
    0x0, 0x5f, 0xd0, 0x0, 0x0, 0x0, 0x5f, 0xd0,
    0x0, 0x0, 0x0, 0x5f, 0xff, 0xff, 0xff, 0xf1,
    0x5f, 0xff, 0xff, 0xff, 0xf1, 0x5f, 0xd0, 0x0,
    0x0, 0x0, 0x5f, 0xd0, 0x0, 0x0, 0x0, 0x5f,
    0xd0, 0x0, 0x0, 0x0, 0x5f, 0xd0, 0x0, 0x0,
    0x0, 0x5f, 0xd0, 0x0, 0x0, 0x0, 0x5f, 0xd0,
    0x0, 0x0, 0x0,

    /* U+0047 "G" */
    0x0, 0x0, 0x18, 0xcf, 0xfc, 0x71, 0x0, 0x0,
    0x5, 0xff, 0xff, 0xff, 0xfd, 0x20, 0x0, 0x5f,
    0xfb, 0x41, 0x15, 0xdf, 0xd0, 0x1, 0xff, 0x90,
    0x0, 0x0, 0x1c, 0x60, 0x7, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xf3, 0x0, 0x0, 0xef, 0xff, 0xf3,
    0xf, 0xf4, 0x0, 0x0, 0xdf, 0xff, 0xf3, 0xc,
    0xf8, 0x0, 0x0, 0x0, 0xe, 0xf3, 0x7, 0xfe,
    0x0, 0x0, 0x0, 0xe, 0xf3, 0x1, 0xff, 0xa0,
    0x0, 0x0, 0xe, 0xf3, 0x0, 0x5f, 0xfc, 0x51,
    0x13, 0xaf, 0xf3, 0x0, 0x5, 0xff, 0xff, 0xff,
    0xff, 0x70, 0x0, 0x0, 0x18, 0xce, 0xfd, 0x92,
    0x0,

    /* U+0048 "H" */
    0x5f, 0xd0, 0x0, 0x0, 0x0, 0xbf, 0x65, 0xfd,
    0x0, 0x0, 0x0, 0xb, 0xf6, 0x5f, 0xd0, 0x0,
    0x0, 0x0, 0xbf, 0x65, 0xfd, 0x0, 0x0, 0x0,
    0xb, 0xf6, 0x5f, 0xd0, 0x0, 0x0, 0x0, 0xbf,
    0x65, 0xfd, 0x0, 0x0, 0x0, 0xb, 0xf6, 0x5f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x65, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf6, 0x5f, 0xd0, 0x0, 0x0,
    0x0, 0xbf, 0x65, 0xfd, 0x0, 0x0, 0x0, 0xb,
    0xf6, 0x5f, 0xd0, 0x0, 0x0, 0x0, 0xbf, 0x65,
    0xfd, 0x0, 0x0, 0x0, 0xb, 0xf6, 0x5f, 0xd0,
    0x0, 0x0, 0x0, 0xbf, 0x65, 0xfd, 0x0, 0x0,
    0x0, 0xb, 0xf6, 0x5f, 0xd0, 0x0, 0x0, 0x0,
    0xbf, 0x60,

    /* U+0049 "I" */
    0x5f, 0xd5, 0xfd, 0x5f, 0xd5, 0xfd, 0x5f, 0xd5,
    0xfd, 0x5f, 0xd5, 0xfd, 0x5f, 0xd5, 0xfd, 0x5f,
    0xd5, 0xfd, 0x5f, 0xd5, 0xfd, 0x5f, 0xd0,

    /* U+004A "J" */
    0x0, 0x0, 0x1, 0xff, 0x0, 0x0, 0x0, 0x1f,
    0xf0, 0x0, 0x0, 0x1, 0xff, 0x0, 0x0, 0x0,
    0x1f, 0xf0, 0x0, 0x0, 0x1, 0xff, 0x0, 0x0,
    0x0, 0x1f, 0xf0, 0x0, 0x0, 0x1, 0xff, 0x0,
    0x0, 0x0, 0x1f, 0xf0, 0x0, 0x0, 0x1, 0xff,
    0x0, 0x0, 0x0, 0x1f, 0xf0, 0x0, 0x0, 0x2,
    0xff, 0x2, 0x90, 0x0, 0x5f, 0xe0, 0xdf, 0x81,
    0x3d, 0xfa, 0x5, 0xff, 0xff, 0xff, 0x20, 0x5,
    0xcf, 0xfa, 0x20, 0x0,

    /* U+004B "K" */
    0x5f, 0xd0, 0x0, 0x0, 0xc, 0xfd, 0x15, 0xfd,
    0x0, 0x0, 0xb, 0xfe, 0x10, 0x5f, 0xd0, 0x0,
    0x9, 0xfe, 0x20, 0x5, 0xfd, 0x0, 0x7, 0xff,
    0x30, 0x0, 0x5f, 0xd0, 0x6, 0xff, 0x40, 0x0,
    0x5, 0xfd, 0x4, 0xff, 0x40, 0x0, 0x0, 0x5f,
    0xd3, 0xff, 0xc0, 0x0, 0x0, 0x5, 0xfe, 0xef,
    0xff, 0x60, 0x0, 0x0, 0x5f, 0xff, 0x89, 0xff,
    0x20, 0x0, 0x5, 0xff, 0x90, 0xd, 0xfd, 0x0,
    0x0, 0x5f, 0xd0, 0x0, 0x2f, 0xf9, 0x0, 0x5,
    0xfd, 0x0, 0x0, 0x6f, 0xf5, 0x0, 0x5f, 0xd0,
    0x0, 0x0, 0xaf, 0xf2, 0x5, 0xfd, 0x0, 0x0,
    0x1, 0xef, 0xc0, 0x5f, 0xd0, 0x0, 0x0, 0x3,
    0xff, 0x80,

    /* U+004C "L" */
    0x5f, 0xd0, 0x0, 0x0, 0x0, 0x5f, 0xd0, 0x0,
    0x0, 0x0, 0x5f, 0xd0, 0x0, 0x0, 0x0, 0x5f,
    0xd0, 0x0, 0x0, 0x0, 0x5f, 0xd0, 0x0, 0x0,
    0x0, 0x5f, 0xd0, 0x0, 0x0, 0x0, 0x5f, 0xd0,
    0x0, 0x0, 0x0, 0x5f, 0xd0, 0x0, 0x0, 0x0,
    0x5f, 0xd0, 0x0, 0x0, 0x0, 0x5f, 0xd0, 0x0,
    0x0, 0x0, 0x5f, 0xd0, 0x0, 0x0, 0x0, 0x5f,
    0xd0, 0x0, 0x0, 0x0, 0x5f, 0xd0, 0x0, 0x0,
    0x0, 0x5f, 0xff, 0xff, 0xff, 0xfd, 0x5f, 0xff,
    0xff, 0xff, 0xfd,

    /* U+004D "M" */
    0x5f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xb5,
    0xff, 0x60, 0x0, 0x0, 0x0, 0xe, 0xfb, 0x5f,
    0xfe, 0x10, 0x0, 0x0, 0x8, 0xff, 0xb5, 0xff,
    0xfa, 0x0, 0x0, 0x2, 0xff, 0xfb, 0x5f, 0xce,
    0xf4, 0x0, 0x0, 0xcf, 0xcf, 0xb5, 0xfb, 0x6f,
    0xd0, 0x0, 0x6f, 0xc6, 0xfb, 0x5f, 0xb0, 0xcf,
    0x70, 0x1e, 0xf2, 0x6f, 0xb5, 0xfb, 0x2, 0xff,
    0x29, 0xf8, 0x6, 0xfb, 0x5f, 0xb0, 0x8, 0xfd,
    0xfd, 0x0, 0x6f, 0xb5, 0xfb, 0x0, 0xd, 0xff,
    0x40, 0x6, 0xfb, 0x5f, 0xb0, 0x0, 0x4f, 0xa0,
    0x0, 0x6f, 0xb5, 0xfb, 0x0, 0x0, 0x10, 0x0,
    0x6, 0xfb, 0x5f, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xb5, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xfb, 0x5f, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xb0,

    /* U+004E "N" */
    0x5f, 0xc0, 0x0, 0x0, 0x0, 0xbf, 0x45, 0xff,
    0x80, 0x0, 0x0, 0xb, 0xf4, 0x5f, 0xff, 0x30,
    0x0, 0x0, 0xbf, 0x45, 0xff, 0xfd, 0x0, 0x0,
    0xb, 0xf4, 0x5f, 0xbd, 0xf9, 0x0, 0x0, 0xbf,
    0x45, 0xfb, 0x2f, 0xf4, 0x0, 0xb, 0xf4, 0x5f,
    0xb0, 0x7f, 0xe1, 0x0, 0xbf, 0x45, 0xfb, 0x0,
    0xbf, 0xb0, 0xb, 0xf4, 0x5f, 0xb0, 0x1, 0xef,
    0x60, 0xbf, 0x45, 0xfb, 0x0, 0x5, 0xff, 0x2b,
    0xf4, 0x5f, 0xb0, 0x0, 0xa, 0xfc, 0xcf, 0x45,
    0xfb, 0x0, 0x0, 0x1e, 0xff, 0xf4, 0x5f, 0xb0,
    0x0, 0x0, 0x4f, 0xff, 0x45, 0xfb, 0x0, 0x0,
    0x0, 0x9f, 0xf4, 0x5f, 0xb0, 0x0, 0x0, 0x0,
    0xdf, 0x40,

    /* U+004F "O" */
    0x0, 0x0, 0x29, 0xdf, 0xeb, 0x50, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0xff, 0xff, 0xb1, 0x0, 0x0,
    0x7f, 0xfa, 0x31, 0x27, 0xff, 0xc0, 0x0, 0x2f,
    0xf8, 0x0, 0x0, 0x3, 0xff, 0x70, 0x8, 0xfd,
    0x0, 0x0, 0x0, 0x7, 0xfe, 0x0, 0xdf, 0x70,
    0x0, 0x0, 0x0, 0x2f, 0xf3, 0xf, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0xef, 0x50, 0xff, 0x30, 0x0,
    0x0, 0x0, 0xd, 0xf6, 0xf, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0xef, 0x50, 0xdf, 0x80, 0x0, 0x0,
    0x0, 0x2f, 0xf3, 0x8, 0xfd, 0x0, 0x0, 0x0,
    0x7, 0xfe, 0x0, 0x2f, 0xf8, 0x0, 0x0, 0x3,
    0xff, 0x70, 0x0, 0x7f, 0xfa, 0x31, 0x27, 0xff,
    0xc0, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xc1,
    0x0, 0x0, 0x0, 0x39, 0xdf, 0xeb, 0x50, 0x0,
    0x0,

    /* U+0050 "P" */
    0x5f, 0xff, 0xff, 0xea, 0x30, 0x5, 0xff, 0xee,
    0xff, 0xff, 0x60, 0x5f, 0xd0, 0x0, 0x2a, 0xff,
    0x35, 0xfd, 0x0, 0x0, 0xd, 0xf8, 0x5f, 0xd0,
    0x0, 0x0, 0xaf, 0xa5, 0xfd, 0x0, 0x0, 0xd,
    0xf8, 0x5f, 0xd0, 0x0, 0x2a, 0xff, 0x35, 0xff,
    0xff, 0xff, 0xff, 0x70, 0x5f, 0xff, 0xff, 0xea,
    0x30, 0x5, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xd0, 0x0, 0x0, 0x0, 0x5, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xd0, 0x0, 0x0, 0x0, 0x5,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xd0, 0x0,
    0x0, 0x0, 0x0,

    /* U+0051 "Q" */
    0x0, 0x0, 0x29, 0xdf, 0xeb, 0x50, 0x0, 0x0,
    0x0, 0x7, 0xff, 0xff, 0xff, 0xfb, 0x10, 0x0,
    0x0, 0x7f, 0xfa, 0x31, 0x27, 0xff, 0xc0, 0x0,
    0x2, 0xff, 0x80, 0x0, 0x0, 0x3f, 0xf7, 0x0,
    0x8, 0xfd, 0x0, 0x0, 0x0, 0x7, 0xfe, 0x0,
    0xd, 0xf7, 0x0, 0x0, 0x0, 0x2, 0xff, 0x30,
    0xf, 0xf4, 0x0, 0x0, 0x0, 0x0, 0xef, 0x50,
    0xf, 0xf3, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x60,
    0xf, 0xf4, 0x0, 0x0, 0x0, 0x0, 0xef, 0x40,
    0xd, 0xf8, 0x0, 0x0, 0x0, 0x2, 0xff, 0x30,
    0x8, 0xfd, 0x0, 0x0, 0x0, 0x7, 0xfd, 0x0,
    0x2, 0xff, 0x80, 0x0, 0x0, 0x3f, 0xf7, 0x0,
    0x0, 0x7f, 0xfa, 0x31, 0x27, 0xff, 0xc0, 0x0,
    0x0, 0x8, 0xff, 0xff, 0xff, 0xfb, 0x10, 0x0,
    0x0, 0x0, 0x39, 0xdf, 0xff, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xfc, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xdf, 0xe3,

    /* U+0052 "R" */
    0x5f, 0xff, 0xff, 0xea, 0x30, 0x0, 0x5, 0xff,
    0xee, 0xff, 0xff, 0x60, 0x0, 0x5f, 0xd0, 0x0,
    0x2a, 0xff, 0x20, 0x5, 0xfd, 0x0, 0x0, 0xd,
    0xf8, 0x0, 0x5f, 0xd0, 0x0, 0x0, 0xaf, 0xa0,
    0x5, 0xfd, 0x0, 0x0, 0xd, 0xf8, 0x0, 0x5f,
    0xd0, 0x0, 0x2a, 0xff, 0x20, 0x5, 0xff, 0xee,
    0xff, 0xff, 0x60, 0x0, 0x5f, 0xff, 0xff, 0xfe,
    0x30, 0x0, 0x5, 0xfd, 0x0, 0x3f, 0xf5, 0x0,
    0x0, 0x5f, 0xd0, 0x0, 0x7f, 0xe1, 0x0, 0x5,
    0xfd, 0x0, 0x0, 0xcf, 0xb0, 0x0, 0x5f, 0xd0,
    0x0, 0x2, 0xff, 0x60, 0x5, 0xfd, 0x0, 0x0,
    0x7, 0xff, 0x20, 0x5f, 0xd0, 0x0, 0x0, 0xc,
    0xfc, 0x0,

    /* U+0053 "S" */
    0x0, 0x18, 0xdf, 0xec, 0x70, 0x0, 0x2, 0xef,
    0xff, 0xff, 0xfc, 0x0, 0xb, 0xfd, 0x41, 0x16,
    0xff, 0x80, 0xf, 0xf4, 0x0, 0x0, 0x3e, 0x50,
    0xf, 0xf4, 0x0, 0x0, 0x0, 0x0, 0xc, 0xfe,
    0x40, 0x0, 0x0, 0x0, 0x2, 0xef, 0xfd, 0x83,
    0x0, 0x0, 0x0, 0x7, 0xcf, 0xff, 0xd5, 0x0,
    0x0, 0x0, 0x2, 0x7d, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xe0, 0x5, 0x20, 0x0, 0x0,
    0x3f, 0xf0, 0x8f, 0xb0, 0x0, 0x0, 0x5f, 0xf0,
    0x2f, 0xfc, 0x41, 0x14, 0xef, 0xa0, 0x5, 0xff,
    0xff, 0xff, 0xfd, 0x10, 0x0, 0x18, 0xcf, 0xfd,
    0x80, 0x0,

    /* U+0054 "T" */
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xf6, 0xcf, 0xff,
    0xff, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x4f, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xe0,
    0x0, 0x0,

    /* U+0055 "U" */
    0x7f, 0xb0, 0x0, 0x0, 0x0, 0xef, 0x47, 0xfb,
    0x0, 0x0, 0x0, 0xe, 0xf4, 0x7f, 0xb0, 0x0,
    0x0, 0x0, 0xef, 0x47, 0xfb, 0x0, 0x0, 0x0,
    0xe, 0xf4, 0x7f, 0xb0, 0x0, 0x0, 0x0, 0xef,
    0x47, 0xfb, 0x0, 0x0, 0x0, 0xe, 0xf4, 0x7f,
    0xb0, 0x0, 0x0, 0x0, 0xef, 0x47, 0xfb, 0x0,
    0x0, 0x0, 0xe, 0xf4, 0x7f, 0xb0, 0x0, 0x0,
    0x0, 0xef, 0x46, 0xfc, 0x0, 0x0, 0x0, 0xf,
    0xf3, 0x4f, 0xf0, 0x0, 0x0, 0x2, 0xff, 0x10,
    0xff, 0x70, 0x0, 0x0, 0xaf, 0xc0, 0x8, 0xff,
    0x82, 0x2, 0xaf, 0xf4, 0x0, 0xa, 0xff, 0xff,
    0xff, 0xf7, 0x0, 0x0, 0x5, 0xbe, 0xfe, 0xa3,
    0x0, 0x0,

    /* U+0056 "V" */
    0xaf, 0xb0, 0x0, 0x0, 0x0, 0xf, 0xf3, 0x4f,
    0xf1, 0x0, 0x0, 0x0, 0x5f, 0xd0, 0xe, 0xf7,
    0x0, 0x0, 0x0, 0xbf, 0x70, 0x7, 0xfd, 0x0,
    0x0, 0x1, 0xff, 0x10, 0x1, 0xff, 0x30, 0x0,
    0x6, 0xfb, 0x0, 0x0, 0xbf, 0x90, 0x0, 0xc,
    0xf5, 0x0, 0x0, 0x5f, 0xe0, 0x0, 0x1f, 0xf0,
    0x0, 0x0, 0xe, 0xf4, 0x0, 0x7f, 0x90, 0x0,
    0x0, 0x8, 0xfa, 0x0, 0xdf, 0x30, 0x0, 0x0,
    0x2, 0xff, 0x12, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0x68, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xcd, 0xf1, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0x0, 0x0,
    0x0,

    /* U+0057 "W" */
    0xbf, 0x90, 0x0, 0x0, 0x2f, 0xe0, 0x0, 0x0,
    0xc, 0xf4, 0x5f, 0xd0, 0x0, 0x0, 0x6f, 0xf3,
    0x0, 0x0, 0x1f, 0xf0, 0x1f, 0xf2, 0x0, 0x0,
    0xbf, 0xf8, 0x0, 0x0, 0x5f, 0xa0, 0xb, 0xf7,
    0x0, 0x0, 0xff, 0xfd, 0x0, 0x0, 0xaf, 0x50,
    0x6, 0xfc, 0x0, 0x4, 0xfb, 0xff, 0x20, 0x0,
    0xef, 0x10, 0x1, 0xff, 0x10, 0x8, 0xf6, 0xaf,
    0x60, 0x2, 0xfc, 0x0, 0x0, 0xcf, 0x50, 0xd,
    0xf1, 0x5f, 0xb0, 0x7, 0xf7, 0x0, 0x0, 0x7f,
    0xa0, 0x1f, 0xc0, 0x1f, 0xf0, 0xb, 0xf2, 0x0,
    0x0, 0x2f, 0xe0, 0x6f, 0x70, 0xc, 0xf5, 0xf,
    0xe0, 0x0, 0x0, 0xd, 0xf4, 0xaf, 0x30, 0x7,
    0xfa, 0x4f, 0x90, 0x0, 0x0, 0x8, 0xf9, 0xee,
    0x0, 0x2, 0xfe, 0x9f, 0x40, 0x0, 0x0, 0x3,
    0xff, 0xf9, 0x0, 0x0, 0xdf, 0xff, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xf4, 0x0, 0x0, 0x8f, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xf0, 0x0, 0x0,
    0x3f, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xb0,
    0x0, 0x0, 0xe, 0xf1, 0x0, 0x0,

    /* U+0058 "X" */
    0x4f, 0xf5, 0x0, 0x0, 0x0, 0xaf, 0xc0, 0x9,
    0xfe, 0x10, 0x0, 0x5, 0xff, 0x20, 0x0, 0xdf,
    0xb0, 0x0, 0x1e, 0xf6, 0x0, 0x0, 0x3f, 0xf6,
    0x0, 0xbf, 0xb0, 0x0, 0x0, 0x8, 0xff, 0x16,
    0xfe, 0x10, 0x0, 0x0, 0x0, 0xcf, 0xcf, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x1, 0xef, 0x9f, 0xf7, 0x0, 0x0, 0x0, 0xa,
    0xfc, 0x6, 0xff, 0x30, 0x0, 0x0, 0x5f, 0xf2,
    0x0, 0xbf, 0xd0, 0x0, 0x1, 0xef, 0x60, 0x0,
    0x1e, 0xf9, 0x0, 0xb, 0xfb, 0x0, 0x0, 0x5,
    0xff, 0x40, 0x7f, 0xe1, 0x0, 0x0, 0x0, 0xaf,
    0xe1,

    /* U+0059 "Y" */
    0x9f, 0xe0, 0x0, 0x0, 0x1, 0xef, 0x40, 0xef,
    0x80, 0x0, 0x0, 0x9f, 0xb0, 0x5, 0xff, 0x20,
    0x0, 0x2f, 0xf2, 0x0, 0xc, 0xfb, 0x0, 0xa,
    0xf9, 0x0, 0x0, 0x2f, 0xf4, 0x3, 0xfe, 0x10,
    0x0, 0x0, 0x8f, 0xd0, 0xcf, 0x60, 0x0, 0x0,
    0x0, 0xef, 0xbf, 0xd0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0xc, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x9, 0xf8, 0x0,
    0x0, 0x0,

    /* U+005A "Z" */
    0xf, 0xff, 0xff, 0xff, 0xff, 0xe0, 0xe, 0xff,
    0xff, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x1e, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xc0, 0x0, 0x0, 0x0,
    0x4, 0xff, 0x20, 0x0, 0x0, 0x0, 0xd, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xd0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0x40, 0x0, 0x0, 0x0, 0xc,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xe1, 0x0,
    0x0, 0x0, 0x1, 0xff, 0x60, 0x0, 0x0, 0x0,
    0xb, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0xff, 0xff, 0xff, 0xf3, 0x9f, 0xff, 0xff, 0xff,
    0xff, 0xf3,

    /* U+005B "[" */
    0x5f, 0xff, 0xfb, 0x5f, 0xff, 0xfa, 0x5f, 0xb0,
    0x0, 0x5f, 0xb0, 0x0, 0x5f, 0xb0, 0x0, 0x5f,
    0xb0, 0x0, 0x5f, 0xb0, 0x0, 0x5f, 0xb0, 0x0,
    0x5f, 0xb0, 0x0, 0x5f, 0xb0, 0x0, 0x5f, 0xb0,
    0x0, 0x5f, 0xb0, 0x0, 0x5f, 0xb0, 0x0, 0x5f,
    0xb0, 0x0, 0x5f, 0xb0, 0x0, 0x5f, 0xb0, 0x0,
    0x5f, 0xb0, 0x0, 0x5f, 0xff, 0xfa, 0x5f, 0xff,
    0xfb,

    /* U+005C "\\" */
    0x8f, 0x70, 0x0, 0x0, 0x2f, 0xd0, 0x0, 0x0,
    0xc, 0xf3, 0x0, 0x0, 0x7, 0xf9, 0x0, 0x0,
    0x1, 0xfe, 0x0, 0x0, 0x0, 0xbf, 0x40, 0x0,
    0x0, 0x5f, 0xa0, 0x0, 0x0, 0xf, 0xf1, 0x0,
    0x0, 0x9, 0xf6, 0x0, 0x0, 0x3, 0xfc, 0x0,
    0x0, 0x0, 0xef, 0x20, 0x0, 0x0, 0x8f, 0x80,
    0x0, 0x0, 0x2f, 0xd0, 0x0, 0x0, 0xc, 0xf3,
    0x0, 0x0, 0x6, 0xf9,

    /* U+005D "]" */
    0xaf, 0xff, 0xf6, 0x9f, 0xff, 0xf6, 0x0, 0xa,
    0xf6, 0x0, 0xa, 0xf6, 0x0, 0xa, 0xf6, 0x0,
    0xa, 0xf6, 0x0, 0xa, 0xf6, 0x0, 0xa, 0xf6,
    0x0, 0xa, 0xf6, 0x0, 0xa, 0xf6, 0x0, 0xa,
    0xf6, 0x0, 0xa, 0xf6, 0x0, 0xa, 0xf6, 0x0,
    0xa, 0xf6, 0x0, 0xa, 0xf6, 0x0, 0xa, 0xf6,
    0x0, 0xa, 0xf6, 0x9f, 0xff, 0xf6, 0xaf, 0xff,
    0xf6,

    /* U+005E "^" */
    0x0, 0x3, 0xff, 0x10, 0x0, 0x0, 0xa, 0xff,
    0x70, 0x0, 0x0, 0x1f, 0xef, 0xe0, 0x0, 0x0,
    0x8f, 0x8a, 0xf5, 0x0, 0x0, 0xef, 0x13, 0xfb,
    0x0, 0x5, 0xfa, 0x0, 0xdf, 0x20, 0xc, 0xf3,
    0x0, 0x6f, 0x90, 0x3f, 0xc0, 0x0, 0xe, 0xf1,

    /* U+005F "_" */
    0xcc, 0xcc, 0xcc, 0xcc, 0x8f, 0xff, 0xff, 0xff,
    0xfb,

    /* U+0060 "`" */
    0x48, 0x40, 0x2, 0xfe, 0x0, 0x8, 0xf6, 0x0,
    0xd, 0xd0,

    /* U+0061 "a" */
    0x0, 0x8, 0xdf, 0xe9, 0x10, 0x0, 0xdf, 0xfe,
    0xff, 0xd0, 0x5, 0xfa, 0x0, 0x2d, 0xf6, 0x0,
    0x20, 0x0, 0x7, 0xfa, 0x0, 0x4, 0x79, 0x89,
    0xfb, 0x2, 0xdf, 0xff, 0xff, 0xfb, 0xc, 0xfb,
    0x20, 0x17, 0xfb, 0xf, 0xf2, 0x0, 0x6, 0xfb,
    0xe, 0xf4, 0x0, 0xc, 0xfb, 0x7, 0xfe, 0x98,
    0xdf, 0xfb, 0x0, 0x6c, 0xfe, 0xa3, 0xfb,

    /* U+0062 "b" */
    0x8f, 0x80, 0x0, 0x0, 0x0, 0x8, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0x80, 0x0, 0x0, 0x0,
    0x8, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x8f, 0x83,
    0xbf, 0xe9, 0x10, 0x8, 0xfb, 0xff, 0xff, 0xfe,
    0x10, 0x8f, 0xfc, 0x20, 0x3d, 0xfb, 0x8, 0xfe,
    0x10, 0x0, 0x2f, 0xf1, 0x8f, 0xa0, 0x0, 0x0,
    0xdf, 0x58, 0xf8, 0x0, 0x0, 0xc, 0xf6, 0x8f,
    0xa0, 0x0, 0x0, 0xdf, 0x58, 0xfe, 0x0, 0x0,
    0x2f, 0xf1, 0x8f, 0xfb, 0x20, 0x2d, 0xfb, 0x8,
    0xf9, 0xff, 0xff, 0xfe, 0x20, 0x8f, 0x53, 0xbf,
    0xe9, 0x10, 0x0,

    /* U+0063 "c" */
    0x0, 0x5, 0xcf, 0xeb, 0x30, 0x0, 0x9f, 0xff,
    0xff, 0xf3, 0x5, 0xff, 0x60, 0x8, 0xf5, 0xc,
    0xf7, 0x0, 0x0, 0x10, 0xf, 0xf2, 0x0, 0x0,
    0x0, 0x1f, 0xf0, 0x0, 0x0, 0x0, 0xf, 0xf2,
    0x0, 0x0, 0x0, 0xc, 0xf7, 0x0, 0x0, 0x10,
    0x5, 0xff, 0x60, 0x8, 0xf5, 0x0, 0x9f, 0xff,
    0xff, 0xf3, 0x0, 0x5, 0xcf, 0xeb, 0x30,

    /* U+0064 "d" */
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xd0, 0x0, 0x0,
    0x0, 0x3, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xd0, 0x0, 0x0, 0x0, 0x3, 0xfd, 0x0, 0x7,
    0xdf, 0xd6, 0x3f, 0xd0, 0xb, 0xff, 0xff, 0xfb,
    0xfd, 0x6, 0xff, 0x50, 0x19, 0xff, 0xd0, 0xdf,
    0x70, 0x0, 0xb, 0xfd, 0xf, 0xf2, 0x0, 0x0,
    0x5f, 0xd1, 0xff, 0x0, 0x0, 0x4, 0xfd, 0xf,
    0xf1, 0x0, 0x0, 0x5f, 0xd0, 0xdf, 0x50, 0x0,
    0x9, 0xfd, 0x6, 0xfd, 0x20, 0x4, 0xff, 0xd0,
    0xb, 0xff, 0xbc, 0xfb, 0xfd, 0x0, 0x7, 0xdf,
    0xd7, 0xf, 0xd0,

    /* U+0065 "e" */
    0x0, 0x6, 0xcf, 0xea, 0x20, 0x0, 0xa, 0xff,
    0xef, 0xff, 0x30, 0x6, 0xfe, 0x30, 0x9, 0xfd,
    0x0, 0xdf, 0x60, 0x0, 0xf, 0xf3, 0xf, 0xfa,
    0x99, 0x99, 0xef, 0x51, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0xf, 0xf1, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0x60, 0x0, 0x1, 0x0, 0x6, 0xff, 0x50, 0x4,
    0xe9, 0x0, 0xa, 0xff, 0xfe, 0xff, 0x70, 0x0,
    0x5, 0xcf, 0xfc, 0x50, 0x0,

    /* U+0066 "f" */
    0x0, 0x4, 0xcf, 0xd7, 0x0, 0x3f, 0xff, 0xf8,
    0x0, 0xaf, 0xa0, 0x10, 0x0, 0xdf, 0x40, 0x0,
    0x0, 0xdf, 0x30, 0x0, 0xbf, 0xff, 0xff, 0xe0,
    0x8d, 0xff, 0xdd, 0xb0, 0x0, 0xef, 0x30, 0x0,
    0x0, 0xef, 0x30, 0x0, 0x0, 0xef, 0x30, 0x0,
    0x0, 0xef, 0x30, 0x0, 0x0, 0xef, 0x30, 0x0,
    0x0, 0xef, 0x30, 0x0, 0x0, 0xef, 0x30, 0x0,
    0x0, 0xef, 0x30, 0x0, 0x0, 0xef, 0x30, 0x0,

    /* U+0067 "g" */
    0x0, 0x7, 0xdf, 0xd6, 0xf, 0xd0, 0xb, 0xff,
    0xff, 0xf9, 0xfd, 0x6, 0xff, 0x50, 0x18, 0xff,
    0xd0, 0xdf, 0x70, 0x0, 0xb, 0xfd, 0xf, 0xf2,
    0x0, 0x0, 0x5f, 0xd1, 0xff, 0x0, 0x0, 0x4,
    0xfd, 0xf, 0xf2, 0x0, 0x0, 0x5f, 0xd0, 0xdf,
    0x60, 0x0, 0xa, 0xfd, 0x6, 0xfe, 0x40, 0x8,
    0xff, 0xd0, 0xb, 0xff, 0xff, 0xfb, 0xfd, 0x0,
    0x7, 0xdf, 0xd6, 0x4f, 0xc0, 0x0, 0x0, 0x0,
    0x5, 0xfb, 0x0, 0x71, 0x0, 0x0, 0xaf, 0x80,
    0x8f, 0xc2, 0x1, 0x8f, 0xf2, 0x1, 0xdf, 0xff,
    0xff, 0xf6, 0x0, 0x1, 0x8d, 0xfe, 0xb3, 0x0,

    /* U+0068 "h" */
    0x8f, 0x80, 0x0, 0x0, 0x0, 0x8f, 0x80, 0x0,
    0x0, 0x0, 0x8f, 0x80, 0x0, 0x0, 0x0, 0x8f,
    0x80, 0x0, 0x0, 0x0, 0x8f, 0x84, 0xcf, 0xe9,
    0x0, 0x8f, 0xcf, 0xff, 0xff, 0xa0, 0x8f, 0xfa,
    0x10, 0x7f, 0xf2, 0x8f, 0xd0, 0x0, 0xd, 0xf6,
    0x8f, 0x90, 0x0, 0xa, 0xf7, 0x8f, 0x80, 0x0,
    0x9, 0xf7, 0x8f, 0x80, 0x0, 0x9, 0xf7, 0x8f,
    0x80, 0x0, 0x9, 0xf7, 0x8f, 0x80, 0x0, 0x9,
    0xf7, 0x8f, 0x80, 0x0, 0x9, 0xf7, 0x8f, 0x80,
    0x0, 0x9, 0xf7,

    /* U+0069 "i" */
    0x6f, 0x6b, 0xfb, 0x3b, 0x30, 0x0, 0x8f, 0x88,
    0xf8, 0x8f, 0x88, 0xf8, 0x8f, 0x88, 0xf8, 0x8f,
    0x88, 0xf8, 0x8f, 0x88, 0xf8, 0x8f, 0x80,

    /* U+006A "j" */
    0x0, 0x0, 0x7f, 0x60, 0x0, 0xc, 0xfb, 0x0,
    0x0, 0x4b, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0x80, 0x0, 0x8, 0xf8, 0x0, 0x0, 0x8f,
    0x80, 0x0, 0x8, 0xf8, 0x0, 0x0, 0x8f, 0x80,
    0x0, 0x8, 0xf8, 0x0, 0x0, 0x8f, 0x80, 0x0,
    0x8, 0xf8, 0x0, 0x0, 0x8f, 0x80, 0x0, 0x8,
    0xf8, 0x0, 0x0, 0x8f, 0x80, 0x0, 0x8, 0xf8,
    0x0, 0x0, 0xaf, 0x60, 0x30, 0x2f, 0xf3, 0x3f,
    0xff, 0xfb, 0x1, 0xbe, 0xe9, 0x0,

    /* U+006B "k" */
    0x8f, 0x80, 0x0, 0x0, 0x0, 0x8f, 0x80, 0x0,
    0x0, 0x0, 0x8f, 0x80, 0x0, 0x0, 0x0, 0x8f,
    0x80, 0x0, 0x0, 0x0, 0x8f, 0x80, 0x0, 0x6f,
    0xe1, 0x8f, 0x80, 0x3, 0xff, 0x30, 0x8f, 0x80,
    0x2e, 0xf5, 0x0, 0x8f, 0x80, 0xdf, 0x70, 0x0,
    0x8f, 0x8a, 0xfd, 0x0, 0x0, 0x8f, 0xef, 0xff,
    0x40, 0x0, 0x8f, 0xfd, 0x9f, 0xe0, 0x0, 0x8f,
    0xf2, 0xd, 0xf9, 0x0, 0x8f, 0x80, 0x4, 0xff,
    0x40, 0x8f, 0x80, 0x0, 0x9f, 0xd0, 0x8f, 0x80,
    0x0, 0xe, 0xf9,

    /* U+006C "l" */
    0x8f, 0x88, 0xf8, 0x8f, 0x88, 0xf8, 0x8f, 0x88,
    0xf8, 0x8f, 0x88, 0xf8, 0x8f, 0x88, 0xf8, 0x8f,
    0x88, 0xf8, 0x8f, 0x88, 0xf8, 0x8f, 0x80,

    /* U+006D "m" */
    0x8f, 0x46, 0xdf, 0xc2, 0x4, 0xcf, 0xd4, 0x8,
    0xfb, 0xff, 0xff, 0xe4, 0xff, 0xff, 0xf2, 0x8f,
    0xf8, 0x3, 0xef, 0xfa, 0x11, 0xdf, 0x98, 0xfc,
    0x0, 0x8, 0xff, 0x0, 0x5, 0xfc, 0x8f, 0x90,
    0x0, 0x5f, 0xc0, 0x0, 0x2f, 0xe8, 0xf8, 0x0,
    0x5, 0xfb, 0x0, 0x1, 0xfe, 0x8f, 0x80, 0x0,
    0x5f, 0xb0, 0x0, 0x1f, 0xe8, 0xf8, 0x0, 0x5,
    0xfb, 0x0, 0x1, 0xfe, 0x8f, 0x80, 0x0, 0x5f,
    0xb0, 0x0, 0x1f, 0xe8, 0xf8, 0x0, 0x5, 0xfb,
    0x0, 0x1, 0xfe, 0x8f, 0x80, 0x0, 0x5f, 0xb0,
    0x0, 0x1f, 0xe0,

    /* U+006E "n" */
    0x8f, 0x44, 0xcf, 0xe9, 0x0, 0x8f, 0x9f, 0xff,
    0xff, 0xb0, 0x8f, 0xfa, 0x10, 0x7f, 0xf2, 0x8f,
    0xd0, 0x0, 0xd, 0xf6, 0x8f, 0x90, 0x0, 0xa,
    0xf7, 0x8f, 0x80, 0x0, 0x9, 0xf7, 0x8f, 0x80,
    0x0, 0x9, 0xf7, 0x8f, 0x80, 0x0, 0x9, 0xf7,
    0x8f, 0x80, 0x0, 0x9, 0xf7, 0x8f, 0x80, 0x0,
    0x9, 0xf7, 0x8f, 0x80, 0x0, 0x9, 0xf7,

    /* U+006F "o" */
    0x0, 0x4, 0xbf, 0xeb, 0x30, 0x0, 0x0, 0x8f,
    0xff, 0xff, 0xf6, 0x0, 0x5, 0xff, 0x60, 0x7,
    0xff, 0x30, 0xc, 0xf8, 0x0, 0x0, 0xaf, 0xa0,
    0xf, 0xf2, 0x0, 0x0, 0x4f, 0xe0, 0x1f, 0xf0,
    0x0, 0x0, 0x2f, 0xf0, 0xf, 0xf2, 0x0, 0x0,
    0x4f, 0xe0, 0xc, 0xf8, 0x0, 0x0, 0xaf, 0xa0,
    0x5, 0xff, 0x60, 0x7, 0xff, 0x30, 0x0, 0x8f,
    0xff, 0xff, 0xf6, 0x0, 0x0, 0x5, 0xcf, 0xeb,
    0x30, 0x0,

    /* U+0070 "p" */
    0x8f, 0x43, 0xbf, 0xe9, 0x10, 0x8, 0xf9, 0xff,
    0xff, 0xfe, 0x10, 0x8f, 0xfb, 0x20, 0x3d, 0xfb,
    0x8, 0xfe, 0x10, 0x0, 0x3f, 0xf1, 0x8f, 0xa0,
    0x0, 0x0, 0xdf, 0x58, 0xf8, 0x0, 0x0, 0xc,
    0xf6, 0x8f, 0xa0, 0x0, 0x0, 0xdf, 0x58, 0xfe,
    0x10, 0x0, 0x2f, 0xf1, 0x8f, 0xfc, 0x20, 0x3d,
    0xfb, 0x8, 0xfb, 0xff, 0xff, 0xfe, 0x20, 0x8f,
    0x83, 0xbf, 0xe9, 0x10, 0x8, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0x80, 0x0, 0x0, 0x0, 0x8,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x8f, 0x80, 0x0,
    0x0, 0x0, 0x8, 0xf8, 0x0, 0x0, 0x0, 0x0,

    /* U+0071 "q" */
    0x0, 0x7, 0xdf, 0xd6, 0xf, 0xd0, 0xb, 0xff,
    0xff, 0xf9, 0xfd, 0x6, 0xff, 0x50, 0x18, 0xff,
    0xd0, 0xdf, 0x70, 0x0, 0xb, 0xfd, 0xf, 0xf2,
    0x0, 0x0, 0x5f, 0xd1, 0xff, 0x0, 0x0, 0x4,
    0xfd, 0xf, 0xf2, 0x0, 0x0, 0x5f, 0xd0, 0xdf,
    0x70, 0x0, 0xa, 0xfd, 0x6, 0xff, 0x50, 0x18,
    0xff, 0xd0, 0xb, 0xff, 0xff, 0xfb, 0xfd, 0x0,
    0x7, 0xdf, 0xd6, 0x3f, 0xd0, 0x0, 0x0, 0x0,
    0x3, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xd0,
    0x0, 0x0, 0x0, 0x3, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xd0, 0x0, 0x0, 0x0, 0x3, 0xfd,

    /* U+0072 "r" */
    0x8f, 0x46, 0xef, 0x98, 0xfa, 0xff, 0xf6, 0x8f,
    0xfa, 0x10, 0x8, 0xfe, 0x0, 0x0, 0x8f, 0xa0,
    0x0, 0x8, 0xf8, 0x0, 0x0, 0x8f, 0x80, 0x0,
    0x8, 0xf8, 0x0, 0x0, 0x8f, 0x80, 0x0, 0x8,
    0xf8, 0x0, 0x0, 0x8f, 0x80, 0x0, 0x0,

    /* U+0073 "s" */
    0x0, 0x6d, 0xfe, 0xa2, 0x0, 0x8f, 0xfe, 0xff,
    0xe2, 0xf, 0xf4, 0x0, 0x9e, 0x41, 0xff, 0x20,
    0x0, 0x10, 0xc, 0xff, 0x94, 0x0, 0x0, 0x8,
    0xef, 0xfe, 0x60, 0x0, 0x0, 0x39, 0xff, 0x50,
    0x31, 0x0, 0x8, 0xfa, 0x4f, 0xb1, 0x0, 0xbf,
    0x90, 0xdf, 0xfd, 0xff, 0xf2, 0x0, 0x8d, 0xfe,
    0xa2, 0x0,

    /* U+0074 "t" */
    0x1, 0xff, 0x0, 0x0, 0x1, 0xff, 0x0, 0x0,
    0x1, 0xff, 0x0, 0x0, 0xdf, 0xff, 0xff, 0xf0,
    0xad, 0xff, 0xdd, 0xd0, 0x1, 0xff, 0x0, 0x0,
    0x1, 0xff, 0x0, 0x0, 0x1, 0xff, 0x0, 0x0,
    0x1, 0xff, 0x0, 0x0, 0x1, 0xff, 0x0, 0x0,
    0x0, 0xff, 0x0, 0x0, 0x0, 0xef, 0x70, 0x20,
    0x0, 0x9f, 0xff, 0xf4, 0x0, 0x8, 0xef, 0xd4,

    /* U+0075 "u" */
    0xbf, 0x50, 0x0, 0xc, 0xf4, 0xbf, 0x50, 0x0,
    0xc, 0xf4, 0xbf, 0x50, 0x0, 0xc, 0xf4, 0xbf,
    0x50, 0x0, 0xc, 0xf4, 0xbf, 0x50, 0x0, 0xc,
    0xf4, 0xbf, 0x50, 0x0, 0xc, 0xf4, 0xaf, 0x60,
    0x0, 0xd, 0xf4, 0x9f, 0x80, 0x0, 0x1f, 0xf4,
    0x6f, 0xe1, 0x0, 0xaf, 0xf4, 0xe, 0xff, 0xce,
    0xfc, 0xf4, 0x2, 0xbf, 0xfb, 0x38, 0xf4,

    /* U+0076 "v" */
    0xaf, 0xa0, 0x0, 0x0, 0xdf, 0x43, 0xff, 0x10,
    0x0, 0x2f, 0xe0, 0xd, 0xf6, 0x0, 0x8, 0xf8,
    0x0, 0x7f, 0xc0, 0x0, 0xdf, 0x20, 0x1, 0xff,
    0x20, 0x3f, 0xb0, 0x0, 0xa, 0xf8, 0x9, 0xf5,
    0x0, 0x0, 0x3f, 0xe0, 0xee, 0x0, 0x0, 0x0,
    0xdf, 0x8f, 0x90, 0x0, 0x0, 0x7, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0x1f, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0x60, 0x0, 0x0,

    /* U+0077 "w" */
    0xaf, 0x70, 0x0, 0xf, 0xf0, 0x0, 0x6, 0xf8,
    0x5f, 0xc0, 0x0, 0x4f, 0xf5, 0x0, 0xb, 0xf3,
    0xf, 0xf1, 0x0, 0x8f, 0xfa, 0x0, 0xf, 0xd0,
    0x9, 0xf6, 0x0, 0xde, 0xff, 0x0, 0x5f, 0x80,
    0x4, 0xfc, 0x2, 0xf9, 0xaf, 0x40, 0xaf, 0x30,
    0x0, 0xef, 0x17, 0xf4, 0x5f, 0xa0, 0xed, 0x0,
    0x0, 0x9f, 0x6c, 0xe0, 0xf, 0xe4, 0xf8, 0x0,
    0x0, 0x3f, 0xcf, 0x90, 0x9, 0xfc, 0xf2, 0x0,
    0x0, 0xd, 0xff, 0x40, 0x4, 0xff, 0xd0, 0x0,
    0x0, 0x8, 0xfe, 0x0, 0x0, 0xef, 0x80, 0x0,
    0x0, 0x2, 0xf9, 0x0, 0x0, 0x9f, 0x20, 0x0,

    /* U+0078 "x" */
    0x7f, 0xe1, 0x0, 0x9, 0xfb, 0x0, 0xcf, 0xa0,
    0x4, 0xfe, 0x10, 0x2, 0xff, 0x51, 0xef, 0x50,
    0x0, 0x6, 0xfe, 0xbf, 0x90, 0x0, 0x0, 0xb,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0x3f, 0xf6, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xe1, 0x0, 0x0, 0x6,
    0xfd, 0xbf, 0xa0, 0x0, 0x2, 0xff, 0x31, 0xff,
    0x50, 0x0, 0xcf, 0x80, 0x6, 0xfe, 0x10, 0x7f,
    0xd0, 0x0, 0xb, 0xfb, 0x0,

    /* U+0079 "y" */
    0xbf, 0x90, 0x0, 0x0, 0xcf, 0x55, 0xff, 0x0,
    0x0, 0x1f, 0xf0, 0xe, 0xf5, 0x0, 0x7, 0xf9,
    0x0, 0x8f, 0xb0, 0x0, 0xcf, 0x30, 0x2, 0xff,
    0x10, 0x2f, 0xd0, 0x0, 0xb, 0xf7, 0x7, 0xf7,
    0x0, 0x0, 0x5f, 0xd0, 0xdf, 0x10, 0x0, 0x0,
    0xef, 0x6f, 0xb0, 0x0, 0x0, 0x8, 0xff, 0xf5,
    0x0, 0x0, 0x0, 0x2f, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0x90, 0x0, 0x0, 0x0, 0xb, 0xf3,
    0x0, 0x0, 0x0, 0x2, 0xfd, 0x0, 0x0, 0x1,
    0x10, 0xbf, 0x60, 0x0, 0x0, 0x9f, 0xff, 0xd0,
    0x0, 0x0, 0x8, 0xef, 0xb1, 0x0, 0x0, 0x0,

    /* U+007A "z" */
    0xf, 0xff, 0xff, 0xff, 0xb0, 0xbb, 0xbb, 0xcf,
    0xf7, 0x0, 0x0, 0x9, 0xfc, 0x0, 0x0, 0x3,
    0xff, 0x20, 0x0, 0x0, 0xdf, 0x70, 0x0, 0x0,
    0x8f, 0xc0, 0x0, 0x0, 0x3f, 0xf3, 0x0, 0x0,
    0xd, 0xf8, 0x0, 0x0, 0x8, 0xfd, 0x0, 0x0,
    0x2, 0xff, 0xdc, 0xcc, 0xcb, 0x6f, 0xff, 0xff,
    0xff, 0xe0,

    /* U+007B "{" */
    0x0, 0x1, 0x9d, 0xf4, 0x0, 0xb, 0xff, 0xf4,
    0x0, 0xf, 0xf7, 0x0, 0x0, 0x1f, 0xf1, 0x0,
    0x0, 0x1f, 0xf0, 0x0, 0x0, 0x1f, 0xf0, 0x0,
    0x0, 0x2f, 0xf0, 0x0, 0x1, 0xaf, 0xc0, 0x0,
    0x4f, 0xfe, 0x30, 0x0, 0x4f, 0xfb, 0x10, 0x0,
    0x15, 0xdf, 0x90, 0x0, 0x0, 0x4f, 0xe0, 0x0,
    0x0, 0x1f, 0xf0, 0x0, 0x0, 0x1f, 0xf0, 0x0,
    0x0, 0x1f, 0xf0, 0x0, 0x0, 0x1f, 0xf1, 0x0,
    0x0, 0xf, 0xf7, 0x0, 0x0, 0xa, 0xff, 0xf3,
    0x0, 0x0, 0x8d, 0xf4,

    /* U+007C "|" */
    0x22, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff,

    /* U+007D "}" */
    0x9f, 0xc6, 0x0, 0x8, 0xff, 0xf5, 0x0, 0x0,
    0xbf, 0xa0, 0x0, 0x6, 0xfc, 0x0, 0x0, 0x5f,
    0xc0, 0x0, 0x5, 0xfc, 0x0, 0x0, 0x4f, 0xd0,
    0x0, 0x1, 0xff, 0x50, 0x0, 0x7, 0xff, 0xe0,
    0x0, 0x3d, 0xfe, 0x0, 0xe, 0xfa, 0x40, 0x4,
    0xfe, 0x0, 0x0, 0x5f, 0xc0, 0x0, 0x5, 0xfc,
    0x0, 0x0, 0x5f, 0xc0, 0x0, 0x6, 0xfb, 0x0,
    0x0, 0xcf, 0xa0, 0x8, 0xff, 0xf4, 0x0, 0x9f,
    0xc5, 0x0, 0x0,

    /* U+007E "~" */
    0x8, 0xee, 0x70, 0x4, 0xc5, 0x5f, 0xff, 0xf7,
    0xa, 0xf5, 0xbf, 0x31, 0xdf, 0xff, 0xe0, 0xab,
    0x0, 0x1b, 0xfd, 0x30,

    /* U+F001 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x31, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x15, 0xae, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x7c, 0xff, 0xff, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x49, 0xef, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x0, 0x4, 0xbf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x0, 0x0, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xfb, 0x69, 0xff, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xfe, 0x95, 0x0, 0x8, 0xff,
    0x0, 0x0, 0xf, 0xff, 0xc7, 0x30, 0x0, 0x0,
    0x8, 0xff, 0x0, 0x0, 0xf, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0x0, 0x0, 0xf, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0x0, 0x0,
    0xf, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0x0, 0x0, 0xf, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0x0, 0x0, 0xf, 0xf8, 0x0, 0x0,
    0x1, 0x7b, 0xbd, 0xff, 0x0, 0x0, 0xf, 0xf8,
    0x0, 0x0, 0x1e, 0xff, 0xff, 0xff, 0x0, 0x13,
    0x2f, 0xf8, 0x0, 0x0, 0x7f, 0xff, 0xff, 0xff,
    0x2b, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x4f, 0xff,
    0xff, 0xfc, 0xdf, 0xff, 0xff, 0xf8, 0x0, 0x0,
    0x6, 0xef, 0xff, 0xa1, 0xff, 0xff, 0xff, 0xf7,
    0x0, 0x0, 0x0, 0x2, 0x20, 0x0, 0x7f, 0xff,
    0xff, 0xd1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0x8a, 0xa6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F008 "" */
    0xc4, 0x3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x40, 0x4c, 0xfd, 0xcd, 0xff, 0xee, 0xee, 0xee,
    0xee, 0xff, 0xdc, 0xdf, 0xfa, 0x8a, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xb8, 0xaf, 0xf4, 0x3,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x9f, 0x40, 0x4f,
    0xf4, 0x4, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0x50, 0x4f, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xff, 0xff, 0xf7, 0x47, 0xfd, 0x77,
    0x77, 0x77, 0x77, 0xdf, 0x84, 0x7f, 0xf4, 0x3,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x40, 0x4f,
    0xf7, 0x47, 0xfd, 0x77, 0x77, 0x77, 0x77, 0xdf,
    0x84, 0x7f, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xff, 0xff, 0xf4, 0x4, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0x50, 0x4f, 0xf4, 0x3,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x9f, 0x40, 0x4f,
    0xfa, 0x8a, 0xfa, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xb8, 0xaf, 0xfd, 0xcd, 0xff, 0xee, 0xee, 0xee,
    0xee, 0xff, 0xdc, 0xdf, 0xc4, 0x3, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x40, 0x4c,

    /* U+F00B "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0xff, 0xfa, 0xc, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0xff, 0xff, 0xfd, 0xe,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfd, 0xe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0xe, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xcf, 0xff, 0xf9, 0xa, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff,
    0xf9, 0xb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xfd, 0xe, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0xe, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xe,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xcf, 0xff,
    0xfa, 0xb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xff, 0xf9, 0xa, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfb, 0xff, 0xff, 0xfd, 0xe,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfd, 0xe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0xe, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xdf, 0xff, 0xfa, 0xc, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F00C "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4e, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff,
    0xff, 0x60, 0x6, 0xe4, 0x0, 0x0, 0x0, 0x4,
    0xff, 0xff, 0xf6, 0x0, 0x7f, 0xff, 0x40, 0x0,
    0x0, 0x4f, 0xff, 0xff, 0x60, 0x0, 0xff, 0xff,
    0xf4, 0x0, 0x4, 0xff, 0xff, 0xf6, 0x0, 0x0,
    0x6f, 0xff, 0xff, 0x40, 0x4f, 0xff, 0xff, 0x60,
    0x0, 0x0, 0x6, 0xff, 0xff, 0xf8, 0xff, 0xff,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xff,
    0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xff, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5e,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F00D "" */
    0x6, 0x70, 0x0, 0x0, 0x0, 0x18, 0x40, 0x8f,
    0xfb, 0x0, 0x0, 0x1, 0xdf, 0xf4, 0xff, 0xff,
    0xb0, 0x0, 0x1d, 0xff, 0xfb, 0x7f, 0xff, 0xfb,
    0x1, 0xdf, 0xff, 0xf4, 0x8, 0xff, 0xff, 0xbd,
    0xff, 0xff, 0x40, 0x0, 0x8f, 0xff, 0xff, 0xff,
    0xf4, 0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0x40,
    0x0, 0x0, 0x1, 0xef, 0xff, 0xfc, 0x0, 0x0,
    0x0, 0x1d, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x1,
    0xdf, 0xff, 0xff, 0xff, 0xfb, 0x0, 0x1d, 0xff,
    0xff, 0x48, 0xff, 0xff, 0xb0, 0xcf, 0xff, 0xf4,
    0x0, 0x8f, 0xff, 0xf9, 0xdf, 0xff, 0x40, 0x0,
    0x8, 0xff, 0xf9, 0x2e, 0xf4, 0x0, 0x0, 0x0,
    0x8f, 0xc0, 0x0, 0x10, 0x0, 0x0, 0x0, 0x1,
    0x0,

    /* U+F011 "" */
    0x0, 0x0, 0x0, 0x0, 0x26, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x64, 0x0,
    0xdf, 0xf4, 0x0, 0x72, 0x0, 0x0, 0x0, 0xb,
    0xfe, 0x10, 0xdf, 0xf4, 0x9, 0xfe, 0x30, 0x0,
    0x0, 0xaf, 0xff, 0x50, 0xdf, 0xf4, 0xe, 0xff,
    0xe1, 0x0, 0x5, 0xff, 0xfb, 0x0, 0xdf, 0xf4,
    0x5, 0xff, 0xfb, 0x0, 0xd, 0xff, 0xb0, 0x0,
    0xdf, 0xf4, 0x0, 0x5f, 0xff, 0x40, 0x4f, 0xff,
    0x20, 0x0, 0xdf, 0xf4, 0x0, 0xb, 0xff, 0xa0,
    0x8f, 0xfb, 0x0, 0x0, 0xdf, 0xf4, 0x0, 0x4,
    0xff, 0xf0, 0xbf, 0xf7, 0x0, 0x0, 0xdf, 0xf4,
    0x0, 0x1, 0xff, 0xf1, 0xbf, 0xf6, 0x0, 0x0,
    0xdf, 0xf4, 0x0, 0x0, 0xff, 0xf2, 0xbf, 0xf7,
    0x0, 0x0, 0x8d, 0xc1, 0x0, 0x0, 0xff, 0xf1,
    0x8f, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0xf0, 0x4f, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xff, 0xb0, 0xe, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0x50, 0x6, 0xff,
    0xf9, 0x0, 0x0, 0x0, 0x4, 0xff, 0xfc, 0x0,
    0x0, 0xaf, 0xff, 0xd5, 0x10, 0x3, 0x9f, 0xff,
    0xf2, 0x0, 0x0, 0xb, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xff, 0xff, 0xff, 0xd2, 0x0, 0x0, 0x0, 0x0,
    0x2, 0x9e, 0xff, 0xff, 0xb6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x12, 0x10, 0x0, 0x0,
    0x0, 0x0,

    /* U+F013 "" */
    0x0, 0x0, 0x0, 0x0, 0x2, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x0, 0x2b, 0xff, 0xff, 0xb2, 0x0, 0x10, 0x0,
    0x0, 0x8f, 0x87, 0xff, 0xff, 0xff, 0xff, 0x79,
    0xf8, 0x0, 0x4, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x40, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x2f, 0xff,
    0xff, 0xff, 0xc7, 0x7c, 0xff, 0xff, 0xff, 0xf2,
    0x7, 0xff, 0xff, 0xfa, 0x0, 0x0, 0xaf, 0xff,
    0xff, 0x70, 0x0, 0x6f, 0xff, 0xf1, 0x0, 0x0,
    0x1f, 0xff, 0xf6, 0x0, 0x0, 0x7f, 0xff, 0xe0,
    0x0, 0x0, 0xe, 0xff, 0xf7, 0x0, 0x0, 0x6f,
    0xff, 0xf1, 0x0, 0x0, 0x1f, 0xff, 0xf6, 0x0,
    0x7, 0xff, 0xff, 0xfa, 0x0, 0x0, 0xaf, 0xff,
    0xff, 0x70, 0x2f, 0xff, 0xff, 0xff, 0xc7, 0x7c,
    0xff, 0xff, 0xff, 0xf2, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x4, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x40,
    0x0, 0x8f, 0x97, 0xff, 0xff, 0xff, 0xff, 0x78,
    0xf8, 0x0, 0x0, 0x1, 0x0, 0x1b, 0xff, 0xff,
    0xb1, 0x0, 0x10, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0x20, 0x0, 0x0,
    0x0, 0x0,

    /* U+F015 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x46, 0x10, 0x0,
    0x67, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xfe, 0x20, 0xf, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xbf, 0xff, 0xff, 0x51, 0xff,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0x2, 0xdf, 0xfc,
    0x8f, 0xff, 0x8f, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x4, 0xff, 0xfa, 0x0, 0x4e, 0xff, 0xff, 0xf1,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xf7, 0x8, 0xd3,
    0x2d, 0xff, 0xff, 0x10, 0x0, 0x0, 0xa, 0xff,
    0xf5, 0x1b, 0xff, 0xf5, 0xb, 0xff, 0xf4, 0x0,
    0x0, 0x1c, 0xff, 0xe2, 0x2d, 0xff, 0xff, 0xf7,
    0x8, 0xff, 0xf6, 0x0, 0x3e, 0xff, 0xc1, 0x4e,
    0xff, 0xff, 0xff, 0xfa, 0x5, 0xff, 0xf9, 0xe,
    0xff, 0x90, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0x13, 0xef, 0xf6, 0x4f, 0x70, 0x9f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x31, 0xcc, 0x0, 0x0,
    0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x4f,
    0xff, 0xff, 0x80, 0x1, 0xef, 0xff, 0xfc, 0x0,
    0x0, 0x0, 0x4, 0xff, 0xff, 0xf6, 0x0, 0xe,
    0xff, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x4f, 0xff,
    0xff, 0x60, 0x0, 0xef, 0xff, 0xfc, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xff, 0xf6, 0x0, 0xe, 0xff,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xff,
    0x40, 0x0, 0xcf, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F019 "" */
    0x0, 0x0, 0x0, 0x0, 0x56, 0x65, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xee, 0xef, 0xff, 0xff, 0xfe, 0xee,
    0x70, 0x0, 0x0, 0x5, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0x6f, 0xff,
    0xff, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xff, 0xff, 0xff, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xff, 0xff, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x7a, 0xaa, 0xaa, 0x91,
    0x4f, 0xf4, 0x19, 0xaa, 0xaa, 0xa7, 0xff, 0xff,
    0xff, 0xfd, 0x23, 0x32, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf9, 0x9f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0xf, 0x48, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xef, 0xef, 0xff,
    0x14, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x41,

    /* U+F01C "" */
    0x0, 0x0, 0x3d, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x1e, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0xa, 0xff, 0x98, 0x88, 0x88, 0x88, 0x88, 0xdf,
    0xf3, 0x0, 0x0, 0x5, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xff, 0xd0, 0x0, 0x1, 0xef,
    0xe1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0x80, 0x0, 0xaf, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xff, 0x30, 0x5f, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xfd,
    0xd, 0xff, 0x98, 0x88, 0x70, 0x0, 0x0, 0x3,
    0x88, 0x88, 0xdf, 0xf5, 0xff, 0xff, 0xff, 0xff,
    0x40, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xff, 0x7f,
    0xff, 0xff, 0xff, 0xfc, 0x0, 0x0, 0x5f, 0xff,
    0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x8f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x8e, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf6, 0x5e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfb, 0x0,

    /* U+F021 "" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0x10, 0x0, 0x0,
    0x7, 0xba, 0x0, 0x0, 0x1, 0x7c, 0xff, 0xff,
    0xb5, 0x0, 0xb, 0xff, 0x0, 0x0, 0x7f, 0xff,
    0xff, 0xff, 0xff, 0xe5, 0xb, 0xff, 0x0, 0xa,
    0xff, 0xff, 0xdb, 0xbe, 0xff, 0xff, 0x9a, 0xff,
    0x0, 0x9f, 0xff, 0xa2, 0x0, 0x0, 0x3b, 0xff,
    0xff, 0xff, 0x5, 0xff, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xff, 0xff, 0xd, 0xff, 0x60, 0x0,
    0x0, 0x7, 0xba, 0x9c, 0xff, 0xff, 0x3f, 0xfc,
    0x0, 0x0, 0x0, 0xc, 0xff, 0xff, 0xff, 0xff,
    0x7f, 0xf6, 0x0, 0x0, 0x0, 0xb, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x6f, 0xf6, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0xcf, 0xf3, 0xff, 0xff, 0xc9, 0xaa,
    0x70, 0x0, 0x0, 0x7, 0xff, 0xd0, 0xff, 0xff,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0x50,
    0xff, 0xff, 0xff, 0xb3, 0x0, 0x0, 0x3b, 0xff,
    0xf9, 0x0, 0xff, 0xa9, 0xff, 0xff, 0xeb, 0xbd,
    0xff, 0xff, 0xa0, 0x0, 0xff, 0xb0, 0x5d, 0xff,
    0xff, 0xff, 0xff, 0xf7, 0x0, 0x0, 0xff, 0xb0,
    0x0, 0x5b, 0xff, 0xff, 0xc8, 0x10, 0x0, 0x0,
    0xab, 0x70, 0x0, 0x0, 0x1, 0x10, 0x0, 0x0,
    0x0, 0x0,

    /* U+F026 "" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0,
    0x2, 0xee, 0x0, 0x0, 0x0, 0x2e, 0xff, 0x0,
    0x0, 0x2, 0xef, 0xff, 0x47, 0x77, 0x7e, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0x0, 0x0, 0x9, 0xff,
    0xff, 0x0, 0x0, 0x0, 0x9f, 0xff, 0x0, 0x0,
    0x0, 0x9, 0xff, 0x0, 0x0, 0x0, 0x0, 0x89,

    /* U+F027 "" */
    0x0, 0x0, 0x0, 0x0, 0x89, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xff, 0xf0, 0x0, 0x0, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0x0, 0x73, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0x1f, 0xf4, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x5f, 0xcf, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x0, 0x4f, 0xdf, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x1f, 0xf5, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x85, 0x4, 0x77, 0x77, 0xef, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xef, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xef, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xee, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x10, 0x0, 0x0,

    /* U+F028 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7b, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x10, 0x0, 0x0, 0x9, 0xfe, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xee, 0x0, 0x0, 0x0, 0x9,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x2, 0xef, 0xf0,
    0x0, 0x8, 0x70, 0x8, 0xfd, 0x0, 0x0, 0x0,
    0x2, 0xef, 0xff, 0x0, 0x0, 0xef, 0xb0, 0xc,
    0xf6, 0x4, 0x77, 0x77, 0xef, 0xff, 0xf0, 0x0,
    0x2, 0xdf, 0x80, 0x3f, 0xd0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x74, 0x1, 0xff, 0x10, 0xcf,
    0x3f, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x1f, 0xf4,
    0x8, 0xf7, 0x7, 0xf6, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x0, 0x5f, 0xc0, 0x3f, 0xa0, 0x4f, 0x9f,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0x0, 0xff, 0x2,
    0xfb, 0x4, 0xf9, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x5f, 0xc0, 0x3f, 0xa0, 0x5f, 0x9f, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0x1f, 0xf4, 0x8, 0xf7,
    0x7, 0xf6, 0xdf, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x74, 0x2, 0xff, 0x10, 0xcf, 0x30, 0x0, 0x0,
    0x9f, 0xff, 0xf0, 0x0, 0x2, 0xef, 0x80, 0x3f,
    0xd0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0x0, 0x0,
    0xef, 0xb0, 0xc, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xf0, 0x0, 0x8, 0x70, 0x8, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x89, 0x0, 0x0, 0x0,
    0x8, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7b,
    0x20, 0x0, 0x0,

    /* U+F03E "" */
    0x5e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xe5, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0xff, 0xf6, 0x38, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60,
    0x0, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x30, 0x0, 0x7f, 0xff, 0xff, 0xfb, 0xef,
    0xff, 0xff, 0xff, 0x80, 0x0, 0xcf, 0xff, 0xff,
    0x90, 0x3e, 0xff, 0xff, 0xff, 0xfa, 0x7c, 0xff,
    0xff, 0xf9, 0x0, 0x3, 0xef, 0xff, 0xff, 0xff,
    0xfc, 0xff, 0xff, 0x90, 0x0, 0x0, 0x3e, 0xff,
    0xff, 0xff, 0x90, 0x6f, 0xf9, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xff, 0xf9, 0x0, 0x6, 0x90, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xff, 0xc8, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x8c, 0xff, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x5e, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe5,

    /* U+F043 "" */
    0x0, 0x0, 0x0, 0x42, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xf4, 0x0, 0x0, 0x0, 0x1, 0xff, 0xff, 0xfc,
    0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0xff, 0x60,
    0x0, 0x0, 0x4f, 0xff, 0xff, 0xff, 0xf2, 0x0,
    0x1, 0xef, 0xff, 0xff, 0xff, 0xfc, 0x0, 0xb,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x3f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0xaf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf6, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfa, 0xff, 0xa6, 0xff, 0xff, 0xff,
    0xff, 0xfb, 0xef, 0x91, 0xff, 0xff, 0xff, 0xff,
    0xfa, 0xbf, 0xe0, 0x9f, 0xff, 0xff, 0xff, 0xf7,
    0x5f, 0xf9, 0x7, 0xcf, 0xff, 0xff, 0xf2, 0xc,
    0xff, 0xb3, 0x9, 0xff, 0xff, 0x80, 0x1, 0xdf,
    0xff, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x9, 0xff,
    0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x15, 0x77,
    0x40, 0x0, 0x0,

    /* U+F048 "" */
    0x47, 0x60, 0x0, 0x0, 0x0, 0x16, 0x1b, 0xff,
    0x10, 0x0, 0x0, 0x2d, 0xfb, 0xbf, 0xf1, 0x0,
    0x0, 0x2e, 0xff, 0xcb, 0xff, 0x10, 0x0, 0x3e,
    0xff, 0xfc, 0xbf, 0xf1, 0x0, 0x4f, 0xff, 0xff,
    0xcb, 0xff, 0x10, 0x5f, 0xff, 0xff, 0xfc, 0xbf,
    0xf1, 0x6f, 0xff, 0xff, 0xff, 0xcb, 0xff, 0x9f,
    0xff, 0xff, 0xff, 0xfc, 0xbf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xcb, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xcb,
    0xff, 0x4e, 0xff, 0xff, 0xff, 0xfc, 0xbf, 0xf1,
    0x2d, 0xff, 0xff, 0xff, 0xcb, 0xff, 0x10, 0x1c,
    0xff, 0xff, 0xfc, 0xbf, 0xf1, 0x0, 0xc, 0xff,
    0xff, 0xcb, 0xff, 0x10, 0x0, 0xb, 0xff, 0xfc,
    0xbf, 0xf1, 0x0, 0x0, 0xa, 0xff, 0xca, 0xff,
    0x10, 0x0, 0x0, 0x8, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F04B "" */
    0x6, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xfd, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xfa, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xd4, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xb2, 0x0, 0x0, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x50, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0x20, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf6, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfb, 0x20, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0x50, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xa2, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xd4, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xfa, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xfd, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F04C "" */
    0x6, 0x77, 0x77, 0x30, 0x0, 0x6, 0x77, 0x77,
    0x30, 0xbf, 0xff, 0xff, 0xf3, 0x0, 0xbf, 0xff,
    0xff, 0xf3, 0xff, 0xff, 0xff, 0xf7, 0x0, 0xff,
    0xff, 0xff, 0xf7, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xf8,
    0x0, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff,
    0xf8, 0x0, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff,
    0xff, 0xf8, 0x0, 0xff, 0xff, 0xff, 0xf8, 0xff,
    0xff, 0xff, 0xf8, 0x0, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xf8, 0x0, 0xff, 0xff, 0xff,
    0xf8, 0xff, 0xff, 0xff, 0xf8, 0x0, 0xff, 0xff,
    0xff, 0xf8, 0xff, 0xff, 0xff, 0xf8, 0x0, 0xff,
    0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xf8,
    0x0, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff,
    0xf8, 0x0, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff,
    0xff, 0xf8, 0x0, 0xff, 0xff, 0xff, 0xf8, 0xff,
    0xff, 0xff, 0xf8, 0x0, 0xff, 0xff, 0xff, 0xf8,
    0xef, 0xff, 0xff, 0xf6, 0x0, 0xef, 0xff, 0xff,
    0xf6, 0x6f, 0xff, 0xff, 0xc1, 0x0, 0x6f, 0xff,
    0xff, 0xc1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F04D "" */
    0x5, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77,
    0x30, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf7, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf6, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F051 "" */
    0x5, 0x20, 0x0, 0x0, 0x0, 0x57, 0x66, 0xff,
    0x40, 0x0, 0x0, 0xc, 0xff, 0x8f, 0xff, 0x60,
    0x0, 0x0, 0xdf, 0xf8, 0xff, 0xff, 0x70, 0x0,
    0xd, 0xff, 0x8f, 0xff, 0xff, 0x80, 0x0, 0xdf,
    0xf8, 0xff, 0xff, 0xff, 0x90, 0xd, 0xff, 0x8f,
    0xff, 0xff, 0xff, 0xb0, 0xdf, 0xf8, 0xff, 0xff,
    0xff, 0xff, 0xcd, 0xff, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0x5d, 0xff, 0x8f, 0xff,
    0xff, 0xff, 0x40, 0xdf, 0xf8, 0xff, 0xff, 0xfe,
    0x30, 0xd, 0xff, 0x8f, 0xff, 0xfe, 0x20, 0x0,
    0xdf, 0xf8, 0xff, 0xfd, 0x20, 0x0, 0xd, 0xff,
    0x7f, 0xfd, 0x10, 0x0, 0x0, 0xdf, 0xf3, 0xfb,
    0x10, 0x0, 0x0, 0xc, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F052 "" */
    0x0, 0x0, 0x0, 0x0, 0x2, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xff, 0xfe, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xff, 0xff, 0xfe, 0x10, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x10,
    0x0, 0x0, 0x5, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0x0, 0x0, 0x4, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0x0, 0x3, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfa, 0x0, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0xe,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x60, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe2, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x70, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F053 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0x80, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0x40, 0x0, 0x0, 0x5f, 0xff, 0xe2, 0x0, 0x0,
    0x5f, 0xff, 0xe2, 0x0, 0x0, 0x5f, 0xff, 0xe3,
    0x0, 0x0, 0x5f, 0xff, 0xe3, 0x0, 0x0, 0x5f,
    0xff, 0xe3, 0x0, 0x0, 0x5f, 0xff, 0xe3, 0x0,
    0x0, 0xd, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x4f,
    0xff, 0xe3, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xe3,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0xe3, 0x0, 0x0,
    0x0, 0x4f, 0xff, 0xe3, 0x0, 0x0, 0x0, 0x5f,
    0xff, 0xe3, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xe2,
    0x0, 0x0, 0x0, 0x5f, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x5f, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F054 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xdb, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xfc, 0x0, 0x0, 0x0,
    0x9, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x9, 0xff,
    0xfc, 0x0, 0x0, 0x0, 0x9, 0xff, 0xfc, 0x0,
    0x0, 0x0, 0x9, 0xff, 0xfc, 0x0, 0x0, 0x0,
    0x9, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x9, 0xff,
    0xfc, 0x0, 0x0, 0x0, 0xd, 0xff, 0xf6, 0x0,
    0x0, 0x9, 0xff, 0xfc, 0x0, 0x0, 0x9, 0xff,
    0xfc, 0x0, 0x0, 0x9, 0xff, 0xfc, 0x0, 0x0,
    0x9, 0xff, 0xfc, 0x0, 0x0, 0x9, 0xff, 0xfc,
    0x0, 0x0, 0x9, 0xff, 0xfc, 0x0, 0x0, 0x0,
    0xcf, 0xfc, 0x0, 0x0, 0x0, 0x2, 0xdb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F067 "" */
    0x0, 0x0, 0x0, 0x2, 0x55, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x37,
    0x77, 0x77, 0x8f, 0xff, 0xc7, 0x77, 0x77, 0x60,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf5, 0x14, 0x44, 0x44, 0x5f, 0xff,
    0xb4, 0x44, 0x44, 0x30, 0x0, 0x0, 0x0, 0x2f,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x21, 0x0,
    0x0, 0x0, 0x0,

    /* U+F068 "" */
    0x49, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99,
    0x80, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf7, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0x2, 0x22, 0x22, 0x22,
    0x22, 0x22, 0x22, 0x22, 0x10,

    /* U+F06E "" */
    0x0, 0x0, 0x0, 0x6, 0xad, 0xff, 0xec, 0x83,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xff, 0xef, 0xff, 0xfc, 0x40, 0x0, 0x0, 0x0,
    0x3, 0xdf, 0xff, 0x92, 0x0, 0x5, 0xdf, 0xff,
    0x90, 0x0, 0x0, 0x4, 0xff, 0xff, 0x50, 0x2,
    0x52, 0x1, 0xcf, 0xff, 0xb0, 0x0, 0x3, 0xff,
    0xff, 0x80, 0x0, 0x7f, 0xf9, 0x1, 0xef, 0xff,
    0xb0, 0x1, 0xef, 0xff, 0xf0, 0x0, 0x8, 0xff,
    0xf7, 0x8, 0xff, 0xff, 0x80, 0xaf, 0xff, 0xfb,
    0x2, 0x25, 0xff, 0xff, 0xe0, 0x3f, 0xff, 0xff,
    0x2f, 0xff, 0xff, 0xa0, 0x7f, 0xff, 0xff, 0xff,
    0x2, 0xff, 0xff, 0xf7, 0x9f, 0xff, 0xfb, 0x5,
    0xff, 0xff, 0xff, 0xc0, 0x3f, 0xff, 0xff, 0x21,
    0xef, 0xff, 0xf0, 0xc, 0xff, 0xff, 0xf5, 0x7,
    0xff, 0xff, 0x80, 0x3, 0xff, 0xff, 0x80, 0x1a,
    0xff, 0xe5, 0x1, 0xef, 0xff, 0xb0, 0x0, 0x4,
    0xff, 0xff, 0x50, 0x0, 0x10, 0x1, 0xcf, 0xff,
    0xb0, 0x0, 0x0, 0x3, 0xdf, 0xff, 0x92, 0x0,
    0x5, 0xdf, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xff, 0xff, 0xef, 0xff, 0xfc, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xad, 0xef, 0xec,
    0x83, 0x0, 0x0, 0x0, 0x0,

    /* U+F070 "" */
    0x0, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xfc, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xff, 0xe4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xff, 0x70, 0x4, 0x8c, 0xef, 0xed, 0x94,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xfb,
    0xef, 0xff, 0xfe, 0xff, 0xff, 0xe6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2d, 0xff, 0xff, 0xc4, 0x0,
    0x4, 0xcf, 0xff, 0xb1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xff, 0x60, 0x3, 0x10, 0x9, 0xff,
    0xfe, 0x20, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff,
    0xf9, 0x4f, 0xfa, 0x0, 0xcf, 0xff, 0xe1, 0x0,
    0x0, 0xb, 0xb0, 0x0, 0x3e, 0xff, 0xef, 0xff,
    0xa0, 0x4f, 0xff, 0xfb, 0x0, 0x0, 0x6f, 0xfd,
    0x30, 0x1, 0xcf, 0xff, 0xff, 0xf1, 0xf, 0xff,
    0xff, 0x50, 0x0, 0xbf, 0xff, 0xf6, 0x0, 0x8,
    0xff, 0xff, 0xf3, 0xe, 0xff, 0xff, 0xa0, 0x0,
    0x6f, 0xff, 0xff, 0x0, 0x0, 0x5f, 0xff, 0xf2,
    0xf, 0xff, 0xff, 0x50, 0x0, 0xc, 0xff, 0xff,
    0x40, 0x0, 0x2, 0xdf, 0xfe, 0x8f, 0xff, 0xfa,
    0x0, 0x0, 0x1, 0xef, 0xff, 0xc0, 0x0, 0x0,
    0xa, 0xff, 0xff, 0xff, 0xd1, 0x0, 0x0, 0x0,
    0x2e, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x6f, 0xff,
    0xfd, 0x10, 0x0, 0x0, 0x0, 0x1, 0xcf, 0xff,
    0xc4, 0x0, 0x0, 0x3, 0xef, 0xfd, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xef, 0xff, 0xfe, 0xe3,
    0x0, 0x1b, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0x9d, 0xef, 0xec, 0x20, 0x0, 0x8f,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xef, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1c, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x60,

    /* U+F071 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xa3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xfe, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xff, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xff, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xff, 0xff, 0xff,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xff, 0xa2, 0x24, 0xff, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xff, 0x90, 0x1, 0xff,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff,
    0xff, 0xa0, 0x2, 0xff, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xff, 0xb0, 0x3, 0xff,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff,
    0xff, 0xc0, 0x4, 0xff, 0xff, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xff, 0xff, 0xd0, 0x5, 0xff,
    0xff, 0xff, 0x40, 0x0, 0x0, 0x6, 0xff, 0xff,
    0xff, 0xf9, 0x9c, 0xff, 0xff, 0xff, 0xd0, 0x0,
    0x0, 0xe, 0xff, 0xff, 0xff, 0xf5, 0x2b, 0xff,
    0xff, 0xff, 0xf7, 0x0, 0x0, 0x8f, 0xff, 0xff,
    0xff, 0x90, 0x1, 0xff, 0xff, 0xff, 0xfe, 0x10,
    0x1, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0xff,
    0xff, 0xff, 0xff, 0x90, 0xa, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0x1a, 0xff, 0xff, 0xff, 0xff, 0xf2,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf7, 0xb, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0x1, 0x68, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x40,

    /* U+F074 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xff, 0x50, 0xef, 0xff,
    0xf6, 0x0, 0x0, 0x1, 0xcf, 0xff, 0xff, 0xf5,
    0xff, 0xff, 0xff, 0x50, 0x0, 0xc, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x0, 0xbf,
    0xff, 0xff, 0xff, 0xf7, 0x22, 0x23, 0xdf, 0xf8,
    0x9, 0xff, 0xf7, 0x2f, 0xff, 0x70, 0x0, 0x0,
    0x2e, 0xb0, 0x7f, 0xff, 0x90, 0xf, 0xf7, 0x0,
    0x0, 0x0, 0x3, 0x6, 0xff, 0xfa, 0x0, 0x7,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff,
    0xfd, 0x3, 0x0, 0x7, 0x60, 0x0, 0x0, 0x0,
    0x2e, 0xff, 0xe1, 0x3f, 0x90, 0xf, 0xf8, 0x0,
    0x22, 0x23, 0xdf, 0xfe, 0x22, 0xef, 0xf7, 0x2f,
    0xff, 0x80, 0xff, 0xff, 0xff, 0xf3, 0x0, 0xcf,
    0xff, 0xff, 0xff, 0xf7, 0xff, 0xff, 0xff, 0x50,
    0x0, 0xc, 0xff, 0xff, 0xff, 0xfe, 0xef, 0xff,
    0xf6, 0x0, 0x0, 0x1, 0xcf, 0xff, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0x30, 0x0,

    /* U+F077 "" */
    0x0, 0x0, 0x0, 0x0, 0x94, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xff,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff,
    0xae, 0xff, 0xf5, 0x0, 0x0, 0x0, 0xc, 0xff,
    0xf9, 0x3, 0xef, 0xff, 0x50, 0x0, 0x0, 0xcf,
    0xff, 0x90, 0x0, 0x3e, 0xff, 0xf5, 0x0, 0xc,
    0xff, 0xf9, 0x0, 0x0, 0x3, 0xef, 0xff, 0x50,
    0xaf, 0xff, 0x90, 0x0, 0x0, 0x0, 0x3e, 0xff,
    0xf2, 0x7f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xef, 0xe1, 0x6, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x29, 0x20,

    /* U+F078 "" */
    0x6, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x29,
    0x20, 0x7f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xef, 0xe1, 0x9f, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x3e, 0xff, 0xf2, 0xb, 0xff, 0xf9, 0x0, 0x0,
    0x3, 0xef, 0xff, 0x50, 0x0, 0xbf, 0xff, 0x90,
    0x0, 0x3e, 0xff, 0xf5, 0x0, 0x0, 0xb, 0xff,
    0xf9, 0x3, 0xef, 0xff, 0x50, 0x0, 0x0, 0x0,
    0xbf, 0xff, 0xae, 0xff, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xff, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xff, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x94, 0x0,
    0x0, 0x0, 0x0,

    /* U+F079 "" */
    0x0, 0x0, 0x9c, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xd1, 0x0, 0x58, 0x88, 0x88, 0x88, 0x88, 0x81,
    0x0, 0x0, 0x0, 0xaf, 0xff, 0xfd, 0x20, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x0, 0xa,
    0xff, 0xff, 0xff, 0xe2, 0x4e, 0xff, 0xff, 0xff,
    0xff, 0xf6, 0x0, 0x0, 0x8f, 0xfc, 0xff, 0xcf,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xf6, 0x0,
    0x0, 0x7f, 0xc2, 0xff, 0x67, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xf6, 0x0, 0x0, 0x3, 0x1,
    0xff, 0x60, 0x30, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xf6, 0x0, 0x0, 0x0, 0x1, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xf6, 0x0, 0x0,
    0x0, 0x1, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xf6, 0x0, 0x0, 0x0, 0x1, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x3, 0xd7, 0x1f, 0xf6,
    0x3d, 0x70, 0x0, 0x1, 0xff, 0x60, 0x0, 0x0,
    0x0, 0xa, 0xff, 0x7f, 0xf9, 0xef, 0xf0, 0x0,
    0x1, 0xff, 0xb8, 0x88, 0x88, 0x88, 0x32, 0xef,
    0xff, 0xff, 0xff, 0x60, 0x0, 0x1, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf2, 0x2e, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0xdf, 0xff, 0xff, 0xff, 0xff,
    0xe2, 0x2, 0xef, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2e,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x30, 0x0, 0x0,

    /* U+F07B "" */
    0x5e, 0xff, 0xff, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xff, 0xff, 0xff, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xa8, 0x88, 0x88, 0x88, 0x60, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x5e, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe5,

    /* U+F093 "" */
    0x0, 0x0, 0x0, 0x0, 0x3, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xd1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0xfd, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0xff, 0xd1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0xff, 0xff, 0xfd, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xff, 0xff,
    0xff, 0xd1, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0x10, 0x0, 0x0, 0xd,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x11, 0x1b, 0xff, 0xff, 0x51, 0x11,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xff, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x7a, 0xaa, 0xaa, 0x2b,
    0xff, 0xff, 0x42, 0xaa, 0xaa, 0xa7, 0xff, 0xff,
    0xff, 0x82, 0x67, 0x76, 0x8, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfb, 0x77, 0x77, 0xbf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0xf, 0x48, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xef, 0xef, 0xff,
    0x14, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x41,

    /* U+F095 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0x62, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xff, 0xfe, 0xb5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff,
    0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xff, 0xff, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2e, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x1, 0x30, 0x0, 0x0, 0x1d, 0xff, 0xfd, 0x0,
    0x0, 0x0, 0x29, 0xff, 0x70, 0x0, 0x3e, 0xff,
    0xff, 0x30, 0x0, 0x4, 0xbf, 0xff, 0xff, 0x40,
    0x7f, 0xff, 0xff, 0x50, 0x0, 0x0, 0xef, 0xff,
    0xff, 0xfe, 0xef, 0xff, 0xff, 0x50, 0x0, 0x0,
    0xc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x40,
    0x0, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xfb, 0x10, 0x0, 0x0, 0x0, 0x4, 0xff, 0xff,
    0xff, 0xff, 0xd5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xff, 0xea, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x57, 0x64, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F0C4 "" */
    0x0, 0x25, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xff, 0xfe, 0x30, 0x0, 0x0, 0x4,
    0xaa, 0x50, 0x7f, 0xff, 0xff, 0xe0, 0x0, 0x0,
    0x8f, 0xff, 0xf5, 0xef, 0xd3, 0x7f, 0xf6, 0x0,
    0x8, 0xff, 0xff, 0xb0, 0xff, 0x80, 0xf, 0xf7,
    0x0, 0x8f, 0xff, 0xfb, 0x0, 0xdf, 0xe7, 0xaf,
    0xf5, 0x8, 0xff, 0xff, 0xb0, 0x0, 0x5f, 0xff,
    0xff, 0xfd, 0x9f, 0xff, 0xfb, 0x0, 0x0, 0x5,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x1, 0x5f, 0xff, 0xff, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xff, 0xff, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x25, 0x9f, 0xff, 0xff, 0xfd,
    0x10, 0x0, 0x0, 0x9, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xd1, 0x0, 0x0, 0x7f, 0xff, 0xff, 0xfb,
    0x6f, 0xff, 0xfd, 0x10, 0x0, 0xef, 0xd3, 0x7f,
    0xf5, 0x5, 0xff, 0xff, 0xd1, 0x0, 0xff, 0x80,
    0xf, 0xf7, 0x0, 0x5f, 0xff, 0xfd, 0x10, 0xdf,
    0xe7, 0xaf, 0xf5, 0x0, 0x5, 0xff, 0xff, 0xd1,
    0x5f, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x4f, 0xff,
    0xf4, 0x5, 0xef, 0xfb, 0x10, 0x0, 0x0, 0x1,
    0x66, 0x20, 0x0, 0x1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F0C5 "" */
    0x0, 0x0, 0x8, 0xbb, 0xbb, 0xbb, 0x50, 0x90,
    0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff, 0x81,
    0xfb, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0x81, 0xff, 0xb0, 0x0, 0x0, 0xf, 0xff, 0xff,
    0xff, 0x81, 0xff, 0xf8, 0x8c, 0xc9, 0xf, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x0, 0xff, 0xfc, 0xf,
    0xff, 0xff, 0xff, 0xd5, 0x44, 0x43, 0xff, 0xfc,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0xff,
    0xfc, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0xff, 0xfc, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfa, 0xff, 0xfc, 0xf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfa, 0xff, 0xfc, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfa, 0xff, 0xfc, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfa, 0xff, 0xfc, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfa, 0xff, 0xfc, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0xff, 0xfc,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0xff,
    0xfc, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0xff, 0xfe, 0xb, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf7, 0xff, 0xff, 0x91, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x80, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x70, 0x0, 0x0, 0x58, 0x88, 0x88, 0x88,
    0x88, 0x87, 0x10, 0x0, 0x0,

    /* U+F0C7 "" */
    0x6, 0x77, 0x77, 0x77, 0x77, 0x77, 0x60, 0x0,
    0x0, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x10, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc1, 0x0, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x1, 0xdf, 0xfc, 0x10, 0xff, 0x80, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xff, 0xc0, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xff, 0xf3, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xf4, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xf4,
    0xff, 0xd8, 0x88, 0x88, 0x88, 0x88, 0xef, 0xff,
    0xf4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf4, 0xff, 0xff, 0xff, 0xe4, 0x2,
    0xcf, 0xff, 0xff, 0xf4, 0xff, 0xff, 0xff, 0x60,
    0x0, 0x2f, 0xff, 0xff, 0xf4, 0xff, 0xff, 0xff,
    0x40, 0x0, 0xf, 0xff, 0xff, 0xf4, 0xff, 0xff,
    0xff, 0xa0, 0x0, 0x6f, 0xff, 0xff, 0xf4, 0xff,
    0xff, 0xff, 0xfc, 0x8a, 0xff, 0xff, 0xff, 0xf4,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf3, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F0C9 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0xac, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xc4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xd5,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xac, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xc4, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F0E0 "" */
    0x5e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xe5, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x7f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0x3, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0x30, 0xe3, 0x1b, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xb1, 0x3e, 0xff, 0x70, 0x7f, 0xff,
    0xff, 0xff, 0xff, 0xf7, 0x7, 0xff, 0xff, 0xfb,
    0x13, 0xdf, 0xff, 0xff, 0xfd, 0x31, 0xbf, 0xff,
    0xff, 0xff, 0xe4, 0xa, 0xff, 0xff, 0xa0, 0x4e,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x5d, 0xd5,
    0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0x20, 0x2, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0xdf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x5e, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe5,

    /* U+F0E7 "" */
    0x0, 0x14, 0x44, 0x44, 0x41, 0x0, 0x0, 0x0,
    0xbf, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x0, 0xef,
    0xff, 0xff, 0xf8, 0x0, 0x0, 0x0, 0xff, 0xff,
    0xff, 0xf3, 0x0, 0x0, 0x2, 0xff, 0xff, 0xff,
    0xd0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x6, 0xff, 0xff, 0xff, 0x40, 0x0,
    0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5,
    0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0xd,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xb0, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x20, 0xb, 0xff, 0xff,
    0xff, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0xff, 0xe1, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0xff, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0x40,
    0x0, 0x0, 0x0,

    /* U+F0EA "" */
    0x0, 0x0, 0x6, 0x71, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x14, 0x44, 0xbf, 0xfe, 0x44, 0x43, 0x0,
    0x0, 0x0, 0xef, 0xff, 0xf9, 0x4f, 0xff, 0xff,
    0x40, 0x0, 0x0, 0xff, 0xff, 0xf8, 0x3f, 0xff,
    0xff, 0x50, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x50, 0x0, 0x0, 0xff, 0xff, 0xff,
    0xa8, 0x88, 0x88, 0x20, 0x0, 0x0, 0xff, 0xff,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xf0, 0xcf, 0xff, 0xff, 0x51, 0xe2, 0x0,
    0xff, 0xff, 0xf0, 0xef, 0xff, 0xff, 0x51, 0xfe,
    0x20, 0xff, 0xff, 0xf0, 0xef, 0xff, 0xff, 0x51,
    0xff, 0xe2, 0xff, 0xff, 0xf0, 0xef, 0xff, 0xff,
    0x50, 0xbb, 0xb7, 0xff, 0xff, 0xf0, 0xef, 0xff,
    0xff, 0x80, 0x0, 0x0, 0xff, 0xff, 0xf0, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xfa, 0xff, 0xff, 0xf0,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xfa, 0xff, 0xff,
    0xf0, 0xef, 0xff, 0xff, 0xff, 0xff, 0xfa, 0xff,
    0xff, 0xf0, 0xef, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0xcf, 0xff, 0xf0, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xfa, 0x0, 0x0, 0x0, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xfa, 0x0, 0x0, 0x0, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xfa, 0x0, 0x0, 0x0, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x7b,
    0xbb, 0xbb, 0xbb, 0xbb, 0xb4,

    /* U+F0F3 "" */
    0x0, 0x0, 0x0, 0x0, 0x31, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xef, 0xff,
    0xfb, 0x20, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff,
    0xff, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x10, 0x0, 0x0, 0x1f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0,
    0x5f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0, 0x0,
    0x0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0x0, 0x0, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf5, 0x0, 0x1, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf9, 0x0, 0x9, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x10, 0x5f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,
    0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0x75, 0x0, 0x0, 0x0, 0x0,

    /* U+F11C "" */
    0x5e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0xe, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0xff,
    0xc8, 0x8f, 0xa8, 0xaf, 0x88, 0xbf, 0x88, 0xfb,
    0x88, 0xff, 0x8f, 0xf8, 0x0, 0xf4, 0x4, 0xf0,
    0x5, 0xe0, 0xe, 0x50, 0xf, 0xf8, 0xff, 0x80,
    0xf, 0x40, 0x4f, 0x0, 0x6f, 0x0, 0xf6, 0x0,
    0xff, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0x94,
    0x6f, 0x64, 0x8f, 0x44, 0xbb, 0x44, 0xff, 0xff,
    0x8f, 0xff, 0xf6, 0x2, 0xf2, 0x5, 0xf0, 0x8,
    0x80, 0xe, 0xff, 0xf8, 0xff, 0xff, 0x94, 0x6f,
    0x64, 0x8f, 0x44, 0xbb, 0x44, 0xff, 0xff, 0x8f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0xff, 0x80, 0xf, 0x40, 0x0,
    0x0, 0x0, 0x0, 0xf6, 0x0, 0xff, 0x8f, 0xf8,
    0x0, 0xf4, 0x0, 0x0, 0x0, 0x0, 0xe, 0x50,
    0xf, 0xf8, 0xff, 0xc8, 0x8f, 0xa8, 0x88, 0x88,
    0x88, 0x88, 0xfb, 0x88, 0xff, 0x8e, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf6, 0x5e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfb, 0x0,

    /* U+F124 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x56, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x7e, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x8f, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xaf,
    0xff, 0xff, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xbf, 0xff, 0xff, 0xff, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x5, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0xfb, 0x0, 0x0, 0x0, 0x6, 0xdf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x18, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0, 0x0,
    0x5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x50, 0x0, 0xe, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x0, 0x0, 0xd, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0,
    0x0, 0x2, 0xac, 0xcc, 0xcc, 0xcd, 0xff, 0xff,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0xff, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xff, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x27, 0x30, 0x0, 0x0, 0x0, 0x0,

    /* U+F15B "" */
    0x24, 0x44, 0x44, 0x44, 0x30, 0x30, 0x0, 0xf,
    0xff, 0xff, 0xff, 0xfc, 0xf, 0x60, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xc0, 0xff, 0x60, 0xf, 0xff,
    0xff, 0xff, 0xfc, 0xf, 0xff, 0x60, 0xff, 0xff,
    0xff, 0xff, 0xc0, 0xff, 0xff, 0x6f, 0xff, 0xff,
    0xff, 0xfc, 0xb, 0xbb, 0xbb, 0xff, 0xff, 0xff,
    0xff, 0xe1, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x8b, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0x80,

    /* U+F1EB "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x23, 0x43, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0x8c, 0xff, 0xff, 0xff, 0xfc, 0x83, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6d, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0x60, 0x0, 0x0, 0x3, 0xdf,
    0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff,
    0xd3, 0x0, 0x8, 0xff, 0xff, 0xfb, 0x72, 0x0,
    0x0, 0x2, 0x7b, 0xff, 0xff, 0xf8, 0xa, 0xff,
    0xff, 0xa2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xaf, 0xff, 0xfa, 0xbf, 0xfd, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3d, 0xff, 0xb0,
    0xba, 0x10, 0x0, 0x5, 0x9d, 0xef, 0xed, 0x95,
    0x0, 0x0, 0x1a, 0xb0, 0x0, 0x0, 0x0, 0x6d,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xbf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xb1, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0xff, 0xfa, 0x53, 0x23, 0x5a, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0xb1, 0x0, 0x0,
    0x0, 0x1, 0xbf, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x9d, 0x91, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xef, 0xe3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F240 "" */
    0x17, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x60, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x80, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0xf, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xfd, 0xff, 0x84, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xc, 0xff, 0xff, 0xf8,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x5b, 0xff, 0xff, 0x84, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0x8f, 0xff,
    0xf8, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x8, 0xff, 0xff, 0x84, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xc8, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x8e,
    0xfd, 0x4e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x4e, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc2, 0x0,

    /* U+F241 "" */
    0x17, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x60, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x80, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0xf, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xfd, 0xff, 0x81, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x60, 0x0, 0xc, 0xff, 0xff, 0xf8,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0,
    0x0, 0x5b, 0xff, 0xff, 0x81, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x60, 0x0, 0x0, 0x8f, 0xff,
    0xf8, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0x8, 0xff, 0xff, 0x81, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x60, 0x0, 0xc, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xc8, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x8e,
    0xfd, 0x4e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x4e, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc2, 0x0,

    /* U+F242 "" */
    0x17, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x60, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x80, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0xf, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xfd, 0xff, 0x81, 0xff, 0xff, 0xff, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xff, 0xf8,
    0x1f, 0xff, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x5b, 0xff, 0xff, 0x81, 0xff, 0xff, 0xff,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xf8, 0x1f, 0xff, 0xff, 0xff, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xff, 0x81, 0xff, 0xff,
    0xff, 0xff, 0xa0, 0x0, 0x0, 0x0, 0xc, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xc8, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x8e,
    0xfd, 0x4e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x4e, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc2, 0x0,

    /* U+F243 "" */
    0x17, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x60, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x80, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0xf, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xfd, 0xff, 0x81, 0xff, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xff, 0xf8,
    0x1f, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5b, 0xff, 0xff, 0x81, 0xff, 0xff, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xf8, 0x1f, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xff, 0x81, 0xff, 0xff,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xc8, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x8e,
    0xfd, 0x4e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x4e, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc2, 0x0,

    /* U+F244 "" */
    0x17, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x60, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x80, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0xf, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xfd, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5b, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xff, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xc8, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x8e,
    0xfd, 0x4e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x4e, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc2, 0x0,

    /* U+F287 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xfe, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7c,
    0xdf, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xfb, 0xbf, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0x70, 0xa, 0xfc, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x32, 0x0, 0x0, 0x9e, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2d, 0xff,
    0x90, 0x1, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1c, 0x30, 0x0, 0xcf, 0xff, 0xf6, 0x3c, 0xf3,
    0x33, 0x33, 0x33, 0x33, 0x33, 0x5f, 0xf9, 0x10,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xd0, 0xcf, 0xff, 0xf6,
    0x33, 0x34, 0xed, 0x33, 0x33, 0x33, 0x33, 0x5f,
    0xfa, 0x10, 0x2d, 0xff, 0x90, 0x0, 0x0, 0x5f,
    0x30, 0x0, 0x0, 0x0, 0x1c, 0x30, 0x0, 0x0,
    0x32, 0x0, 0x0, 0x0, 0xd, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xf3, 0xa, 0xff, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xce,
    0xae, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xbe, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x22, 0x20, 0x0, 0x0, 0x0,

    /* U+F293 "" */
    0x0, 0x0, 0x0, 0x34, 0x31, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xbf, 0xff, 0xff, 0xe7, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0xfa, 0xff, 0xff, 0xb0, 0x0,
    0x4, 0xff, 0xff, 0xf1, 0x9f, 0xff, 0xf8, 0x0,
    0xd, 0xff, 0xff, 0xf1, 0xa, 0xff, 0xff, 0x10,
    0x3f, 0xff, 0xff, 0xf1, 0x0, 0xbf, 0xff, 0x60,
    0x7f, 0xfd, 0x8f, 0xf1, 0x66, 0xc, 0xff, 0xa0,
    0xaf, 0xf8, 0x7, 0xf1, 0x6f, 0x13, 0xff, 0xd0,
    0xcf, 0xff, 0x70, 0x61, 0x53, 0x1e, 0xff, 0xf0,
    0xdf, 0xff, 0xf7, 0x0, 0x1, 0xdf, 0xff, 0xf0,
    0xef, 0xff, 0xff, 0x60, 0xc, 0xff, 0xff, 0xf0,
    0xef, 0xff, 0xff, 0x30, 0x7, 0xff, 0xff, 0xf0,
    0xdf, 0xff, 0xf3, 0x0, 0x10, 0x8f, 0xff, 0xf0,
    0xcf, 0xff, 0x30, 0xb1, 0x67, 0x9, 0xff, 0xf0,
    0x9f, 0xf6, 0xb, 0xf2, 0x6e, 0x2, 0xff, 0xd0,
    0x6f, 0xff, 0xcf, 0xf2, 0x52, 0x2e, 0xff, 0xa0,
    0x1f, 0xff, 0xff, 0xf2, 0x2, 0xef, 0xff, 0x50,
    0x9, 0xff, 0xff, 0xf2, 0x2e, 0xff, 0xfe, 0x0,
    0x0, 0xdf, 0xff, 0xf4, 0xef, 0xff, 0xf5, 0x0,
    0x0, 0x1a, 0xff, 0xff, 0xff, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x27, 0xab, 0xb9, 0x50, 0x0, 0x0,

    /* U+F2ED "" */
    0x0, 0x0, 0x2, 0xab, 0xbb, 0xb7, 0x0, 0x0,
    0x0, 0x57, 0x77, 0x7c, 0xff, 0xff, 0xff, 0x77,
    0x77, 0x72, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf7, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x20, 0xc, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x40, 0xc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x40,
    0xc, 0xff, 0x77, 0xff, 0x3b, 0xfe, 0x1e, 0xff,
    0x40, 0xc, 0xff, 0x66, 0xff, 0x2a, 0xfe, 0xe,
    0xff, 0x40, 0xc, 0xff, 0x66, 0xff, 0x2a, 0xfe,
    0xe, 0xff, 0x40, 0xc, 0xff, 0x66, 0xff, 0x2a,
    0xfe, 0xe, 0xff, 0x40, 0xc, 0xff, 0x66, 0xff,
    0x2a, 0xfe, 0xe, 0xff, 0x40, 0xc, 0xff, 0x66,
    0xff, 0x2a, 0xfe, 0xe, 0xff, 0x40, 0xc, 0xff,
    0x66, 0xff, 0x2a, 0xfe, 0xe, 0xff, 0x40, 0xc,
    0xff, 0x66, 0xff, 0x2a, 0xfe, 0xe, 0xff, 0x40,
    0xc, 0xff, 0x66, 0xff, 0x2a, 0xfe, 0xe, 0xff,
    0x40, 0xc, 0xff, 0x77, 0xff, 0x3b, 0xfe, 0x1e,
    0xff, 0x40, 0xb, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x30, 0x7, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0x0, 0x0, 0x57, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x72, 0x0,

    /* U+F304 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x64, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xdf, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xef, 0xff, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x90, 0x8f, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xef, 0xb0, 0x8f, 0xff, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xef, 0xff, 0xb0, 0x8f,
    0xfe, 0x20, 0x0, 0x0, 0x0, 0x1, 0xef, 0xff,
    0xff, 0xb0, 0x8e, 0x10, 0x0, 0x0, 0x0, 0x1,
    0xef, 0xff, 0xff, 0xff, 0xb0, 0x10, 0x0, 0x0,
    0x0, 0x1, 0xef, 0xff, 0xff, 0xff, 0xfe, 0x10,
    0x0, 0x0, 0x0, 0x1, 0xef, 0xff, 0xff, 0xff,
    0xfe, 0x20, 0x0, 0x0, 0x0, 0x1, 0xef, 0xff,
    0xff, 0xff, 0xfe, 0x20, 0x0, 0x0, 0x0, 0x1,
    0xef, 0xff, 0xff, 0xff, 0xfe, 0x20, 0x0, 0x0,
    0x0, 0x1, 0xef, 0xff, 0xff, 0xff, 0xfe, 0x20,
    0x0, 0x0, 0x0, 0x1, 0xef, 0xff, 0xff, 0xff,
    0xfe, 0x20, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xff, 0xff, 0xfe, 0x20, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xff, 0xff, 0xfe, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xfe, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xff,
    0xfe, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xfe, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0x75, 0x31, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F55A "" */
    0x0, 0x0, 0x0, 0x6e, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfb, 0x20, 0x0, 0x0, 0x8f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfb, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x0, 0x8f,
    0xff, 0xff, 0xff, 0x85, 0xff, 0xff, 0x58, 0xff,
    0xff, 0xff, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xb0,
    0x4, 0xff, 0x40, 0xb, 0xff, 0xff, 0xf0, 0x8f,
    0xff, 0xff, 0xff, 0xff, 0x40, 0x4, 0x40, 0x4,
    0xff, 0xff, 0xff, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x40, 0x0, 0x4, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x10, 0x0,
    0xef, 0xff, 0xff, 0xff, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x40, 0x0, 0x4, 0xff, 0xff, 0xff,
    0xf0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0x40, 0x4,
    0x40, 0x4, 0xff, 0xff, 0xff, 0x0, 0x8f, 0xff,
    0xff, 0xff, 0xb0, 0x4, 0xff, 0x40, 0xb, 0xff,
    0xff, 0xf0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0x85,
    0xff, 0xff, 0x58, 0xff, 0xff, 0xff, 0x0, 0x0,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0x0, 0x0, 0x8f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x0,
    0x0, 0x0, 0x6e, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x20,

    /* U+F7C2 "" */
    0x0, 0x0, 0x28, 0x88, 0x88, 0x88, 0x73, 0x0,
    0x0, 0x2e, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0,
    0x1d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x1d,
    0xf6, 0xe, 0x50, 0xd6, 0x8, 0xff, 0x1d, 0xff,
    0x60, 0xe5, 0xd, 0x60, 0x8f, 0xfc, 0xff, 0xf6,
    0xe, 0x50, 0xd6, 0x8, 0xff, 0xff, 0xff, 0x60,
    0xe5, 0xd, 0x60, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe9, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x7, 0xab,
    0xbb, 0xbb, 0xbb, 0xbb, 0xa6, 0x0,

    /* U+F8A2 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xf1, 0x0, 0x0, 0x1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0x10,
    0x0, 0xa, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xf1, 0x0, 0xb, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xff, 0x10, 0xc, 0xff, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xf1, 0x1c,
    0xff, 0xff, 0xcb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbf,
    0xff, 0x1c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0x9f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0x9f,
    0xff, 0xf9, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66,
    0x40, 0x0, 0x8f, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5c, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 86, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 78, .box_w = 3, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 23, .adv_w = 120, .box_w = 6, .box_h = 6, .ofs_x = 1, .ofs_y = 11},
    {.bitmap_index = 41, .adv_w = 211, .box_w = 13, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 139, .adv_w = 186, .box_w = 11, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 255, .adv_w = 255, .box_w = 16, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 375, .adv_w = 230, .box_w = 15, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 495, .adv_w = 64, .box_w = 2, .box_h = 6, .ofs_x = 1, .ofs_y = 11},
    {.bitmap_index = 501, .adv_w = 114, .box_w = 6, .box_h = 20, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 561, .adv_w = 114, .box_w = 6, .box_h = 20, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 621, .adv_w = 141, .box_w = 9, .box_h = 8, .ofs_x = 0, .ofs_y = 7},
    {.bitmap_index = 657, .adv_w = 186, .box_w = 11, .box_h = 10, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 712, .adv_w = 81, .box_w = 4, .box_h = 7, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 726, .adv_w = 156, .box_w = 8, .box_h = 2, .ofs_x = 1, .ofs_y = 5},
    {.bitmap_index = 734, .adv_w = 77, .box_w = 3, .box_h = 3, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 739, .adv_w = 129, .box_w = 8, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 799, .adv_w = 186, .box_w = 11, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 882, .adv_w = 186, .box_w = 7, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 935, .adv_w = 186, .box_w = 10, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1010, .adv_w = 186, .box_w = 11, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1093, .adv_w = 186, .box_w = 12, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1183, .adv_w = 186, .box_w = 10, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1258, .adv_w = 186, .box_w = 10, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1333, .adv_w = 186, .box_w = 10, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1408, .adv_w = 186, .box_w = 11, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1491, .adv_w = 186, .box_w = 11, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1574, .adv_w = 85, .box_w = 3, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1591, .adv_w = 88, .box_w = 4, .box_h = 15, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 1621, .adv_w = 186, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 1682, .adv_w = 186, .box_w = 11, .box_h = 6, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 1715, .adv_w = 186, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 1776, .adv_w = 144, .box_w = 10, .box_h = 15, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 1851, .adv_w = 314, .box_w = 19, .box_h = 19, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 2032, .adv_w = 219, .box_w = 14, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2137, .adv_w = 211, .box_w = 12, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2227, .adv_w = 212, .box_w = 13, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2325, .adv_w = 232, .box_w = 13, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2423, .adv_w = 192, .box_w = 11, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2506, .adv_w = 182, .box_w = 10, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2581, .adv_w = 226, .box_w = 14, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2686, .adv_w = 242, .box_w = 13, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2784, .adv_w = 88, .box_w = 3, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2807, .adv_w = 153, .box_w = 9, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2875, .adv_w = 224, .box_w = 13, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2973, .adv_w = 183, .box_w = 10, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3048, .adv_w = 278, .box_w = 15, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3161, .adv_w = 239, .box_w = 13, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3259, .adv_w = 246, .box_w = 15, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3372, .adv_w = 195, .box_w = 11, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3455, .adv_w = 246, .box_w = 16, .box_h = 18, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 3599, .adv_w = 210, .box_w = 13, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3697, .adv_w = 185, .box_w = 12, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3787, .adv_w = 186, .box_w = 12, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3877, .adv_w = 237, .box_w = 13, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3975, .adv_w = 217, .box_w = 14, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4080, .adv_w = 313, .box_w = 20, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4230, .adv_w = 217, .box_w = 14, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4335, .adv_w = 204, .box_w = 13, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4433, .adv_w = 186, .box_w = 12, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4523, .adv_w = 114, .box_w = 6, .box_h = 19, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 4580, .adv_w = 129, .box_w = 8, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4640, .adv_w = 114, .box_w = 6, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4697, .adv_w = 157, .box_w = 10, .box_h = 8, .ofs_x = 0, .ofs_y = 7},
    {.bitmap_index = 4737, .adv_w = 140, .box_w = 9, .box_h = 2, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 4746, .adv_w = 101, .box_w = 5, .box_h = 4, .ofs_x = 1, .ofs_y = 12},
    {.bitmap_index = 4756, .adv_w = 177, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4811, .adv_w = 196, .box_w = 11, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4894, .adv_w = 159, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4949, .adv_w = 196, .box_w = 11, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5032, .adv_w = 176, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5093, .adv_w = 114, .box_w = 8, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5157, .adv_w = 196, .box_w = 11, .box_h = 16, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 5245, .adv_w = 188, .box_w = 10, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5320, .adv_w = 80, .box_w = 3, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5343, .adv_w = 80, .box_w = 7, .box_h = 20, .ofs_x = -3, .ofs_y = -5},
    {.bitmap_index = 5413, .adv_w = 173, .box_w = 10, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5488, .adv_w = 80, .box_w = 3, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5511, .adv_w = 275, .box_w = 15, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5594, .adv_w = 188, .box_w = 10, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5649, .adv_w = 190, .box_w = 12, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5715, .adv_w = 196, .box_w = 11, .box_h = 16, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 5803, .adv_w = 196, .box_w = 11, .box_h = 16, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 5891, .adv_w = 125, .box_w = 7, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5930, .adv_w = 147, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5980, .adv_w = 124, .box_w = 8, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6036, .adv_w = 188, .box_w = 10, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 6091, .adv_w = 170, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6152, .adv_w = 254, .box_w = 16, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6240, .adv_w = 164, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6301, .adv_w = 172, .box_w = 11, .box_h = 16, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 6389, .adv_w = 153, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6439, .adv_w = 123, .box_w = 8, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6515, .adv_w = 63, .box_w = 2, .box_h = 20, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 6535, .adv_w = 123, .box_w = 7, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6602, .adv_w = 186, .box_w = 10, .box_h = 4, .ofs_x = 1, .ofs_y = 5},
    {.bitmap_index = 6622, .adv_w = 320, .box_w = 20, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 6832, .adv_w = 320, .box_w = 20, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6982, .adv_w = 320, .box_w = 20, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7172, .adv_w = 320, .box_w = 20, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7322, .adv_w = 220, .box_w = 14, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7427, .adv_w = 320, .box_w = 20, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 7637, .adv_w = 320, .box_w = 20, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 7847, .adv_w = 360, .box_w = 23, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 8066, .adv_w = 320, .box_w = 20, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 8276, .adv_w = 360, .box_w = 23, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8449, .adv_w = 320, .box_w = 20, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 8659, .adv_w = 160, .box_w = 10, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8739, .adv_w = 240, .box_w = 15, .box_h = 16, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 8859, .adv_w = 360, .box_w = 23, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 9078, .adv_w = 320, .box_w = 20, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9228, .adv_w = 220, .box_w = 14, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 9375, .adv_w = 280, .box_w = 13, .box_h = 19, .ofs_x = 2, .ofs_y = -2},
    {.bitmap_index = 9499, .adv_w = 280, .box_w = 18, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 9688, .adv_w = 280, .box_w = 18, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 9859, .adv_w = 280, .box_w = 18, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 10030, .adv_w = 280, .box_w = 13, .box_h = 19, .ofs_x = 2, .ofs_y = -2},
    {.bitmap_index = 10154, .adv_w = 280, .box_w = 19, .box_h = 19, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 10335, .adv_w = 200, .box_w = 11, .box_h = 19, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 10440, .adv_w = 200, .box_w = 11, .box_h = 19, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 10545, .adv_w = 280, .box_w = 18, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 10716, .adv_w = 280, .box_w = 18, .box_h = 5, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 10761, .adv_w = 360, .box_w = 23, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 10934, .adv_w = 400, .box_w = 26, .box_h = 21, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 11207, .adv_w = 360, .box_w = 24, .box_h = 21, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 11459, .adv_w = 320, .box_w = 20, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 11649, .adv_w = 280, .box_w = 18, .box_h = 11, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 11748, .adv_w = 280, .box_w = 18, .box_h = 11, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 11847, .adv_w = 400, .box_w = 26, .box_h = 16, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 12055, .adv_w = 320, .box_w = 20, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 12205, .adv_w = 320, .box_w = 20, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 12415, .adv_w = 320, .box_w = 21, .box_h = 21, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 12636, .adv_w = 280, .box_w = 18, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 12807, .adv_w = 280, .box_w = 18, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 12996, .adv_w = 280, .box_w = 18, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 13167, .adv_w = 280, .box_w = 18, .box_h = 17, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 13320, .adv_w = 320, .box_w = 20, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 13470, .adv_w = 200, .box_w = 14, .box_h = 21, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 13617, .adv_w = 280, .box_w = 18, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 13806, .adv_w = 280, .box_w = 18, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 13995, .adv_w = 360, .box_w = 23, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 14168, .adv_w = 320, .box_w = 22, .box_h = 21, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 14399, .adv_w = 240, .box_w = 15, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 14557, .adv_w = 400, .box_w = 25, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 14795, .adv_w = 400, .box_w = 25, .box_h = 13, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 14958, .adv_w = 400, .box_w = 25, .box_h = 13, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 15121, .adv_w = 400, .box_w = 25, .box_h = 13, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 15284, .adv_w = 400, .box_w = 25, .box_h = 13, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 15447, .adv_w = 400, .box_w = 25, .box_h = 13, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 15610, .adv_w = 400, .box_w = 26, .box_h = 17, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 15831, .adv_w = 280, .box_w = 16, .box_h = 21, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 15999, .adv_w = 280, .box_w = 18, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 16188, .adv_w = 320, .box_w = 21, .box_h = 21, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 16409, .adv_w = 400, .box_w = 25, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 16597, .adv_w = 240, .box_w = 15, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 16755, .adv_w = 322, .box_w = 21, .box_h = 13, .ofs_x = 0, .ofs_y = 1}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_1[] = {
    0x0, 0x7, 0xa, 0xb, 0xc, 0x10, 0x12, 0x14,
    0x18, 0x1b, 0x20, 0x25, 0x26, 0x27, 0x3d, 0x42,
    0x47, 0x4a, 0x4b, 0x4c, 0x50, 0x51, 0x52, 0x53,
    0x66, 0x67, 0x6d, 0x6f, 0x70, 0x73, 0x76, 0x77,
    0x78, 0x7a, 0x92, 0x94, 0xc3, 0xc4, 0xc6, 0xc8,
    0xdf, 0xe6, 0xe9, 0xf2, 0x11b, 0x123, 0x15a, 0x1ea,
    0x23f, 0x240, 0x241, 0x242, 0x243, 0x286, 0x292, 0x2ec,
    0x303, 0x559, 0x7c1, 0x8a1
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 61441, .range_length = 2210, .glyph_id_start = 96,
        .unicode_list = unicode_list_1, .glyph_id_ofs_list = NULL, .list_length = 60, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/


/*Map glyph_ids to kern left classes*/
static const uint8_t kern_left_class_mapping[] =
{
    0, 0, 0, 1, 0, 0, 0, 0,
    1, 2, 3, 0, 4, 0, 4, 0,
    5, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 4,
    6, 7, 8, 9, 10, 7, 11, 12,
    13, 14, 14, 15, 16, 17, 14, 14,
    7, 18, 0, 19, 20, 21, 15, 5,
    22, 23, 24, 25, 2, 8, 3, 0,
    0, 0, 26, 27, 28, 29, 30, 31,
    32, 26, 0, 33, 34, 29, 26, 26,
    27, 27, 0, 35, 36, 37, 32, 38,
    38, 39, 38, 40, 2, 0, 3, 4,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0
};

/*Map glyph_ids to kern right classes*/
static const uint8_t kern_right_class_mapping[] =
{
    0, 1, 0, 2, 0, 0, 0, 0,
    2, 0, 3, 0, 4, 5, 4, 5,
    6, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 4, 0, 0,
    7, 8, 6, 9, 8, 9, 9, 9,
    8, 9, 9, 10, 9, 9, 9, 9,
    8, 9, 8, 9, 11, 12, 13, 14,
    15, 16, 17, 18, 0, 14, 3, 0,
    5, 0, 19, 20, 21, 21, 21, 22,
    21, 20, 0, 23, 20, 20, 24, 24,
    21, 0, 21, 24, 25, 26, 27, 28,
    28, 29, 30, 31, 0, 0, 3, 4,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0
};

/*Kern values between classes*/
static const int8_t kern_class_values[] =
{
    0, 0, 0, 0, 0, 0, 5, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 6, 0, 6, 6,
    3, 0, 4, 0, 0, 22, 0, 0,
    6, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 8, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -17, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -13, 6, 6, -14,
    -45, -29, 7, -12, 0, -39, -3, 6,
    0, 0, 0, 0, 0, 0, -24, 0,
    -24, -8, 0, -15, -19, -2, -15, -14,
    -18, -14, -17, 0, 0, 0, -11, -32,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -7, 0, 0, -14, -12,
    0, 0, 0, -12, 0, -10, 0, -12,
    -6, -11, -18, -7, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -13, -37, 0, -20, 8, 0, -22,
    -12, 0, 0, 0, -27, -5, -29, -22,
    0, -36, 7, 0, 0, -4, 0, 0,
    0, 0, 0, 0, -14, 0, -14, 0,
    0, -4, 0, 0, 0, -5, 0, 0,
    0, 4, 0, -11, 0, -14, -5, 0,
    -18, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    3, 0, -10, 7, 0, 11, -5, 0,
    0, 0, 2, 0, -1, 0, 0, -1,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -7, 0, -7, 0, 0, 0,
    0, 0, 3, 0, 4, -3, 0, 4,
    0, 0, 0, -4, 0, 0, -4, 0,
    -3, 0, -4, -4, 0, 0, -3, -3,
    -3, -7, -3, -7, 0, -3, 8, 0,
    2, -42, -19, 13, -2, 0, -44, 0,
    7, 0, 0, 0, 0, 0, 0, -13,
    0, -9, -3, 0, -6, 0, -4, 0,
    -7, -12, -7, -8, 0, 0, 0, 0,
    6, 0, 4, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 2, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -10, -5,
    0, 0, 0, -10, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -13, 0, 0, -35, 6, 0, -2,
    -19, -5, 0, -5, 0, -8, 0, 0,
    0, 0, 0, -9, 0, -11, -13, 0,
    -5, -5, -13, -13, -21, -11, -21, 0,
    -16, -33, 0, -29, 8, 0, -23, -15,
    0, 5, -3, -42, -14, -47, -35, 0,
    -57, 0, -2, 0, -6, -6, 0, -1,
    0, -9, -8, -30, 0, -30, 0, -3,
    3, 0, 4, -46, -26, 5, 0, 0,
    -51, 0, 0, 0, -1, -2, -8, 0,
    -10, -11, 0, -10, 0, 0, 0, 0,
    0, 0, 4, 0, 4, 0, 0, -4,
    0, -3, 12, 0, -1, -3, 0, 0,
    2, -4, -4, -9, -6, 0, -16, 0,
    0, 0, -4, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 3, 0, 0, 0, 7,
    0, 0, -5, 0, 0, -7, 2, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -10, 10, 0, -26,
    -36, -27, 12, -10, 0, -45, 0, 7,
    0, 6, 6, 0, 0, 0, -38, 0,
    -36, -14, 0, -29, -36, -11, -28, -34,
    -34, -34, -28, -3, 6, 0, -8, -25,
    -22, 0, -6, 0, -23, -1, 6, 0,
    0, 0, 0, 0, 0, -25, 0, -20,
    -5, 0, -14, -15, 0, -11, -7, -11,
    -7, -11, 0, 0, 6, -29, 3, 0,
    4, -11, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -8, 0, -11, 0,
    0, -5, -6, -10, -10, -20, 0, -20,
    0, -10, 4, 6, -23, -44, -36, 3,
    -18, 0, -44, -8, 0, 0, 0, 0,
    0, 0, 0, -36, 0, -34, -16, 0,
    -27, -30, -11, -25, -24, -22, -24, -25,
    0, 0, 3, -15, 6, 0, 3, -9,
    0, 0, -3, 0, 0, 0, 0, 0,
    0, 0, -2, 0, -5, 0, 0, 0,
    0, 0, 0, -11, 0, -11, 0, 0,
    -13, 0, -1, 0, 0, -10, 0, 0,
    0, 0, -28, 0, -24, -21, -3, -32,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -4, 0, -4, 0, 0, -16,
    0, -3, -10, 0, -13, 0, 0, 0,
    0, -36, 0, -24, -20, -11, -34, 0,
    -3, 0, 0, -3, 0, 0, 0, -2,
    0, -7, -8, -7, -7, 0, 1, 0,
    6, 8, 0, -4, 0, 0, 0, 0,
    -25, 0, -17, -11, 6, -25, 0, 1,
    0, -1, 4, 0, 0, 0, 7, 0,
    0, 2, 0, 4, 0, 0, 4, 0,
    0, 0, 4, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -2, 0, 5, -1,
    0, -8, 0, 0, 0, 0, -23, 0,
    -21, -16, -4, -29, 0, 0, 0, 0,
    0, 0, 0, 4, 0, 0, -1, -2,
    -1, 0, 0, 15, 0, -2, -25, 0,
    14, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -9, 0, -8, 1,
    0, 0, 0, 4, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -9,
    0, 0, 0, 0, -29, 0, -15, -14,
    0, -27, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 4, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 13, 0,
    0, 0, 0, 0, 0, 0, 0, -7,
    0, 0, -9, 8, 0, -15, 0, 0,
    0, 0, -29, 0, -20, -18, 0, -27,
    0, -9, 0, -7, 0, 0, 0, -4,
    0, -3, 0, 0, 0, 0, 0, 8,
    0, 3, -35, -15, -9, 0, 0, -38,
    0, 0, 0, -14, 0, -16, -24, 0,
    -13, 0, -11, 0, 0, 0, -2, 9,
    0, 0, 0, 0, 0, 0, -9, 0,
    0, 0, 0, -10, 0, 0, 0, 0,
    -32, 0, -23, -16, -1, -34, 0, 0,
    0, 0, 0, 0, 0, 3, 0, 0,
    -3, 0, -3, 4, 0, -2, 3, -1,
    9, 0, -8, 0, 0, 0, 0, -22,
    0, -15, 0, -1, -22, 0, 0, 0,
    -3, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -29,
    -14, -9, 0, 0, -26, 0, -34, 0,
    -14, -7, -20, -24, 0, -6, 0, -7,
    0, 0, 0, -3, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -6, 3, 0,
    -13, 0, 0, 0, 0, -34, 0, -18,
    -11, 0, -22, 0, -5, 0, -8, 0,
    0, 0, 0, 4, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 3, 0, -10,
    0, 0, 0, 0, -31, 0, -18, -10,
    0, -24, 0, -5, 0, -7, 0, 0,
    0, 1, 0, 0, 0, 0, 0, 0
};


/*Collect the kern class' data in one place*/
static const lv_font_fmt_txt_kern_classes_t kern_classes =
{
    .class_pair_values   = kern_class_values,
    .left_class_mapping  = kern_left_class_mapping,
    .right_class_mapping = kern_right_class_mapping,
    .left_class_cnt      = 40,
    .right_class_cnt     = 31,
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LVGL_VERSION_MAJOR == 8
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
#endif

#if LVGL_VERSION_MAJOR >= 8
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_classes,
    .kern_scale = 16,
    .cmap_num = 2,
    .bpp = 4,
    .kern_classes = 1,
    .bitmap_format = 0,
#if LVGL_VERSION_MAJOR == 8
    .cache = &cache
#endif
};



/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LVGL_VERSION_MAJOR >= 8
const lv_font_t lv_font_HarmonyOS_Sans_SC_Medium_20 = {
#else
lv_font_t lv_font_HarmonyOS_Sans_SC_Medium_20 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 20,          /*The maximum line height required by the font*/
    .base_line = 3,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -1,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc,          /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
#if LV_VERSION_CHECK(8, 2, 0) || LVGL_VERSION_MAJOR >= 9
    .fallback = NULL,
#endif
    .user_data = NULL,
};



#endif /*#if LV_FONT_HARMONYOS_SANS_SC_MEDIUM_20*/

