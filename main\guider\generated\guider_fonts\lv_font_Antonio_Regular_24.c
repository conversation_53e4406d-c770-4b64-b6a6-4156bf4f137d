/*
* Copyright (c) 2011-12, vernon adams (<EMAIL>), with Reserved Font Names '<PERSON>'.
* This Font Software is licensed under the SIL Open Font License, Version 1.1.
* And is also available with a FAQ at: http://scripts.sil.org/OFL
*/
/*******************************************************************************
 * Size: 24 px
 * Bpp: 4
 * Opts: undefined
 ******************************************************************************/

#ifdef LV_LVGL_H_INCLUDE_SIMPLE
#include "lvgl.h"
#else
#include "lvgl.h"
#endif

#ifndef LV_FONT_ANTONIO_REGULAR_24
#define LV_FONT_ANTONIO_REGULAR_24 1
#endif

#if LV_FONT_ANTONIO_REGULAR_24

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0x4f, 0xf7, 0x3f, 0xf6, 0x2f, 0xf5, 0x1f, 0xf4,
    0xf, 0xf3, 0xf, 0xf2, 0xe, 0xf1, 0xd, 0xf1,
    0xd, 0xf0, 0xc, 0xf0, 0xb, 0xe0, 0xa, 0xd0,
    0x9, 0xc0, 0x8, 0xb0, 0x7, 0xa0, 0x6, 0xa0,
    0x1, 0x10, 0x0, 0x0, 0x2b, 0xb4, 0x3f, 0xf6,
    0x3f, 0xf6,

    /* U+0022 "\"" */
    0x6f, 0xd0, 0xbf, 0x84, 0xfc, 0x9, 0xf7, 0x2f,
    0xa0, 0x7f, 0x51, 0xf8, 0x6, 0xf4, 0xf, 0x70,
    0x4f, 0x20, 0xe5, 0x3, 0xf0, 0xc, 0x40, 0x1f,
    0x0, 0x61, 0x0, 0x70,

    /* U+0023 "#" */
    0x0, 0x0, 0xd, 0xf2, 0x7, 0xf8, 0x0, 0x0,
    0xf, 0xf0, 0xa, 0xf5, 0x0, 0x0, 0x3f, 0xc0,
    0xd, 0xf2, 0x0, 0x0, 0x7f, 0x90, 0x1f, 0xf0,
    0x0, 0x0, 0xaf, 0x50, 0x4f, 0xc0, 0x0, 0x0,
    0xdf, 0x20, 0x7f, 0x90, 0x0, 0x0, 0xff, 0x0,
    0xaf, 0x60, 0x7, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x4, 0x9c, 0xfc, 0x9a, 0xff, 0x99, 0x0, 0x9,
    0xf6, 0x3, 0xfc, 0x0, 0x0, 0xc, 0xf3, 0x6,
    0xf9, 0x0, 0x0, 0xf, 0xf0, 0x9, 0xf6, 0x0,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x19, 0xcf,
    0xd9, 0x9f, 0xf9, 0x92, 0x0, 0x9f, 0x60, 0x3f,
    0xd0, 0x0, 0x0, 0xcf, 0x30, 0x6f, 0xa0, 0x0,
    0x0, 0xff, 0x0, 0x9f, 0x70, 0x0, 0x2, 0xfd,
    0x0, 0xcf, 0x30, 0x0, 0x5, 0xfa, 0x0, 0xff,
    0x0, 0x0, 0x8, 0xf7, 0x2, 0xfd, 0x0, 0x0,
    0xb, 0xf4, 0x5, 0xfa, 0x0, 0x0,

    /* U+0024 "$" */
    0x0, 0xd, 0xa0, 0x0, 0x0, 0xd, 0xa0, 0x0,
    0x0, 0xd, 0xa0, 0x0, 0x8, 0xff, 0xfd, 0x20,
    0x6f, 0xfe, 0xff, 0xc0, 0xcf, 0xb0, 0x4f, 0xf1,
    0xef, 0x60, 0xf, 0xf5, 0xef, 0x70, 0xd, 0xf6,
    0xcf, 0xc0, 0xd, 0xf6, 0x6f, 0xf5, 0x2, 0x31,
    0xd, 0xff, 0x40, 0x0, 0x2, 0xef, 0xf4, 0x0,
    0x0, 0x2e, 0xff, 0x20, 0x0, 0x2, 0xff, 0xc0,
    0x45, 0x20, 0x6f, 0xf4, 0xdf, 0x80, 0xe, 0xf9,
    0xcf, 0x80, 0xb, 0xfb, 0xbf, 0xa0, 0xb, 0xfa,
    0x8f, 0xe1, 0x1f, 0xf8, 0x2f, 0xff, 0xff, 0xf2,
    0x4, 0xef, 0xfe, 0x50, 0x0, 0xb, 0xd0, 0x0,
    0x0, 0xa, 0xd0, 0x0, 0x0, 0xa, 0xd0, 0x0,

    /* U+0025 "%" */
    0x0, 0x9e, 0xfe, 0x80, 0x0, 0x0, 0x0, 0x4f,
    0xb0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xff, 0x80,
    0x0, 0x0, 0xa, 0xf5, 0x0, 0x0, 0x0, 0xe,
    0xf5, 0x6, 0xfe, 0x0, 0x0, 0x1, 0xfe, 0x0,
    0x0, 0x0, 0x1, 0xff, 0x0, 0x1f, 0xf0, 0x0,
    0x0, 0x7f, 0x90, 0x0, 0x0, 0x0, 0x2f, 0xf0,
    0x1, 0xff, 0x10, 0x0, 0xd, 0xf2, 0x0, 0x0,
    0x0, 0x2, 0xff, 0x0, 0x1f, 0xf1, 0x0, 0x3,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf0, 0x1,
    0xff, 0x10, 0x0, 0xaf, 0x60, 0x0, 0x0, 0x0,
    0x2, 0xff, 0x0, 0x1f, 0xf1, 0x0, 0x1f, 0xf0,
    0x3, 0xcf, 0xfc, 0x40, 0x2f, 0xf0, 0x1, 0xff,
    0x10, 0x6, 0xf9, 0x1, 0xef, 0xff, 0xff, 0x22,
    0xff, 0x0, 0x1f, 0xf1, 0x0, 0xdf, 0x30, 0x5f,
    0xd0, 0xc, 0xf7, 0x1f, 0xf0, 0x2, 0xff, 0x0,
    0x3f, 0xd0, 0x8, 0xfa, 0x0, 0x8f, 0xa0, 0xef,
    0x50, 0x6f, 0xd0, 0x9, 0xf7, 0x0, 0x9f, 0x90,
    0x7, 0xfa, 0x8, 0xff, 0xff, 0xf7, 0x0, 0xff,
    0x10, 0x9, 0xf9, 0x0, 0x7f, 0xb0, 0x9, 0xef,
    0xe8, 0x0, 0x6f, 0xa0, 0x0, 0x9f, 0x90, 0x7,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf4, 0x0,
    0x9, 0xf9, 0x0, 0x7f, 0xb0, 0x0, 0x0, 0x0,
    0x2, 0xfd, 0x0, 0x0, 0x9f, 0x90, 0x7, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0x70, 0x0, 0x8,
    0xf9, 0x0, 0x7f, 0xa0, 0x0, 0x0, 0x0, 0xf,
    0xf1, 0x0, 0x0, 0x7f, 0xa0, 0x8, 0xf9, 0x0,
    0x0, 0x0, 0x5, 0xfb, 0x0, 0x0, 0x5, 0xfc,
    0x0, 0xbf, 0x70, 0x0, 0x0, 0x0, 0xcf, 0x50,
    0x0, 0x0, 0xe, 0xfc, 0xcf, 0xf1, 0x0, 0x0,
    0x0, 0x2f, 0xe0, 0x0, 0x0, 0x0, 0x3c, 0xff,
    0xc4, 0x0,

    /* U+0026 "&" */
    0x0, 0x3, 0xcf, 0xfa, 0x10, 0x0, 0x0, 0x1f,
    0xfd, 0xef, 0xa0, 0x0, 0x0, 0x8f, 0xc0, 0xd,
    0xf0, 0x0, 0x0, 0xbf, 0x80, 0xb, 0xf2, 0x0,
    0x0, 0xbf, 0x90, 0xb, 0xf2, 0x0, 0x0, 0x8f,
    0xc0, 0xe, 0xf0, 0x0, 0x0, 0x3f, 0xf1, 0x5f,
    0xb0, 0x0, 0x0, 0xe, 0xf7, 0xef, 0x30, 0x0,
    0x0, 0x8, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x1,
    0xff, 0xd0, 0x0, 0x0, 0x0, 0x6, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0xf1, 0x0, 0x70,
    0x0, 0xdf, 0x9d, 0xf7, 0x7, 0xf4, 0x7, 0xfe,
    0x15, 0xfd, 0x2f, 0xf1, 0xe, 0xf8, 0x0, 0xef,
    0xef, 0x60, 0x1f, 0xf3, 0x0, 0x7f, 0xfd, 0x0,
    0x2f, 0xf0, 0x0, 0xf, 0xf7, 0x0, 0x1f, 0xf0,
    0x0, 0x4f, 0xfa, 0x0, 0xe, 0xf7, 0x1, 0xdf,
    0xff, 0x20, 0x6, 0xff, 0xef, 0xfa, 0x9f, 0x90,
    0x0, 0x7e, 0xfd, 0x70, 0x2f, 0xf1,

    /* U+0027 "'" */
    0xaf, 0x98, 0xf7, 0x7f, 0x55, 0xf4, 0x4f, 0x22,
    0xf1, 0x1f, 0x0, 0x70,

    /* U+0028 "(" */
    0x2, 0xbf, 0x80, 0xef, 0xf7, 0x4f, 0xf3, 0x6,
    0xff, 0x0, 0x7f, 0xf0, 0x7, 0xff, 0x0, 0x7f,
    0xf0, 0x7, 0xff, 0x0, 0x7f, 0xf0, 0x7, 0xff,
    0x0, 0x7f, 0xf0, 0x7, 0xff, 0x0, 0x7f, 0xf0,
    0x7, 0xff, 0x0, 0x7f, 0xf0, 0x7, 0xff, 0x0,
    0x7f, 0xf0, 0x7, 0xff, 0x0, 0x6f, 0xf0, 0x5,
    0xff, 0x0, 0x2f, 0xf5, 0x0, 0xdf, 0xf9, 0x1,
    0x8c, 0x70,

    /* U+0029 ")" */
    0xee, 0x90, 0xd, 0xff, 0x90, 0x9, 0xfe, 0x0,
    0x5f, 0xf0, 0x5, 0xff, 0x10, 0x4f, 0xf1, 0x4,
    0xff, 0x20, 0x4f, 0xf2, 0x4, 0xff, 0x20, 0x4f,
    0xf2, 0x4, 0xff, 0x20, 0x4f, 0xf2, 0x4, 0xff,
    0x20, 0x4f, 0xf2, 0x4, 0xff, 0x20, 0x4f, 0xf2,
    0x4, 0xff, 0x20, 0x4f, 0xf1, 0x4, 0xff, 0x10,
    0x5f, 0xf0, 0xa, 0xfe, 0xe, 0xff, 0x80, 0xcb,
    0x70, 0x0,

    /* U+002A "*" */
    0x0, 0x4, 0xf8, 0x0, 0x0, 0x70, 0x2f, 0x60,
    0x72, 0x5f, 0xd3, 0xf6, 0xbf, 0xa0, 0x7e, 0xff,
    0xff, 0x81, 0x0, 0x3e, 0xff, 0x60, 0x2, 0xbf,
    0xcf, 0xdf, 0xc4, 0x3f, 0x91, 0xf5, 0x7f, 0x80,
    0x30, 0x2f, 0x70, 0x30, 0x0, 0x3, 0xc6, 0x0,
    0x0,

    /* U+002B "+" */
    0x0, 0x18, 0x40, 0x0, 0x0, 0x2f, 0x90, 0x0,
    0x0, 0x2f, 0x90, 0x0, 0x8a, 0xbf, 0xda, 0xa2,
    0xcf, 0xff, 0xff, 0xf3, 0x0, 0x2f, 0x90, 0x0,
    0x0, 0x2f, 0x90, 0x0, 0x0, 0x2f, 0x90, 0x0,

    /* U+002C "," */
    0x7c, 0xc5, 0x9f, 0xf6, 0x9f, 0xf6, 0x4, 0xf3,
    0x7, 0xf0, 0xb, 0xa0, 0xe, 0x60,

    /* U+002D "-" */
    0x99, 0x99, 0x93, 0xff, 0xff, 0xf5,

    /* U+002E "." */
    0x55, 0xf, 0xf1, 0xff, 0x10,

    /* U+002F "/" */
    0x0, 0x0, 0xf, 0xf3, 0x0, 0x0, 0x3f, 0xe0,
    0x0, 0x0, 0x7f, 0xb0, 0x0, 0x0, 0xbf, 0x70,
    0x0, 0x0, 0xef, 0x30, 0x0, 0x2, 0xff, 0x0,
    0x0, 0x6, 0xfb, 0x0, 0x0, 0xa, 0xf7, 0x0,
    0x0, 0xe, 0xf3, 0x0, 0x0, 0x2f, 0xf0, 0x0,
    0x0, 0x6f, 0xb0, 0x0, 0x0, 0xaf, 0x70, 0x0,
    0x0, 0xef, 0x30, 0x0, 0x2, 0xff, 0x0, 0x0,
    0x6, 0xfc, 0x0, 0x0, 0xa, 0xf8, 0x0, 0x0,
    0xe, 0xf4, 0x0, 0x0, 0x2f, 0xf0, 0x0, 0x0,
    0x5f, 0xc0, 0x0, 0x0, 0x9f, 0x80, 0x0, 0x0,
    0xdf, 0x40, 0x0, 0x0,

    /* U+0030 "0" */
    0x2, 0xbe, 0xfe, 0x90, 0x1, 0xef, 0xff, 0xff,
    0xa0, 0x7f, 0xf5, 0x1a, 0xff, 0x2a, 0xfc, 0x0,
    0x1f, 0xf5, 0xcf, 0xa0, 0x0, 0xff, 0x7c, 0xf9,
    0x0, 0xe, 0xf7, 0xdf, 0x90, 0x0, 0xef, 0x8d,
    0xf9, 0x0, 0xe, 0xf8, 0xdf, 0x90, 0x0, 0xef,
    0x8d, 0xf9, 0x0, 0xe, 0xf8, 0xdf, 0x90, 0x0,
    0xef, 0x8d, 0xf9, 0x0, 0xe, 0xf8, 0xdf, 0x90,
    0x0, 0xef, 0x8d, 0xf9, 0x0, 0xe, 0xf8, 0xdf,
    0x90, 0x0, 0xef, 0x8c, 0xf9, 0x0, 0xe, 0xf7,
    0xcf, 0xa0, 0x0, 0xff, 0x6a, 0xfc, 0x0, 0x1f,
    0xf4, 0x6f, 0xf4, 0x19, 0xff, 0x10, 0xef, 0xff,
    0xff, 0xa0, 0x2, 0xbe, 0xfe, 0x80, 0x0,

    /* U+0031 "1" */
    0x0, 0x7, 0xf8, 0x0, 0x4f, 0xf8, 0x3a, 0xff,
    0xf8, 0x7f, 0xce, 0xf8, 0x23, 0xe, 0xf8, 0x0,
    0xe, 0xf8, 0x0, 0xe, 0xf8, 0x0, 0xe, 0xf8,
    0x0, 0xe, 0xf8, 0x0, 0xe, 0xf8, 0x0, 0xe,
    0xf8, 0x0, 0xe, 0xf8, 0x0, 0xe, 0xf8, 0x0,
    0xe, 0xf8, 0x0, 0xe, 0xf8, 0x0, 0xe, 0xf8,
    0x0, 0xe, 0xf8, 0x0, 0xe, 0xf8, 0x0, 0xe,
    0xf8, 0x0, 0xe, 0xf8, 0x0, 0xe, 0xf8,

    /* U+0032 "2" */
    0x0, 0x5c, 0xfe, 0x70, 0x0, 0x4f, 0xff, 0xff,
    0x50, 0xa, 0xfd, 0x2a, 0xfc, 0x0, 0xdf, 0x70,
    0x4f, 0xf0, 0xf, 0xf5, 0x1, 0xff, 0x20, 0xff,
    0x40, 0x1f, 0xf3, 0x1f, 0xf4, 0x3, 0xff, 0x21,
    0xff, 0x40, 0x4f, 0xf0, 0x7, 0x71, 0x7, 0xfd,
    0x0, 0x0, 0x0, 0xdf, 0x80, 0x0, 0x0, 0x5f,
    0xf2, 0x0, 0x0, 0xd, 0xfa, 0x0, 0x0, 0x6,
    0xff, 0x20, 0x0, 0x0, 0xdf, 0x90, 0x0, 0x0,
    0x5f, 0xf1, 0x0, 0x0, 0xc, 0xf8, 0x0, 0x0,
    0x3, 0xff, 0x20, 0x0, 0x0, 0x8f, 0xc0, 0x0,
    0x0, 0xc, 0xf8, 0x0, 0x0, 0x0, 0xef, 0xed,
    0xdd, 0xc0, 0xf, 0xff, 0xff, 0xfe, 0x0,

    /* U+0033 "3" */
    0x1, 0xae, 0xfd, 0x70, 0x0, 0xcf, 0xff, 0xff,
    0x70, 0x3f, 0xf7, 0x2a, 0xff, 0x7, 0xfe, 0x0,
    0x2f, 0xf3, 0x9f, 0xc0, 0x0, 0xff, 0x58, 0xeb,
    0x0, 0xf, 0xf6, 0x0, 0x0, 0x0, 0xff, 0x50,
    0x0, 0x0, 0x3f, 0xf2, 0x0, 0x0, 0x1b, 0xfc,
    0x0, 0x0, 0xdf, 0xfd, 0x20, 0x0, 0xc, 0xff,
    0xd2, 0x0, 0x0, 0x1, 0xcf, 0xc0, 0x0, 0x0,
    0x3, 0xff, 0x10, 0x0, 0x0, 0xf, 0xf5, 0x7b,
    0x70, 0x0, 0xff, 0x6a, 0xfb, 0x0, 0xf, 0xf6,
    0x9f, 0xb0, 0x0, 0xff, 0x67, 0xfd, 0x0, 0x2f,
    0xf3, 0x3f, 0xf5, 0x2a, 0xfe, 0x0, 0xcf, 0xff,
    0xff, 0x70, 0x1, 0xae, 0xfd, 0x60, 0x0,

    /* U+0034 "4" */
    0x0, 0x0, 0x7f, 0xf3, 0x0, 0x0, 0xb, 0xff,
    0x30, 0x0, 0x1, 0xff, 0xf3, 0x0, 0x0, 0x5f,
    0xff, 0x30, 0x0, 0xa, 0xef, 0xf3, 0x0, 0x0,
    0xea, 0xff, 0x30, 0x0, 0x3f, 0x6f, 0xf3, 0x0,
    0x8, 0xf1, 0xff, 0x30, 0x0, 0xdd, 0xf, 0xf3,
    0x0, 0x2f, 0x90, 0xff, 0x30, 0x7, 0xf5, 0xf,
    0xf3, 0x0, 0xbf, 0x10, 0xff, 0x30, 0xf, 0xc0,
    0xf, 0xf3, 0x5, 0xf8, 0x0, 0xff, 0x30, 0xaf,
    0x40, 0xf, 0xf3, 0xd, 0xff, 0xff, 0xff, 0xfd,
    0xef, 0xff, 0xff, 0xff, 0xe0, 0x0, 0x0, 0xff,
    0x30, 0x0, 0x0, 0xf, 0xf3, 0x0, 0x0, 0x0,
    0xff, 0x30, 0x0, 0x0, 0xf, 0xf3, 0x0,

    /* U+0035 "5" */
    0x4f, 0xff, 0xff, 0xf3, 0x4f, 0xff, 0xff, 0xf3,
    0x4f, 0xf3, 0x33, 0x30, 0x4f, 0xe0, 0x0, 0x0,
    0x4f, 0xe0, 0x0, 0x0, 0x4f, 0xe0, 0x0, 0x0,
    0x4f, 0xfa, 0xfe, 0x40, 0x4f, 0xff, 0xff, 0xe0,
    0x4f, 0xf5, 0x3f, 0xf4, 0x4f, 0xe0, 0xb, 0xf7,
    0x3, 0x30, 0xa, 0xf9, 0x0, 0x0, 0xa, 0xfa,
    0x0, 0x0, 0xa, 0xfa, 0x0, 0x0, 0xa, 0xfa,
    0x25, 0x40, 0xa, 0xfa, 0x8f, 0xd0, 0xa, 0xfa,
    0x7f, 0xd0, 0xa, 0xf9, 0x6f, 0xe0, 0xb, 0xf8,
    0x4f, 0xf4, 0x3f, 0xf4, 0xd, 0xff, 0xff, 0xe0,
    0x1, 0xae, 0xfc, 0x30,

    /* U+0036 "6" */
    0x1, 0xae, 0xfd, 0x70, 0x0, 0xdf, 0xff, 0xff,
    0x60, 0x4f, 0xf6, 0x2b, 0xfc, 0x8, 0xfd, 0x0,
    0x5f, 0xf0, 0xaf, 0xb0, 0x4, 0xff, 0x1b, 0xfa,
    0x0, 0x3f, 0xf1, 0xcf, 0xa0, 0x0, 0x33, 0xc,
    0xfa, 0x0, 0x0, 0x0, 0xcf, 0xcb, 0xff, 0xd2,
    0xc, 0xff, 0xff, 0xff, 0xd0, 0xcf, 0xe3, 0x7,
    0xff, 0x3c, 0xfa, 0x0, 0xe, 0xf7, 0xcf, 0xa0,
    0x0, 0xcf, 0x8c, 0xfa, 0x0, 0xc, 0xf9, 0xbf,
    0xa0, 0x0, 0xcf, 0x9b, 0xfa, 0x0, 0xc, 0xf9,
    0xaf, 0xb0, 0x0, 0xcf, 0x97, 0xfe, 0x0, 0xe,
    0xf7, 0x3f, 0xf8, 0x27, 0xff, 0x30, 0xbf, 0xff,
    0xff, 0xc0, 0x0, 0x8e, 0xfe, 0x91, 0x0,

    /* U+0037 "7" */
    0xaf, 0xff, 0xff, 0xff, 0x8a, 0xff, 0xff, 0xff,
    0xf8, 0x1, 0x11, 0x11, 0xff, 0x50, 0x0, 0x0,
    0x5f, 0xf0, 0x0, 0x0, 0xa, 0xfa, 0x0, 0x0,
    0x0, 0xff, 0x50, 0x0, 0x0, 0x4f, 0xf1, 0x0,
    0x0, 0x9, 0xfb, 0x0, 0x0, 0x0, 0xef, 0x70,
    0x0, 0x0, 0x2f, 0xf3, 0x0, 0x0, 0x6, 0xff,
    0x0, 0x0, 0x0, 0xaf, 0xb0, 0x0, 0x0, 0xe,
    0xf8, 0x0, 0x0, 0x2, 0xff, 0x50, 0x0, 0x0,
    0x5f, 0xf1, 0x0, 0x0, 0x8, 0xff, 0x0, 0x0,
    0x0, 0xaf, 0xd0, 0x0, 0x0, 0xd, 0xfa, 0x0,
    0x0, 0x0, 0xff, 0x90, 0x0, 0x0, 0x1f, 0xf7,
    0x0, 0x0, 0x2, 0xff, 0x60, 0x0, 0x0,

    /* U+0038 "8" */
    0x2, 0xae, 0xfd, 0x70, 0x0, 0xdf, 0xff, 0xff,
    0x70, 0x5f, 0xf4, 0xa, 0xfe, 0x8, 0xfe, 0x0,
    0x4f, 0xf1, 0x9f, 0xc0, 0x3, 0xff, 0x39, 0xfc,
    0x0, 0x3f, 0xf3, 0x8f, 0xd0, 0x4, 0xff, 0x15,
    0xff, 0x0, 0x6f, 0xe0, 0xe, 0xf6, 0x1c, 0xf8,
    0x0, 0x5f, 0xff, 0xfc, 0x0, 0xa, 0xff, 0xcf,
    0xf3, 0x2, 0xff, 0x40, 0xbf, 0xa0, 0x7f, 0xe0,
    0x6, 0xfe, 0xb, 0xfb, 0x0, 0x3f, 0xf2, 0xcf,
    0xa0, 0x2, 0xff, 0x3c, 0xfa, 0x0, 0x2f, 0xf3,
    0xbf, 0xa0, 0x3, 0xff, 0x3a, 0xfc, 0x0, 0x4f,
    0xf1, 0x6f, 0xf4, 0x1a, 0xfd, 0x0, 0xdf, 0xff,
    0xff, 0x60, 0x2, 0xae, 0xfd, 0x60, 0x0,

    /* U+0039 "9" */
    0x0, 0x6d, 0xff, 0xb2, 0x0, 0x5f, 0xff, 0xff,
    0xd0, 0xc, 0xfc, 0x36, 0xff, 0x50, 0xff, 0x50,
    0xe, 0xf8, 0x2f, 0xf3, 0x0, 0xcf, 0xa3, 0xff,
    0x20, 0xb, 0xfb, 0x3f, 0xf2, 0x0, 0xbf, 0xb3,
    0xff, 0x20, 0xb, 0xfb, 0x2f, 0xf3, 0x0, 0xbf,
    0xb0, 0xff, 0x50, 0xb, 0xfb, 0xc, 0xfc, 0x12,
    0xdf, 0xb0, 0x5f, 0xff, 0xff, 0xfb, 0x0, 0x6d,
    0xec, 0xdf, 0xb0, 0x0, 0x0, 0xb, 0xfb, 0x3,
    0x42, 0x0, 0xbf, 0xb0, 0xcf, 0x70, 0xb, 0xfa,
    0xc, 0xf8, 0x0, 0xbf, 0x90, 0xaf, 0x90, 0xc,
    0xf7, 0x6, 0xfe, 0x34, 0xff, 0x30, 0x1f, 0xff,
    0xff, 0xc0, 0x0, 0x3c, 0xfe, 0xa1, 0x0,

    /* U+003A ":" */
    0x3f, 0xfb, 0x3f, 0xfb, 0x3f, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xfa, 0x3f, 0xfb, 0x3f, 0xfb,

    /* U+003B ";" */
    0x25, 0x54, 0x7, 0xff, 0xd0, 0x7f, 0xfd, 0x7,
    0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0xf0, 0x5f, 0xff, 0x5, 0xff, 0xf0, 0x0, 0xed,
    0x0, 0x1f, 0x80, 0x5, 0xf4, 0x0, 0x8f, 0x0,
    0x5, 0x60, 0x0,

    /* U+003C "<" */
    0x0, 0x0, 0x0, 0x14, 0x0, 0x0, 0x5, 0xe8,
    0x0, 0x1, 0xbf, 0xf7, 0x0, 0x5e, 0xff, 0x80,
    0x1b, 0xff, 0xc2, 0x0, 0x4f, 0xe6, 0x0, 0x0,
    0x4f, 0xd4, 0x0, 0x0, 0x1c, 0xff, 0x91, 0x0,
    0x0, 0x7f, 0xfe, 0x50, 0x0, 0x3, 0xdf, 0xf6,
    0x0, 0x0, 0x8, 0xf8, 0x0, 0x0, 0x0, 0x35,

    /* U+003D "=" */
    0xd, 0xdd, 0xdd, 0xdb, 0xf, 0xff, 0xff, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xee, 0xee, 0xeb, 0xf, 0xff, 0xff, 0xfd,

    /* U+003E ">" */
    0x50, 0x0, 0x0, 0xf, 0xb1, 0x0, 0x0, 0xff,
    0xe5, 0x0, 0x2, 0xcf, 0xfa, 0x10, 0x0, 0x7f,
    0xfe, 0x50, 0x0, 0x2b, 0xfc, 0x0, 0x0, 0x8f,
    0xc0, 0x4, 0xdf, 0xf7, 0x1a, 0xff, 0xd3, 0xe,
    0xff, 0x80, 0x0, 0xfd, 0x30, 0x0, 0x8, 0x0,
    0x0, 0x0,

    /* U+003F "?" */
    0x1, 0xae, 0xfb, 0x20, 0xb, 0xff, 0xff, 0xd0,
    0x2f, 0xf6, 0x6f, 0xf2, 0x4f, 0xf1, 0x1f, 0xf4,
    0x5f, 0xf0, 0xf, 0xf5, 0x5f, 0xf0, 0xf, 0xf6,
    0x5f, 0xf0, 0xf, 0xf5, 0x15, 0x50, 0x2f, 0xf5,
    0x0, 0x0, 0x4f, 0xf2, 0x0, 0x0, 0x9f, 0xf0,
    0x0, 0x5, 0xff, 0x80, 0x1, 0xcf, 0xfb, 0x0,
    0x3, 0xff, 0x40, 0x0, 0x3, 0xfe, 0x0, 0x0,
    0x3, 0xfe, 0x0, 0x0, 0x0, 0x22, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x87, 0x0, 0x0,
    0x2, 0xfe, 0x0, 0x0, 0x2, 0xfe, 0x0, 0x0,
    0x2, 0xfe, 0x0, 0x0,

    /* U+0040 "@" */
    0x0, 0x0, 0x4a, 0xdf, 0xfc, 0x70, 0x0, 0x0,
    0x0, 0xaf, 0xfe, 0xcd, 0xff, 0xd1, 0x0, 0x0,
    0xaf, 0xc3, 0x0, 0x1, 0x8f, 0xb0, 0x0, 0x5f,
    0xc0, 0x0, 0x0, 0x0, 0x9f, 0x40, 0xd, 0xf3,
    0x0, 0x0, 0x0, 0x1, 0xfa, 0x2, 0xfb, 0x0,
    0x5b, 0xa2, 0x59, 0xa, 0xf0, 0x7f, 0x70, 0x4f,
    0xff, 0xdc, 0xc0, 0x7f, 0x29, 0xf4, 0xb, 0xf4,
    0x17, 0xfc, 0x5, 0xf4, 0xcf, 0x10, 0xfa, 0x0,
    0xd, 0xc0, 0x4f, 0x5d, 0xf0, 0x2f, 0x70, 0x0,
    0xdc, 0x4, 0xf6, 0xdf, 0x3, 0xf7, 0x0, 0xd,
    0xc0, 0x4f, 0x6d, 0xf0, 0x2f, 0x70, 0x0, 0xdc,
    0x4, 0xf5, 0xdf, 0x1, 0xf9, 0x0, 0xf, 0xc0,
    0x6f, 0x3b, 0xf2, 0xd, 0xe1, 0x7, 0xee, 0x9,
    0xf0, 0x9f, 0x40, 0x6f, 0xeb, 0xf4, 0xfc, 0xf9,
    0x5, 0xf7, 0x0, 0x7e, 0xe6, 0x6, 0xeb, 0x10,
    0x1f, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xef, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xef, 0xeb, 0xab, 0x70, 0x0, 0x0, 0x0, 0x1,
    0x8d, 0xff, 0xd7, 0x0, 0x0, 0x0,

    /* U+0041 "A" */
    0x0, 0x2, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x5f,
    0xff, 0x10, 0x0, 0x0, 0x7, 0xff, 0xf3, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0x60, 0x0, 0x0, 0xc,
    0xfc, 0xf8, 0x0, 0x0, 0x0, 0xef, 0x8f, 0xb0,
    0x0, 0x0, 0xf, 0xf3, 0xfe, 0x0, 0x0, 0x3,
    0xfe, 0xf, 0xf0, 0x0, 0x0, 0x5f, 0xc0, 0xef,
    0x30, 0x0, 0x8, 0xfa, 0xc, 0xf5, 0x0, 0x0,
    0xaf, 0x70, 0x9f, 0x80, 0x0, 0xc, 0xf5, 0x7,
    0xfb, 0x0, 0x0, 0xff, 0x30, 0x4f, 0xd0, 0x0,
    0x1f, 0xf1, 0x2, 0xff, 0x0, 0x4, 0xff, 0x0,
    0xf, 0xf2, 0x0, 0x6f, 0xff, 0xff, 0xff, 0x50,
    0x8, 0xff, 0xff, 0xff, 0xf8, 0x0, 0xbf, 0x80,
    0x0, 0x9f, 0xa0, 0xd, 0xf6, 0x0, 0x6, 0xfd,
    0x0, 0xff, 0x40, 0x0, 0x4f, 0xf0, 0x2f, 0xf2,
    0x0, 0x1, 0xff, 0x20,

    /* U+0042 "B" */
    0x2f, 0xff, 0xfe, 0xb2, 0x2, 0xff, 0xff, 0xff,
    0xe0, 0x2f, 0xf4, 0x4, 0xff, 0x52, 0xff, 0x40,
    0xd, 0xf8, 0x2f, 0xf4, 0x0, 0xcf, 0x92, 0xff,
    0x40, 0xc, 0xfa, 0x2f, 0xf4, 0x0, 0xcf, 0x92,
    0xff, 0x40, 0xd, 0xf7, 0x2f, 0xf4, 0x3, 0xff,
    0x22, 0xff, 0xff, 0xff, 0x80, 0x2f, 0xfe, 0xef,
    0xfc, 0x2, 0xff, 0x40, 0x1e, 0xf7, 0x2f, 0xf4,
    0x0, 0x9f, 0xb2, 0xff, 0x40, 0x8, 0xfe, 0x2f,
    0xf4, 0x0, 0x7f, 0xf2, 0xff, 0x40, 0x7, 0xff,
    0x2f, 0xf4, 0x0, 0x8f, 0xe2, 0xff, 0x40, 0xa,
    0xfc, 0x2f, 0xf4, 0x3, 0xff, 0x82, 0xff, 0xff,
    0xff, 0xf2, 0x2f, 0xff, 0xff, 0xb3, 0x0,

    /* U+0043 "C" */
    0x0, 0x7d, 0xfe, 0x80, 0x0, 0x8f, 0xff, 0xff,
    0x90, 0x1f, 0xfa, 0x29, 0xff, 0x15, 0xff, 0x10,
    0x1f, 0xf4, 0x7f, 0xf0, 0x0, 0xef, 0x68, 0xfe,
    0x0, 0xe, 0xf7, 0x8f, 0xe0, 0x0, 0xef, 0x78,
    0xfe, 0x0, 0xe, 0xf7, 0x8f, 0xe0, 0x0, 0x23,
    0x18, 0xfe, 0x0, 0x0, 0x0, 0x8f, 0xe0, 0x0,
    0x0, 0x8, 0xfe, 0x0, 0x0, 0x0, 0x8f, 0xe0,
    0x0, 0x78, 0x48, 0xfe, 0x0, 0xe, 0xf8, 0x8f,
    0xe0, 0x0, 0xef, 0x78, 0xfe, 0x0, 0xe, 0xf7,
    0x7f, 0xf0, 0x0, 0xff, 0x64, 0xff, 0x10, 0x1f,
    0xf4, 0xf, 0xf9, 0x2a, 0xff, 0x0, 0x8f, 0xff,
    0xff, 0x80, 0x0, 0x7d, 0xfe, 0x80, 0x0,

    /* U+0044 "D" */
    0x5f, 0xff, 0xfe, 0x91, 0x5, 0xff, 0xff, 0xff,
    0xc0, 0x5f, 0xf1, 0x7, 0xff, 0x35, 0xff, 0x10,
    0xe, 0xf8, 0x5f, 0xf1, 0x0, 0xcf, 0xa5, 0xff,
    0x10, 0xb, 0xfb, 0x5f, 0xf1, 0x0, 0xbf, 0xb5,
    0xff, 0x10, 0xb, 0xfb, 0x5f, 0xf1, 0x0, 0xbf,
    0xb5, 0xff, 0x10, 0xb, 0xfb, 0x5f, 0xf1, 0x0,
    0xbf, 0xb5, 0xff, 0x10, 0xb, 0xfb, 0x5f, 0xf1,
    0x0, 0xbf, 0xb5, 0xff, 0x10, 0xb, 0xfb, 0x5f,
    0xf1, 0x0, 0xbf, 0xb5, 0xff, 0x10, 0xb, 0xfb,
    0x5f, 0xf1, 0x0, 0xcf, 0xa5, 0xff, 0x10, 0xe,
    0xf7, 0x5f, 0xf1, 0x6, 0xff, 0x35, 0xff, 0xff,
    0xff, 0xb0, 0x5f, 0xff, 0xfe, 0x90, 0x0,

    /* U+0045 "E" */
    0x5f, 0xff, 0xff, 0xd5, 0xff, 0xff, 0xfd, 0x5f,
    0xf2, 0x0, 0x5, 0xff, 0x10, 0x0, 0x5f, 0xf1,
    0x0, 0x5, 0xff, 0x10, 0x0, 0x5f, 0xf1, 0x0,
    0x5, 0xff, 0x10, 0x0, 0x5f, 0xf1, 0x0, 0x5,
    0xff, 0xff, 0xfa, 0x5f, 0xff, 0xff, 0xa5, 0xff,
    0x20, 0x0, 0x5f, 0xf1, 0x0, 0x5, 0xff, 0x10,
    0x0, 0x5f, 0xf1, 0x0, 0x5, 0xff, 0x10, 0x0,
    0x5f, 0xf1, 0x0, 0x5, 0xff, 0x10, 0x0, 0x5f,
    0xf2, 0x0, 0x5, 0xff, 0xff, 0xfe, 0x5f, 0xff,
    0xff, 0xe0,

    /* U+0046 "F" */
    0x5f, 0xff, 0xff, 0xe5, 0xff, 0xff, 0xfe, 0x5f,
    0xf2, 0x0, 0x5, 0xff, 0x10, 0x0, 0x5f, 0xf1,
    0x0, 0x5, 0xff, 0x10, 0x0, 0x5f, 0xf1, 0x0,
    0x5, 0xff, 0x10, 0x0, 0x5f, 0xf2, 0x11, 0x5,
    0xff, 0xff, 0xfa, 0x5f, 0xff, 0xff, 0xa5, 0xff,
    0x10, 0x0, 0x5f, 0xf1, 0x0, 0x5, 0xff, 0x10,
    0x0, 0x5f, 0xf1, 0x0, 0x5, 0xff, 0x10, 0x0,
    0x5f, 0xf1, 0x0, 0x5, 0xff, 0x10, 0x0, 0x5f,
    0xf1, 0x0, 0x5, 0xff, 0x10, 0x0, 0x5f, 0xf1,
    0x0, 0x0,

    /* U+0047 "G" */
    0x0, 0x7d, 0xfe, 0x90, 0x0, 0x8f, 0xff, 0xff,
    0xb0, 0xf, 0xfa, 0x27, 0xff, 0x24, 0xff, 0x10,
    0xf, 0xf5, 0x7f, 0xf0, 0x0, 0xdf, 0x78, 0xfe,
    0x0, 0xd, 0xf8, 0x8f, 0xe0, 0x0, 0xdf, 0x88,
    0xfe, 0x0, 0x9, 0xb5, 0x8f, 0xe0, 0x0, 0x0,
    0x8, 0xfe, 0x0, 0x0, 0x0, 0x8f, 0xe0, 0x6f,
    0xff, 0x88, 0xfe, 0x5, 0xef, 0xf8, 0x8f, 0xe0,
    0x0, 0xbf, 0x88, 0xfe, 0x0, 0xb, 0xf8, 0x8f,
    0xe0, 0x0, 0xbf, 0x88, 0xfe, 0x0, 0xb, 0xf8,
    0x7f, 0xf0, 0x0, 0xcf, 0x84, 0xff, 0x0, 0xe,
    0xf8, 0x1f, 0xf7, 0x4, 0xff, 0x80, 0x8f, 0xfe,
    0xfd, 0xf8, 0x0, 0x8e, 0xfc, 0x2d, 0x80,

    /* U+0048 "H" */
    0x5f, 0xf1, 0x0, 0x9f, 0xd5, 0xff, 0x10, 0x9,
    0xfd, 0x5f, 0xf1, 0x0, 0x9f, 0xd5, 0xff, 0x10,
    0x9, 0xfd, 0x5f, 0xf1, 0x0, 0x9f, 0xd5, 0xff,
    0x10, 0x9, 0xfd, 0x5f, 0xf1, 0x0, 0x9f, 0xd5,
    0xff, 0x10, 0x9, 0xfd, 0x5f, 0xf1, 0x0, 0x9f,
    0xd5, 0xff, 0xff, 0xff, 0xfd, 0x5f, 0xff, 0xff,
    0xff, 0xd5, 0xff, 0x10, 0x9, 0xfd, 0x5f, 0xf1,
    0x0, 0x9f, 0xd5, 0xff, 0x10, 0x9, 0xfd, 0x5f,
    0xf1, 0x0, 0x9f, 0xd5, 0xff, 0x10, 0x9, 0xfd,
    0x5f, 0xf1, 0x0, 0x9f, 0xd5, 0xff, 0x10, 0x9,
    0xfd, 0x5f, 0xf1, 0x0, 0x9f, 0xd5, 0xff, 0x10,
    0x9, 0xfd, 0x5f, 0xf1, 0x0, 0x9f, 0xd0,

    /* U+0049 "I" */
    0x2f, 0xf4, 0x2f, 0xf4, 0x2f, 0xf4, 0x2f, 0xf4,
    0x2f, 0xf4, 0x2f, 0xf4, 0x2f, 0xf4, 0x2f, 0xf4,
    0x2f, 0xf4, 0x2f, 0xf4, 0x2f, 0xf4, 0x2f, 0xf4,
    0x2f, 0xf4, 0x2f, 0xf4, 0x2f, 0xf4, 0x2f, 0xf4,
    0x2f, 0xf4, 0x2f, 0xf4, 0x2f, 0xf4, 0x2f, 0xf4,
    0x2f, 0xf4,

    /* U+004A "J" */
    0x0, 0x0, 0x0, 0xff, 0x60, 0x0, 0x0, 0xf,
    0xf6, 0x0, 0x0, 0x0, 0xff, 0x60, 0x0, 0x0,
    0xf, 0xf6, 0x0, 0x0, 0x0, 0xff, 0x60, 0x0,
    0x0, 0xf, 0xf6, 0x0, 0x0, 0x0, 0xff, 0x60,
    0x0, 0x0, 0xf, 0xf6, 0x0, 0x0, 0x0, 0xff,
    0x60, 0x0, 0x0, 0xf, 0xf6, 0x0, 0x0, 0x0,
    0xff, 0x60, 0x0, 0x0, 0xf, 0xf6, 0x0, 0x0,
    0x0, 0xff, 0x60, 0x99, 0x20, 0xf, 0xf6, 0xf,
    0xf5, 0x0, 0xff, 0x60, 0xff, 0x50, 0xf, 0xf6,
    0xf, 0xf5, 0x0, 0xff, 0x60, 0xff, 0x60, 0x1f,
    0xf4, 0xb, 0xfc, 0x28, 0xff, 0x10, 0x4f, 0xff,
    0xff, 0x90, 0x0, 0x5d, 0xfe, 0x80, 0x0,

    /* U+004B "K" */
    0x5f, 0xf1, 0x0, 0x6f, 0xf1, 0x5f, 0xf1, 0x0,
    0xcf, 0xa0, 0x5f, 0xf1, 0x2, 0xff, 0x40, 0x5f,
    0xf1, 0x8, 0xfe, 0x0, 0x5f, 0xf1, 0xd, 0xf8,
    0x0, 0x5f, 0xf1, 0x3f, 0xf2, 0x0, 0x5f, 0xf1,
    0x9f, 0xc0, 0x0, 0x5f, 0xf2, 0xef, 0x70, 0x0,
    0x5f, 0xf7, 0xff, 0x10, 0x0, 0x5f, 0xfd, 0xfb,
    0x0, 0x0, 0x5f, 0xff, 0xf7, 0x0, 0x0, 0x5f,
    0xfc, 0xfc, 0x0, 0x0, 0x5f, 0xf6, 0xff, 0x30,
    0x0, 0x5f, 0xf2, 0xef, 0x90, 0x0, 0x5f, 0xf2,
    0x8f, 0xe0, 0x0, 0x5f, 0xf2, 0x2f, 0xf5, 0x0,
    0x5f, 0xf2, 0xc, 0xfb, 0x0, 0x5f, 0xf2, 0x6,
    0xff, 0x20, 0x5f, 0xf2, 0x0, 0xff, 0x80, 0x5f,
    0xf2, 0x0, 0x9f, 0xe0, 0x5f, 0xf2, 0x0, 0x3f,
    0xf4,

    /* U+004C "L" */
    0x5f, 0xf1, 0x0, 0x5, 0xff, 0x10, 0x0, 0x5f,
    0xf1, 0x0, 0x5, 0xff, 0x10, 0x0, 0x5f, 0xf1,
    0x0, 0x5, 0xff, 0x10, 0x0, 0x5f, 0xf1, 0x0,
    0x5, 0xff, 0x10, 0x0, 0x5f, 0xf1, 0x0, 0x5,
    0xff, 0x10, 0x0, 0x5f, 0xf1, 0x0, 0x5, 0xff,
    0x10, 0x0, 0x5f, 0xf1, 0x0, 0x5, 0xff, 0x10,
    0x0, 0x5f, 0xf1, 0x0, 0x5, 0xff, 0x10, 0x0,
    0x5f, 0xf1, 0x0, 0x5, 0xff, 0x10, 0x0, 0x5f,
    0xf2, 0x0, 0x5, 0xff, 0xff, 0xfe, 0x5f, 0xff,
    0xff, 0xe0,

    /* U+004D "M" */
    0x5f, 0xfc, 0x0, 0x0, 0x9, 0xff, 0x85, 0xff,
    0xf0, 0x0, 0x0, 0xcf, 0xf8, 0x5f, 0xff, 0x20,
    0x0, 0xe, 0xff, 0x85, 0xff, 0xf5, 0x0, 0x1,
    0xff, 0xf8, 0x5f, 0xff, 0x70, 0x0, 0x4f, 0xff,
    0x85, 0xfe, 0xfa, 0x0, 0x6, 0xfe, 0xf8, 0x5f,
    0xbf, 0xd0, 0x0, 0x9f, 0xbf, 0x85, 0xfb, 0xdf,
    0x0, 0xb, 0xf9, 0xf8, 0x5f, 0xbb, 0xf2, 0x0,
    0xee, 0x8f, 0x85, 0xfc, 0x8f, 0x50, 0x1f, 0xb9,
    0xf8, 0x5f, 0xc5, 0xf8, 0x3, 0xf8, 0x9f, 0x85,
    0xfc, 0x2f, 0xa0, 0x6f, 0x6a, 0xf8, 0x5f, 0xd0,
    0xfd, 0x8, 0xf3, 0xaf, 0x85, 0xfd, 0xc, 0xf0,
    0xbf, 0xa, 0xf8, 0x5f, 0xd0, 0x9f, 0x3e, 0xd0,
    0xaf, 0x85, 0xfd, 0x7, 0xf6, 0xfb, 0xa, 0xf8,
    0x5f, 0xd0, 0x4f, 0xbf, 0x80, 0xaf, 0x85, 0xfd,
    0x1, 0xff, 0xf5, 0xa, 0xf8, 0x5f, 0xd0, 0xe,
    0xff, 0x20, 0xaf, 0x85, 0xfd, 0x0, 0xbf, 0xf0,
    0xa, 0xf8, 0x5f, 0xd0, 0x8, 0xfd, 0x0, 0xaf,
    0x80,

    /* U+004E "N" */
    0x5f, 0xa0, 0x0, 0xf, 0xf3, 0x5f, 0xf0, 0x0,
    0xf, 0xf3, 0x5f, 0xf5, 0x0, 0xf, 0xf3, 0x5f,
    0xfa, 0x0, 0xf, 0xf3, 0x5f, 0xff, 0x0, 0xf,
    0xf3, 0x5f, 0xff, 0x50, 0xf, 0xf3, 0x5f, 0xff,
    0xb0, 0xf, 0xf3, 0x5f, 0xde, 0xf0, 0xf, 0xf3,
    0x5f, 0xd9, 0xf5, 0xf, 0xf3, 0x5f, 0xd4, 0xfb,
    0xe, 0xf3, 0x5f, 0xe0, 0xff, 0xd, 0xf3, 0x5f,
    0xe0, 0xaf, 0x5d, 0xf3, 0x5f, 0xe0, 0x5f, 0xbc,
    0xf3, 0x5f, 0xe0, 0xf, 0xfc, 0xf3, 0x5f, 0xe0,
    0xa, 0xff, 0xf3, 0x5f, 0xe0, 0x5, 0xff, 0xf3,
    0x5f, 0xe0, 0x0, 0xff, 0xf3, 0x5f, 0xe0, 0x0,
    0xbf, 0xf3, 0x5f, 0xe0, 0x0, 0x6f, 0xf3, 0x5f,
    0xe0, 0x0, 0x1f, 0xf3, 0x5f, 0xe0, 0x0, 0xb,
    0xf3,

    /* U+004F "O" */
    0x0, 0x8d, 0xfe, 0x90, 0x0, 0x9f, 0xff, 0xff,
    0xb0, 0x1f, 0xfa, 0x28, 0xff, 0x35, 0xff, 0x10,
    0xf, 0xf6, 0x7f, 0xe0, 0x0, 0xdf, 0x88, 0xfe,
    0x0, 0xd, 0xf9, 0x8f, 0xe0, 0x0, 0xdf, 0x98,
    0xfe, 0x0, 0xd, 0xf9, 0x8f, 0xe0, 0x0, 0xdf,
    0x98, 0xfe, 0x0, 0xd, 0xf9, 0x8f, 0xe0, 0x0,
    0xdf, 0x98, 0xfe, 0x0, 0xd, 0xf9, 0x8f, 0xe0,
    0x0, 0xdf, 0x98, 0xfe, 0x0, 0xd, 0xf9, 0x8f,
    0xe0, 0x0, 0xdf, 0x98, 0xfe, 0x0, 0xd, 0xf9,
    0x7f, 0xe0, 0x0, 0xdf, 0x85, 0xff, 0x10, 0xf,
    0xf6, 0x1f, 0xf9, 0x28, 0xff, 0x20, 0x9f, 0xff,
    0xff, 0xa0, 0x0, 0x8e, 0xfe, 0x90, 0x0,

    /* U+0050 "P" */
    0x5f, 0xff, 0xfe, 0xa1, 0x5, 0xff, 0xff, 0xff,
    0xd0, 0x5f, 0xf1, 0x5, 0xff, 0x45, 0xff, 0x10,
    0xe, 0xf8, 0x5f, 0xf1, 0x0, 0xcf, 0xa5, 0xff,
    0x10, 0xb, 0xfb, 0x5f, 0xf1, 0x0, 0xbf, 0xb5,
    0xff, 0x10, 0xb, 0xfb, 0x5f, 0xf1, 0x0, 0xcf,
    0xa5, 0xff, 0x10, 0xe, 0xf8, 0x5f, 0xf2, 0x17,
    0xff, 0x45, 0xff, 0xff, 0xff, 0xd0, 0x5f, 0xff,
    0xfe, 0xa1, 0x5, 0xff, 0x10, 0x0, 0x0, 0x5f,
    0xf1, 0x0, 0x0, 0x5, 0xff, 0x10, 0x0, 0x0,
    0x5f, 0xf1, 0x0, 0x0, 0x5, 0xff, 0x10, 0x0,
    0x0, 0x5f, 0xf1, 0x0, 0x0, 0x5, 0xff, 0x10,
    0x0, 0x0, 0x5f, 0xf1, 0x0, 0x0, 0x0,

    /* U+0051 "Q" */
    0x0, 0x8d, 0xfe, 0x90, 0x0, 0x9f, 0xff, 0xff,
    0xb0, 0x1f, 0xfa, 0x28, 0xff, 0x35, 0xff, 0x10,
    0xf, 0xf6, 0x7f, 0xe0, 0x0, 0xdf, 0x88, 0xfe,
    0x0, 0xd, 0xf9, 0x8f, 0xe0, 0x0, 0xdf, 0x98,
    0xfe, 0x0, 0xd, 0xf9, 0x8f, 0xe0, 0x0, 0xdf,
    0x98, 0xfe, 0x0, 0xd, 0xf9, 0x8f, 0xe0, 0x0,
    0xdf, 0x98, 0xfe, 0x0, 0xd, 0xf9, 0x8f, 0xe0,
    0x0, 0xdf, 0x98, 0xfe, 0x0, 0xd, 0xf9, 0x8f,
    0xe0, 0x0, 0xdf, 0x98, 0xfe, 0x0, 0xd, 0xf9,
    0x7f, 0xe0, 0x0, 0xdf, 0x85, 0xff, 0x0, 0xf,
    0xf6, 0x2f, 0xf8, 0x18, 0xff, 0x20, 0xbf, 0xff,
    0xff, 0xb0, 0x1, 0xae, 0xff, 0xb1, 0x0, 0x0,
    0x6, 0xfc, 0x0, 0x0, 0x0, 0x8, 0xfa, 0x0,
    0x0, 0x0, 0xa, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+0052 "R" */
    0x5f, 0xff, 0xff, 0xc4, 0x5, 0xff, 0xff, 0xff,
    0xf2, 0x5f, 0xf1, 0x2, 0xff, 0x75, 0xff, 0x10,
    0xc, 0xfa, 0x5f, 0xf1, 0x0, 0xbf, 0xa5, 0xff,
    0x10, 0xb, 0xfb, 0x5f, 0xf1, 0x0, 0xbf, 0xa5,
    0xff, 0x10, 0xd, 0xf8, 0x5f, 0xf1, 0x2, 0xff,
    0x45, 0xff, 0xdd, 0xff, 0x90, 0x5f, 0xff, 0xff,
    0xf9, 0x5, 0xff, 0x10, 0x3f, 0xf3, 0x5f, 0xf1,
    0x0, 0xcf, 0x75, 0xff, 0x10, 0xb, 0xf9, 0x5f,
    0xf1, 0x0, 0xbf, 0xa5, 0xff, 0x10, 0xb, 0xfb,
    0x5f, 0xf1, 0x0, 0xbf, 0xb5, 0xff, 0x10, 0xb,
    0xfb, 0x5f, 0xf1, 0x0, 0xaf, 0xb5, 0xff, 0x10,
    0xa, 0xfc, 0x5f, 0xf1, 0x0, 0x9f, 0xd0,

    /* U+0053 "S" */
    0x3, 0xbf, 0xfc, 0x40, 0x0, 0xef, 0xff, 0xff,
    0x30, 0x6f, 0xf5, 0x3d, 0xfa, 0x9, 0xfb, 0x0,
    0x7f, 0xd0, 0xbf, 0x90, 0x5, 0xff, 0xb, 0xfa,
    0x0, 0x5f, 0xf0, 0x8f, 0xe1, 0x5, 0xff, 0x2,
    0xff, 0xb0, 0x13, 0x30, 0x8, 0xff, 0xa0, 0x0,
    0x0, 0xb, 0xff, 0x90, 0x0, 0x0, 0xb, 0xff,
    0x70, 0x0, 0x0, 0xc, 0xff, 0x20, 0x12, 0x10,
    0x1e, 0xfa, 0xa, 0xfb, 0x0, 0x7f, 0xf0, 0xaf,
    0xb0, 0x2, 0xff, 0x3a, 0xfb, 0x0, 0x1f, 0xf4,
    0x9f, 0xc0, 0x1, 0xff, 0x46, 0xfe, 0x0, 0x2f,
    0xf2, 0x3f, 0xf7, 0x29, 0xfe, 0x0, 0xbf, 0xff,
    0xff, 0x70, 0x0, 0x9e, 0xfd, 0x70, 0x0,

    /* U+0054 "T" */
    0xaf, 0xff, 0xff, 0xf2, 0xaf, 0xff, 0xff, 0xf2,
    0x0, 0x6f, 0xf0, 0x0, 0x0, 0x6f, 0xf0, 0x0,
    0x0, 0x6f, 0xf0, 0x0, 0x0, 0x6f, 0xf0, 0x0,
    0x0, 0x6f, 0xf0, 0x0, 0x0, 0x6f, 0xf0, 0x0,
    0x0, 0x6f, 0xf0, 0x0, 0x0, 0x6f, 0xf0, 0x0,
    0x0, 0x6f, 0xf0, 0x0, 0x0, 0x6f, 0xf0, 0x0,
    0x0, 0x6f, 0xf0, 0x0, 0x0, 0x6f, 0xf0, 0x0,
    0x0, 0x6f, 0xf0, 0x0, 0x0, 0x6f, 0xf0, 0x0,
    0x0, 0x6f, 0xf0, 0x0, 0x0, 0x6f, 0xf0, 0x0,
    0x0, 0x6f, 0xf0, 0x0, 0x0, 0x6f, 0xf0, 0x0,
    0x0, 0x6f, 0xf0, 0x0,

    /* U+0055 "U" */
    0x6f, 0xf0, 0x0, 0xbf, 0xb6, 0xff, 0x0, 0xb,
    0xfb, 0x6f, 0xf0, 0x0, 0xbf, 0xb6, 0xff, 0x0,
    0xb, 0xfb, 0x6f, 0xf0, 0x0, 0xbf, 0xb6, 0xff,
    0x0, 0xb, 0xfb, 0x6f, 0xf0, 0x0, 0xbf, 0xb6,
    0xff, 0x0, 0xb, 0xfb, 0x6f, 0xf0, 0x0, 0xbf,
    0xb6, 0xff, 0x0, 0xb, 0xfb, 0x6f, 0xf0, 0x0,
    0xbf, 0xb6, 0xff, 0x0, 0xb, 0xfb, 0x6f, 0xf0,
    0x0, 0xbf, 0xb6, 0xff, 0x0, 0xb, 0xfb, 0x6f,
    0xf0, 0x0, 0xbf, 0xb6, 0xff, 0x0, 0xb, 0xfb,
    0x6f, 0xf0, 0x0, 0xbf, 0xa4, 0xff, 0x10, 0xc,
    0xf8, 0xf, 0xfa, 0x15, 0xff, 0x50, 0x9f, 0xff,
    0xff, 0xd0, 0x0, 0x8d, 0xfe, 0xa1, 0x0,

    /* U+0056 "V" */
    0x3f, 0xf1, 0x0, 0x4, 0xff, 0x0, 0xff, 0x30,
    0x0, 0x6f, 0xd0, 0xe, 0xf6, 0x0, 0x8, 0xfb,
    0x0, 0xbf, 0x80, 0x0, 0xaf, 0x90, 0x9, 0xfb,
    0x0, 0xc, 0xf6, 0x0, 0x6f, 0xd0, 0x0, 0xef,
    0x40, 0x3, 0xff, 0x0, 0xf, 0xf2, 0x0, 0x1f,
    0xf2, 0x3, 0xff, 0x0, 0x0, 0xef, 0x50, 0x5f,
    0xd0, 0x0, 0xb, 0xf7, 0x7, 0xfa, 0x0, 0x0,
    0x9f, 0xa0, 0x9f, 0x80, 0x0, 0x6, 0xfc, 0xb,
    0xf6, 0x0, 0x0, 0x4f, 0xe0, 0xdf, 0x30, 0x0,
    0x1, 0xff, 0x1f, 0xf1, 0x0, 0x0, 0xe, 0xf5,
    0xfe, 0x0, 0x0, 0x0, 0xcf, 0x9f, 0xc0, 0x0,
    0x0, 0x9, 0xfe, 0xfa, 0x0, 0x0, 0x0, 0x6f,
    0xff, 0x70, 0x0, 0x0, 0x4, 0xff, 0xf5, 0x0,
    0x0, 0x0, 0x1f, 0xff, 0x30, 0x0, 0x0, 0x0,
    0xff, 0xf0, 0x0, 0x0,

    /* U+0057 "W" */
    0x3f, 0xf0, 0x0, 0xf, 0xf2, 0x0, 0x1f, 0xf2,
    0xf, 0xf2, 0x0, 0x2f, 0xf4, 0x0, 0x2f, 0xf0,
    0xe, 0xf4, 0x0, 0x4f, 0xf7, 0x0, 0x4f, 0xe0,
    0xc, 0xf5, 0x0, 0x6f, 0xf9, 0x0, 0x5f, 0xc0,
    0xa, 0xf7, 0x0, 0x9f, 0xfb, 0x0, 0x7f, 0xa0,
    0x8, 0xf9, 0x0, 0xbf, 0xfe, 0x0, 0x8f, 0x80,
    0x6, 0xfb, 0x0, 0xdf, 0xbf, 0x0, 0xaf, 0x60,
    0x4, 0xfc, 0x0, 0xfd, 0x9f, 0x30, 0xbf, 0x50,
    0x2, 0xfe, 0x2, 0xfb, 0x6f, 0x50, 0xdf, 0x30,
    0x0, 0xff, 0x4, 0xf8, 0x4f, 0x70, 0xef, 0x10,
    0x0, 0xef, 0x27, 0xf6, 0x2f, 0xa0, 0xff, 0x0,
    0x0, 0xcf, 0x39, 0xf3, 0xf, 0xc1, 0xfd, 0x0,
    0x0, 0xaf, 0x5b, 0xf1, 0xd, 0xe3, 0xfb, 0x0,
    0x0, 0x8f, 0x7d, 0xf0, 0xb, 0xf6, 0xf9, 0x0,
    0x0, 0x6f, 0x9f, 0xc0, 0x9, 0xf9, 0xf8, 0x0,
    0x0, 0x3f, 0xdf, 0xa0, 0x7, 0xfd, 0xf6, 0x0,
    0x0, 0x1f, 0xff, 0x70, 0x4, 0xff, 0xf4, 0x0,
    0x0, 0xf, 0xff, 0x50, 0x2, 0xff, 0xf2, 0x0,
    0x0, 0xd, 0xff, 0x20, 0x0, 0xff, 0xf0, 0x0,
    0x0, 0xb, 0xff, 0x0, 0x0, 0xdf, 0xe0, 0x0,
    0x0, 0x9, 0xfe, 0x0, 0x0, 0xbf, 0xc0, 0x0,

    /* U+0058 "X" */
    0x1f, 0xf0, 0x0, 0x7f, 0xb0, 0xdf, 0x40, 0xb,
    0xf6, 0x8, 0xf9, 0x0, 0xff, 0x20, 0x4f, 0xd0,
    0x3f, 0xe0, 0x0, 0xff, 0x27, 0xfa, 0x0, 0xa,
    0xf7, 0xcf, 0x50, 0x0, 0x6f, 0xcf, 0xf1, 0x0,
    0x1, 0xff, 0xfd, 0x0, 0x0, 0xc, 0xff, 0x90,
    0x0, 0x0, 0x8f, 0xf4, 0x0, 0x0, 0x9, 0xff,
    0x0, 0x0, 0x0, 0xdf, 0xf2, 0x0, 0x0, 0x1f,
    0xff, 0x70, 0x0, 0x5, 0xff, 0xfc, 0x0, 0x0,
    0xaf, 0x9f, 0xf0, 0x0, 0xe, 0xf4, 0xcf, 0x50,
    0x2, 0xff, 0x7, 0xf9, 0x0, 0x6f, 0xc0, 0x3f,
    0xe0, 0xb, 0xf7, 0x0, 0xef, 0x30, 0xff, 0x30,
    0x9, 0xf7, 0x3f, 0xf0, 0x0, 0x5f, 0xc0,

    /* U+0059 "Y" */
    0x9f, 0xb0, 0x0, 0xe, 0xf7, 0x5f, 0xf0, 0x0,
    0x2f, 0xf2, 0xf, 0xf4, 0x0, 0x6f, 0xe0, 0xb,
    0xf9, 0x0, 0xaf, 0x90, 0x7, 0xfd, 0x0, 0xef,
    0x50, 0x2, 0xff, 0x22, 0xff, 0x10, 0x0, 0xdf,
    0x66, 0xfc, 0x0, 0x0, 0x8f, 0xba, 0xf8, 0x0,
    0x0, 0x4f, 0xfe, 0xf4, 0x0, 0x0, 0xf, 0xff,
    0xf0, 0x0, 0x0, 0xa, 0xff, 0xb0, 0x0, 0x0,
    0x6, 0xff, 0x70, 0x0, 0x0, 0x2, 0xff, 0x40,
    0x0, 0x0, 0x2, 0xff, 0x40, 0x0, 0x0, 0x2,
    0xff, 0x40, 0x0, 0x0, 0x2, 0xff, 0x40, 0x0,
    0x0, 0x2, 0xff, 0x40, 0x0, 0x0, 0x2, 0xff,
    0x40, 0x0, 0x0, 0x2, 0xff, 0x40, 0x0, 0x0,
    0x2, 0xff, 0x40, 0x0, 0x0, 0x2, 0xff, 0x40,
    0x0,

    /* U+005A "Z" */
    0x4f, 0xff, 0xff, 0xf1, 0x4f, 0xff, 0xff, 0xf1,
    0x0, 0x0, 0x8f, 0xf0, 0x0, 0x0, 0xcf, 0xb0,
    0x0, 0x0, 0xff, 0x70, 0x0, 0x4, 0xff, 0x30,
    0x0, 0x8, 0xff, 0x0, 0x0, 0xc, 0xfb, 0x0,
    0x0, 0xf, 0xf7, 0x0, 0x0, 0x4f, 0xf3, 0x0,
    0x0, 0x8f, 0xf0, 0x0, 0x0, 0xcf, 0xb0, 0x0,
    0x0, 0xff, 0x70, 0x0, 0x4, 0xff, 0x30, 0x0,
    0x8, 0xff, 0x0, 0x0, 0xc, 0xfb, 0x0, 0x0,
    0x1f, 0xf7, 0x0, 0x0, 0x4f, 0xf3, 0x0, 0x0,
    0x8f, 0xf0, 0x0, 0x0, 0xaf, 0xff, 0xff, 0xf0,
    0xaf, 0xff, 0xff, 0xf0,

    /* U+005B "[" */
    0x7f, 0xff, 0xf6, 0x7f, 0xfe, 0xe5, 0x7f, 0xf0,
    0x0, 0x7f, 0xf0, 0x0, 0x7f, 0xf0, 0x0, 0x7f,
    0xf0, 0x0, 0x7f, 0xf0, 0x0, 0x7f, 0xf0, 0x0,
    0x7f, 0xf0, 0x0, 0x7f, 0xf0, 0x0, 0x7f, 0xf0,
    0x0, 0x7f, 0xf0, 0x0, 0x7f, 0xf0, 0x0, 0x7f,
    0xf0, 0x0, 0x7f, 0xf0, 0x0, 0x7f, 0xf0, 0x0,
    0x7f, 0xf0, 0x0, 0x7f, 0xf0, 0x0, 0x7f, 0xf0,
    0x0, 0x7f, 0xfe, 0xe5, 0x7f, 0xff, 0xf6,

    /* U+005C "\\" */
    0x8f, 0x90, 0x0, 0x0, 0x5f, 0xd0, 0x0, 0x0,
    0x1f, 0xf0, 0x0, 0x0, 0xd, 0xf4, 0x0, 0x0,
    0x9, 0xf8, 0x0, 0x0, 0x6, 0xfb, 0x0, 0x0,
    0x2, 0xff, 0x0, 0x0, 0x0, 0xef, 0x30, 0x0,
    0x0, 0xbf, 0x60, 0x0, 0x0, 0x7f, 0xa0, 0x0,
    0x0, 0x3f, 0xe0, 0x0, 0x0, 0xf, 0xf1, 0x0,
    0x0, 0xc, 0xf5, 0x0, 0x0, 0x8, 0xf9, 0x0,
    0x0, 0x5, 0xfc, 0x0, 0x0, 0x1, 0xff, 0x0,
    0x0, 0x0, 0xdf, 0x40, 0x0, 0x0, 0xaf, 0x70,
    0x0, 0x0, 0x6f, 0xb0, 0x0, 0x0, 0x2f, 0xe0,
    0x0, 0x0, 0xe, 0xf2,

    /* U+005D "]" */
    0xf, 0xff, 0xfd, 0xe, 0xef, 0xfd, 0x0, 0x9,
    0xfd, 0x0, 0x9, 0xfd, 0x0, 0x9, 0xfd, 0x0,
    0x9, 0xfd, 0x0, 0x9, 0xfd, 0x0, 0x9, 0xfd,
    0x0, 0x9, 0xfd, 0x0, 0x9, 0xfd, 0x0, 0x9,
    0xfd, 0x0, 0x9, 0xfd, 0x0, 0x9, 0xfd, 0x0,
    0x9, 0xfd, 0x0, 0x9, 0xfd, 0x0, 0x9, 0xfd,
    0x0, 0x9, 0xfd, 0x0, 0x9, 0xfd, 0x0, 0x9,
    0xfd, 0xe, 0xef, 0xfd, 0xf, 0xff, 0xfd,

    /* U+005E "^" */
    0x0, 0xa, 0xff, 0xb0, 0x0, 0x0, 0xe, 0xff,
    0xe0, 0x0, 0x0, 0x2f, 0xed, 0xf3, 0x0, 0x0,
    0x6f, 0xa9, 0xf7, 0x0, 0x0, 0xaf, 0x76, 0xfb,
    0x0, 0x0, 0xef, 0x32, 0xff, 0x0, 0x2, 0xff,
    0x0, 0xff, 0x30, 0x6, 0xfc, 0x0, 0xbf, 0x70,
    0xb, 0xf9, 0x0, 0x8f, 0xb0, 0xe, 0xf5, 0x0,
    0x4f, 0xf0, 0x3f, 0xf2, 0x0, 0x1f, 0xf4,

    /* U+005F "_" */
    0x5c, 0xcc, 0xcc, 0xcc, 0x27, 0xff, 0xff, 0xff,
    0xf3,

    /* U+0060 "`" */
    0x10, 0x0, 0x9, 0xc3, 0x0, 0x9f, 0xfb, 0x22,
    0x7b, 0xfc, 0x0, 0x0, 0x20,

    /* U+0061 "a" */
    0x0, 0x8e, 0xfe, 0x90, 0x0, 0x9f, 0xff, 0xff,
    0x90, 0xf, 0xf9, 0x18, 0xff, 0x2, 0xff, 0x30,
    0x3f, 0xf2, 0x4f, 0xf1, 0x2, 0xff, 0x44, 0xdd,
    0x10, 0x2f, 0xf4, 0x0, 0x0, 0x5, 0xff, 0x40,
    0x0, 0x6d, 0xff, 0xf4, 0x3, 0xdf, 0xd7, 0xff,
    0x40, 0xef, 0xa0, 0x2f, 0xf4, 0x5f, 0xf0, 0x2,
    0xff, 0x47, 0xfe, 0x0, 0x2f, 0xf4, 0x8f, 0xe0,
    0x2, 0xff, 0x48, 0xfe, 0x0, 0x2f, 0xf4, 0x7f,
    0xf0, 0x2, 0xff, 0x44, 0xff, 0x30, 0x8f, 0xf4,
    0xe, 0xff, 0xee, 0xff, 0x40, 0x3d, 0xfc, 0x4f,
    0xf4,

    /* U+0062 "b" */
    0x4f, 0xf2, 0x0, 0x0, 0x4, 0xff, 0x20, 0x0,
    0x0, 0x4f, 0xf2, 0x0, 0x0, 0x4, 0xff, 0x4c,
    0xfc, 0x20, 0x4f, 0xfe, 0xff, 0xfd, 0x4, 0xff,
    0xb1, 0x6f, 0xf3, 0x4f, 0xf4, 0x0, 0xff, 0x64,
    0xff, 0x20, 0xe, 0xf7, 0x4f, 0xf2, 0x0, 0xef,
    0x84, 0xff, 0x20, 0xe, 0xf8, 0x4f, 0xf2, 0x0,
    0xef, 0x84, 0xff, 0x20, 0xe, 0xf8, 0x4f, 0xf2,
    0x0, 0xef, 0x84, 0xff, 0x20, 0xe, 0xf8, 0x4f,
    0xf2, 0x0, 0xef, 0x84, 0xff, 0x20, 0xe, 0xf8,
    0x4f, 0xf2, 0x0, 0xef, 0x84, 0xff, 0x40, 0xf,
    0xf6, 0x4f, 0xfa, 0x6, 0xff, 0x34, 0xff, 0xef,
    0xff, 0xd0, 0x4f, 0xf4, 0xcf, 0xd3, 0x0,

    /* U+0063 "c" */
    0x0, 0x9e, 0xfd, 0x70, 0x0, 0xbf, 0xff, 0xff,
    0x50, 0x2f, 0xf7, 0x2c, 0xfc, 0x6, 0xff, 0x0,
    0x5f, 0xf0, 0x7f, 0xe0, 0x4, 0xff, 0x8, 0xfe,
    0x0, 0x4f, 0xf1, 0x8f, 0xe0, 0x3, 0xbb, 0x8,
    0xfe, 0x0, 0x0, 0x0, 0x8f, 0xe0, 0x0, 0x0,
    0x8, 0xfe, 0x0, 0x0, 0x0, 0x8f, 0xe0, 0x0,
    0x0, 0x8, 0xfe, 0x0, 0x4f, 0xf1, 0x8f, 0xe0,
    0x4, 0xff, 0x17, 0xfe, 0x0, 0x4f, 0xf0, 0x5f,
    0xf0, 0x6, 0xfe, 0x2, 0xff, 0x72, 0xcf, 0xb0,
    0xa, 0xff, 0xff, 0xf5, 0x0, 0x9, 0xef, 0xd6,
    0x0,

    /* U+0064 "d" */
    0x0, 0x0, 0x2, 0xff, 0x40, 0x0, 0x0, 0x2f,
    0xf4, 0x0, 0x0, 0x2, 0xff, 0x40, 0x1b, 0xfd,
    0x5f, 0xf4, 0xc, 0xff, 0xff, 0xff, 0x43, 0xff,
    0x71, 0xbf, 0xf4, 0x6f, 0xf0, 0x4, 0xff, 0x48,
    0xfe, 0x0, 0x2f, 0xf4, 0x8f, 0xe0, 0x2, 0xff,
    0x48, 0xfe, 0x0, 0x2f, 0xf4, 0x8f, 0xe0, 0x2,
    0xff, 0x48, 0xfe, 0x0, 0x2f, 0xf4, 0x8f, 0xe0,
    0x2, 0xff, 0x48, 0xfe, 0x0, 0x2f, 0xf4, 0x8f,
    0xe0, 0x2, 0xff, 0x48, 0xfe, 0x0, 0x2f, 0xf4,
    0x8f, 0xe0, 0x2, 0xff, 0x46, 0xff, 0x0, 0x4f,
    0xf4, 0x3f, 0xf4, 0x9, 0xff, 0x40, 0xdf, 0xfe,
    0xef, 0xf4, 0x3, 0xcf, 0xc4, 0xff, 0x40,

    /* U+0065 "e" */
    0x0, 0x9e, 0xfd, 0x60, 0x0, 0xaf, 0xff, 0xff,
    0x50, 0x2f, 0xf7, 0xa, 0xfc, 0x5, 0xff, 0x0,
    0x5f, 0xf0, 0x7f, 0xe0, 0x4, 0xff, 0x18, 0xfd,
    0x0, 0x4f, 0xf1, 0x8f, 0xd0, 0x4, 0xff, 0x28,
    0xff, 0xaa, 0xbf, 0xf2, 0x8f, 0xff, 0xff, 0xff,
    0x28, 0xfe, 0x0, 0x0, 0x0, 0x8f, 0xe0, 0x0,
    0x0, 0x8, 0xfe, 0x0, 0x3d, 0xd1, 0x8f, 0xe0,
    0x4, 0xff, 0x17, 0xfe, 0x0, 0x4f, 0xf0, 0x6f,
    0xf0, 0x6, 0xff, 0x2, 0xff, 0x61, 0xbf, 0xc0,
    0xb, 0xff, 0xff, 0xf5, 0x0, 0x19, 0xef, 0xd6,
    0x0,

    /* U+0066 "f" */
    0x0, 0x8, 0xef, 0xa0, 0x5, 0xff, 0xeb, 0x0,
    0x9f, 0xd0, 0x0, 0xa, 0xfc, 0x0, 0x2f, 0xff,
    0xff, 0xd2, 0xce, 0xff, 0xca, 0x0, 0xaf, 0xc0,
    0x0, 0xa, 0xfc, 0x0, 0x0, 0xaf, 0xc0, 0x0,
    0xa, 0xfc, 0x0, 0x0, 0xaf, 0xc0, 0x0, 0xa,
    0xfc, 0x0, 0x0, 0xaf, 0xc0, 0x0, 0xa, 0xfc,
    0x0, 0x0, 0xaf, 0xc0, 0x0, 0xa, 0xfc, 0x0,
    0x0, 0xaf, 0xc0, 0x0, 0xa, 0xfc, 0x0, 0x0,
    0xaf, 0xc0, 0x0, 0xa, 0xfc, 0x0, 0x0, 0xaf,
    0xc0, 0x0,

    /* U+0067 "g" */
    0x1, 0xbf, 0xd6, 0xff, 0x30, 0xbf, 0xff, 0xef,
    0xf3, 0x2f, 0xf7, 0x1b, 0xff, 0x36, 0xff, 0x0,
    0x5f, 0xf3, 0x7f, 0xd0, 0x3, 0xff, 0x38, 0xfd,
    0x0, 0x2f, 0xf3, 0x9f, 0xd0, 0x2, 0xff, 0x39,
    0xfd, 0x0, 0x2f, 0xf3, 0x9f, 0xd0, 0x2, 0xff,
    0x39, 0xfd, 0x0, 0x2f, 0xf3, 0x9f, 0xd0, 0x2,
    0xff, 0x38, 0xfd, 0x0, 0x2f, 0xf3, 0x8f, 0xe0,
    0x3, 0xff, 0x36, 0xff, 0x0, 0x5f, 0xf3, 0x2f,
    0xf8, 0xa, 0xff, 0x30, 0xdf, 0xff, 0xef, 0xf3,
    0x2, 0xbe, 0xc5, 0xff, 0x30, 0x0, 0x0, 0x3f,
    0xf2, 0x8, 0x20, 0xa, 0xfe, 0x6, 0xff, 0xff,
    0xff, 0x60, 0x8, 0xdf, 0xfc, 0x50, 0x0,

    /* U+0068 "h" */
    0x4f, 0xf2, 0x0, 0x0, 0x4, 0xff, 0x20, 0x0,
    0x0, 0x4f, 0xf2, 0x0, 0x0, 0x4, 0xff, 0x28,
    0xee, 0x70, 0x4f, 0xfb, 0xff, 0xff, 0x44, 0xff,
    0xc2, 0x4f, 0xf9, 0x4f, 0xf4, 0x0, 0xbf, 0xb4,
    0xff, 0x20, 0xa, 0xfc, 0x4f, 0xf2, 0x0, 0xaf,
    0xc4, 0xff, 0x20, 0xa, 0xfc, 0x4f, 0xf2, 0x0,
    0xaf, 0xc4, 0xff, 0x20, 0xa, 0xfc, 0x4f, 0xf2,
    0x0, 0xaf, 0xc4, 0xff, 0x20, 0xa, 0xfc, 0x4f,
    0xf2, 0x0, 0xaf, 0xc4, 0xff, 0x20, 0xa, 0xfc,
    0x4f, 0xf2, 0x0, 0xaf, 0xc4, 0xff, 0x20, 0xa,
    0xfc, 0x4f, 0xf2, 0x0, 0xaf, 0xc4, 0xff, 0x20,
    0xa, 0xfc, 0x4f, 0xf2, 0x0, 0xaf, 0xc0,

    /* U+0069 "i" */
    0x3f, 0xf2, 0x3f, 0xf2, 0x3, 0x30, 0x0, 0x0,
    0x3f, 0xf2, 0x3f, 0xf2, 0x3f, 0xf2, 0x3f, 0xf2,
    0x3f, 0xf2, 0x3f, 0xf2, 0x3f, 0xf2, 0x3f, 0xf2,
    0x3f, 0xf2, 0x3f, 0xf2, 0x3f, 0xf2, 0x3f, 0xf2,
    0x3f, 0xf2, 0x3f, 0xf2, 0x3f, 0xf2, 0x3f, 0xf2,
    0x3f, 0xf2, 0x3f, 0xf2,

    /* U+006A "j" */
    0x0, 0xef, 0x80, 0xe, 0xf8, 0x0, 0x33, 0x10,
    0xd, 0xf8, 0x0, 0xdf, 0x80, 0xd, 0xf8, 0x0,
    0xdf, 0x80, 0xd, 0xf8, 0x0, 0xdf, 0x80, 0xd,
    0xf8, 0x0, 0xdf, 0x80, 0xd, 0xf8, 0x0, 0xdf,
    0x80, 0xd, 0xf8, 0x0, 0xdf, 0x80, 0xd, 0xf8,
    0x0, 0xdf, 0x80, 0xd, 0xf8, 0x0, 0xdf, 0x80,
    0xd, 0xf8, 0x0, 0xdf, 0x80, 0xe, 0xf7, 0x1d,
    0xff, 0x51, 0xff, 0xa0,

    /* U+006B "k" */
    0x4f, 0xf2, 0x0, 0x0, 0x0, 0x4f, 0xf2, 0x0,
    0x0, 0x0, 0x4f, 0xf2, 0x0, 0x0, 0x0, 0x4f,
    0xf2, 0x0, 0xaf, 0xc0, 0x4f, 0xf2, 0x1, 0xff,
    0x60, 0x4f, 0xf2, 0x6, 0xfe, 0x0, 0x4f, 0xf2,
    0xd, 0xf8, 0x0, 0x4f, 0xf2, 0x3f, 0xf2, 0x0,
    0x4f, 0xf2, 0xaf, 0xb0, 0x0, 0x4f, 0xf3, 0xff,
    0x40, 0x0, 0x4f, 0xf9, 0xfe, 0x0, 0x0, 0x4f,
    0xfe, 0xf7, 0x0, 0x0, 0x4f, 0xfe, 0xf9, 0x0,
    0x0, 0x4f, 0xf7, 0xff, 0x0, 0x0, 0x4f, 0xf2,
    0xef, 0x60, 0x0, 0x4f, 0xf2, 0x8f, 0xd0, 0x0,
    0x4f, 0xf2, 0x2f, 0xf4, 0x0, 0x4f, 0xf2, 0xb,
    0xfb, 0x0, 0x4f, 0xf2, 0x4, 0xff, 0x20, 0x4f,
    0xf2, 0x0, 0xdf, 0x80, 0x4f, 0xf2, 0x0, 0x7f,
    0xf0,

    /* U+006C "l" */
    0x4f, 0xf2, 0x4f, 0xf2, 0x4f, 0xf2, 0x4f, 0xf2,
    0x4f, 0xf2, 0x4f, 0xf2, 0x4f, 0xf2, 0x4f, 0xf2,
    0x4f, 0xf2, 0x4f, 0xf2, 0x4f, 0xf2, 0x4f, 0xf2,
    0x4f, 0xf2, 0x4f, 0xf2, 0x4f, 0xf2, 0x4f, 0xf2,
    0x4f, 0xf2, 0x4f, 0xf2, 0x4f, 0xf2, 0x4f, 0xf2,
    0x4f, 0xf2,

    /* U+006D "m" */
    0x4f, 0xf2, 0x9f, 0xe6, 0x6, 0xdf, 0xb1, 0x4,
    0xff, 0xbf, 0xff, 0xf9, 0xff, 0xff, 0xa0, 0x4f,
    0xfb, 0x24, 0xff, 0xf5, 0x2a, 0xff, 0x4, 0xff,
    0x30, 0xc, 0xfb, 0x0, 0x4f, 0xf1, 0x4f, 0xf2,
    0x0, 0xcf, 0xa0, 0x3, 0xff, 0x24, 0xff, 0x20,
    0xc, 0xfa, 0x0, 0x3f, 0xf2, 0x4f, 0xf2, 0x0,
    0xcf, 0xa0, 0x3, 0xff, 0x24, 0xff, 0x20, 0xc,
    0xfa, 0x0, 0x3f, 0xf2, 0x4f, 0xf2, 0x0, 0xcf,
    0xa0, 0x3, 0xff, 0x24, 0xff, 0x20, 0xc, 0xfa,
    0x0, 0x3f, 0xf2, 0x4f, 0xf2, 0x0, 0xcf, 0xa0,
    0x3, 0xff, 0x24, 0xff, 0x20, 0xc, 0xfa, 0x0,
    0x3f, 0xf2, 0x4f, 0xf2, 0x0, 0xcf, 0xa0, 0x3,
    0xff, 0x24, 0xff, 0x20, 0xc, 0xfa, 0x0, 0x3f,
    0xf2, 0x4f, 0xf2, 0x0, 0xcf, 0xa0, 0x3, 0xff,
    0x24, 0xff, 0x20, 0xc, 0xfa, 0x0, 0x3f, 0xf2,
    0x4f, 0xf2, 0x0, 0xcf, 0xa0, 0x3, 0xff, 0x24,
    0xff, 0x20, 0xc, 0xfa, 0x0, 0x3f, 0xf2,

    /* U+006E "n" */
    0x4f, 0xf2, 0x8e, 0xe7, 0x4, 0xff, 0xbf, 0xff,
    0xf4, 0x4f, 0xfb, 0x13, 0xff, 0x94, 0xff, 0x30,
    0xa, 0xfb, 0x4f, 0xf2, 0x0, 0xaf, 0xc4, 0xff,
    0x20, 0xa, 0xfc, 0x4f, 0xf2, 0x0, 0xaf, 0xc4,
    0xff, 0x20, 0xa, 0xfc, 0x4f, 0xf2, 0x0, 0xaf,
    0xc4, 0xff, 0x20, 0xa, 0xfc, 0x4f, 0xf2, 0x0,
    0xaf, 0xc4, 0xff, 0x20, 0xa, 0xfc, 0x4f, 0xf2,
    0x0, 0xaf, 0xc4, 0xff, 0x20, 0xa, 0xfc, 0x4f,
    0xf2, 0x0, 0xaf, 0xc4, 0xff, 0x20, 0xa, 0xfc,
    0x4f, 0xf2, 0x0, 0xaf, 0xc4, 0xff, 0x20, 0xa,
    0xfc,

    /* U+006F "o" */
    0x0, 0x9e, 0xfd, 0x60, 0x0, 0xaf, 0xff, 0xff,
    0x60, 0x1f, 0xf7, 0x1b, 0xfd, 0x5, 0xff, 0x0,
    0x4f, 0xf1, 0x7f, 0xe0, 0x3, 0xff, 0x38, 0xfe,
    0x0, 0x2f, 0xf3, 0x8f, 0xe0, 0x2, 0xff, 0x48,
    0xfe, 0x0, 0x2f, 0xf4, 0x8f, 0xe0, 0x2, 0xff,
    0x48, 0xfe, 0x0, 0x2f, 0xf4, 0x8f, 0xe0, 0x2,
    0xff, 0x48, 0xfe, 0x0, 0x2f, 0xf4, 0x8f, 0xe0,
    0x2, 0xff, 0x37, 0xfe, 0x0, 0x3f, 0xf3, 0x5f,
    0xf0, 0x5, 0xff, 0x12, 0xff, 0x71, 0xbf, 0xe0,
    0xb, 0xff, 0xff, 0xf7, 0x0, 0x9, 0xef, 0xd7,
    0x0,

    /* U+0070 "p" */
    0x4f, 0xf4, 0xbf, 0xc3, 0x4, 0xff, 0xef, 0xff,
    0xe0, 0x4f, 0xfa, 0x15, 0xff, 0x34, 0xff, 0x40,
    0xe, 0xf6, 0x4f, 0xf2, 0x0, 0xdf, 0x74, 0xff,
    0x20, 0xd, 0xf8, 0x4f, 0xf2, 0x0, 0xdf, 0x84,
    0xff, 0x20, 0xd, 0xf8, 0x4f, 0xf2, 0x0, 0xdf,
    0x84, 0xff, 0x20, 0xd, 0xf8, 0x4f, 0xf2, 0x0,
    0xdf, 0x84, 0xff, 0x20, 0xd, 0xf8, 0x4f, 0xf2,
    0x0, 0xdf, 0x84, 0xff, 0x20, 0xe, 0xf7, 0x4f,
    0xf4, 0x0, 0xff, 0x64, 0xff, 0xb1, 0x7f, 0xf2,
    0x4f, 0xfe, 0xff, 0xfc, 0x4, 0xff, 0x4d, 0xfb,
    0x10, 0x4f, 0xf2, 0x0, 0x0, 0x4, 0xff, 0x20,
    0x0, 0x0, 0x4f, 0xf2, 0x0, 0x0, 0x0,

    /* U+0071 "q" */
    0x3, 0xdf, 0xc4, 0xff, 0x40, 0xdf, 0xff, 0xef,
    0xf4, 0x3f, 0xf6, 0xb, 0xff, 0x46, 0xff, 0x0,
    0x4f, 0xf4, 0x8f, 0xe0, 0x3, 0xff, 0x48, 0xfd,
    0x0, 0x2f, 0xf4, 0x8f, 0xd0, 0x2, 0xff, 0x48,
    0xfd, 0x0, 0x2f, 0xf4, 0x8f, 0xd0, 0x2, 0xff,
    0x48, 0xfd, 0x0, 0x2f, 0xf4, 0x8f, 0xd0, 0x2,
    0xff, 0x48, 0xfd, 0x0, 0x2f, 0xf4, 0x8f, 0xd0,
    0x2, 0xff, 0x47, 0xfe, 0x0, 0x3f, 0xf4, 0x5f,
    0xf0, 0x5, 0xff, 0x42, 0xff, 0x61, 0xbf, 0xf4,
    0xb, 0xff, 0xff, 0xff, 0x40, 0x1b, 0xfd, 0x5f,
    0xf4, 0x0, 0x0, 0x2, 0xff, 0x40, 0x0, 0x0,
    0x2f, 0xf4, 0x0, 0x0, 0x2, 0xff, 0x40,

    /* U+0072 "r" */
    0x4f, 0xf2, 0x9e, 0x24, 0xff, 0xcf, 0xe2, 0x4f,
    0xfd, 0x30, 0x4, 0xff, 0x30, 0x0, 0x4f, 0xf2,
    0x0, 0x4, 0xff, 0x20, 0x0, 0x4f, 0xf2, 0x0,
    0x4, 0xff, 0x20, 0x0, 0x4f, 0xf2, 0x0, 0x4,
    0xff, 0x20, 0x0, 0x4f, 0xf2, 0x0, 0x4, 0xff,
    0x20, 0x0, 0x4f, 0xf2, 0x0, 0x4, 0xff, 0x20,
    0x0, 0x4f, 0xf2, 0x0, 0x4, 0xff, 0x20, 0x0,
    0x4f, 0xf2, 0x0, 0x4, 0xff, 0x20, 0x0,

    /* U+0073 "s" */
    0x5, 0xdf, 0xea, 0x0, 0x5f, 0xff, 0xff, 0xa0,
    0xcf, 0xc2, 0x7f, 0xf1, 0xef, 0x60, 0xf, 0xf4,
    0xef, 0x60, 0xe, 0xf6, 0xcf, 0xb0, 0xd, 0xf6,
    0x6f, 0xf4, 0x4, 0x52, 0xd, 0xfe, 0x30, 0x0,
    0x2, 0xef, 0xf3, 0x0, 0x0, 0x2e, 0xfe, 0x20,
    0x0, 0x2, 0xef, 0xc0, 0x56, 0x30, 0x5f, 0xf4,
    0xdf, 0x80, 0xe, 0xf9, 0xcf, 0x80, 0xb, 0xfb,
    0xbf, 0xa0, 0xc, 0xfa, 0x7f, 0xf3, 0x4f, 0xf7,
    0x1f, 0xff, 0xff, 0xf1, 0x3, 0xbf, 0xfb, 0x30,

    /* U+0074 "t" */
    0x0, 0xdf, 0x80, 0x0, 0xd, 0xf8, 0x0, 0x0,
    0xdf, 0x80, 0x7, 0xff, 0xff, 0xf7, 0x5d, 0xff,
    0xed, 0x50, 0xd, 0xf8, 0x0, 0x0, 0xdf, 0x80,
    0x0, 0xd, 0xf8, 0x0, 0x0, 0xdf, 0x80, 0x0,
    0xd, 0xf8, 0x0, 0x0, 0xdf, 0x80, 0x0, 0xd,
    0xf8, 0x0, 0x0, 0xdf, 0x80, 0x0, 0xd, 0xf8,
    0x0, 0x0, 0xdf, 0x80, 0x0, 0xd, 0xf8, 0x0,
    0x0, 0xdf, 0x80, 0x0, 0xd, 0xf8, 0x0, 0x0,
    0xdf, 0xa0, 0x0, 0xb, 0xff, 0xf7, 0x0, 0x3d,
    0xff, 0x70,

    /* U+0075 "u" */
    0x6f, 0xf0, 0x0, 0xcf, 0xa6, 0xff, 0x0, 0xc,
    0xfa, 0x6f, 0xf0, 0x0, 0xcf, 0xa6, 0xff, 0x0,
    0xc, 0xfa, 0x6f, 0xf0, 0x0, 0xcf, 0xa6, 0xff,
    0x0, 0xc, 0xfa, 0x6f, 0xf0, 0x0, 0xcf, 0xa6,
    0xff, 0x0, 0xc, 0xfa, 0x6f, 0xf0, 0x0, 0xcf,
    0xa6, 0xff, 0x0, 0xc, 0xfa, 0x6f, 0xf0, 0x0,
    0xcf, 0xa6, 0xff, 0x0, 0xc, 0xfa, 0x6f, 0xf0,
    0x0, 0xcf, 0xa6, 0xff, 0x0, 0xc, 0xfa, 0x5f,
    0xf1, 0x0, 0xdf, 0xa3, 0xff, 0x60, 0x4f, 0xfa,
    0xd, 0xff, 0xef, 0xdf, 0xa0, 0x2c, 0xfd, 0x4c,
    0xfa,

    /* U+0076 "v" */
    0x6f, 0xc0, 0x0, 0xff, 0x44, 0xfe, 0x0, 0xf,
    0xf1, 0x2f, 0xf0, 0x2, 0xff, 0x0, 0xff, 0x20,
    0x4f, 0xd0, 0xe, 0xf4, 0x6, 0xfb, 0x0, 0xbf,
    0x60, 0x8f, 0x80, 0x9, 0xf8, 0xa, 0xf6, 0x0,
    0x7f, 0xa0, 0xbf, 0x40, 0x5, 0xfc, 0xd, 0xf2,
    0x0, 0x3f, 0xe0, 0xff, 0x0, 0x0, 0xff, 0x1f,
    0xd0, 0x0, 0xe, 0xf4, 0xfb, 0x0, 0x0, 0xcf,
    0x7f, 0x90, 0x0, 0xa, 0xfa, 0xf7, 0x0, 0x0,
    0x8f, 0xff, 0x40, 0x0, 0x5, 0xff, 0xf2, 0x0,
    0x0, 0x3f, 0xff, 0x0, 0x0, 0x1, 0xff, 0xe0,
    0x0,

    /* U+0077 "w" */
    0x5f, 0xa0, 0x5, 0xfe, 0x0, 0xf, 0xe3, 0xfb,
    0x0, 0x7f, 0xf1, 0x2, 0xfc, 0x1f, 0xd0, 0x9,
    0xff, 0x30, 0x3f, 0xb0, 0xfe, 0x0, 0xbf, 0xf5,
    0x5, 0xf9, 0xe, 0xf0, 0xe, 0xdf, 0x80, 0x6f,
    0x70, 0xcf, 0x10, 0xfa, 0xea, 0x7, 0xf5, 0xa,
    0xf3, 0x2f, 0x7c, 0xc0, 0x9f, 0x30, 0x8f, 0x44,
    0xf5, 0xae, 0xa, 0xf1, 0x6, 0xf6, 0x6f, 0x28,
    0xf1, 0xcf, 0x0, 0x5f, 0x79, 0xf0, 0x6f, 0x3d,
    0xd0, 0x3, 0xf9, 0xbe, 0x4, 0xf5, 0xfb, 0x0,
    0x1f, 0xad, 0xb0, 0x2f, 0x8f, 0xa0, 0x0, 0xfb,
    0xf9, 0x0, 0xfb, 0xf8, 0x0, 0xd, 0xef, 0x70,
    0xe, 0xef, 0x60, 0x0, 0xbf, 0xf4, 0x0, 0xcf,
    0xf4, 0x0, 0xa, 0xff, 0x20, 0xa, 0xff, 0x20,
    0x0, 0x8f, 0xf0, 0x0, 0x8f, 0xf0, 0x0, 0x6,
    0xfd, 0x0, 0x6, 0xfe, 0x0,

    /* U+0078 "x" */
    0x5f, 0xc0, 0x0, 0xff, 0x20, 0xff, 0x10, 0x4f,
    0xd0, 0xb, 0xf6, 0x9, 0xf7, 0x0, 0x6f, 0xc0,
    0xdf, 0x20, 0x1, 0xff, 0x4f, 0xd0, 0x0, 0xc,
    0xfd, 0xf8, 0x0, 0x0, 0x7f, 0xff, 0x30, 0x0,
    0x1, 0xff, 0xe0, 0x0, 0x0, 0xd, 0xf9, 0x0,
    0x0, 0x0, 0xff, 0xb0, 0x0, 0x0, 0x4f, 0xff,
    0x0, 0x0, 0x9, 0xff, 0xf5, 0x0, 0x0, 0xef,
    0xaf, 0xa0, 0x0, 0x4f, 0xc3, 0xff, 0x0, 0x9,
    0xf7, 0xe, 0xf4, 0x0, 0xef, 0x30, 0x9f, 0x90,
    0x3f, 0xe0, 0x4, 0xfe, 0x8, 0xfa, 0x0, 0xf,
    0xf4,

    /* U+0079 "y" */
    0x4f, 0xf0, 0x0, 0x8f, 0xc2, 0xff, 0x20, 0xa,
    0xf9, 0xf, 0xf4, 0x0, 0xcf, 0x70, 0xdf, 0x60,
    0xe, 0xf4, 0xa, 0xf8, 0x0, 0xff, 0x20, 0x7f,
    0xa0, 0x2f, 0xf0, 0x5, 0xfd, 0x4, 0xfd, 0x0,
    0x2f, 0xf0, 0x6f, 0xa0, 0x0, 0xff, 0x18, 0xf8,
    0x0, 0xd, 0xf3, 0x9f, 0x50, 0x0, 0xaf, 0x5b,
    0xf2, 0x0, 0x7, 0xf6, 0xcf, 0x0, 0x0, 0x5f,
    0x8e, 0xd0, 0x0, 0x2, 0xfa, 0xfb, 0x0, 0x0,
    0xf, 0xff, 0x80, 0x0, 0x0, 0xdf, 0xf6, 0x0,
    0x0, 0xa, 0xff, 0x30, 0x0, 0x0, 0x8f, 0xf1,
    0x0, 0x0, 0x7, 0xfe, 0x0, 0x0, 0x9d, 0xff,
    0xb0, 0x0, 0xb, 0xff, 0xd3, 0x0, 0x0,

    /* U+007A "z" */
    0xd, 0xff, 0xff, 0xf4, 0xd, 0xff, 0xff, 0xf3,
    0x1, 0x11, 0x6f, 0xf0, 0x0, 0x0, 0x9f, 0xc0,
    0x0, 0x0, 0xef, 0x70, 0x0, 0x2, 0xff, 0x30,
    0x0, 0x7, 0xfe, 0x0, 0x0, 0xb, 0xfa, 0x0,
    0x0, 0xf, 0xf6, 0x0, 0x0, 0x4f, 0xf1, 0x0,
    0x0, 0x8f, 0xd0, 0x0, 0x0, 0xdf, 0x90, 0x0,
    0x1, 0xff, 0x40, 0x0, 0x5, 0xff, 0x0, 0x0,
    0xa, 0xfb, 0x0, 0x0, 0xe, 0xf8, 0x11, 0x10,
    0x1f, 0xff, 0xff, 0xf0, 0x1f, 0xff, 0xff, 0xf0,

    /* U+007B "{" */
    0x0, 0x5, 0xdf, 0x90, 0x1, 0xff, 0xf9, 0x0,
    0x5f, 0xf4, 0x0, 0x6, 0xff, 0x0, 0x0, 0x7f,
    0xf0, 0x0, 0x7, 0xff, 0x0, 0x0, 0x7f, 0xf0,
    0x0, 0x7, 0xff, 0x0, 0x0, 0x7f, 0xe0, 0x0,
    0x1c, 0xfc, 0x0, 0x1f, 0xff, 0x50, 0x1, 0xff,
    0x90, 0x0, 0xb, 0xff, 0x90, 0x0, 0x9, 0xfd,
    0x0, 0x0, 0x7f, 0xe0, 0x0, 0x7, 0xff, 0x0,
    0x0, 0x7f, 0xf0, 0x0, 0x7, 0xff, 0x0, 0x0,
    0x6f, 0xf0, 0x0, 0x6, 0xff, 0x0, 0x0, 0x4f,
    0xf4, 0x0, 0x1, 0xff, 0xf9, 0x0, 0x4, 0xcf,
    0x90,

    /* U+007C "|" */
    0x78, 0x2f, 0xf5, 0xff, 0x5f, 0xf5, 0xff, 0x5f,
    0xf5, 0xff, 0x5f, 0xf5, 0xff, 0x5f, 0xf5, 0xff,
    0x5f, 0xf5, 0xff, 0x5f, 0xf5, 0xff, 0x5f, 0xf5,
    0xff, 0x5f, 0xf5, 0xff, 0x5f, 0xf5, 0xff, 0x5f,
    0xf5, 0xff, 0x5f, 0xf5,

    /* U+007D "}" */
    0x1f, 0xea, 0x0, 0x0, 0xff, 0xf9, 0x0, 0x0,
    0xbf, 0xd0, 0x0, 0x7, 0xfe, 0x0, 0x0, 0x7f,
    0xf0, 0x0, 0x7, 0xff, 0x0, 0x0, 0x7f, 0xf0,
    0x0, 0x6, 0xff, 0x0, 0x0, 0x6f, 0xf0, 0x0,
    0x4, 0xff, 0x50, 0x0, 0xc, 0xff, 0x90, 0x0,
    0x3e, 0xf9, 0x0, 0x1f, 0xfd, 0x60, 0x5, 0xff,
    0x10, 0x0, 0x6f, 0xf0, 0x0, 0x6, 0xff, 0x0,
    0x0, 0x7f, 0xf0, 0x0, 0x7, 0xff, 0x0, 0x0,
    0x7f, 0xf0, 0x0, 0x7, 0xfe, 0x0, 0x1, 0xbf,
    0xd0, 0x1, 0xff, 0xf8, 0x0, 0xf, 0xe9, 0x0,
    0x0,

    /* U+007E "~" */
    0x0, 0x56, 0x30, 0x0, 0x59, 0x30, 0xcf, 0xff,
    0xfb, 0x9f, 0xf5, 0x6f, 0xff, 0xff, 0xff, 0xfd,
    0x3, 0xc6, 0x3, 0x7b, 0xed, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F001 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x7b, 0xe8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x49, 0xdf, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x16, 0xbf, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x3, 0x8c, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0x3a,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x0, 0x0, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xaf, 0xff,
    0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xd8, 0x40, 0xf, 0xff, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xfb, 0x62, 0x0, 0x0, 0xf, 0xff,
    0x0, 0x0, 0x0, 0xff, 0xfe, 0x94, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xff, 0x0, 0x0, 0x0, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff,
    0x0, 0x0, 0x0, 0xff, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xff, 0x0, 0x0, 0x0, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff,
    0x0, 0x0, 0x0, 0xff, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xff, 0x0, 0x0, 0x0, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff,
    0x0, 0x0, 0x0, 0xff, 0xf0, 0x0, 0x0, 0x0,
    0x3a, 0xdf, 0xdf, 0xff, 0x0, 0x0, 0x0, 0xff,
    0xf0, 0x0, 0x0, 0x6, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x0, 0x0, 0xff, 0xf0, 0x0, 0x0, 0xe,
    0xff, 0xff, 0xff, 0xff, 0x3, 0xad, 0xfd, 0xff,
    0xf0, 0x0, 0x0, 0xe, 0xff, 0xff, 0xff, 0xfe,
    0x6f, 0xff, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x6,
    0xff, 0xff, 0xff, 0xf6, 0xef, 0xff, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0x3a, 0xef, 0xea, 0x30,
    0xef, 0xff, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xff, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xae, 0xfe, 0xa3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F008 "" */
    0xb7, 0x0, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0x0, 0x7b, 0xfd, 0x88, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x88, 0xdf,
    0xff, 0xff, 0xff, 0xb4, 0x44, 0x44, 0x44, 0x44,
    0x5f, 0xff, 0xff, 0xff, 0xf9, 0x0, 0xcf, 0x80,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xf5, 0x0, 0x9f,
    0xf8, 0x0, 0xbf, 0x80, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xf4, 0x0, 0x8f, 0xf9, 0x0, 0xcf, 0x80,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xf5, 0x0, 0x9f,
    0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xff, 0xfd, 0x88, 0xef, 0xa2,
    0x22, 0x22, 0x22, 0x22, 0x3f, 0xfb, 0x88, 0xdf,
    0xf8, 0x0, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0x0, 0x8f, 0xf8, 0x0, 0xbf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0, 0x8f,
    0xfd, 0x88, 0xef, 0xa2, 0x22, 0x22, 0x22, 0x22,
    0x3f, 0xfb, 0x88, 0xdf, 0xff, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0xf9, 0x0, 0xcf, 0x80, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xf5, 0x0, 0x9f, 0xf8, 0x0, 0xbf, 0x80,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xf4, 0x0, 0x8f,
    0xf9, 0x0, 0xcf, 0x80, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xf5, 0x0, 0x9f, 0xff, 0xff, 0xff, 0xb4,
    0x44, 0x44, 0x44, 0x44, 0x5f, 0xff, 0xff, 0xff,
    0xfd, 0x88, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfb, 0x88, 0xdf, 0xb7, 0x0, 0xbf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0, 0x7b,

    /* U+F00B "" */
    0x14, 0x44, 0x44, 0x10, 0x3, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x41, 0xef, 0xff, 0xff, 0xe0,
    0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xff, 0xff, 0xff, 0xf0, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x7b, 0xbb, 0xbb, 0x60, 0x2a, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xb7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xff, 0xff, 0xb0, 0x4f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfb, 0xff, 0xff, 0xff, 0xf0,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xbf, 0xff, 0xff, 0xb0,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x14, 0x44, 0x44, 0x10,
    0x3, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x41,
    0xef, 0xff, 0xff, 0xe0, 0x6f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xf0,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x7b, 0xbb, 0xbb, 0x60,
    0x2a, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xb7,

    /* U+F00C "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbd, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xe2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xff, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xff, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xff, 0xff, 0xd1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xff, 0xff, 0xfd, 0x10,
    0x2, 0xdb, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xff, 0xff, 0xd1, 0x0, 0x2e, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0xc, 0xff, 0xff, 0xfd, 0x10, 0x0,
    0xdf, 0xff, 0xfc, 0x0, 0x0, 0x0, 0xcf, 0xff,
    0xff, 0xd1, 0x0, 0x0, 0xdf, 0xff, 0xff, 0xc0,
    0x0, 0xc, 0xff, 0xff, 0xfd, 0x10, 0x0, 0x0,
    0x1d, 0xff, 0xff, 0xfc, 0x0, 0xcf, 0xff, 0xff,
    0xd1, 0x0, 0x0, 0x0, 0x1, 0xdf, 0xff, 0xff,
    0xcc, 0xff, 0xff, 0xfd, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x1d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xdf, 0xff,
    0xff, 0xff, 0xfd, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1d, 0xff, 0xff, 0xff, 0xd1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xdf,
    0xff, 0xfd, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1d, 0xff, 0xd1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xcc, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F00D "" */
    0x1, 0x41, 0x0, 0x0, 0x0, 0x0, 0x3, 0x30,
    0x1, 0xdf, 0xd1, 0x0, 0x0, 0x0, 0x7, 0xff,
    0x70, 0xcf, 0xff, 0xd1, 0x0, 0x0, 0x7, 0xff,
    0xff, 0x4e, 0xff, 0xff, 0xd1, 0x0, 0x7, 0xff,
    0xff, 0xf6, 0x4f, 0xff, 0xff, 0xd1, 0x7, 0xff,
    0xff, 0xfb, 0x0, 0x4f, 0xff, 0xff, 0xd8, 0xff,
    0xff, 0xfb, 0x0, 0x0, 0x4f, 0xff, 0xff, 0xff,
    0xff, 0xfb, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xff,
    0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff,
    0xff, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff,
    0xff, 0xff, 0xd1, 0x0, 0x0, 0x0, 0x7, 0xff,
    0xff, 0xff, 0xff, 0xd1, 0x0, 0x0, 0x7, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xd1, 0x0, 0x7, 0xff,
    0xff, 0xfb, 0x5f, 0xff, 0xff, 0xd1, 0x7, 0xff,
    0xff, 0xfb, 0x0, 0x4f, 0xff, 0xff, 0xd1, 0xff,
    0xff, 0xfb, 0x0, 0x0, 0x4f, 0xff, 0xff, 0x7a,
    0xff, 0xfb, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xf3,
    0xb, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf4,
    0x0, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F011 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xef, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0x60, 0x0, 0xef, 0xfd, 0x0,
    0x7, 0x70, 0x0, 0x0, 0x0, 0x1, 0xcf, 0xf3,
    0x0, 0xef, 0xfd, 0x0, 0x5f, 0xfb, 0x0, 0x0,
    0x0, 0xc, 0xff, 0xfb, 0x0, 0xef, 0xfd, 0x0,
    0xcf, 0xff, 0xb0, 0x0, 0x0, 0x9f, 0xff, 0xf6,
    0x0, 0xef, 0xfd, 0x0, 0x7f, 0xff, 0xf7, 0x0,
    0x3, 0xff, 0xff, 0x60, 0x0, 0xef, 0xfd, 0x0,
    0x8, 0xff, 0xff, 0x20, 0xa, 0xff, 0xf9, 0x0,
    0x0, 0xef, 0xfd, 0x0, 0x0, 0xbf, 0xff, 0x90,
    0x1f, 0xff, 0xe0, 0x0, 0x0, 0xef, 0xfd, 0x0,
    0x0, 0x1f, 0xff, 0xf0, 0x5f, 0xff, 0x90, 0x0,
    0x0, 0xef, 0xfd, 0x0, 0x0, 0xa, 0xff, 0xf3,
    0x8f, 0xff, 0x40, 0x0, 0x0, 0xef, 0xfd, 0x0,
    0x0, 0x6, 0xff, 0xf6, 0x9f, 0xff, 0x20, 0x0,
    0x0, 0xef, 0xfd, 0x0, 0x0, 0x4, 0xff, 0xf8,
    0x9f, 0xff, 0x20, 0x0, 0x0, 0xef, 0xfc, 0x0,
    0x0, 0x4, 0xff, 0xf7, 0x8f, 0xff, 0x40, 0x0,
    0x0, 0x37, 0x72, 0x0, 0x0, 0x6, 0xff, 0xf6,
    0x5f, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xff, 0xf4, 0x1f, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xf0,
    0xb, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xff, 0xa0, 0x3, 0xff, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xff, 0x20,
    0x0, 0xaf, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x1,
    0x9f, 0xff, 0xf8, 0x0, 0x0, 0xd, 0xff, 0xff,
    0xe8, 0x42, 0x24, 0x9e, 0xff, 0xff, 0xb0, 0x0,
    0x0, 0x1, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfb, 0x10, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3b, 0xff, 0xff, 0xff, 0xff,
    0xb3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x15, 0x89, 0x97, 0x51, 0x0, 0x0, 0x0, 0x0,

    /* U+F013 "" */
    0x0, 0x0, 0x0, 0x0, 0x4, 0x89, 0x98, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xc3, 0x2c, 0xff, 0xff, 0xff, 0xff,
    0xc2, 0x3c, 0xc0, 0x0, 0x0, 0x8f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x30, 0xa, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0x94, 0x49, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0x6, 0xef, 0xff, 0xff,
    0xf4, 0x0, 0x0, 0x4f, 0xff, 0xff, 0xfe, 0x60,
    0x0, 0x1e, 0xff, 0xff, 0x90, 0x0, 0x0, 0x9,
    0xff, 0xff, 0xe1, 0x0, 0x0, 0xf, 0xff, 0xff,
    0x40, 0x0, 0x0, 0x4, 0xff, 0xff, 0xf0, 0x0,
    0x0, 0xf, 0xff, 0xff, 0x40, 0x0, 0x0, 0x4,
    0xff, 0xff, 0xf0, 0x0, 0x0, 0x1e, 0xff, 0xff,
    0x90, 0x0, 0x0, 0x9, 0xff, 0xff, 0xe1, 0x0,
    0x6, 0xef, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x4f,
    0xff, 0xff, 0xfe, 0x60, 0xf, 0xff, 0xff, 0xff,
    0xff, 0x94, 0x49, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xa0, 0x3, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x30,
    0x0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0x0, 0x0, 0xc, 0xc3, 0x2c,
    0xff, 0xff, 0xff, 0xff, 0xc2, 0x3c, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0x89, 0x98, 0x40, 0x0, 0x0, 0x0, 0x0,

    /* U+F015 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x13, 0x10,
    0x0, 0x2, 0x44, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xfe, 0x40, 0x0, 0xdf,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xff, 0xff, 0x70, 0xe, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xff,
    0xff, 0xa0, 0xef, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xcf, 0xff, 0xb3, 0xbf, 0xff, 0xce,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xef,
    0xff, 0x90, 0x0, 0x9f, 0xff, 0xff, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xff, 0x60, 0x5d,
    0x50, 0x6f, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xfe, 0x30, 0x8f, 0xff, 0x80, 0x3e,
    0xff, 0xfe, 0x0, 0x0, 0x0, 0x1b, 0xff, 0xfd,
    0x20, 0xaf, 0xff, 0xff, 0xa0, 0x2d, 0xff, 0xfb,
    0x0, 0x0, 0x2d, 0xff, 0xfb, 0x1, 0xcf, 0xff,
    0xff, 0xff, 0xc1, 0xb, 0xff, 0xfd, 0x20, 0x4f,
    0xff, 0xf8, 0x3, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xe3, 0x8, 0xff, 0xfe, 0x4e, 0xff, 0xf5, 0x5,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x5,
    0xff, 0xfe, 0x4f, 0xe3, 0x8, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0x3, 0xef, 0x40,
    0x41, 0x7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf7, 0x1, 0x40, 0x0, 0x0, 0x8f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0xff,
    0xeb, 0xbb, 0xef, 0xff, 0xff, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xff, 0xff, 0xf4, 0x0, 0x4,
    0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xff, 0xff, 0x40, 0x0, 0x4f, 0xff, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xff,
    0xf4, 0x0, 0x4, 0xff, 0xff, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0x40, 0x0,
    0x4f, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xff, 0xff, 0xf3, 0x0, 0x3, 0xff, 0xff,
    0xff, 0x70, 0x0, 0x0, 0x0, 0x3, 0xbb, 0xbb,
    0xba, 0x10, 0x0, 0x1a, 0xbb, 0xbb, 0xb3, 0x0,
    0x0,

    /* U+F019 "" */
    0x0, 0x0, 0x0, 0x0, 0x9, 0xdd, 0xdd, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xad, 0xdd,
    0xdf, 0xff, 0xff, 0xfd, 0xdd, 0xda, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x1d, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xd1, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xdf, 0xff, 0xff, 0xff, 0xff,
    0xfd, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1d,
    0xff, 0xff, 0xff, 0xff, 0xd1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xdf, 0xff, 0xff, 0xfd,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1d, 0xff, 0xff, 0xd1, 0x0, 0x0, 0x0, 0x0,
    0x25, 0x55, 0x55, 0x55, 0x11, 0xdf, 0xfd, 0x11,
    0x55, 0x55, 0x55, 0x52, 0xef, 0xff, 0xff, 0xff,
    0xd1, 0x1d, 0xd1, 0x1d, 0xff, 0xff, 0xff, 0xfe,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x10, 0x1, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf9, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x71, 0xe7, 0x1e, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xb6, 0xfb, 0x6f, 0xff, 0xbf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,

    /* U+F01C "" */
    0x0, 0x0, 0x3, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xd3, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe1, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0x5f, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xff, 0x50, 0x0, 0x0, 0x1e,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xfe, 0x10, 0x0, 0xa, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xfa,
    0x0, 0x5, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xf5, 0x1, 0xef,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xff, 0xe1, 0xaf, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xff, 0xaf, 0xff, 0xff, 0xff, 0xff, 0x90, 0x0,
    0x0, 0x0, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x20, 0x0, 0x0, 0x2f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf9, 0x0, 0x0, 0xa, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xd3, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xd3,

    /* U+F021 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0x32, 0x0, 0x0, 0x0, 0x0,
    0x3, 0x67, 0x75, 0x20, 0x0, 0x0, 0x4f, 0xff,
    0x0, 0x0, 0x0, 0x29, 0xff, 0xff, 0xff, 0xfd,
    0x70, 0x0, 0x4f, 0xff, 0x0, 0x0, 0x8, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0x60, 0x3f, 0xff,
    0x0, 0x1, 0xcf, 0xff, 0xff, 0xfe, 0xef, 0xff,
    0xff, 0xfb, 0x3f, 0xff, 0x0, 0xd, 0xff, 0xff,
    0xb4, 0x0, 0x0, 0x5b, 0xff, 0xff, 0xdf, 0xff,
    0x0, 0xaf, 0xff, 0xe4, 0x0, 0x0, 0x0, 0x0,
    0x4d, 0xff, 0xff, 0xff, 0x4, 0xff, 0xfd, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff, 0xff,
    0xc, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x6, 0xff,
    0xee, 0xef, 0xff, 0xff, 0x1f, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x5f, 0xff, 0x10, 0x0, 0x0, 0x0, 0x7, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x4, 0x43, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x44, 0x44, 0x44, 0x44, 0x43,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x34, 0x44, 0x44, 0x44, 0x44, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x34, 0x40, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x70, 0x0, 0x0, 0x0, 0x1, 0xff, 0xf5,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xf1, 0xff, 0xff, 0xfd, 0xee,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xb0,
    0xff, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xdf, 0xff, 0x40, 0xff, 0xff, 0xff, 0xd3,
    0x0, 0x0, 0x0, 0x0, 0x4e, 0xff, 0xfa, 0x0,
    0xff, 0xfe, 0xff, 0xff, 0xb5, 0x0, 0x0, 0x4b,
    0xff, 0xff, 0xd0, 0x0, 0xff, 0xf3, 0xbf, 0xff,
    0xff, 0xfe, 0xef, 0xff, 0xff, 0xfc, 0x10, 0x0,
    0xff, 0xf3, 0x6, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x80, 0x0, 0x0, 0xff, 0xf4, 0x0, 0x17,
    0xdf, 0xff, 0xff, 0xff, 0xa2, 0x0, 0x0, 0x0,
    0xff, 0xf4, 0x0, 0x0, 0x2, 0x67, 0x76, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x23, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F026 "" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0xa7, 0x0, 0x0,
    0x0, 0x0, 0x1c, 0xff, 0x0, 0x0, 0x0, 0x1,
    0xcf, 0xff, 0x0, 0x0, 0x0, 0x1c, 0xff, 0xff,
    0x14, 0x44, 0x44, 0xcf, 0xff, 0xff, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x7b, 0xbb,
    0xbb, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0x5f,
    0xff, 0xff, 0x0, 0x0, 0x0, 0x5, 0xff, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x10,

    /* U+F027 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x96, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff,
    0xff, 0x0, 0x0, 0x0, 0x13, 0x33, 0x33, 0xcf,
    0xff, 0xff, 0x0, 0x0, 0x0, 0xdf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x0, 0x4a, 0x20, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x0, 0xcf, 0xe1, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0x3e, 0xfa,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0x6,
    0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x5, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x2e, 0xfa, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x0, 0xcf, 0xf2, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x5b, 0x30, 0x7c, 0xcc, 0xcc,
    0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x21,
    0x0, 0x0, 0x0,

    /* U+F028 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xb1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xd1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1a, 0x70, 0x0,
    0x0, 0x0, 0x6, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1c, 0xff, 0x0, 0x0, 0x6, 0x30,
    0x5, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x1c,
    0xff, 0xf0, 0x0, 0x3, 0xff, 0x70, 0x8, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x1c, 0xff, 0xff, 0x0,
    0x0, 0xc, 0xff, 0x70, 0xc, 0xfc, 0x1, 0x44,
    0x44, 0x4c, 0xff, 0xff, 0xf0, 0x0, 0x0, 0xa,
    0xff, 0x30, 0x4f, 0xf3, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x5a, 0x20, 0xc, 0xfb, 0x0,
    0xdf, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0xc, 0xfe, 0x20, 0x4f, 0xf1, 0x8, 0xfb, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0x2e, 0xfa,
    0x0, 0xff, 0x50, 0x5f, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0x0, 0x6f, 0xe0, 0xc, 0xf7,
    0x4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x6, 0xfe, 0x0, 0xcf, 0x70, 0x4f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x2, 0xef,
    0xa0, 0xf, 0xf5, 0x6, 0xfe, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x0, 0xcf, 0xe2, 0x4, 0xff,
    0x10, 0x9f, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x4, 0xa2, 0x0, 0xcf, 0xb0, 0xd, 0xf8,
    0x7b, 0xbb, 0xbb, 0xff, 0xff, 0xff, 0x0, 0x0,
    0x0, 0xaf, 0xf3, 0x4, 0xff, 0x30, 0x0, 0x0,
    0x5, 0xff, 0xff, 0xf0, 0x0, 0x0, 0xcf, 0xf7,
    0x0, 0xcf, 0xc0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0xff, 0x0, 0x0, 0x3f, 0xf7, 0x0, 0x6f, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xf0, 0x0,
    0x0, 0x53, 0x0, 0x4f, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xfd, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xfb, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0x0, 0x0, 0x0,

    /* U+F03E "" */
    0x3d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xd3, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0xff, 0xff, 0xca, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x1d,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc0, 0x0, 0x4, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x0, 0x2,
    0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff,
    0xff, 0xe1, 0x0, 0x8, 0xff, 0xff, 0xff, 0xf4,
    0x4, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x42, 0x8f,
    0xff, 0xff, 0xff, 0x40, 0x0, 0x4f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0x4, 0xff, 0xff, 0xff, 0xff, 0xff, 0x6b,
    0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x4f, 0xff,
    0xff, 0xff, 0xf4, 0x0, 0xbf, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xff, 0xff, 0xff, 0x40, 0x0,
    0xb, 0x40, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xff, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0x3d, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd3,

    /* U+F043 "" */
    0x0, 0x0, 0x0, 0xc, 0xe3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xff, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xff, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xff,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x1e, 0xff, 0xff,
    0xff, 0xff, 0xf4, 0x0, 0x0, 0xa, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe0, 0x0, 0x5, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x90, 0x0, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x20, 0x5f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0xa, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe0, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x2f,
    0xff, 0x2d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0xef, 0xf1, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x3c, 0xff, 0x44, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf1, 0x8f, 0xfc, 0x7, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0x1, 0xff, 0xf9, 0x2, 0x7c, 0xff, 0xff,
    0xff, 0x50, 0x7, 0xff, 0xfb, 0x40, 0x6f, 0xff,
    0xff, 0xb0, 0x0, 0xa, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xd1, 0x0, 0x0, 0x8, 0xff, 0xff, 0xff,
    0xff, 0xb0, 0x0, 0x0, 0x0, 0x2, 0x8d, 0xff,
    0xda, 0x40, 0x0, 0x0,

    /* U+F048 "" */
    0x34, 0x40, 0x0, 0x0, 0x0, 0x0, 0x4, 0xf,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x1d, 0xfb, 0xff,
    0xf6, 0x0, 0x0, 0x0, 0x2d, 0xff, 0xff, 0xff,
    0x60, 0x0, 0x0, 0x2e, 0xff, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0x3f, 0xff, 0xff, 0xff, 0xff, 0x60,
    0x0, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0,
    0x5f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60, 0x6f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x8f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xdf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x8e, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf6, 0x1d, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x60, 0x1c, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf6, 0x0, 0xb, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x60, 0x0, 0xa, 0xff, 0xff, 0xff, 0xff,
    0xf6, 0x0, 0x0, 0x9, 0xff, 0xff, 0xff, 0xff,
    0x60, 0x0, 0x0, 0x7, 0xff, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xea, 0xbb, 0x30,
    0x0, 0x0, 0x0, 0x5, 0xb4,

    /* U+F04B "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xdf, 0x91, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xff, 0xe6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xff, 0xff, 0xfc, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xa1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x40, 0x0, 0x0,
    0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xa2, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x50,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xb2, 0xf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x6f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x20,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe5, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfb, 0x20, 0x0, 0x0, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xd4, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0xfa, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xd3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xff, 0xfe, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3d, 0xf9, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F04C "" */
    0x19, 0xcc, 0xcc, 0xc9, 0x10, 0x0, 0x19, 0xcc,
    0xcc, 0xc9, 0x1b, 0xff, 0xff, 0xff, 0xfb, 0x0,
    0xb, 0xff, 0xff, 0xff, 0xfb, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x0, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0x0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xff, 0xff, 0xff, 0xfe, 0x0, 0xe, 0xff, 0xff,
    0xff, 0xfe, 0x5f, 0xff, 0xff, 0xff, 0x50, 0x0,
    0x5f, 0xff, 0xff, 0xff, 0x50, 0x13, 0x33, 0x33,
    0x10, 0x0, 0x0, 0x13, 0x33, 0x33, 0x10,

    /* U+F04D "" */
    0x1, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x41, 0x6, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf6, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xb1, 0x8b, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0x81,

    /* U+F051 "" */
    0x4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x44, 0x3b,
    0xfd, 0x10, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xff,
    0xfd, 0x20, 0x0, 0x0, 0x6, 0xff, 0xff, 0xff,
    0xfe, 0x30, 0x0, 0x0, 0x6f, 0xff, 0xff, 0xff,
    0xff, 0x40, 0x0, 0x6, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x50, 0x0, 0x6f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x60, 0x6, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x70, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x86, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x8f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x16, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0x0, 0x6f, 0xff,
    0xff, 0xff, 0xff, 0xfb, 0x0, 0x6, 0xff, 0xff,
    0xff, 0xff, 0xfa, 0x0, 0x0, 0x6f, 0xff, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x6, 0xff, 0xff, 0xff,
    0xf7, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xef, 0xf6,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xf4, 0xb5, 0x0,
    0x0, 0x0, 0x0, 0x3b, 0xba,

    /* U+F052 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x37, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff,
    0xff, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xff, 0xff, 0xff, 0xfe, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2e, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x20, 0x0, 0x0, 0x0, 0x0, 0x1d,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x10, 0x0,
    0x0, 0x0, 0xc, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfc, 0x0, 0x0, 0x0, 0xc, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x0, 0x0,
    0xb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfb, 0x0, 0x8, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0xd, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x0, 0x3d, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x0,
    0xb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0xe, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0x0, 0x4b, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0x40,

    /* U+F053 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xff, 0x80, 0x0, 0x0, 0x0, 0xaf,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xfa,
    0x0, 0x0, 0x0, 0xaf, 0xff, 0xfa, 0x0, 0x0,
    0x0, 0xaf, 0xff, 0xfa, 0x0, 0x0, 0x0, 0xaf,
    0xff, 0xfa, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xfa,
    0x0, 0x0, 0x0, 0xaf, 0xff, 0xfa, 0x0, 0x0,
    0x0, 0x8f, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xff, 0xa0, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0xa, 0xff, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0xa, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x0,

    /* U+F054 "" */
    0x0, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0xa, 0xff, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xff, 0xa0, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xff, 0xa0, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x0, 0xaf, 0xff, 0xf8, 0x0, 0x0,
    0x0, 0xaf, 0xff, 0xfa, 0x0, 0x0, 0x0, 0xaf,
    0xff, 0xfa, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xfa,
    0x0, 0x0, 0x0, 0xaf, 0xff, 0xfa, 0x0, 0x0,
    0x0, 0xaf, 0xff, 0xfa, 0x0, 0x0, 0x0, 0xaf,
    0xff, 0xfa, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xfa,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F067 "" */
    0x0, 0x0, 0x0, 0x0, 0x3, 0x43, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xff, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xd1, 0x77, 0x77, 0x77, 0x7d, 0xff, 0xfd, 0x77,
    0x77, 0x77, 0x71, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xff, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xab, 0xa2, 0x0, 0x0, 0x0, 0x0,

    /* U+F068 "" */
    0x16, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77,
    0x77, 0x76, 0x1d, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F06E "" */
    0x0, 0x0, 0x0, 0x0, 0x16, 0xad, 0xef, 0xed,
    0xa6, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xff,
    0xe9, 0x54, 0x59, 0xef, 0xff, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x2d, 0xff, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x9f, 0xff, 0xfd, 0x20, 0x0, 0x0, 0x2e,
    0xff, 0xff, 0x70, 0x0, 0x14, 0x20, 0x0, 0x7f,
    0xff, 0xfe, 0x20, 0x0, 0x1e, 0xff, 0xff, 0xb0,
    0x0, 0x6, 0xff, 0xc2, 0x0, 0xbf, 0xff, 0xfe,
    0x10, 0xb, 0xff, 0xff, 0xf3, 0x0, 0x0, 0x5f,
    0xff, 0xe2, 0x3, 0xff, 0xff, 0xfb, 0x6, 0xff,
    0xff, 0xfe, 0x0, 0x0, 0x1c, 0xff, 0xff, 0xa0,
    0xe, 0xff, 0xff, 0xf5, 0xef, 0xff, 0xff, 0xc0,
    0xd, 0xcf, 0xff, 0xff, 0xfe, 0x0, 0xcf, 0xff,
    0xff, 0xee, 0xff, 0xff, 0xfc, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0xc, 0xff, 0xff, 0xfe, 0x5f,
    0xff, 0xff, 0xe0, 0xc, 0xff, 0xff, 0xff, 0xfc,
    0x0, 0xef, 0xff, 0xff, 0x50, 0xbf, 0xff, 0xff,
    0x30, 0x4f, 0xff, 0xff, 0xff, 0x50, 0x3f, 0xff,
    0xff, 0xb0, 0x1, 0xdf, 0xff, 0xfb, 0x0, 0x7f,
    0xff, 0xff, 0x80, 0xb, 0xff, 0xff, 0xe1, 0x0,
    0x2, 0xef, 0xff, 0xf7, 0x0, 0x39, 0xb9, 0x30,
    0x7, 0xff, 0xff, 0xe2, 0x0, 0x0, 0x2, 0xcf,
    0xff, 0xf9, 0x0, 0x0, 0x0, 0x9, 0xff, 0xff,
    0xd2, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xfe,
    0x95, 0x45, 0x9e, 0xff, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2a, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfb, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0x6a, 0xdf, 0xfe, 0xda, 0x71, 0x0,
    0x0, 0x0, 0x0,

    /* U+F070 "" */
    0x5, 0xe6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xff, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xff, 0xfc, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xfe, 0x40, 0x0, 0x48, 0xce, 0xff, 0xeb,
    0x72, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0xff, 0x88, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xef, 0xff, 0xff, 0xff, 0xb6, 0x45, 0x7d, 0xff,
    0xff, 0xb1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xbf, 0xff, 0xfd, 0x20, 0x0, 0x0, 0x6, 0xff,
    0xff, 0xe4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xff, 0xf6, 0x1, 0x76, 0x20, 0x4, 0xff,
    0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x10, 0x0,
    0x4e, 0xff, 0xfa, 0x1f, 0xff, 0x90, 0x8, 0xff,
    0xff, 0xf3, 0x0, 0x0, 0x0, 0x6e, 0x40, 0x0,
    0x2c, 0xff, 0xfd, 0xff, 0xff, 0x80, 0xf, 0xff,
    0xff, 0xe1, 0x0, 0x0, 0x2f, 0xff, 0x70, 0x0,
    0x9, 0xff, 0xff, 0xff, 0xff, 0x0, 0xbf, 0xff,
    0xff, 0x90, 0x0, 0x9, 0xff, 0xff, 0xb1, 0x0,
    0x6, 0xff, 0xff, 0xff, 0xf3, 0x9, 0xff, 0xff,
    0xff, 0x10, 0x0, 0x9f, 0xff, 0xff, 0xd1, 0x0,
    0x3, 0xdf, 0xff, 0xff, 0x30, 0x9f, 0xff, 0xff,
    0xf1, 0x0, 0x2, 0xff, 0xff, 0xff, 0x40, 0x0,
    0x1, 0xbf, 0xff, 0xf4, 0xb, 0xff, 0xff, 0xf9,
    0x0, 0x0, 0x7, 0xff, 0xff, 0xf9, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0xf8, 0xff, 0xff, 0xfd, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xff, 0xf1, 0x0, 0x0,
    0x0, 0x4e, 0xff, 0xff, 0xff, 0xff, 0x20, 0x0,
    0x0, 0x0, 0xb, 0xff, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x1c, 0xff, 0xff, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xff, 0xc2, 0x0, 0x0,
    0x0, 0x9, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0xff, 0xfb, 0x64, 0x51,
    0x0, 0x5, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x9f, 0xff, 0xff, 0xff, 0xe3,
    0x0, 0x2, 0xdf, 0xff, 0xc1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0x9d, 0xef, 0xfe, 0xb1,
    0x0, 0x0, 0xaf, 0xff, 0xe4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xff, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3e, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1b, 0xc0,

    /* U+F071 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1c, 0xfc,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xff, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0xff, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xff, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1e, 0xff, 0xff,
    0xff, 0xfe, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xff, 0xff, 0xff, 0xff,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xff, 0xa4, 0x44, 0xaf, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff,
    0xf7, 0x0, 0x7, 0xff, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0xff, 0x80,
    0x0, 0x8f, 0xff, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xff, 0xf9, 0x0, 0x9,
    0xff, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xff, 0xff, 0xa0, 0x0, 0xaf, 0xff,
    0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xff, 0xff, 0xfb, 0x0, 0xb, 0xff, 0xff, 0xff,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xff,
    0xff, 0xc0, 0x0, 0xcf, 0xff, 0xff, 0xff, 0x30,
    0x0, 0x0, 0x0, 0xc, 0xff, 0xff, 0xff, 0xfd,
    0x0, 0xd, 0xff, 0xff, 0xff, 0xfc, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x0,
    0xef, 0xff, 0xff, 0xff, 0xfd, 0x10, 0x2d, 0xff,
    0xff, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x7f, 0xff,
    0xff, 0xff, 0xff, 0x60, 0x0, 0x6f, 0xff, 0xff,
    0xff, 0xff, 0x70, 0x0, 0x1f, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0x0, 0x8, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x10, 0xa, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0x26, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0xd,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x0, 0x3d, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0x30,

    /* U+F074 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x69, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xfc, 0x0, 0x9b, 0xbb, 0xbb, 0x30,
    0x0, 0x0, 0x0, 0x3b, 0xbb, 0xff, 0xff, 0xc0,
    0xff, 0xff, 0xff, 0xf3, 0x0, 0x0, 0x2, 0xef,
    0xff, 0xff, 0xff, 0xfb, 0xff, 0xff, 0xff, 0xfe,
    0x20, 0x0, 0x1e, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xff, 0xff, 0xff, 0xff, 0xe1, 0x1, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xe3, 0x0, 0x0, 0x3e, 0xff,
    0xe2, 0xc, 0xff, 0xfe, 0x20, 0xff, 0xfe, 0x30,
    0x0, 0x0, 0x3, 0xff, 0x30, 0xaf, 0xff, 0xf3,
    0x0, 0xff, 0xe3, 0x0, 0x0, 0x0, 0x0, 0x54,
    0x9, 0xff, 0xff, 0x40, 0x0, 0xbe, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0xf9, 0x4, 0x50,
    0x0, 0xbe, 0x30, 0x0, 0x0, 0x0, 0x3, 0xff,
    0xff, 0xa0, 0x3f, 0xf3, 0x0, 0xff, 0xe3, 0x0,
    0x0, 0x0, 0x2e, 0xff, 0xfc, 0x2, 0xef, 0xfe,
    0x30, 0xff, 0xfe, 0x30, 0xff, 0xff, 0xff, 0xff,
    0xd1, 0x1, 0xef, 0xff, 0xff, 0xff, 0xff, 0xe3,
    0xff, 0xff, 0xff, 0xfe, 0x10, 0x0, 0x2e, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xe2,
    0x0, 0x0, 0x3, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0x9b, 0xbb, 0xbb, 0x30, 0x0, 0x0, 0x0, 0x3b,
    0xbb, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x69, 0x0, 0x0,

    /* U+F077 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xff, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xff,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xff, 0xfd, 0xff, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xff, 0xf9, 0xa, 0xff, 0xff, 0x90,
    0x0, 0x0, 0x0, 0xaf, 0xff, 0xf9, 0x0, 0xa,
    0xff, 0xff, 0x90, 0x0, 0x0, 0xaf, 0xff, 0xf9,
    0x0, 0x0, 0xa, 0xff, 0xff, 0x90, 0x0, 0xaf,
    0xff, 0xf9, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff,
    0x90, 0x9f, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xff, 0x79, 0xff, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xff, 0xf8, 0xb, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xfa,
    0x0, 0x2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x0,

    /* U+F078 "" */
    0x0, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x20, 0x0, 0xbf, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xa0, 0x9f, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0x89,
    0xff, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xff, 0xf7, 0xa, 0xff, 0xff, 0x90, 0x0, 0x0,
    0x0, 0xaf, 0xff, 0xf9, 0x0, 0xa, 0xff, 0xff,
    0x90, 0x0, 0x0, 0xaf, 0xff, 0xf9, 0x0, 0x0,
    0xa, 0xff, 0xff, 0x90, 0x0, 0xaf, 0xff, 0xf9,
    0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0x90, 0xaf,
    0xff, 0xf9, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xff, 0xdf, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xff, 0xff, 0xff, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F079 "" */
    0x0, 0x0, 0x3, 0xd9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xef, 0xfa, 0x0, 0x0, 0x34, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x10, 0x0, 0x0, 0x0,
    0x3, 0xef, 0xff, 0xfa, 0x0, 0x7f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0x3,
    0xef, 0xff, 0xff, 0xfa, 0x5, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0x0, 0x0, 0x3, 0xff,
    0xff, 0xff, 0xff, 0xfb, 0x5, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbf, 0xff, 0x10, 0x0, 0x0, 0xef, 0xfe,
    0xdf, 0xfb, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xef, 0xf1, 0x0, 0x0, 0xd, 0xfe, 0x2b,
    0xff, 0x48, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xff, 0x10, 0x0, 0x0, 0x17, 0x20, 0xbf,
    0xf4, 0x5, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xef, 0xf1, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0x70, 0xef, 0xf1,
    0x4f, 0xc0, 0x0, 0x0, 0xb, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0x6e, 0xff, 0x5f,
    0xff, 0x60, 0x0, 0x0, 0xbf, 0xf7, 0x44, 0x44,
    0x44, 0x44, 0x20, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0xd1, 0x0, 0x0, 0xb, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x50, 0xbf, 0xff, 0xff, 0xff, 0xd1,
    0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x0, 0xbf, 0xff, 0xff, 0xd1, 0x0,
    0x0, 0x0, 0x4, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0x60, 0x0, 0xbf, 0xff, 0xd1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xd1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x40, 0x0, 0x0, 0x0,

    /* U+F07B "" */
    0x3d, 0xff, 0xff, 0xff, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd3,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0x3d, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd3,

    /* U+F093 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xff, 0xff, 0xff, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xff, 0xff, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x2, 0x22,
    0x2f, 0xff, 0xff, 0xf2, 0x22, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x25, 0x55, 0x55, 0x52, 0xf, 0xff, 0xff, 0xf0,
    0x25, 0x55, 0x55, 0x52, 0xef, 0xff, 0xff, 0xf9,
    0xc, 0xff, 0xff, 0xc0, 0x9f, 0xff, 0xff, 0xfe,
    0xff, 0xff, 0xff, 0xff, 0x20, 0x1, 0x10, 0x2,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf9, 0x88, 0x88, 0x9f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x71, 0xe7, 0x1e, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xb6, 0xfb, 0x6f, 0xff, 0xbf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,

    /* U+F095 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xea, 0x63,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xff,
    0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xff, 0xff, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xff, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xff, 0xff, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xff, 0xff, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0xff, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xff, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xdf, 0xff, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xff, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x1, 0x40,
    0x0, 0x0, 0x0, 0xaf, 0xff, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x3a, 0xff, 0xa0, 0x0, 0x1, 0xbf,
    0xff, 0xff, 0x70, 0x0, 0x0, 0x5, 0xcf, 0xff,
    0xff, 0x70, 0x3, 0xdf, 0xff, 0xff, 0xa0, 0x0,
    0x0, 0xd, 0xff, 0xff, 0xff, 0xff, 0x6a, 0xff,
    0xff, 0xff, 0xb0, 0x0, 0x0, 0x0, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xe7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xff, 0xfc, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xfe, 0xc9,
    0x62, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F0C4 "" */
    0x0, 0x2, 0x42, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xcf, 0xff, 0xc2, 0x0,
    0x0, 0x0, 0x0, 0x37, 0x73, 0x0, 0x2e, 0xff,
    0xff, 0xfe, 0x20, 0x0, 0x0, 0x8, 0xff, 0xff,
    0x90, 0xaf, 0xff, 0xcf, 0xff, 0xa0, 0x0, 0x0,
    0x9f, 0xff, 0xff, 0xb0, 0xef, 0xf4, 0x4, 0xff,
    0xe0, 0x0, 0x9, 0xff, 0xff, 0xfb, 0x0, 0xff,
    0xf1, 0x1, 0xff, 0xf0, 0x0, 0x9f, 0xff, 0xff,
    0xb0, 0x0, 0xcf, 0xfb, 0x5b, 0xff, 0xc0, 0x9,
    0xff, 0xff, 0xfb, 0x0, 0x0, 0x5f, 0xff, 0xff,
    0xff, 0xf5, 0x9f, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x39, 0xbe, 0xff, 0xff,
    0xff, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xef, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xff, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x4a, 0xff,
    0xff, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0x2,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x0,
    0x0, 0x0, 0x2e, 0xff, 0xff, 0xff, 0xfb, 0xef,
    0xff, 0xff, 0x50, 0x0, 0x0, 0xaf, 0xff, 0xcf,
    0xff, 0xd0, 0x2e, 0xff, 0xff, 0xf5, 0x0, 0x0,
    0xef, 0xf4, 0x4, 0xff, 0xe0, 0x2, 0xef, 0xff,
    0xff, 0x50, 0x0, 0xff, 0xf1, 0x1, 0xff, 0xf0,
    0x0, 0x2e, 0xff, 0xff, 0xf5, 0x0, 0xcf, 0xfb,
    0x5b, 0xff, 0xc0, 0x0, 0x2, 0xef, 0xff, 0xff,
    0x50, 0x5f, 0xff, 0xff, 0xff, 0x50, 0x0, 0x0,
    0x2e, 0xff, 0xff, 0xd0, 0x8, 0xff, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x1, 0xae, 0xea, 0x20, 0x0,
    0x39, 0xb9, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F0C5 "" */
    0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xff, 0xf0,
    0x7a, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff,
    0xff, 0xff, 0x8, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0x8f, 0xfb, 0x0,
    0x0, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0x8,
    0xff, 0xfa, 0x47, 0x88, 0x40, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x48, 0x88, 0x7f, 0xff, 0xf8, 0xf,
    0xff, 0xff, 0xff, 0xff, 0x40, 0x0, 0x0, 0xff,
    0xff, 0x80, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x80, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0xf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x80, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfc, 0x4, 0x78, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x74, 0xff, 0xff, 0xfa, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x0,
    0x0, 0xb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfb, 0x0, 0x0, 0x0,

    /* U+F0C7 "" */
    0x1, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x42,
    0x0, 0x0, 0x6, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0x0, 0x0, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0xf,
    0xff, 0xcb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbd, 0xff,
    0xf8, 0x0, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xff, 0xf8, 0xf, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xf7, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xff, 0xef, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff,
    0x54, 0x44, 0x44, 0x44, 0x44, 0x45, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0xce, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x8, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x0,
    0x0, 0xc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x80, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfa, 0x0, 0x0, 0xa, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0x0,
    0x2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf7, 0x57, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xb1, 0x8b, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0x81,

    /* U+F0C9 "" */
    0x13, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33,
    0x33, 0x33, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x13, 0x33, 0x33, 0x33, 0x33, 0x33,
    0x33, 0x33, 0x33, 0x33, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x57, 0x77, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x5f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0xee, 0xee, 0xee, 0xee,
    0xee, 0xee, 0xee, 0xee, 0xee, 0xeb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x33, 0x33,
    0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x31,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe1,
    0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33,
    0x33, 0x31,

    /* U+F0E0 "" */
    0x3d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xd3, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x2d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xd2, 0x0, 0x9f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x0,
    0xe4, 0x5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x50, 0x4e, 0xff, 0x80, 0x2c, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xc2, 0x8, 0xff,
    0xff, 0xfc, 0x20, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xf9, 0x1, 0xcf, 0xff, 0xff, 0xff, 0xf5, 0x4,
    0xef, 0xff, 0xff, 0xfe, 0x40, 0x5e, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x90, 0x1c, 0xff, 0xff, 0xb1,
    0x9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x20, 0x6d, 0xd6, 0x2, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x6f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xd9, 0x9d, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0x3d, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd3,

    /* U+F0E7 "" */
    0x0, 0x4f, 0xff, 0xff, 0xff, 0xf3, 0x0, 0x0,
    0x0, 0x9, 0xff, 0xff, 0xff, 0xff, 0x60, 0x0,
    0x0, 0x0, 0xbf, 0xff, 0xff, 0xff, 0xf1, 0x0,
    0x0, 0x0, 0xd, 0xff, 0xff, 0xff, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x1f, 0xff, 0xff, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xff, 0xff, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xff, 0xff,
    0xc8, 0x88, 0x87, 0x30, 0x8, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x0, 0xaf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xa0, 0xc, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf2, 0x0, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0x0, 0x0, 0x37,
    0x88, 0x88, 0xaf, 0xff, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xff, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xff, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0x70,
    0x0, 0x0, 0x0, 0x0,

    /* U+F0EA "" */
    0x0, 0x0, 0x3, 0xdf, 0xd3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0x78, 0x88, 0xff, 0xef, 0xf8,
    0x88, 0x74, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff,
    0x90, 0x9f, 0xff, 0xff, 0xf0, 0x0, 0x0, 0xf,
    0xff, 0xff, 0xf9, 0x9, 0xff, 0xff, 0xff, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff,
    0xff, 0xf0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xe5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xff, 0xff, 0xf4, 0x7, 0x88, 0x88,
    0x88, 0x3, 0x30, 0x0, 0xff, 0xff, 0xff, 0x7,
    0xff, 0xff, 0xff, 0xf0, 0x8f, 0x40, 0xf, 0xff,
    0xff, 0xf0, 0x8f, 0xff, 0xff, 0xff, 0x8, 0xff,
    0x40, 0xff, 0xff, 0xff, 0x8, 0xff, 0xff, 0xff,
    0xf0, 0x8f, 0xff, 0x4f, 0xff, 0xff, 0xf0, 0x8f,
    0xff, 0xff, 0xff, 0x8, 0xff, 0xfe, 0xff, 0xff,
    0xff, 0x8, 0xff, 0xff, 0xff, 0xf0, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xf0, 0x8f, 0xff, 0xff, 0xff,
    0xb8, 0x88, 0x88, 0xff, 0xff, 0xff, 0x8, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x8, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x8f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0x78, 0x88, 0x80, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfb,

    /* U+F0F3 "" */
    0x0, 0x0, 0x0, 0x0, 0x8, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x5f, 0xff, 0x51, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf9, 0x0, 0x0, 0x0,
    0x4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3,
    0x0, 0x0, 0x0, 0xaf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x90, 0x0, 0x0, 0xe, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x0, 0x1, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0x0, 0x0, 0x4f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x40,
    0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xd0, 0x0, 0x5f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x50,
    0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x3d, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0xdf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd2,
    0x78, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x72, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xff, 0xe1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xcf, 0xc3,
    0x0, 0x0, 0x0, 0x0,

    /* U+F11C "" */
    0x3d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x3d, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x10, 0x1f, 0x90, 0x9, 0xf1, 0x1,
    0xf9, 0x0, 0x9f, 0x10, 0x1f, 0xff, 0xff, 0xf0,
    0x0, 0xf8, 0x0, 0x8f, 0x0, 0xf, 0x80, 0x8,
    0xf0, 0x0, 0xff, 0xff, 0xff, 0x10, 0x1f, 0x90,
    0x9, 0xf1, 0x1, 0xf9, 0x0, 0x9f, 0x10, 0x1f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xb8, 0x8f, 0xe8, 0x8b, 0xfb, 0x88, 0xfe,
    0x88, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0,
    0xcc, 0x0, 0x4f, 0x40, 0xc, 0xc0, 0x4, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x40, 0xc, 0xc0, 0x4,
    0xf4, 0x0, 0xcc, 0x0, 0x4f, 0xff, 0xff, 0xff,
    0xff, 0xfb, 0x88, 0xfe, 0x88, 0xbf, 0xb8, 0x8f,
    0xe8, 0x8b, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0x1, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xf1, 0x1, 0xff, 0xff,
    0xff, 0x0, 0xf, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0x0, 0xf, 0xff, 0xff, 0xf1, 0x1,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xf1,
    0x1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xd3, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xd3,

    /* U+F124 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3a,
    0xfc, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4b, 0xff, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5c,
    0xff, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6e, 0xff, 0xff, 0xff, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x8e,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x2, 0x9f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x3, 0xaf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0x4, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x6, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x50, 0x0, 0x3, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0x0, 0x0, 0xd,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf6, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x0,
    0x0, 0x9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x57,
    0x88, 0x88, 0x88, 0x8c, 0xff, 0xff, 0xff, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xff, 0xff, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xff, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xd3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F15B "" */
    0xbf, 0xff, 0xff, 0xff, 0xff, 0x80, 0xe4, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0xff,
    0x40, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80,
    0xff, 0xf4, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x80, 0xff, 0xff, 0x40, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x80, 0xff, 0xff, 0xf4, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x80, 0xff, 0xff, 0xfe, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0x88, 0x88, 0x88,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xbf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,

    /* U+F1EB "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0x56, 0x77, 0x65,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x7c, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xc7, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0x50, 0x0, 0x0, 0x0, 0x3, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfd, 0x30, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xff,
    0xc9, 0x65, 0x44, 0x56, 0x9c, 0xff, 0xff, 0xff,
    0xf8, 0x0, 0x1c, 0xff, 0xff, 0xfd, 0x61, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x16, 0xdf, 0xff, 0xff,
    0xc1, 0xdf, 0xff, 0xfe, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xef, 0xff, 0xfd,
    0x9f, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xf9, 0x9,
    0xf6, 0x0, 0x0, 0x0, 0x48, 0xcd, 0xff, 0xdc,
    0x84, 0x0, 0x0, 0x0, 0x6f, 0x90, 0x0, 0x10,
    0x0, 0x0, 0x6e, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe6, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0, 0x0,
    0x3d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xd3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0xff, 0xff, 0xfe, 0xcc, 0xef, 0xff, 0xff, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff,
    0xc5, 0x10, 0x0, 0x1, 0x5c, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xe4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4e, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x22,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1d, 0xff, 0xc1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0xff, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xaa, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F240 "" */
    0x1, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x30, 0x0, 0x6f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x0, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x60, 0xff, 0xfb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbd, 0xff, 0xa1, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xfe, 0xff, 0xf0, 0x8f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x8,
    0xff, 0xff, 0xff, 0xf0, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x5, 0xbf,
    0xff, 0xff, 0xf0, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0xf, 0xff,
    0xff, 0xf0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x0, 0xf, 0xff, 0xff,
    0xf0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x2, 0x4f, 0xff, 0xff, 0xf0,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x8, 0xff, 0xff, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xff, 0xff, 0xf4, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x4a, 0xff, 0xd7, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x70, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x30, 0x18, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xa4, 0x0,

    /* U+F241 "" */
    0x1, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x30, 0x0, 0x6f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x0, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x60, 0xff, 0xfb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbd, 0xff, 0xa1, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xfe, 0xff, 0xf0, 0xbf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x8,
    0xff, 0xff, 0xff, 0xf0, 0xbf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x5, 0xbf,
    0xff, 0xff, 0xf0, 0xbf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xff, 0xf0, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff,
    0xf0, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf4, 0x0, 0x0, 0x2, 0x4f, 0xff, 0xff, 0xf0,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xff, 0xff, 0xf4, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x4a, 0xff, 0xd7, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x70, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x30, 0x18, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xa4, 0x0,

    /* U+F242 "" */
    0x1, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x30, 0x0, 0x6f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x0, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x60, 0xff, 0xfb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbd, 0xff, 0xa1, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xfe, 0xff, 0xf0, 0x8f, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xff, 0xff, 0xf0, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xbf,
    0xff, 0xff, 0xf0, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xff, 0xf0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff,
    0xf0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0x4f, 0xff, 0xff, 0xf0,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xff, 0xff, 0xf4, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x4a, 0xff, 0xd7, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x70, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x30, 0x18, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xa4, 0x0,

    /* U+F243 "" */
    0x1, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x30, 0x0, 0x6f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x0, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x60, 0xff, 0xfb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbd, 0xff, 0xa1, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xfe, 0xff, 0xf0, 0xbf, 0xff, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xff, 0xff, 0xf0, 0xbf, 0xff, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xbf,
    0xff, 0xff, 0xf0, 0xbf, 0xff, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xff, 0xf0, 0xbf, 0xff, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff,
    0xf0, 0xbf, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0x4f, 0xff, 0xff, 0xf0,
    0xbf, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xff, 0xff, 0xf4, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x4a, 0xff, 0xd7, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x70, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x30, 0x18, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xa4, 0x0,

    /* U+F244 "" */
    0x1, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x30, 0x0, 0x6f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x0, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x60, 0xff, 0xfb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbd, 0xff, 0xa1, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xfe, 0xff, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xbf,
    0xff, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0x4f, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xff, 0xff, 0xf4, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x4a, 0xff, 0xd7, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x70, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x30, 0x18, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xa4, 0x0,

    /* U+F287 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2d,
    0xfe, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x27, 0x7d, 0xff,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe, 0xf5, 0x3a, 0xff, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xf6, 0x0, 0x9, 0xda, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x0,
    0x0, 0x0, 0xed, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xfe, 0x50,
    0x0, 0x6f, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa4, 0x0, 0x0, 0x8f, 0xff, 0xff, 0x30,
    0x1e, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xfa, 0x10, 0xe, 0xff, 0xff, 0xfd, 0xbe,
    0xfe, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xff, 0xff, 0x60, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfb, 0xa, 0xff, 0xff, 0xf5, 0x0, 0x0,
    0x1c, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xd4, 0x0, 0x1c, 0xff, 0xf9, 0x0, 0x0, 0x0,
    0x2f, 0xa0, 0x0, 0x0, 0x0, 0x0, 0xe, 0x70,
    0x0, 0x0, 0x4, 0x63, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xfb, 0x0, 0x8f, 0xff, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xf8, 0x4b, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xff, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0x5c, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x11, 0x10, 0x0, 0x0, 0x0, 0x0,

    /* U+F293 "" */
    0x0, 0x0, 0x2, 0x8c, 0xef, 0xfe, 0xa6, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0x30, 0x0, 0x0, 0xc, 0xff, 0xff, 0xf4,
    0xff, 0xff, 0xff, 0x40, 0x0, 0xa, 0xff, 0xff,
    0xff, 0x5, 0xff, 0xff, 0xfe, 0x10, 0x2, 0xff,
    0xff, 0xff, 0xf0, 0x5, 0xff, 0xff, 0xf8, 0x0,
    0x9f, 0xff, 0xff, 0xff, 0x0, 0x6, 0xff, 0xff,
    0xe0, 0xe, 0xff, 0xfc, 0xff, 0xf0, 0x16, 0x7,
    0xff, 0xff, 0x21, 0xff, 0xf8, 0x9, 0xff, 0x1,
    0xf6, 0x7, 0xff, 0xf5, 0x4f, 0xff, 0xd1, 0x9,
    0xf0, 0x1f, 0x70, 0x6f, 0xff, 0x86, 0xff, 0xff,
    0xd1, 0x7, 0x1, 0x70, 0x5f, 0xff, 0xf9, 0x7f,
    0xff, 0xff, 0xd1, 0x0, 0x0, 0x3f, 0xff, 0xff,
    0xa7, 0xff, 0xff, 0xff, 0xc1, 0x0, 0x2e, 0xff,
    0xff, 0xfb, 0x7f, 0xff, 0xff, 0xff, 0x30, 0x5,
    0xff, 0xff, 0xff, 0xb7, 0xff, 0xff, 0xff, 0x40,
    0x0, 0x6, 0xff, 0xff, 0xfa, 0x6f, 0xff, 0xff,
    0x40, 0x30, 0x3, 0x7, 0xff, 0xff, 0xa4, 0xff,
    0xff, 0x40, 0x4e, 0x1, 0xe2, 0x8, 0xff, 0xf8,
    0x2f, 0xff, 0x70, 0x4f, 0xf0, 0x1f, 0x90, 0x2f,
    0xff, 0x60, 0xef, 0xff, 0x7f, 0xff, 0x1, 0xb0,
    0x2e, 0xff, 0xf3, 0xa, 0xff, 0xff, 0xff, 0xf0,
    0x0, 0x2e, 0xff, 0xff, 0x0, 0x4f, 0xff, 0xff,
    0xff, 0x0, 0x2e, 0xff, 0xff, 0x90, 0x0, 0xbf,
    0xff, 0xff, 0xf0, 0x2e, 0xff, 0xff, 0xf2, 0x0,
    0x1, 0xdf, 0xff, 0xff, 0x3e, 0xff, 0xff, 0xf7,
    0x0, 0x0, 0x0, 0xaf, 0xff, 0xff, 0xff, 0xff,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x28, 0xce, 0xff,
    0xec, 0x82, 0x0, 0x0,

    /* U+F2ED "" */
    0x0, 0x0, 0x0, 0x1a, 0xcc, 0xcc, 0xca, 0x10,
    0x0, 0x0, 0x2, 0x44, 0x44, 0x49, 0xff, 0xff,
    0xff, 0xf9, 0x44, 0x44, 0x42, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x9b, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80, 0x8, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x8f,
    0xff, 0x66, 0xff, 0xd1, 0xdf, 0xf6, 0x6f, 0xff,
    0x80, 0x8, 0xff, 0xf4, 0x4f, 0xfc, 0xc, 0xff,
    0x44, 0xff, 0xf8, 0x0, 0x8f, 0xff, 0x44, 0xff,
    0xc0, 0xcf, 0xf4, 0x4f, 0xff, 0x80, 0x8, 0xff,
    0xf4, 0x4f, 0xfc, 0xc, 0xff, 0x44, 0xff, 0xf8,
    0x0, 0x8f, 0xff, 0x44, 0xff, 0xc0, 0xcf, 0xf4,
    0x4f, 0xff, 0x80, 0x8, 0xff, 0xf4, 0x4f, 0xfc,
    0xc, 0xff, 0x44, 0xff, 0xf8, 0x0, 0x8f, 0xff,
    0x44, 0xff, 0xc0, 0xcf, 0xf4, 0x4f, 0xff, 0x80,
    0x8, 0xff, 0xf4, 0x4f, 0xfc, 0xc, 0xff, 0x44,
    0xff, 0xf8, 0x0, 0x8f, 0xff, 0x44, 0xff, 0xc0,
    0xcf, 0xf4, 0x4f, 0xff, 0x80, 0x8, 0xff, 0xf4,
    0x4f, 0xfc, 0xc, 0xff, 0x44, 0xff, 0xf8, 0x0,
    0x8f, 0xff, 0x44, 0xff, 0xc0, 0xcf, 0xf4, 0x4f,
    0xff, 0x80, 0x8, 0xff, 0xf6, 0x6f, 0xfd, 0x1d,
    0xff, 0x66, 0xff, 0xf8, 0x0, 0x7f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x5,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0x0, 0x9, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf9, 0x0,

    /* U+F304 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6e, 0xe5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xff,
    0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x50, 0x3f, 0xff, 0xff, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xb0, 0x3f,
    0xff, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0xb0, 0x3f, 0xff, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xff,
    0xb0, 0x3f, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0xff, 0xff, 0xb0, 0x3f, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xff,
    0xff, 0xff, 0xb0, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff,
    0xff, 0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xff, 0xff, 0xff, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0xff, 0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xdb, 0xa8, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F55A "" */
    0x0, 0x0, 0x0, 0x2, 0xbf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x80, 0x0,
    0x0, 0x0, 0x3e, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x0,
    0x3, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0x0, 0x0, 0x3e,
    0xff, 0xff, 0xff, 0xff, 0xcf, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xff, 0x0, 0x3, 0xef, 0xff,
    0xff, 0xff, 0xf7, 0x6, 0xff, 0xff, 0x60, 0x8f,
    0xff, 0xff, 0xff, 0x0, 0x3e, 0xff, 0xff, 0xff,
    0xff, 0xc0, 0x0, 0x6f, 0xf6, 0x0, 0xc, 0xff,
    0xff, 0xff, 0x3, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xf6, 0x0, 0x6, 0x60, 0x0, 0x6f, 0xff, 0xff,
    0xff, 0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x60, 0x0, 0x0, 0x6, 0xff, 0xff, 0xff, 0xff,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0,
    0x0, 0x6f, 0xff, 0xff, 0xff, 0xff, 0x3f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x60, 0x0, 0x0,
    0x6, 0xff, 0xff, 0xff, 0xff, 0x3, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf6, 0x0, 0x6, 0x60, 0x0,
    0x6f, 0xff, 0xff, 0xff, 0x0, 0x3e, 0xff, 0xff,
    0xff, 0xff, 0xc0, 0x0, 0x6f, 0xf6, 0x0, 0xc,
    0xff, 0xff, 0xff, 0x0, 0x3, 0xef, 0xff, 0xff,
    0xff, 0xf8, 0x6, 0xff, 0xff, 0x60, 0x7f, 0xff,
    0xff, 0xff, 0x0, 0x0, 0x3e, 0xff, 0xff, 0xff,
    0xff, 0xcf, 0xff, 0xff, 0xfc, 0xff, 0xff, 0xff,
    0xff, 0x0, 0x0, 0x3, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x0, 0x0, 0x0, 0x3e, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0x0, 0x2, 0xbf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0x80,

    /* U+F7C2 "" */
    0x0, 0x0, 0x7, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x80, 0x0, 0x0, 0x7f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0x0, 0x7, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0x0, 0x7f, 0xf4, 0x8, 0xf0,
    0xe, 0x90, 0xf, 0xff, 0x7, 0xff, 0xf4, 0x8,
    0xf0, 0xe, 0x90, 0xf, 0xff, 0x7f, 0xff, 0xf4,
    0x8, 0xf0, 0xe, 0x90, 0xf, 0xff, 0xff, 0xff,
    0xf4, 0x8, 0xf0, 0xe, 0x90, 0xf, 0xff, 0xff,
    0xff, 0xfa, 0x8c, 0xf8, 0x8f, 0xc8, 0x8f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x8f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x8,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x80,

    /* U+F8A2 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0x10, 0x0, 0x0,
    0x3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0xf1, 0x0, 0x0, 0x1c, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0x10, 0x0,
    0x1d, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xf1, 0x0, 0x2d, 0xff, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0x10,
    0x2e, 0xff, 0xff, 0xa2, 0x22, 0x22, 0x22, 0x22,
    0x22, 0x2b, 0xff, 0xf1, 0x3e, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x1d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0x8f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x0, 0x8f, 0xff, 0xff, 0xd9, 0x99, 0x99,
    0x99, 0x99, 0x99, 0x99, 0x99, 0x60, 0x0, 0x7f,
    0xff, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4a, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 94, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 99, .box_w = 4, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 42, .adv_w = 177, .box_w = 7, .box_h = 8, .ofs_x = 2, .ofs_y = 13},
    {.bitmap_index = 70, .adv_w = 242, .box_w = 12, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 196, .adv_w = 165, .box_w = 8, .box_h = 24, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 292, .adv_w = 408, .box_w = 23, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 534, .adv_w = 184, .box_w = 12, .box_h = 21, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 660, .adv_w = 77, .box_w = 3, .box_h = 8, .ofs_x = 1, .ofs_y = 13},
    {.bitmap_index = 672, .adv_w = 106, .box_w = 5, .box_h = 23, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 730, .adv_w = 106, .box_w = 5, .box_h = 23, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 788, .adv_w = 180, .box_w = 9, .box_h = 9, .ofs_x = 1, .ofs_y = 12},
    {.bitmap_index = 829, .adv_w = 152, .box_w = 8, .box_h = 8, .ofs_x = 1, .ofs_y = 6},
    {.bitmap_index = 861, .adv_w = 87, .box_w = 4, .box_h = 7, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 875, .adv_w = 131, .box_w = 6, .box_h = 2, .ofs_x = 1, .ofs_y = 9},
    {.bitmap_index = 881, .adv_w = 100, .box_w = 3, .box_h = 3, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 886, .adv_w = 147, .box_w = 8, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 970, .adv_w = 172, .box_w = 9, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1065, .adv_w = 121, .box_w = 6, .box_h = 21, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1128, .adv_w = 167, .box_w = 9, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1223, .adv_w = 170, .box_w = 9, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1318, .adv_w = 167, .box_w = 9, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1413, .adv_w = 167, .box_w = 8, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1497, .adv_w = 167, .box_w = 9, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1592, .adv_w = 167, .box_w = 9, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1687, .adv_w = 167, .box_w = 9, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1782, .adv_w = 167, .box_w = 9, .box_h = 21, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1877, .adv_w = 104, .box_w = 4, .box_h = 12, .ofs_x = 1, .ofs_y = 3},
    {.bitmap_index = 1901, .adv_w = 105, .box_w = 5, .box_h = 17, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 1944, .adv_w = 137, .box_w = 8, .box_h = 12, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 1992, .adv_w = 135, .box_w = 8, .box_h = 6, .ofs_x = 0, .ofs_y = 7},
    {.bitmap_index = 2016, .adv_w = 137, .box_w = 7, .box_h = 12, .ofs_x = 1, .ofs_y = 3},
    {.bitmap_index = 2058, .adv_w = 150, .box_w = 8, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2142, .adv_w = 264, .box_w = 15, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2300, .adv_w = 176, .box_w = 11, .box_h = 21, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2416, .adv_w = 179, .box_w = 9, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2511, .adv_w = 172, .box_w = 9, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2606, .adv_w = 178, .box_w = 9, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2701, .adv_w = 145, .box_w = 7, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2775, .adv_w = 140, .box_w = 7, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2849, .adv_w = 177, .box_w = 9, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2944, .adv_w = 185, .box_w = 9, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3039, .adv_w = 99, .box_w = 4, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3081, .adv_w = 161, .box_w = 9, .box_h = 21, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3176, .adv_w = 168, .box_w = 10, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3281, .adv_w = 134, .box_w = 7, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3355, .adv_w = 243, .box_w = 13, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3492, .adv_w = 190, .box_w = 10, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3597, .adv_w = 177, .box_w = 9, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3692, .adv_w = 167, .box_w = 9, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3787, .adv_w = 177, .box_w = 9, .box_h = 25, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 3900, .adv_w = 179, .box_w = 9, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3995, .adv_w = 164, .box_w = 9, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4090, .adv_w = 119, .box_w = 8, .box_h = 21, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4174, .adv_w = 181, .box_w = 9, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4269, .adv_w = 173, .box_w = 11, .box_h = 21, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4385, .adv_w = 255, .box_w = 16, .box_h = 21, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4553, .adv_w = 153, .box_w = 9, .box_h = 21, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4648, .adv_w = 157, .box_w = 10, .box_h = 21, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4753, .adv_w = 143, .box_w = 8, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4837, .adv_w = 143, .box_w = 6, .box_h = 21, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 4900, .adv_w = 163, .box_w = 8, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4984, .adv_w = 143, .box_w = 6, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5047, .adv_w = 193, .box_w = 10, .box_h = 11, .ofs_x = 1, .ofs_y = 7},
    {.bitmap_index = 5102, .adv_w = 140, .box_w = 9, .box_h = 2, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5111, .adv_w = 122, .box_w = 5, .box_h = 5, .ofs_x = 1, .ofs_y = 4},
    {.bitmap_index = 5124, .adv_w = 174, .box_w = 9, .box_h = 18, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5205, .adv_w = 176, .box_w = 9, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5300, .adv_w = 165, .box_w = 9, .box_h = 18, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5381, .adv_w = 176, .box_w = 9, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5476, .adv_w = 167, .box_w = 9, .box_h = 18, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5557, .adv_w = 117, .box_w = 7, .box_h = 21, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5631, .adv_w = 174, .box_w = 9, .box_h = 21, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 5726, .adv_w = 182, .box_w = 9, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5821, .adv_w = 94, .box_w = 4, .box_h = 22, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5865, .adv_w = 100, .box_w = 5, .box_h = 24, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 5925, .adv_w = 162, .box_w = 10, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 6030, .adv_w = 94, .box_w = 4, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 6072, .adv_w = 269, .box_w = 15, .box_h = 18, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 6207, .adv_w = 182, .box_w = 9, .box_h = 18, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 6288, .adv_w = 171, .box_w = 9, .box_h = 18, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 6369, .adv_w = 176, .box_w = 9, .box_h = 21, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 6464, .adv_w = 175, .box_w = 9, .box_h = 21, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 6559, .adv_w = 122, .box_w = 7, .box_h = 18, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 6622, .adv_w = 151, .box_w = 8, .box_h = 18, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 6694, .adv_w = 115, .box_w = 7, .box_h = 21, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6768, .adv_w = 182, .box_w = 9, .box_h = 18, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 6849, .adv_w = 141, .box_w = 9, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6930, .adv_w = 218, .box_w = 13, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7047, .adv_w = 139, .box_w = 9, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7128, .adv_w = 148, .box_w = 9, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 7223, .adv_w = 128, .box_w = 8, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7295, .adv_w = 150, .box_w = 7, .box_h = 23, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 7376, .adv_w = 101, .box_w = 3, .box_h = 24, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 7412, .adv_w = 150, .box_w = 7, .box_h = 23, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 7493, .adv_w = 209, .box_w = 11, .box_h = 5, .ofs_x = 1, .ofs_y = 9},
    {.bitmap_index = 7521, .adv_w = 384, .box_w = 24, .box_h = 25, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 7821, .adv_w = 384, .box_w = 24, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8037, .adv_w = 384, .box_w = 24, .box_h = 22, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 8301, .adv_w = 384, .box_w = 24, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8517, .adv_w = 264, .box_w = 17, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8670, .adv_w = 384, .box_w = 24, .box_h = 24, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 8958, .adv_w = 384, .box_w = 24, .box_h = 24, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 9246, .adv_w = 432, .box_w = 27, .box_h = 22, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 9543, .adv_w = 384, .box_w = 24, .box_h = 24, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 9831, .adv_w = 432, .box_w = 27, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 10074, .adv_w = 384, .box_w = 24, .box_h = 26, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 10386, .adv_w = 192, .box_w = 12, .box_h = 19, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 10500, .adv_w = 288, .box_w = 18, .box_h = 19, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 10671, .adv_w = 432, .box_w = 27, .box_h = 24, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 10995, .adv_w = 384, .box_w = 24, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 11211, .adv_w = 264, .box_w = 17, .box_h = 24, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 11415, .adv_w = 336, .box_w = 15, .box_h = 22, .ofs_x = 3, .ofs_y = -2},
    {.bitmap_index = 11580, .adv_w = 336, .box_w = 21, .box_h = 26, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 11853, .adv_w = 336, .box_w = 21, .box_h = 22, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 12084, .adv_w = 336, .box_w = 21, .box_h = 22, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 12315, .adv_w = 336, .box_w = 15, .box_h = 22, .ofs_x = 3, .ofs_y = -2},
    {.bitmap_index = 12480, .adv_w = 336, .box_w = 23, .box_h = 22, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 12733, .adv_w = 240, .box_w = 13, .box_h = 22, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 12876, .adv_w = 240, .box_w = 13, .box_h = 22, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 13019, .adv_w = 336, .box_w = 21, .box_h = 22, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 13250, .adv_w = 336, .box_w = 21, .box_h = 6, .ofs_x = 0, .ofs_y = 6},
    {.bitmap_index = 13313, .adv_w = 432, .box_w = 27, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 13556, .adv_w = 480, .box_w = 31, .box_h = 24, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 13928, .adv_w = 432, .box_w = 29, .box_h = 24, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 14276, .adv_w = 384, .box_w = 24, .box_h = 22, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 14540, .adv_w = 336, .box_w = 21, .box_h = 14, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 14687, .adv_w = 336, .box_w = 21, .box_h = 14, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 14834, .adv_w = 480, .box_w = 31, .box_h = 19, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 15129, .adv_w = 384, .box_w = 24, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 15345, .adv_w = 384, .box_w = 24, .box_h = 24, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 15633, .adv_w = 384, .box_w = 25, .box_h = 25, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 15946, .adv_w = 336, .box_w = 22, .box_h = 22, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 16188, .adv_w = 336, .box_w = 21, .box_h = 24, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 16440, .adv_w = 336, .box_w = 21, .box_h = 22, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 16671, .adv_w = 336, .box_w = 21, .box_h = 20, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 16881, .adv_w = 384, .box_w = 24, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 17097, .adv_w = 240, .box_w = 17, .box_h = 24, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 17301, .adv_w = 336, .box_w = 21, .box_h = 24, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 17553, .adv_w = 336, .box_w = 21, .box_h = 24, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 17805, .adv_w = 432, .box_w = 27, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 18048, .adv_w = 384, .box_w = 26, .box_h = 26, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 18386, .adv_w = 288, .box_w = 18, .box_h = 24, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 18602, .adv_w = 480, .box_w = 30, .box_h = 23, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 18947, .adv_w = 480, .box_w = 30, .box_h = 16, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 19187, .adv_w = 480, .box_w = 30, .box_h = 16, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 19427, .adv_w = 480, .box_w = 30, .box_h = 16, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 19667, .adv_w = 480, .box_w = 30, .box_h = 16, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 19907, .adv_w = 480, .box_w = 30, .box_h = 16, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 20147, .adv_w = 480, .box_w = 31, .box_h = 20, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 20457, .adv_w = 336, .box_w = 19, .box_h = 24, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 20685, .adv_w = 336, .box_w = 21, .box_h = 24, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 20937, .adv_w = 384, .box_w = 25, .box_h = 25, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 21250, .adv_w = 480, .box_w = 30, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 21520, .adv_w = 288, .box_w = 18, .box_h = 24, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 21736, .adv_w = 386, .box_w = 25, .box_h = 16, .ofs_x = 0, .ofs_y = 1}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_1[] = {
    0x0, 0x7, 0xa, 0xb, 0xc, 0x10, 0x12, 0x14,
    0x18, 0x1b, 0x20, 0x25, 0x26, 0x27, 0x3d, 0x42,
    0x47, 0x4a, 0x4b, 0x4c, 0x50, 0x51, 0x52, 0x53,
    0x66, 0x67, 0x6d, 0x6f, 0x70, 0x73, 0x76, 0x77,
    0x78, 0x7a, 0x92, 0x94, 0xc3, 0xc4, 0xc6, 0xc8,
    0xdf, 0xe6, 0xe9, 0xf2, 0x11b, 0x123, 0x15a, 0x1ea,
    0x23f, 0x240, 0x241, 0x242, 0x243, 0x286, 0x292, 0x2ec,
    0x303, 0x559, 0x7c1, 0x8a1
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 61441, .range_length = 2210, .glyph_id_start = 96,
        .unicode_list = unicode_list_1, .glyph_id_ofs_list = NULL, .list_length = 60, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/


/*Map glyph_ids to kern left classes*/
static const uint8_t kern_left_class_mapping[] =
{
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 1, 2, 3, 4, 0, 5,
    6, 0, 0, 7, 8, 9, 0, 0,
    10, 11, 0, 12, 0, 0, 0, 13,
    14, 0, 15, 0, 0, 0, 0, 0,
    0, 0, 0, 16, 17, 0, 0, 0,
    0, 0, 0, 0, 18, 0, 0, 0,
    19, 0, 0, 20, 0, 0, 0, 21,
    22, 0, 23, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0
};

/*Map glyph_ids to kern right classes*/
static const uint8_t kern_right_class_mapping[] =
{
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 1, 0, 2,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 3, 0, 4, 0, 0, 0,
    5, 0, 0, 6, 0, 0, 0, 0,
    7, 0, 8, 0, 9, 10, 11, 12,
    13, 14, 15, 0, 0, 0, 0, 0,
    0, 0, 16, 0, 17, 0, 18, 0,
    19, 0, 0, 0, 0, 0, 0, 0,
    20, 0, 0, 0, 21, 0, 22, 23,
    24, 25, 26, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0
};

/*Kern values between classes*/
static const int8_t kern_class_values[] =
{
    0, 0, 0, -4, -4, 0, -4, 0,
    0, -5, -3, -8, -10, 0, -8, 0,
    0, 0, 0, 0, 0, -1, -3, -2,
    0, -4, -12, -16, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -2, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -12, -13,
    -2, 0, 0, 0, 0, 0, 0, 0,
    0, -2, -2, 0, -2, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -19, -22, -6, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -1, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -4, -12, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -1, -1, 0, -1, 0, -1, 0,
    0, 0, 0, 0, 0, -2, 0, 0,
    0, -1, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -3, -3, 0, -4, -4,
    -2, -2, 0, -6, -6, 0, -8, 0,
    0, 0, 0, 0, 0, 0, 0, -1,
    0, -2, -4, -6, -1, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -2,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -43, -48, -6, 0,
    0, -8, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -1, -1, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -15, -13, -8, -1, -1, -7, -1, 0,
    0, 0, 0, 0, 0, 0, 0, -2,
    0, -2, -1, -2, 0, 0, 0, 0,
    0, 0, 0, -7, -7, 0, 0, 0,
    -2, 0, 0, 0, 0, 0, 0, 0,
    0, -1, -1, -1, -1, 0, 0, 0,
    0, 0, 0, 0, -2, -1, -7, 0,
    0, 0, -1, 0, 0, 0, 0, 0,
    0, 0, 0, -2, 0, 0, 0, 0,
    -2, 0, 0, 0, 0, 0, -6, -7,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -7, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -1, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -1, 0, 0, 0, 0,
    0, 0, 0, 0, -1, 0, -1, -15,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -1, 0, 0,
    -2, -1, 0, 0, 0, 0, 0, 0,
    -4, -9, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -4, -13, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -1, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -9, -16, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -1,
    0, 0, 0, 0, 0, 0
};


/*Collect the kern class' data in one place*/
static const lv_font_fmt_txt_kern_classes_t kern_classes =
{
    .class_pair_values   = kern_class_values,
    .left_class_mapping  = kern_left_class_mapping,
    .right_class_mapping = kern_right_class_mapping,
    .left_class_cnt      = 23,
    .right_class_cnt     = 26,
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LVGL_VERSION_MAJOR == 8
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
#endif

#if LVGL_VERSION_MAJOR >= 8
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_classes,
    .kern_scale = 16,
    .cmap_num = 2,
    .bpp = 4,
    .kern_classes = 1,
    .bitmap_format = 0,
#if LVGL_VERSION_MAJOR == 8
    .cache = &cache
#endif
};



/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LVGL_VERSION_MAJOR >= 8
const lv_font_t lv_font_Antonio_Regular_24 = {
#else
lv_font_t lv_font_Antonio_Regular_24 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 24,          /*The maximum line height required by the font*/
    .base_line = 3,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = 0,
    .underline_thickness = 0,
#endif
    .dsc = &font_dsc,          /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
#if LV_VERSION_CHECK(8, 2, 0) || LVGL_VERSION_MAJOR >= 9
    .fallback = NULL,
#endif
    .user_data = NULL,
};



#endif /*#if LV_FONT_ANTONIO_REGULAR_24*/

