#include "events_init.h"
#include <stdio.h>
#include <string.h>
#include "lvgl.h"
#include "ui_user_inc.h"
#include "esp_log.h"
#include "hal_wifi.h"
#if LV_USE_GUIDER_SIMULATOR && LV_USE_FREEMASTER
#include "freemaster_client.h"
#endif

static const char *TAG = "WIFI_PAGE";

// WiFi服务状态
static bool wifi_service_started = false;
static bool wifi_connected = false;
static char current_ip[32] = "0.0.0.0";
static lv_timer_t *wifi_status_timer = NULL;

// 更新WiFi显示信息
static void update_wifi_display(lv_ui *ui)
{
    if (!ui || !ui->WIFI_page_label_ip || !ui->WIFI_page_qrcode_ip) {
        return;
    }

    // 获取当前连接状态和IP地址
    bool is_connected = hal_wifi_get_connect_state();
    char *ip = hal_wifi_get_ip();

    if (is_connected && ip != NULL) {
        // WiFi已连接且有IP地址
        bool need_update = false;

        // 检查是否需要更新IP显示
        if (strcmp(ip, current_ip) != 0 || strcmp(current_ip, "Not Connected") == 0) {
            strncpy(current_ip, ip, sizeof(current_ip) - 1);
            current_ip[sizeof(current_ip) - 1] = '\0';
            need_update = true;
        }

        // 检查UI标签是否需要更新
        if (ui->WIFI_page_label_ip) {
            const char *current_label_text = lv_label_get_text(ui->WIFI_page_label_ip);
            if (current_label_text == NULL || strcmp(current_label_text, current_ip) != 0) {
                need_update = true;
            }
        }

        if (need_update) {
            // 更新IP标签
            if (ui->WIFI_page_label_ip) {
                lv_label_set_text(ui->WIFI_page_label_ip, current_ip);
            }

            // 更新二维码显示连接状态
            if (ui->WIFI_page_qrcode_ip) {
                char qr_data[64];
                snprintf(qr_data, sizeof(qr_data), "STA Connected: %s", current_ip);
                lv_qrcode_update(ui->WIFI_page_qrcode_ip, qr_data, strlen(qr_data));
            }

            ESP_LOGI(TAG, "WiFi STA IP updated: %s", current_ip);
        }
    } else if (!is_connected && strcmp(current_ip, "Not Connected") != 0) {
        // 连接断开，重置显示
        strcpy(current_ip, "Not Connected");

        if (ui->WIFI_page_label_ip) {
            lv_label_set_text(ui->WIFI_page_label_ip, current_ip);
        }

        // 重置二维码为默认内容
        if (ui->WIFI_page_qrcode_ip) {
            const char *default_qr = "WiFi STA not connected";
            lv_qrcode_update(ui->WIFI_page_qrcode_ip, default_qr, strlen(default_qr));
        }

        ESP_LOGI(TAG, "WiFi STA disconnected, display reset");
    }
}

// 更新WiFi状态标签
static void update_wifi_status_label(lv_ui *ui)
{
    if (!ui || !ui->WIFI_page_label_state) {
        return;
    }

    if (!wifi_service_started) {
        lv_label_set_text(ui->WIFI_page_label_state, "Click button to start WiFi");
    } else {
        // 检查是否连接到WiFi网络（仅STA模式）
        if (hal_wifi_get_connect_state()) {
            lv_label_set_text(ui->WIFI_page_label_state, "WiFi connected successfully");
        } else {
            lv_label_set_text(ui->WIFI_page_label_state, "WiFi connecting...");
        }
    }
}

// WiFi状态检查定时器回调
static void wifi_status_timer_cb(lv_timer_t *timer)
{
    lv_ui *ui = (lv_ui *)timer->user_data;
    if (!ui) {
        return;
    }

    // 检查连接状态和IP地址变化
    bool is_connected = hal_wifi_get_connect_state();
    char *ip = hal_wifi_get_ip();
    bool new_connected = is_connected || (ip != NULL && strcmp(ip, "0.0.0.0") != 0);

    if (new_connected != wifi_connected) {
        wifi_connected = new_connected;
        ESP_LOGI(TAG, "WiFi status changed: %s (IP: %s)",
                 wifi_connected ? "ready" : "not ready",
                 ip ? ip : "none");

        // 更新状态标签
        update_wifi_status_label(ui);
    }

    // 更新显示信息
    update_wifi_display(ui);
}

// WiFi服务按钮事件处理
static void wifi_service_btn_event_handler(lv_event_t *e)
{
    lv_event_code_t code = lv_event_get_code(e);
    lv_ui *ui = (lv_ui *)lv_event_get_user_data(e);

    switch (code) {
    case LV_EVENT_CLICKED:
    {
        ESP_LOGI(TAG, "WiFi service button clicked");

        if (!wifi_service_started) {
            // 启动WiFi服务
            ESP_LOGI(TAG, "Starting WiFi service...");

            // 先初始化WiFi基础设施
            hal_wifi_init();

            // 然后启动WiFi服务
            hal_wifi_start();
            wifi_service_started = true;

            // 更新按钮文本
            if (ui && ui->WIFI_page_btn_sw_label) {
                lv_label_set_text(ui->WIFI_page_btn_sw_label, "Running");
            }

            // 启动状态检查定时器
            if (wifi_status_timer == NULL) {
                wifi_status_timer = lv_timer_create(wifi_status_timer_cb, 2000, ui);
                ESP_LOGI(TAG, "WiFi status timer started");
            }

            // 更新状态标签
            update_wifi_status_label(ui);

        } else {
            ESP_LOGI(TAG, "WiFi service already started");
        }
        break;
    }
    default:
        break;
    }
}

void events_init_WIFI_page (lv_ui *ui)
{
    ESP_LOGI(TAG, "Initializing WiFi page events");

    // 注册返回按钮事件
    lv_obj_add_event_cb(ui->WIFI_page_btn_back, back_to_menu_event_handler, LV_EVENT_ALL, ui);

    // 注册WiFi服务按钮事件
    lv_obj_add_event_cb(ui->WIFI_page_btn_sw, wifi_service_btn_event_handler, LV_EVENT_ALL, ui);

    // 检查当前WiFi实际状态，而不是重置为初始值
    // 首先检查WiFi连接状态
    bool is_connected = hal_wifi_get_connect_state();
    char *current_ip_addr = hal_wifi_get_ip();

    // 如果连接状态为true或者能获取到有效IP，说明WiFi已连接
    if (is_connected || (current_ip_addr != NULL && strcmp(current_ip_addr, "0.0.0.0") != 0)) {
        // WiFi服务已经在运行并连接
        wifi_service_started = true;
        wifi_connected = true;

        if (current_ip_addr != NULL) {
            strncpy(current_ip, current_ip_addr, sizeof(current_ip) - 1);
            current_ip[sizeof(current_ip) - 1] = '\0';
        } else {
            strcpy(current_ip, "Connected");
        }

        // 更新按钮文本
        if (ui && ui->WIFI_page_btn_sw_label) {
            lv_label_set_text(ui->WIFI_page_btn_sw_label, "Running");
        }

        ESP_LOGI(TAG, "WiFi service already running, connected: %s, IP: %s",
                 is_connected ? "true" : "false", current_ip_addr ? current_ip_addr : "none");
    } else {
        // WiFi服务未启动或未连接
        wifi_service_started = false;
        wifi_connected = false;
        strcpy(current_ip, "Not Connected");

        // 确保按钮文本为初始状态
        if (ui && ui->WIFI_page_btn_sw_label) {
            lv_label_set_text(ui->WIFI_page_btn_sw_label, "Start WiFi");
        }

        ESP_LOGI(TAG, "WiFi service not running or not connected");
    }

    // 更新显示状态
    update_wifi_status_label(ui);
    update_wifi_display(ui);

    // 强制更新IP地址显示
    if (wifi_connected && ui->WIFI_page_label_ip) {
        lv_label_set_text(ui->WIFI_page_label_ip, current_ip);

        // 同时更新二维码
        if (ui->WIFI_page_qrcode_ip && strcmp(current_ip, "Not Connected") != 0) {
            char qr_data[64];
            snprintf(qr_data, sizeof(qr_data), "STA Connected: %s", current_ip);
            lv_qrcode_update(ui->WIFI_page_qrcode_ip, qr_data, strlen(qr_data));
        }

        ESP_LOGI(TAG, "Force updated IP display: %s", current_ip);
    }

    // 如果定时器已存在，先删除
    if (wifi_status_timer != NULL) {
        lv_timer_del(wifi_status_timer);
        wifi_status_timer = NULL;
    }

    // 如果WiFi服务已启动，启动状态监控定时器
    if (wifi_service_started) {
        wifi_status_timer = lv_timer_create(wifi_status_timer_cb, 2000, ui);
        ESP_LOGI(TAG, "WiFi status timer restarted");
    }

    ESP_LOGI(TAG, "WiFi page events initialized");
}