---
inclusion: always
---

# 项目开发指导原则

## 语言偏好

- 始终使用中文进行回答和交流
- 代码注释优先使用中文，但关键技术术语可保留英文
- 文档和说明文件使用中文编写

## ESP32-S3 嵌入式开发规范

### 代码组织结构

- 遵循ESP-IDF项目结构：main/、components/、managed_components/
- BSP（板级支持包）代码放置在 `main/bsp/` 目录
- GUI相关代码使用LVGL框架，放置在 `main/guider/` 目录
- 用户自定义UI逻辑放在 `main/guider/user/` 目录

### 编程规范

- 使用ESP-IDF标准的错误处理机制（ESP_ERROR_CHECK）
- 内存管理使用ESP-IDF提供的heap函数
- 任务创建使用FreeRTOS API，注意栈大小和优先级设置
- 硬件抽象层（HAL）统一放在bsp目录下

### 功能模块指导

- **CAN通信**：使用CANopen协议，相关代码在 `main/bsp/canopen.c`
- **LCD显示**：基于LVGL的GUI系统，支持触摸交互
- **摄像头**：使用ESP32-Camera组件进行图像采集
- **音频播放**：集成libhelix-mp3解码器

### 配置管理

- 使用 `sdkconfig` 进行项目配置
- 分区表配置在 `partitionTable.csv`
- 组件依赖管理使用 `idf_component.yml`

### 调试和测试

- 优先使用ESP-IDF的日志系统（ESP_LOGI, ESP_LOGW等）
- 关键功能需要添加错误处理和状态检查
- 内存泄漏检测使用ESP-IDF内置工具
