--style=kr
--indent=spaces=4
--indent-classes
--indent-switches
--indent-cases
--indent-preproc-block
--indent-preproc-define
--indent-col1-comments
--pad-oper
--unpad-paren
--align-pointer=middle
--align-reference=middle
--convert-tabs
--max-code-length=120
--break-after-logical
--break-closing-braces
--attach-closing-while
--min-conditional-indent=0
--max-continuation-indent=120
--mode=c
--lineend=linux
--suffix=none
--preserve-date
--formatted
--ignore-exclude-errors
--ignore-exclude-errors-x
--exclude=assets
--exclude=../src/core/lv_obj_style_gen.c
--exclude=../src/core/lv_obj_style_gen.h
--exclude=../src/extra/libs/gif/gifdec.c
--exclude=../src/extra/libs/gif/gifdec.h
--exclude=../src/extra/libs/png/lodepng.c
--exclude=../src/extra/libs/png/lodepng.h
--exclude=../src/extra/libs/qrcode/qrcodegen.c
--exclude=../src/extra/libs/qrcode/qrcodegen.h
--exclude=../src/extra/libs/sjpg/tjpgd.c
--exclude=../src/extra/libs/sjpg/tjpgd.h
--exclude=../src/extra/libs/sjpg/tjpgdcnf.h
--exclude=../src/misc/lv_style_gen.c
--exclude=../src/misc/lv_style_gen.h
--exclude=../src/lv_conf_internal.h
--exclude=../tests/src/test_cases/_test_template.c
