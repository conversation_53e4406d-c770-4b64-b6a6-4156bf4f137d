#ifndef _AUDIO_ENCODER_H
#define _AUDIO_ENCODER_H

#include <stdbool.h>

typedef struct audio_encoder *audio_encoder_t;

typedef void (*audio_encoder_callback)(void *arg, uint8_t *data, int len);

typedef struct
{
    int sample_rate;
    int channels;
    int bits;
    int duration_ms;
} audio_encoder_cfg_t;

audio_encoder_t audio_encoder_create(audio_encoder_cfg_t *cfg);
void audio_encoder_destroy(audio_encoder_t encoder);
void audio_encoder_send_data(audio_encoder_t encoder, int16_t *pcm, int len);
void audio_encoder_reset(audio_encoder_t encoder);

void audio_encoder_set_dtx(audio_encoder_t encoder, bool enable);
void audio_encoder_set_complexity(audio_encoder_t encoder, int complexity);

void audio_encoder_set_callback(audio_encoder_t encoder, audio_encoder_callback cb);
void audio_encoder_set_user_data(audio_encoder_t encoder, void *user_data);

#endif
