/*******************************************************************************
 * Size: 10 px
 * Bpp: 4
 * Opts: undefined
 ******************************************************************************/

#ifdef LV_LVGL_H_INCLUDE_SIMPLE
#include "lvgl.h"
#else
#include "lvgl.h"
#endif

#ifndef LV_FONT_HARMONYOS_SANS_SC_LIGHT_10
#define LV_FONT_HARMONYOS_SANS_SC_LIGHT_10 1
#endif

#if LV_FONT_HARMONYOS_SANS_SC_LIGHT_10

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0x26, 0x26, 0x16, 0x15, 0x15, 0x1, 0x25,

    /* U+0022 "\"" */
    0x10, 0x16, 0x27, 0x61, 0x60,

    /* U+0023 "#" */
    0x0, 0x80, 0x35, 0x0, 0x8, 0x7, 0x10, 0x18,
    0xa7, 0xc7, 0x0, 0x62, 0x7, 0x0, 0x6c, 0x8a,
    0x93, 0x0, 0x80, 0x70, 0x0, 0x16, 0x8, 0x0,
    0x0,

    /* U+0024 "$" */
    0x0, 0x72, 0x0, 0x7, 0xba, 0x40, 0x44, 0x72,
    0x70, 0x37, 0x72, 0x0, 0x4, 0xb8, 0x10, 0x0,
    0x73, 0xa0, 0x61, 0x72, 0x81, 0x19, 0xb9, 0x70,
    0x0, 0x72, 0x0, 0x0, 0x31, 0x0,

    /* U+0025 "%" */
    0x58, 0x90, 0x8, 0x0, 0x80, 0x43, 0x45, 0x0,
    0x28, 0x70, 0x90, 0x0, 0x0, 0x7, 0x20, 0x0,
    0x0, 0x27, 0x38, 0x70, 0x0, 0x90, 0x80, 0x53,
    0x6, 0x30, 0x58, 0x90,

    /* U+0026 "&" */
    0x0, 0x78, 0x30, 0x0, 0x62, 0xa, 0x0, 0x7,
    0x20, 0xa0, 0x0, 0x1a, 0x92, 0x0, 0x8, 0x78,
    0x7, 0x15, 0x40, 0x38, 0x90, 0x64, 0x0, 0x7a,
    0x0, 0x88, 0x86, 0x47,

    /* U+0027 "'" */
    0x10, 0x61, 0x61,

    /* U+0028 "(" */
    0x0, 0x40, 0x5, 0x40, 0x9, 0x0, 0x27, 0x0,
    0x45, 0x0, 0x35, 0x0, 0x8, 0x0, 0x8, 0x0,
    0x1, 0x70,

    /* U+0029 ")" */
    0x40, 0x1, 0x70, 0x8, 0x10, 0x35, 0x1, 0x70,
    0x26, 0x5, 0x30, 0x90, 0x53, 0x0,

    /* U+002A "*" */
    0x12, 0x42, 0x2a, 0xb2, 0x46, 0x74, 0x1, 0x10,

    /* U+002B "+" */
    0x0, 0x10, 0x0, 0x7, 0x0, 0x37, 0xb7, 0x70,
    0x7, 0x0, 0x0, 0x70, 0x0,

    /* U+002C "," */
    0x16, 0x15, 0x0,

    /* U+002D "-" */
    0x27, 0x77, 0x10,

    /* U+002E "." */
    0x24,

    /* U+002F "/" */
    0x0, 0x44, 0x0, 0x90, 0x1, 0x70, 0x7, 0x20,
    0x9, 0x0, 0x35, 0x0, 0x80, 0x0,

    /* U+0030 "0" */
    0x7, 0x89, 0x20, 0x27, 0x0, 0x90, 0x63, 0x0,
    0xa0, 0x72, 0x0, 0xa0, 0x63, 0x0, 0x90, 0x27,
    0x0, 0x90, 0x7, 0x89, 0x20,

    /* U+0031 "1" */
    0x29, 0x86, 0x18, 0x1, 0x80, 0x18, 0x1, 0x80,
    0x18, 0x1, 0x80,

    /* U+0032 "2" */
    0x8, 0x88, 0x33, 0x30, 0xa, 0x0, 0x0, 0x90,
    0x0, 0x92, 0x0, 0x83, 0x0, 0x75, 0x0, 0x5c,
    0x88, 0x70,

    /* U+0033 "3" */
    0x8, 0x89, 0x22, 0x30, 0x9, 0x0, 0x2, 0x80,
    0x8, 0xc2, 0x0, 0x0, 0xa3, 0x10, 0xa, 0x8,
    0x88, 0x40,

    /* U+0034 "4" */
    0x0, 0x19, 0x0, 0x0, 0x82, 0x0, 0x1, 0x80,
    0x0, 0x8, 0x15, 0x40, 0x27, 0x5, 0x40, 0x68,
    0x8a, 0xa3, 0x0, 0x5, 0x40,

    /* U+0035 "5" */
    0xa, 0x88, 0x50, 0x90, 0x0, 0xc, 0x88, 0x0,
    0x50, 0x19, 0x0, 0x0, 0xa1, 0x30, 0xa, 0x8,
    0x89, 0x20,

    /* U+0036 "6" */
    0x0, 0x27, 0x0, 0x0, 0x70, 0x0, 0x6, 0x98,
    0x10, 0x18, 0x0, 0xa0, 0x53, 0x0, 0x81, 0x56,
    0x0, 0xa0, 0x8, 0x89, 0x30,

    /* U+0037 "7" */
    0x38, 0x88, 0xc0, 0x0, 0x27, 0x0, 0x8, 0x10,
    0x0, 0x90, 0x0, 0x63, 0x0, 0xa, 0x0, 0x3,
    0x60, 0x0,

    /* U+0038 "8" */
    0x7, 0x78, 0x20, 0x27, 0x0, 0x90, 0x28, 0x1,
    0x80, 0x8, 0x9b, 0x20, 0x55, 0x0, 0xa0, 0x73,
    0x0, 0xa0, 0x8, 0x78, 0x50,

    /* U+0039 "9" */
    0x8, 0x89, 0x36, 0x40, 0xb, 0x81, 0x0, 0x94,
    0x60, 0x17, 0x6, 0x8b, 0x0, 0x3, 0x40, 0x1,
    0x80, 0x0,

    /* U+003A ":" */
    0x16, 0x0, 0x0, 0x0, 0x16,

    /* U+003B ";" */
    0x6, 0x0, 0x0, 0x0, 0x7, 0x16, 0x0,

    /* U+003C "<" */
    0x0, 0x0, 0x30, 0x6, 0x84, 0x4c, 0x20, 0x0,
    0x38, 0x72, 0x0, 0x0, 0x60,

    /* U+003D "=" */
    0x37, 0x77, 0x70, 0x0, 0x0, 0x37, 0x77, 0x70,

    /* U+003E ">" */
    0x21, 0x0, 0x1, 0x78, 0x30, 0x0, 0x7, 0xb0,
    0x58, 0x60, 0x33, 0x0, 0x0,

    /* U+003F "?" */
    0x28, 0x83, 0x70, 0xa, 0x0, 0x9, 0x0, 0x82,
    0x4, 0x50, 0x6, 0x10, 0x0, 0x0, 0x3, 0x30,

    /* U+0040 "@" */
    0x0, 0x16, 0x77, 0x72, 0x0, 0x3, 0x81, 0x0,
    0x7, 0x40, 0x9, 0x5, 0x88, 0x90, 0x80, 0x43,
    0x9, 0x0, 0xb0, 0x35, 0x61, 0x17, 0x0, 0x90,
    0x26, 0x43, 0x9, 0x0, 0xb1, 0x53, 0x8, 0x2,
    0x87, 0x28, 0x60, 0x3, 0x70, 0x0, 0x1, 0x0,
    0x0, 0x17, 0x77, 0x71, 0x0,

    /* U+0041 "A" */
    0x0, 0x4a, 0x0, 0x0, 0x8, 0x81, 0x0, 0x1,
    0x71, 0x70, 0x0, 0x71, 0x9, 0x0, 0xb, 0x88,
    0xa5, 0x4, 0x40, 0x0, 0xa0, 0x90, 0x0, 0x8,
    0x20,

    /* U+0042 "B" */
    0x1c, 0x87, 0x80, 0x18, 0x0, 0x45, 0x18, 0x0,
    0x64, 0x1c, 0x79, 0xb1, 0x18, 0x0, 0x19, 0x18,
    0x0, 0xa, 0x1c, 0x77, 0x93,

    /* U+0043 "C" */
    0x1, 0x98, 0x85, 0x0, 0xa0, 0x0, 0x50, 0x45,
    0x0, 0x0, 0x6, 0x30, 0x0, 0x0, 0x45, 0x0,
    0x0, 0x0, 0xa0, 0x0, 0x50, 0x2, 0x98, 0x86,
    0x0,

    /* U+0044 "D" */
    0x1c, 0x78, 0x81, 0x1, 0x80, 0x0, 0xa0, 0x18,
    0x0, 0x4, 0x51, 0x80, 0x0, 0x27, 0x18, 0x0,
    0x4, 0x51, 0x80, 0x0, 0xa0, 0x1c, 0x88, 0x81,
    0x0,

    /* U+0045 "E" */
    0x1c, 0x88, 0x81, 0x18, 0x0, 0x0, 0x18, 0x0,
    0x0, 0x1c, 0x88, 0x60, 0x18, 0x0, 0x0, 0x18,
    0x0, 0x0, 0x1c, 0x88, 0x82,

    /* U+0046 "F" */
    0x1c, 0x88, 0x81, 0x18, 0x0, 0x0, 0x18, 0x0,
    0x0, 0x1c, 0x88, 0x60, 0x18, 0x0, 0x0, 0x18,
    0x0, 0x0, 0x18, 0x0, 0x0,

    /* U+0047 "G" */
    0x1, 0x88, 0x88, 0x0, 0xa0, 0x0, 0x21, 0x45,
    0x0, 0x0, 0x6, 0x30, 0x7, 0x84, 0x45, 0x0,
    0x3, 0x50, 0xa1, 0x0, 0x45, 0x1, 0x98, 0x88,
    0x0,

    /* U+0048 "H" */
    0x18, 0x0, 0x3, 0x51, 0x80, 0x0, 0x35, 0x18,
    0x0, 0x3, 0x51, 0xc8, 0x88, 0x95, 0x18, 0x0,
    0x3, 0x51, 0x80, 0x0, 0x35, 0x18, 0x0, 0x3,
    0x50,

    /* U+0049 "I" */
    0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18,

    /* U+004A "J" */
    0x0, 0x17, 0x0, 0x17, 0x0, 0x17, 0x0, 0x17,
    0x0, 0x17, 0x20, 0x26, 0x78, 0x91,

    /* U+004B "K" */
    0x18, 0x0, 0x29, 0x1, 0x80, 0x29, 0x0, 0x18,
    0x28, 0x0, 0x1, 0xa9, 0x90, 0x0, 0x1b, 0x5,
    0x60, 0x1, 0x80, 0x8, 0x30, 0x18, 0x0, 0xa,
    0x10,

    /* U+004C "L" */
    0x18, 0x0, 0x0, 0x18, 0x0, 0x0, 0x18, 0x0,
    0x0, 0x18, 0x0, 0x0, 0x18, 0x0, 0x0, 0x18,
    0x0, 0x0, 0x1c, 0x88, 0x81,

    /* U+004D "M" */
    0x1c, 0x0, 0x0, 0x39, 0x1b, 0x60, 0x0, 0x99,
    0x18, 0x82, 0x8, 0x29, 0x18, 0x9, 0x36, 0x9,
    0x18, 0x3, 0xb0, 0x9, 0x18, 0x0, 0x0, 0x9,
    0x18, 0x0, 0x0, 0x9,

    /* U+004E "N" */
    0x1c, 0x0, 0x4, 0x51, 0xa7, 0x0, 0x45, 0x17,
    0x63, 0x4, 0x51, 0x70, 0x91, 0x45, 0x17, 0x1,
    0x94, 0x51, 0x70, 0x4, 0xa5, 0x17, 0x0, 0x8,
    0x50,

    /* U+004F "O" */
    0x2, 0x98, 0x86, 0x0, 0xa0, 0x0, 0x46, 0x45,
    0x0, 0x0, 0xa6, 0x30, 0x0, 0x9, 0x45, 0x0,
    0x0, 0xa0, 0xa0, 0x0, 0x46, 0x2, 0x98, 0x87,
    0x0,

    /* U+0050 "P" */
    0x1c, 0x77, 0x70, 0x18, 0x0, 0x55, 0x18, 0x0,
    0x74, 0x1c, 0x87, 0x50, 0x18, 0x0, 0x0, 0x18,
    0x0, 0x0, 0x18, 0x0, 0x0,

    /* U+0051 "Q" */
    0x2, 0x98, 0x86, 0x0, 0xa, 0x0, 0x4, 0x60,
    0x45, 0x0, 0x0, 0xa0, 0x63, 0x0, 0x0, 0x90,
    0x45, 0x0, 0x0, 0xa0, 0xa, 0x0, 0x4, 0x60,
    0x2, 0x98, 0x98, 0x0, 0x0, 0x0, 0x9, 0x40,
    0x0, 0x0, 0x0, 0x61,

    /* U+0052 "R" */
    0x1c, 0x77, 0x80, 0x1, 0x80, 0x5, 0x50, 0x18,
    0x0, 0x73, 0x1, 0xc8, 0xb5, 0x0, 0x18, 0x5,
    0x50, 0x1, 0x80, 0x9, 0x10, 0x18, 0x0, 0x1a,
    0x0,

    /* U+0053 "S" */
    0x8, 0x78, 0x60, 0x64, 0x0, 0x40, 0x39, 0x0,
    0x0, 0x2, 0x79, 0x30, 0x0, 0x0, 0xa1, 0x60,
    0x0, 0x81, 0x19, 0x88, 0x60,

    /* U+0054 "T" */
    0x68, 0xc8, 0x82, 0x0, 0x90, 0x0, 0x0, 0x90,
    0x0, 0x0, 0x90, 0x0, 0x0, 0x90, 0x0, 0x0,
    0x90, 0x0, 0x0, 0x90, 0x0,

    /* U+0055 "U" */
    0x26, 0x0, 0x5, 0x42, 0x60, 0x0, 0x54, 0x26,
    0x0, 0x5, 0x42, 0x60, 0x0, 0x54, 0x27, 0x0,
    0x6, 0x30, 0xa0, 0x0, 0xa0, 0x3, 0x98, 0x94,
    0x0,

    /* U+0056 "V" */
    0x90, 0x0, 0x8, 0x14, 0x60, 0x0, 0x90, 0xa,
    0x0, 0x44, 0x0, 0x72, 0x8, 0x0, 0x1, 0x81,
    0x70, 0x0, 0x8, 0x71, 0x0, 0x0, 0x3a, 0x0,
    0x0,

    /* U+0057 "W" */
    0x90, 0x0, 0x93, 0x0, 0x53, 0x54, 0x0, 0xa8,
    0x0, 0x80, 0x18, 0x3, 0x59, 0x0, 0x80, 0x9,
    0x7, 0x6, 0x23, 0x40, 0x6, 0x38, 0x1, 0x78,
    0x0, 0x1, 0xa6, 0x0, 0x98, 0x0, 0x0, 0xb1,
    0x0, 0x75, 0x0,

    /* U+0058 "X" */
    0x65, 0x0, 0x9, 0x0, 0x92, 0x9, 0x10, 0x0,
    0xa6, 0x40, 0x0, 0x5, 0xb0, 0x0, 0x1, 0x96,
    0x50, 0x0, 0x91, 0x9, 0x20, 0x73, 0x0, 0xa,
    0x0,

    /* U+0059 "Y" */
    0x83, 0x0, 0x27, 0xa, 0x0, 0x90, 0x3, 0x76,
    0x40, 0x0, 0x89, 0x0, 0x0, 0x45, 0x0, 0x0,
    0x45, 0x0, 0x0, 0x45, 0x0,

    /* U+005A "Z" */
    0x38, 0x88, 0xd2, 0x0, 0x3, 0x70, 0x0, 0xa,
    0x0, 0x0, 0x82, 0x0, 0x3, 0x70, 0x0, 0x9,
    0x0, 0x0, 0x8a, 0x88, 0x83,

    /* U+005B "[" */
    0x19, 0x71, 0x70, 0x17, 0x1, 0x70, 0x17, 0x1,
    0x70, 0x17, 0x1, 0x70, 0x19, 0x70,

    /* U+005C "\\" */
    0x80, 0x0, 0x35, 0x0, 0x9, 0x0, 0x7, 0x20,
    0x1, 0x70, 0x0, 0x90, 0x0, 0x44,

    /* U+005D "]" */
    0x58, 0x20, 0x53, 0x5, 0x30, 0x53, 0x5, 0x30,
    0x53, 0x5, 0x30, 0x53, 0x69, 0x20,

    /* U+005E "^" */
    0x1, 0x40, 0x0, 0x7a, 0x0, 0x8, 0x26, 0x6,
    0x20, 0x80,

    /* U+005F "_" */
    0x77, 0x77,

    /* U+0060 "`" */
    0x22, 0x0, 0x80,

    /* U+0061 "a" */
    0x8, 0x89, 0x20, 0x20, 0x9, 0x6, 0x66, 0x95,
    0x40, 0x9, 0x18, 0x76, 0x80,

    /* U+0062 "b" */
    0x26, 0x0, 0x0, 0x26, 0x0, 0x0, 0x29, 0x89,
    0x70, 0x2a, 0x0, 0x64, 0x27, 0x0, 0x26, 0x2a,
    0x0, 0x64, 0x28, 0x88, 0x70,

    /* U+0063 "c" */
    0x7, 0x88, 0x43, 0x60, 0x1, 0x63, 0x0, 0x3,
    0x60, 0x1, 0x6, 0x88, 0x40,

    /* U+0064 "d" */
    0x0, 0x0, 0x62, 0x0, 0x0, 0x62, 0x7, 0x98,
    0x92, 0x46, 0x0, 0xa2, 0x63, 0x0, 0x62, 0x46,
    0x0, 0xa2, 0x7, 0x88, 0x82,

    /* U+0065 "e" */
    0x7, 0x89, 0x34, 0x60, 0x9, 0x68, 0x77, 0x93,
    0x50, 0x0, 0x7, 0x88, 0x60,

    /* U+0066 "f" */
    0x2, 0x83, 0x9, 0x0, 0x8, 0x0, 0x5c, 0x70,
    0x9, 0x0, 0x9, 0x0, 0x9, 0x0, 0x9, 0x0,

    /* U+0067 "g" */
    0x7, 0x98, 0x82, 0x46, 0x0, 0x92, 0x63, 0x0,
    0x72, 0x46, 0x0, 0xa2, 0x7, 0x88, 0x92, 0x1,
    0x0, 0x80, 0x8, 0x88, 0x50,

    /* U+0068 "h" */
    0x26, 0x0, 0x2, 0x60, 0x0, 0x29, 0x89, 0x52,
    0x90, 0x9, 0x26, 0x0, 0x92, 0x60, 0x9, 0x26,
    0x0, 0x90,

    /* U+0069 "i" */
    0x25, 0x0, 0x26, 0x26, 0x26, 0x26, 0x26,

    /* U+006A "j" */
    0x0, 0x25, 0x0, 0x0, 0x0, 0x27, 0x0, 0x27,
    0x0, 0x27, 0x0, 0x27, 0x0, 0x27, 0x0, 0x26,
    0x7, 0x91,

    /* U+006B "k" */
    0x26, 0x0, 0x2, 0x60, 0x0, 0x26, 0x6, 0x42,
    0x65, 0x50, 0x2b, 0xb1, 0x2, 0x80, 0xa0, 0x26,
    0x2, 0x80,

    /* U+006C "l" */
    0x26, 0x26, 0x26, 0x26, 0x26, 0x26, 0x26,

    /* U+006D "m" */
    0x28, 0x8a, 0x48, 0xa3, 0x29, 0x1, 0xb0, 0x9,
    0x26, 0x0, 0x80, 0x9, 0x26, 0x0, 0x80, 0x9,
    0x26, 0x0, 0x80, 0x9,

    /* U+006E "n" */
    0x28, 0x89, 0x52, 0x90, 0x9, 0x26, 0x0, 0x92,
    0x60, 0x9, 0x26, 0x0, 0x90,

    /* U+006F "o" */
    0x6, 0x99, 0x50, 0x36, 0x0, 0x81, 0x63, 0x0,
    0x54, 0x36, 0x0, 0x81, 0x7, 0x89, 0x50,

    /* U+0070 "p" */
    0x28, 0x89, 0x70, 0x2a, 0x0, 0x64, 0x27, 0x0,
    0x26, 0x2a, 0x0, 0x64, 0x28, 0x88, 0x70, 0x26,
    0x0, 0x0, 0x26, 0x0, 0x0,

    /* U+0071 "q" */
    0x7, 0x98, 0x82, 0x46, 0x0, 0xa2, 0x63, 0x0,
    0x62, 0x46, 0x0, 0xa2, 0x7, 0x88, 0x82, 0x0,
    0x0, 0x62, 0x0, 0x0, 0x62,

    /* U+0072 "r" */
    0x29, 0x83, 0x29, 0x0, 0x26, 0x0, 0x26, 0x0,
    0x26, 0x0,

    /* U+0073 "s" */
    0x29, 0x88, 0x5, 0x50, 0x10, 0x6, 0x85, 0x2,
    0x0, 0x90, 0x39, 0x88, 0x0,

    /* U+0074 "t" */
    0x4, 0x0, 0x9, 0x0, 0x6c, 0x70, 0x9, 0x0,
    0x9, 0x0, 0x9, 0x0, 0x8, 0x81,

    /* U+0075 "u" */
    0x35, 0x0, 0x93, 0x50, 0x9, 0x35, 0x0, 0x92,
    0x70, 0xb, 0x8, 0x88, 0x80,

    /* U+0076 "v" */
    0x90, 0x0, 0x83, 0x60, 0x53, 0x9, 0x8, 0x0,
    0x56, 0x50, 0x0, 0xc0, 0x0,

    /* U+0077 "w" */
    0x90, 0x9, 0x30, 0x34, 0x53, 0x8, 0x80, 0x70,
    0x8, 0x34, 0x80, 0x70, 0x8, 0x80, 0x38, 0x30,
    0x3, 0x80, 0xb, 0x0,

    /* U+0078 "x" */
    0x74, 0x6, 0x30, 0x95, 0x70, 0x2, 0xe0, 0x0,
    0x94, 0x70, 0x73, 0x7, 0x30,

    /* U+0079 "y" */
    0x90, 0x0, 0x84, 0x60, 0x63, 0x9, 0x8, 0x0,
    0x67, 0x50, 0x0, 0xc0, 0x0, 0x16, 0x0, 0x69,
    0x0, 0x0,

    /* U+007A "z" */
    0x37, 0x7d, 0x0, 0x5, 0x40, 0x2, 0x80, 0x0,
    0x90, 0x0, 0x7a, 0x77, 0x10,

    /* U+007B "{" */
    0x2, 0x81, 0x8, 0x10, 0x8, 0x0, 0x9, 0x0,
    0x68, 0x0, 0x9, 0x0, 0x8, 0x0, 0x8, 0x10,
    0x2, 0x81,

    /* U+007C "|" */
    0x30, 0x70, 0x70, 0x70, 0x70, 0x70, 0x70, 0x70,
    0x70,

    /* U+007D "}" */
    0x65, 0x0, 0x9, 0x0, 0x9, 0x0, 0x9, 0x0,
    0x3, 0xc0, 0x9, 0x0, 0x9, 0x0, 0x9, 0x0,
    0x65, 0x0,

    /* U+007E "~" */
    0x7, 0x60, 0x62, 0x32, 0x84,

    /* U+4E2D "中" */
    0x0, 0x8, 0x0, 0x0, 0x68, 0x8c, 0x88, 0x82,
    0x80, 0x8, 0x0, 0x35, 0x80, 0x8, 0x0, 0x35,
    0x80, 0x8, 0x0, 0x35, 0x78, 0x8c, 0x88, 0x83,
    0x0, 0x8, 0x0, 0x0, 0x0, 0x8, 0x0, 0x0,
    0x0, 0x8, 0x0, 0x0,

    /* U+51FA "出" */
    0x30, 0x0, 0x70, 0x3, 0x71, 0x0, 0x70, 0x8,
    0x71, 0x0, 0x70, 0x8, 0x68, 0x88, 0xb8, 0x87,
    0x0, 0x0, 0x70, 0x0, 0x70, 0x0, 0x70, 0x7,
    0x80, 0x0, 0x70, 0x8, 0xa8, 0x88, 0xc8, 0x8a,
    0x0, 0x0, 0x0, 0x8,

    /* U+5217 "列" */
    0x8, 0xaa, 0x87, 0x0, 0x80, 0x7, 0x10, 0x7,
    0x8, 0x0, 0xb8, 0x93, 0x70, 0x80, 0x35, 0x6,
    0x17, 0x8, 0x9, 0x91, 0x90, 0x70, 0x84, 0x41,
    0xa6, 0x7, 0x8, 0x0, 0xa, 0x0, 0x50, 0x80,
    0x9, 0x20, 0x0, 0x8, 0x9, 0x20, 0x0, 0x38,
    0x90, 0x10, 0x0, 0x0, 0x0,

    /* U+524D "前" */
    0x0, 0x30, 0x0, 0x30, 0x0, 0x0, 0x26, 0x0,
    0x80, 0x0, 0x57, 0x77, 0x77, 0x77, 0x71, 0x6,
    0x77, 0x30, 0x12, 0x30, 0x8, 0x0, 0x83, 0x43,
    0x40, 0xb, 0x77, 0x83, 0x43, 0x40, 0x8, 0x0,
    0x83, 0x43, 0x40, 0xa, 0x77, 0x82, 0x43, 0x40,
    0x8, 0x0, 0x80, 0x3, 0x40, 0x8, 0x28, 0x50,
    0x89, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+538B "压" */
    0x8, 0x88, 0x88, 0x88, 0x81, 0x8, 0x0, 0x7,
    0x0, 0x0, 0x8, 0x0, 0x9, 0x0, 0x0, 0x8,
    0x48, 0x8c, 0x88, 0x70, 0x8, 0x0, 0x9, 0x10,
    0x0, 0x8, 0x0, 0x9, 0x19, 0x0, 0x9, 0x0,
    0x9, 0x1, 0x50, 0x46, 0x88, 0x8c, 0x88, 0x83,
    0x40, 0x0, 0x0, 0x0, 0x0,

    /* U+5907 "备" */
    0x0, 0x4, 0x0, 0x0, 0x0, 0x0, 0x7a, 0x77,
    0x78, 0x0, 0x7, 0x94, 0x0, 0x76, 0x0, 0x43,
    0x4, 0x9a, 0x20, 0x0, 0x25, 0x87, 0x36, 0x87,
    0x40, 0x45, 0x77, 0x77, 0x78, 0x41, 0x8, 0x0,
    0x80, 0x7, 0x0, 0x8, 0x66, 0xb6, 0x6b, 0x0,
    0x8, 0x0, 0x80, 0x7, 0x0, 0x8, 0x77, 0x77,
    0x7b, 0x0,

    /* U+5EA6 "度" */
    0x0, 0x0, 0x13, 0x0, 0x0, 0x4, 0x77, 0x7c,
    0x77, 0x72, 0x8, 0x7, 0x0, 0x8, 0x0, 0x8,
    0x7b, 0x77, 0x7b, 0x72, 0x8, 0x7, 0x0, 0x8,
    0x0, 0x8, 0x3, 0x66, 0x63, 0x0, 0x8, 0x49,
    0x77, 0x7a, 0x20, 0x8, 0x3, 0x60, 0x75, 0x0,
    0x35, 0x2, 0x9b, 0x92, 0x0, 0x52, 0x85, 0x0,
    0x5, 0x73,

    /* U+5F53 "当" */
    0x5, 0x0, 0x80, 0x1, 0x40, 0x19, 0x8, 0x1,
    0x90, 0x0, 0x12, 0x80, 0x40, 0x0, 0x88, 0x88,
    0x88, 0x88, 0x0, 0x0, 0x0, 0x0, 0x80, 0x58,
    0x88, 0x88, 0x88, 0x0, 0x0, 0x0, 0x0, 0x82,
    0x88, 0x88, 0x88, 0x88, 0x0, 0x0, 0x0, 0x0,
    0x80,

    /* U+672A "未" */
    0x0, 0x0, 0x17, 0x0, 0x0, 0x3, 0x88, 0x8b,
    0x88, 0x60, 0x0, 0x0, 0x17, 0x0, 0x0, 0x8,
    0x88, 0x8b, 0x88, 0x83, 0x0, 0x0, 0x8a, 0x40,
    0x0, 0x0, 0x8, 0x27, 0x81, 0x0, 0x0, 0x83,
    0x17, 0x9, 0x10, 0x1a, 0x30, 0x17, 0x0, 0x94,
    0x11, 0x0, 0x17, 0x0, 0x2,

    /* U+679C "果" */
    0x2, 0xa7, 0x7b, 0x77, 0x80, 0x2, 0x60, 0x7,
    0x0, 0x80, 0x2, 0xa7, 0x7b, 0x77, 0x80, 0x2,
    0xa7, 0x7b, 0x77, 0x80, 0x0, 0x0, 0x7, 0x0,
    0x0, 0x7, 0x77, 0xcd, 0x97, 0x73, 0x0, 0x8,
    0x27, 0x73, 0x0, 0x4, 0x81, 0x7, 0x6, 0x81,
    0x24, 0x0, 0x7, 0x0, 0x15,

    /* U+6D4B "测" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x53, 0xb7, 0xb0,
    0x7, 0x0, 0x7, 0x57, 0x7, 0x73, 0x80, 0x76,
    0x70, 0x77, 0x2, 0x27, 0x67, 0x7, 0x70, 0x21,
    0x76, 0x70, 0x77, 0x7, 0x6, 0x65, 0x7, 0x70,
    0x80, 0x9, 0x10, 0x47, 0x8, 0x7, 0x7, 0x0,
    0x72, 0x56, 0x30, 0x13, 0x69, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+7259 "牙" */
    0x7, 0x77, 0x77, 0xc7, 0x71, 0x0, 0x70, 0x0,
    0x80, 0x0, 0x2, 0x60, 0x0, 0x80, 0x0, 0x5,
    0x98, 0x88, 0xc8, 0x82, 0x0, 0x0, 0x46, 0x80,
    0x0, 0x0, 0x2, 0x80, 0x80, 0x0, 0x0, 0x49,
    0x0, 0x80, 0x0, 0x8, 0x70, 0x0, 0x80, 0x0,
    0x22, 0x0, 0x48, 0x90, 0x0,

    /* U+7535 "电" */
    0x0, 0x8, 0x0, 0x0, 0x8, 0x88, 0xc8, 0x88,
    0x70, 0x80, 0x8, 0x0, 0x8, 0x9, 0x77, 0xc7,
    0x77, 0x80, 0x80, 0x8, 0x0, 0x8, 0x9, 0x88,
    0xc8, 0x88, 0x80, 0x0, 0x8, 0x0, 0x0, 0x80,
    0x0, 0x80, 0x0, 0x8, 0x0, 0x4, 0x87, 0x79,
    0x40, 0x0, 0x0, 0x0, 0x0,

    /* U+7ED3 "结" */
    0x0, 0x70, 0x0, 0x80, 0x0, 0x5, 0x31, 0x88,
    0xc8, 0x83, 0x19, 0x47, 0x0, 0x80, 0x0, 0x35,
    0xa0, 0x68, 0xa8, 0x80, 0x3, 0x50, 0x0, 0x0,
    0x0, 0x1c, 0x76, 0x78, 0x88, 0xa0, 0x0, 0x0,
    0x80, 0x0, 0x80, 0x27, 0x88, 0x87, 0x77, 0xa0,
    0x11, 0x0, 0x80, 0x0, 0x80,

    /* U+84DD "蓝" */
    0x17, 0x7b, 0x77, 0xc7, 0x74, 0x0, 0x8, 0x0,
    0x80, 0x0, 0x4, 0x16, 0x3, 0x97, 0x72, 0x5,
    0x27, 0x16, 0x42, 0x0, 0x5, 0x27, 0x42, 0x7,
    0x40, 0x1, 0x77, 0x77, 0x77, 0x30, 0x3, 0x51,
    0x52, 0x51, 0x60, 0x3, 0x51, 0x52, 0x51, 0x60,
    0x29, 0xa8, 0xa8, 0xa8, 0xb6,

    /* U+884C "行" */
    0x0, 0x13, 0x0, 0x0, 0x0, 0x0, 0xa1, 0x48,
    0x88, 0x81, 0x9, 0x20, 0x0, 0x0, 0x0, 0x22,
    0x24, 0x0, 0x0, 0x0, 0x0, 0x90, 0x88, 0x8a,
    0x84, 0x9, 0x80, 0x0, 0x9, 0x0, 0x53, 0x80,
    0x0, 0x9, 0x0, 0x0, 0x80, 0x0, 0x9, 0x0,
    0x0, 0x80, 0x0, 0x9, 0x0, 0x0, 0x80, 0x6,
    0x88, 0x0,

    /* U+8868 "表" */
    0x6, 0x77, 0x7b, 0x77, 0x72, 0x0, 0x0, 0x7,
    0x0, 0x0, 0x2, 0x77, 0x7b, 0x77, 0x60, 0x0,
    0x0, 0x7, 0x0, 0x0, 0x17, 0x77, 0xcb, 0x77,
    0x74, 0x0, 0x9, 0x25, 0x36, 0x70, 0x5, 0x99,
    0x0, 0x85, 0x0, 0x23, 0x8, 0x37, 0x6, 0x83,
    0x0, 0x9, 0x40, 0x0, 0x3,

    /* U+8BBE "设" */
    0x1, 0x0, 0x0, 0x0, 0x0, 0x4, 0x70, 0xb,
    0x7a, 0x20, 0x0, 0x41, 0x16, 0x6, 0x20, 0x0,
    0x1, 0xa1, 0x4, 0x94, 0x38, 0x81, 0x20, 0x0,
    0x0, 0x0, 0x80, 0xa8, 0x77, 0xa0, 0x0, 0x80,
    0x18, 0x5, 0x40, 0x0, 0x84, 0x4, 0x96, 0x0,
    0x0, 0xd6, 0x28, 0x78, 0x20, 0x1, 0x35, 0x60,
    0x0, 0x65,

    /* U+8BD5 "试" */
    0x6, 0x0, 0x0, 0x73, 0x40, 0x2, 0x47, 0x88,
    0xc8, 0xa2, 0x11, 0x0, 0x0, 0x70, 0x0, 0x6c,
    0x0, 0x0, 0x70, 0x0, 0x7, 0x7, 0xc8, 0x72,
    0x0, 0x7, 0x0, 0x80, 0x43, 0x0, 0x7, 0x31,
    0x80, 0x16, 0x22, 0x7, 0x85, 0xc9, 0x38, 0x52,
    0x5, 0x3, 0x0, 0x6, 0xa0,

    /* U+8F93 "输" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0x30, 0x2,
    0x94, 0x0, 0x3b, 0x77, 0x18, 0x6, 0x40, 0x7,
    0x22, 0x88, 0x88, 0x66, 0x15, 0x70, 0x54, 0x33,
    0x30, 0x27, 0xb5, 0x83, 0x66, 0x70, 0x0, 0x70,
    0x83, 0x66, 0x70, 0x48, 0xb4, 0x96, 0x66, 0x70,
    0x0, 0x70, 0x60, 0x60, 0x70, 0x0, 0x70, 0x66,
    0x46, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+8FD0 "运" */
    0x2, 0x0, 0x0, 0x0, 0x0, 0x6, 0x30, 0x88,
    0x88, 0x60, 0x0, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x5, 0x88, 0x88, 0x83, 0x38, 0x70, 0x8, 0x11,
    0x0, 0x0, 0x80, 0x17, 0x8, 0x10, 0x0, 0x80,
    0x90, 0x2, 0x80, 0x0, 0x84, 0xb8, 0x87, 0x91,
    0x8, 0xa3, 0x0, 0x0, 0x10, 0x35, 0x6, 0x88,
    0x87, 0x85, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+901F "速" */
    0x6, 0x13, 0x77, 0xc7, 0x72, 0x0, 0x70, 0x0,
    0x80, 0x0, 0x0, 0x0, 0x97, 0xb7, 0x90, 0x28,
    0x60, 0x80, 0x80, 0x80, 0x0, 0x80, 0x78, 0xd6,
    0x70, 0x0, 0x80, 0x8, 0xa8, 0x0, 0x0, 0x83,
    0x90, 0x81, 0xa1, 0x8, 0x84, 0x0, 0x70, 0x0,
    0x35, 0x7, 0x87, 0x77, 0x75, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F001 "" */
    0x0, 0x0, 0x0, 0x4, 0x9c, 0x10, 0x0, 0x16,
    0xbf, 0xff, 0xf2, 0x0, 0x3f, 0xff, 0xff, 0xff,
    0x20, 0x5, 0xff, 0xd9, 0x41, 0xf2, 0x0, 0x5f,
    0x20, 0x0, 0x1f, 0x20, 0x5, 0xe0, 0x0, 0x1,
    0xf2, 0x0, 0x5e, 0x0, 0x7, 0x9f, 0x20, 0x48,
    0xe0, 0x7, 0xff, 0xf2, 0xaf, 0xfe, 0x0, 0x2b,
    0xd8, 0x7, 0xff, 0x90, 0x0, 0x0, 0x0, 0x1,
    0x10, 0x0, 0x0, 0x0, 0x0,

    /* U+F008 "" */
    0x41, 0x88, 0x88, 0x88, 0x14, 0xeb, 0xe7, 0x77,
    0x7e, 0xbe, 0xa2, 0xd0, 0x0, 0xd, 0x2a, 0xeb,
    0xe3, 0x33, 0x3e, 0xbe, 0xb4, 0xfb, 0xbb, 0xbf,
    0x4b, 0xd9, 0xd0, 0x0, 0xd, 0x9d, 0xb5, 0xd0,
    0x0, 0xd, 0x5b, 0xb7, 0xff, 0xff, 0xff, 0x7b,

    /* U+F00B "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xd6, 0xff,
    0xff, 0xff, 0xff, 0xe7, 0xff, 0xff, 0xff, 0x67,
    0x52, 0x77, 0x77, 0x76, 0xef, 0xc6, 0xff, 0xff,
    0xfe, 0xff, 0xe7, 0xff, 0xff, 0xff, 0x67, 0x52,
    0x77, 0x77, 0x76, 0xef, 0xc6, 0xff, 0xff, 0xfe,
    0xff, 0xe7, 0xff, 0xff, 0xff, 0x78, 0x63, 0x88,
    0x88, 0x87,

    /* U+F00C "" */
    0x0, 0x0, 0x0, 0x0, 0x41, 0x0, 0x0, 0x0,
    0x6, 0xfd, 0x0, 0x0, 0x0, 0x6f, 0xf7, 0x7c,
    0x10, 0x6, 0xff, 0x70, 0xdf, 0xc1, 0x6f, 0xf7,
    0x0, 0x1d, 0xfe, 0xff, 0x70, 0x0, 0x1, 0xdf,
    0xf7, 0x0, 0x0, 0x0, 0x1c, 0x60, 0x0, 0x0,

    /* U+F00D "" */
    0x0, 0x0, 0x0, 0xc, 0xd1, 0x2, 0xea, 0xaf,
    0xd4, 0xef, 0x80, 0xaf, 0xff, 0x80, 0x2, 0xff,
    0xf1, 0x2, 0xef, 0xdf, 0xd1, 0xdf, 0x80, 0xaf,
    0xb6, 0x70, 0x0, 0x85,

    /* U+F011 "" */
    0x0, 0x0, 0xa6, 0x0, 0x0, 0x2, 0xa0, 0xea,
    0x29, 0x0, 0xe, 0xe1, 0xea, 0x5f, 0xa0, 0x7f,
    0x40, 0xea, 0x8, 0xf3, 0xbd, 0x0, 0xea, 0x1,
    0xf7, 0xcc, 0x0, 0xb7, 0x0, 0xf8, 0xaf, 0x0,
    0x0, 0x4, 0xf6, 0x4f, 0xa0, 0x0, 0x1d, 0xf1,
    0x9, 0xfd, 0x89, 0xef, 0x50, 0x0, 0x6d, 0xff,
    0xc4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F013 "" */
    0x0, 0x1, 0x88, 0x10, 0x0, 0x0, 0x5, 0xff,
    0x40, 0x0, 0x1e, 0xcf, 0xff, 0xfc, 0xd0, 0x7f,
    0xff, 0xdd, 0xff, 0xf7, 0x2d, 0xfa, 0x0, 0xbf,
    0xd1, 0xb, 0xf7, 0x0, 0x8f, 0xa0, 0x6f, 0xfe,
    0x55, 0xef, 0xf6, 0x4f, 0xff, 0xff, 0xff, 0xf3,
    0x6, 0x3a, 0xff, 0xa3, 0x60, 0x0, 0x3, 0xff,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F015 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0x90, 0xf8, 0x0, 0x0, 0x9, 0xf8, 0xec,
    0xf8, 0x0, 0x1, 0xbe, 0x5a, 0x5c, 0xf8, 0x0,
    0x2d, 0xd5, 0xef, 0xf6, 0xaf, 0x50, 0xda, 0x6f,
    0xff, 0xff, 0x87, 0xf1, 0x11, 0xff, 0xff, 0xff,
    0xf5, 0x10, 0x2, 0xff, 0xc3, 0x9f, 0xf6, 0x0,
    0x2, 0xff, 0xb0, 0x7f, 0xf6, 0x0, 0x1, 0xbb,
    0x70, 0x4b, 0xb3, 0x0,

    /* U+F019 "" */
    0x0, 0x0, 0x88, 0x20, 0x0, 0x0, 0x1, 0xff,
    0x60, 0x0, 0x0, 0x1, 0xff, 0x60, 0x0, 0x0,
    0x1, 0xff, 0x60, 0x0, 0x1, 0xff, 0xff, 0xff,
    0x60, 0x0, 0x5f, 0xff, 0xfa, 0x0, 0x0, 0x5,
    0xff, 0xb0, 0x0, 0x8b, 0xb9, 0x8b, 0x8b, 0xb9,
    0xdf, 0xff, 0xff, 0xfe, 0xdf, 0xcf, 0xff, 0xff,
    0xfc, 0xbe, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F01C "" */
    0x0, 0x24, 0x44, 0x44, 0x30, 0x0, 0x1, 0xef,
    0xff, 0xff, 0xf4, 0x0, 0xb, 0xc0, 0x0, 0x0,
    0x8e, 0x10, 0x6e, 0x10, 0x0, 0x0, 0xc, 0xa0,
    0xee, 0xcb, 0x10, 0xa, 0xcd, 0xf2, 0xff, 0xff,
    0xb8, 0x9f, 0xff, 0xf4, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xe1,

    /* U+F021 "" */
    0x0, 0x4, 0x87, 0x30, 0x5f, 0x2, 0xdf, 0xfe,
    0xfc, 0x7f, 0x1e, 0xd3, 0x0, 0x3c, 0xff, 0x9f,
    0x10, 0x5, 0xfe, 0xff, 0x44, 0x0, 0x2, 0x66,
    0x66, 0x12, 0x22, 0x0, 0x0, 0x11, 0xff, 0xff,
    0x50, 0x0, 0xda, 0xff, 0xa3, 0x10, 0x8, 0xf4,
    0xfc, 0xfb, 0x66, 0xbf, 0x80, 0xf5, 0x4c, 0xff,
    0xd5, 0x0, 0x31, 0x0, 0x0, 0x0, 0x0,

    /* U+F026 "" */
    0x0, 0x0, 0x70, 0x0, 0xbf, 0xab, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x3e, 0xf0, 0x0, 0x2c,

    /* U+F027 "" */
    0x0, 0x0, 0x70, 0x0, 0x0, 0xb, 0xf0, 0x0,
    0xab, 0xdf, 0xf0, 0x20, 0xff, 0xff, 0xf0, 0xa6,
    0xff, 0xff, 0xf0, 0x59, 0xff, 0xff, 0xf0, 0x92,
    0x0, 0x3e, 0xf0, 0x0, 0x0, 0x2, 0xc0, 0x0,

    /* U+F028 "" */
    0x0, 0x0, 0x0, 0x0, 0x40, 0x0, 0x0, 0x0,
    0x70, 0x0, 0xaa, 0x0, 0x0, 0xb, 0xf0, 0xa,
    0x4a, 0x70, 0xab, 0xdf, 0xf0, 0x23, 0xe2, 0xe0,
    0xff, 0xff, 0xf0, 0xa6, 0x95, 0xc2, 0xff, 0xff,
    0xf0, 0x59, 0x76, 0xc3, 0xff, 0xff, 0xf0, 0x92,
    0xc3, 0xe1, 0x0, 0x3e, 0xf0, 0x9, 0xa6, 0xb0,
    0x0, 0x2, 0xc0, 0x3, 0x3e, 0x20, 0x0, 0x0,
    0x0, 0x0, 0xc3, 0x0,

    /* U+F03E "" */
    0x24, 0x44, 0x44, 0x44, 0x42, 0xff, 0xef, 0xff,
    0xff, 0xff, 0xf3, 0xd, 0xff, 0xef, 0xff, 0xf8,
    0x4e, 0xfe, 0x25, 0xff, 0xff, 0x9d, 0xe2, 0x0,
    0x6f, 0xf9, 0x1, 0x20, 0x0, 0x4f, 0xf7, 0x44,
    0x44, 0x44, 0x7f, 0xcf, 0xff, 0xff, 0xff, 0xfc,

    /* U+F043 "" */
    0x0, 0x1a, 0x0, 0x0, 0x7, 0xf5, 0x0, 0x0,
    0xef, 0xc0, 0x0, 0x8f, 0xff, 0x60, 0x3f, 0xff,
    0xff, 0x1b, 0xff, 0xff, 0xf9, 0xfb, 0xff, 0xff,
    0xdd, 0x6e, 0xff, 0xfc, 0x7e, 0x59, 0xff, 0x60,
    0x9f, 0xff, 0x80, 0x0, 0x13, 0x10, 0x0,

    /* U+F048 "" */
    0x0, 0x0, 0x0, 0xe, 0x70, 0x3, 0xe4, 0xe7,
    0x4, 0xff, 0x5e, 0x75, 0xff, 0xf5, 0xec, 0xff,
    0xff, 0x5e, 0xff, 0xff, 0xf5, 0xea, 0xef, 0xff,
    0x5e, 0x71, 0xdf, 0xf5, 0xe7, 0x1, 0xcf, 0x59,
    0x50, 0x0, 0x92,

    /* U+F04B "" */
    0x88, 0x0, 0x0, 0x0, 0xf, 0xfe, 0x50, 0x0,
    0x0, 0xff, 0xff, 0xc3, 0x0, 0xf, 0xff, 0xff,
    0xf9, 0x10, 0xff, 0xff, 0xff, 0xfe, 0x5f, 0xff,
    0xff, 0xff, 0xfa, 0xff, 0xff, 0xff, 0xf8, 0xf,
    0xff, 0xff, 0xb2, 0x0, 0xff, 0xfd, 0x40, 0x0,
    0xe, 0xf7, 0x0, 0x0, 0x0, 0x11, 0x0, 0x0,
    0x0, 0x0,

    /* U+F04C "" */
    0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0x90, 0xdf,
    0xf9, 0xff, 0xfc, 0xf, 0xff, 0xcf, 0xff, 0xc0,
    0xff, 0xfc, 0xff, 0xfc, 0xf, 0xff, 0xcf, 0xff,
    0xc0, 0xff, 0xfc, 0xff, 0xfc, 0xf, 0xff, 0xcf,
    0xff, 0xc0, 0xff, 0xfc, 0xff, 0xfb, 0xf, 0xff,
    0xb8, 0xbb, 0x50, 0x8b, 0xb5,

    /* U+F04D "" */
    0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0xff, 0xff,
    0xf9, 0xff, 0xff, 0xff, 0xff, 0xcf, 0xff, 0xff,
    0xff, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xcf, 0xff,
    0xff, 0xff, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xcf,
    0xff, 0xff, 0xff, 0xfc, 0xff, 0xff, 0xff, 0xff,
    0xb8, 0xbb, 0xbb, 0xbb, 0xb5,

    /* U+F051 "" */
    0x0, 0x0, 0x0, 0xb, 0xa0, 0x0, 0xe7, 0xcf,
    0xb0, 0xe, 0x7c, 0xff, 0xc1, 0xe7, 0xcf, 0xff,
    0xdf, 0x7c, 0xff, 0xff, 0xf7, 0xcf, 0xff, 0x9e,
    0x7c, 0xff, 0x70, 0xe7, 0xcf, 0x60, 0xe, 0x77,
    0x50, 0x0, 0x95,

    /* U+F052 "" */
    0x0, 0x0, 0x2, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0x60, 0x0, 0x0, 0x9, 0xff, 0xf5, 0x0, 0x0,
    0x8f, 0xff, 0xff, 0x40, 0x6, 0xff, 0xff, 0xff,
    0xf3, 0xe, 0xff, 0xff, 0xff, 0xfa, 0x3, 0x66,
    0x66, 0x66, 0x62, 0xd, 0xff, 0xff, 0xff, 0xf9,
    0xf, 0xff, 0xff, 0xff, 0xfb, 0x6, 0x88, 0x88,
    0x88, 0x84,

    /* U+F053 "" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0xc7, 0x0, 0x1d,
    0xf5, 0x1, 0xdf, 0x50, 0x1d, 0xf5, 0x0, 0x4f,
    0xd0, 0x0, 0x6, 0xfc, 0x0, 0x0, 0x6f, 0xc0,
    0x0, 0x6, 0xf9, 0x0, 0x0, 0x51,

    /* U+F054 "" */
    0x0, 0x0, 0x0, 0x3e, 0x30, 0x0, 0x2e, 0xf3,
    0x0, 0x2, 0xef, 0x30, 0x0, 0x2e, 0xe3, 0x0,
    0x9, 0xf8, 0x0, 0x8f, 0xa0, 0x8, 0xfa, 0x0,
    0x5f, 0xa0, 0x0, 0x6, 0x0, 0x0,

    /* U+F067 "" */
    0x0, 0x0, 0x10, 0x0, 0x0, 0x0, 0x8f, 0x40,
    0x0, 0x0, 0x9, 0xf5, 0x0, 0x0, 0x0, 0x9f,
    0x50, 0x0, 0x9b, 0xbd, 0xfc, 0xbb, 0x6f, 0xff,
    0xff, 0xff, 0xfb, 0x13, 0x3a, 0xf7, 0x33, 0x10,
    0x0, 0x9f, 0x50, 0x0, 0x0, 0x9, 0xf5, 0x0,
    0x0, 0x0, 0x39, 0x10, 0x0,

    /* U+F068 "" */
    0xbd, 0xdd, 0xdd, 0xdd, 0x8e, 0xff, 0xff, 0xff,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F06E "" */
    0x0, 0x4, 0x8a, 0x95, 0x0, 0x0, 0x1, 0xcf,
    0x84, 0x6e, 0xe3, 0x0, 0x1e, 0xf5, 0x8, 0x72,
    0xff, 0x40, 0xbf, 0xe0, 0x2d, 0xf5, 0xbf, 0xe0,
    0xdf, 0xe3, 0xff, 0xf6, 0xaf, 0xf1, 0x4f, 0xf3,
    0xaf, 0xd1, 0xef, 0x70, 0x5, 0xfd, 0x31, 0x2b,
    0xf7, 0x0, 0x0, 0x19, 0xdf, 0xea, 0x30, 0x0,

    /* U+F070 "" */
    0xb6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xfa,
    0x16, 0xaa, 0x83, 0x0, 0x0, 0x3, 0xef, 0xe6,
    0x49, 0xfb, 0x0, 0x0, 0x1, 0xbe, 0x49, 0x28,
    0xfd, 0x0, 0x1d, 0x40, 0x8f, 0xfe, 0x1f, 0xf9,
    0x4, 0xff, 0x50, 0x5f, 0xf1, 0xff, 0xb0, 0xa,
    0xfc, 0x0, 0x2d, 0xdf, 0xf2, 0x0, 0xa, 0xfa,
    0x10, 0x1b, 0xf7, 0x0, 0x0, 0x4, 0xbe, 0xe4,
    0x8, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x0,

    /* U+F071 "" */
    0x0, 0x0, 0x2, 0xe6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xe1, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xff, 0x90, 0x0, 0x0, 0x0, 0xd, 0xe7, 0xcf,
    0x20, 0x0, 0x0, 0x7, 0xfc, 0x8, 0xfb, 0x0,
    0x0, 0x1, 0xef, 0xd0, 0x9f, 0xf4, 0x0, 0x0,
    0x9f, 0xff, 0x5c, 0xff, 0xd0, 0x0, 0x2f, 0xff,
    0xe1, 0xaf, 0xff, 0x60, 0xb, 0xff, 0xfe, 0x2b,
    0xff, 0xfe, 0x0, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xf2, 0x1, 0x33, 0x33, 0x33, 0x33, 0x32, 0x0,

    /* U+F074 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xc1, 0xef, 0xd1, 0x3, 0xef, 0xfc, 0x99,
    0xfb, 0x2e, 0xec, 0xf8, 0x0, 0x54, 0xde, 0x25,
    0x70, 0x0, 0xc, 0xf4, 0x1, 0x10, 0x0, 0xbf,
    0x5c, 0x78, 0xd1, 0xff, 0xf6, 0xa, 0xff, 0xfd,
    0x78, 0x60, 0x0, 0x7c, 0xf6, 0x0, 0x0, 0x0,
    0x5, 0x60,

    /* U+F077 "" */
    0x0, 0x0, 0x30, 0x0, 0x0, 0x0, 0xaf, 0x60,
    0x0, 0x0, 0xaf, 0xef, 0x60, 0x0, 0xaf, 0x90,
    0xcf, 0x60, 0x9f, 0x80, 0x0, 0xcf, 0x57, 0x80,
    0x0, 0x0, 0xa4,

    /* U+F078 "" */
    0x11, 0x0, 0x0, 0x2, 0xc, 0xe2, 0x0, 0x5,
    0xf8, 0x3f, 0xe2, 0x5, 0xfd, 0x10, 0x3f, 0xe7,
    0xfd, 0x10, 0x0, 0x3f, 0xfd, 0x10, 0x0, 0x0,
    0x3b, 0x10, 0x0,

    /* U+F079 "" */
    0x0, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xc3, 0xff, 0xff, 0xf5, 0x0, 0xbe, 0xfe, 0xb3,
    0x44, 0x4e, 0x60, 0x4, 0x3f, 0x34, 0x0, 0x0,
    0xd6, 0x0, 0x2, 0xf2, 0x0, 0x0, 0xd, 0x60,
    0x0, 0x2f, 0x20, 0x0, 0x8c, 0xea, 0xf1, 0x1,
    0xff, 0xff, 0xf7, 0xdf, 0xf7, 0x0, 0x4, 0x44,
    0x44, 0x11, 0xc7, 0x0,

    /* U+F07B "" */
    0x58, 0x88, 0x20, 0x0, 0x0, 0xff, 0xff, 0xe4,
    0x44, 0x41, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xcf, 0xff, 0xff, 0xff, 0xfc,

    /* U+F093 "" */
    0x0, 0x0, 0x33, 0x0, 0x0, 0x0, 0x3, 0xff,
    0x40, 0x0, 0x0, 0x3f, 0xff, 0xf4, 0x0, 0x2,
    0xff, 0xff, 0xff, 0x20, 0x0, 0x36, 0xff, 0x63,
    0x0, 0x0, 0x4, 0xff, 0x40, 0x0, 0x0, 0x4,
    0xff, 0x40, 0x0, 0x9a, 0xa5, 0xff, 0x5a, 0xa9,
    0xff, 0xff, 0xdd, 0xfe, 0xdf, 0xff, 0xff, 0xff,
    0xfc, 0xbe, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F095 "" */
    0x0, 0x0, 0x0, 0x0, 0xa8, 0x40, 0x0, 0x0,
    0x0, 0x5f, 0xff, 0x0, 0x0, 0x0, 0xc, 0xff,
    0xe0, 0x0, 0x0, 0x0, 0x6f, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0x50, 0x0, 0x0, 0x0, 0x5f,
    0xd0, 0x0, 0x39, 0x10, 0x4f, 0xf4, 0x0, 0xbf,
    0xfc, 0x9f, 0xf6, 0x0, 0xd, 0xff, 0xff, 0xe4,
    0x0, 0x0, 0x9f, 0xfd, 0x81, 0x0, 0x0, 0x1,
    0x31, 0x0, 0x0, 0x0, 0x0,

    /* U+F0C4 "" */
    0x0, 0x0, 0x0, 0x0, 0x6, 0xfe, 0x30, 0x5,
    0xc6, 0xe7, 0xbb, 0x5, 0xff, 0x4d, 0xbd, 0xb4,
    0xff, 0x40, 0x3c, 0xff, 0xff, 0x40, 0x0, 0x8,
    0xff, 0xb0, 0x0, 0x6f, 0xff, 0xdf, 0x80, 0xe,
    0x7b, 0xb2, 0xef, 0x80, 0xdb, 0xd9, 0x2, 0xef,
    0x73, 0xca, 0x10, 0x2, 0x72,

    /* U+F0C5 "" */
    0x0, 0x5d, 0xdd, 0x48, 0x0, 0x8, 0xff, 0xf6,
    0xf8, 0xcc, 0x8f, 0xff, 0x84, 0x3f, 0xe8, 0xff,
    0xff, 0xfc, 0xfe, 0x8f, 0xff, 0xff, 0xcf, 0xe8,
    0xff, 0xff, 0xfc, 0xfe, 0x8f, 0xff, 0xff, 0xcf,
    0xe7, 0xff, 0xff, 0xfc, 0xff, 0x46, 0x66, 0x66,
    0x3f, 0xff, 0xff, 0xf4, 0x0, 0x34, 0x44, 0x43,
    0x0, 0x0,

    /* U+F0C7 "" */
    0x2, 0x22, 0x22, 0x0, 0xe, 0xff, 0xff, 0xfe,
    0x20, 0xf5, 0x22, 0x22, 0xfe, 0x1f, 0x40, 0x0,
    0xe, 0xf8, 0xf7, 0x44, 0x44, 0xff, 0x9f, 0xff,
    0xff, 0xff, 0xf9, 0xff, 0xf6, 0xc, 0xff, 0x9f,
    0xff, 0x20, 0x9f, 0xf9, 0xff, 0xfc, 0x7f, 0xff,
    0x9a, 0xdd, 0xdd, 0xdd, 0xd4,

    /* U+F0C9 "" */
    0x67, 0x77, 0x77, 0x77, 0x4e, 0xff, 0xff, 0xff,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x6, 0x77, 0x77,
    0x77, 0x74, 0xef, 0xff, 0xff, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x45, 0x55, 0x55, 0x55, 0x3f,
    0xff, 0xff, 0xff, 0xfb, 0x11, 0x11, 0x11, 0x11,
    0x0,

    /* U+F0E0 "" */
    0x58, 0x88, 0x88, 0x88, 0x84, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x6f, 0xff, 0xff, 0xff, 0xf6, 0xc5,
    0xdf, 0xff, 0xfd, 0x5c, 0xfe, 0x6a, 0xff, 0xa5,
    0xef, 0xff, 0xf9, 0x55, 0x9f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xcf, 0xff, 0xff, 0xff, 0xfc,

    /* U+F0E7 "" */
    0x3, 0xaa, 0xa2, 0x0, 0x7, 0xff, 0xf2, 0x0,
    0x9, 0xff, 0xd0, 0x0, 0xb, 0xff, 0xd8, 0x81,
    0xe, 0xff, 0xff, 0xe1, 0xe, 0xff, 0xff, 0x60,
    0x0, 0x5, 0xfd, 0x0, 0x0, 0x9, 0xf4, 0x0,
    0x0, 0xd, 0xa0, 0x0, 0x0, 0xf, 0x20, 0x0,
    0x0, 0x2, 0x0, 0x0,

    /* U+F0EA "" */
    0x1, 0x79, 0x11, 0x0, 0xf, 0xfc, 0x9f, 0xf4,
    0x0, 0xff, 0xfd, 0xcc, 0x30, 0xf, 0xfa, 0x79,
    0x93, 0x40, 0xff, 0x8e, 0xff, 0x6f, 0x5f, 0xf8,
    0xef, 0xf7, 0x64, 0xff, 0x8e, 0xff, 0xff, 0xcf,
    0xf8, 0xef, 0xff, 0xfc, 0x46, 0x3e, 0xff, 0xff,
    0xc0, 0x0, 0xdf, 0xff, 0xfc, 0x0, 0x2, 0x44,
    0x44, 0x20,

    /* U+F0F3 "" */
    0x0, 0x1, 0x90, 0x0, 0x0, 0x2, 0xaf, 0x81,
    0x0, 0x2, 0xff, 0xff, 0xd0, 0x0, 0x9f, 0xff,
    0xff, 0x50, 0xc, 0xff, 0xff, 0xf8, 0x0, 0xef,
    0xff, 0xff, 0xa0, 0x3f, 0xff, 0xff, 0xfe, 0xd,
    0xff, 0xff, 0xff, 0xf9, 0x46, 0x66, 0x66, 0x66,
    0x20, 0x0, 0xbf, 0x70, 0x0, 0x0, 0x0, 0x30,
    0x0, 0x0,

    /* U+F11C "" */
    0x24, 0x44, 0x44, 0x44, 0x44, 0x30, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf3, 0xf4, 0xa0, 0xa0, 0xb0,
    0xb0, 0xf4, 0xff, 0xbe, 0xae, 0xae, 0xaf, 0xf4,
    0xff, 0x3a, 0xa, 0xa, 0xf, 0xf4, 0xfb, 0xea,
    0xaa, 0xaa, 0xea, 0xf4, 0xf7, 0xb4, 0x44, 0x44,
    0xc4, 0xf4, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xe1,

    /* U+F124 "" */
    0x0, 0x0, 0x0, 0x0, 0x29, 0x70, 0x0, 0x0,
    0x0, 0x3a, 0xff, 0xe0, 0x0, 0x0, 0x4b, 0xff,
    0xff, 0x70, 0x0, 0x5d, 0xff, 0xff, 0xff, 0x10,
    0xc, 0xff, 0xff, 0xff, 0xf9, 0x0, 0xa, 0xee,
    0xef, 0xff, 0xf2, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0xa, 0xff, 0x30, 0x0,
    0x0, 0x0, 0xa, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x20,
    0x0, 0x0,

    /* U+F15B "" */
    0xef, 0xff, 0x5b, 0x0, 0xff, 0xff, 0x6f, 0xb0,
    0xff, 0xff, 0x68, 0x83, 0xff, 0xff, 0xfd, 0xd6,
    0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xf7,
    0x0, 0x0, 0x0, 0x0,

    /* U+F1EB "" */
    0x0, 0x0, 0x1, 0x10, 0x0, 0x0, 0x0, 0x5,
    0xbf, 0xff, 0xfd, 0x81, 0x0, 0x2c, 0xfe, 0xa8,
    0x78, 0xcf, 0xf7, 0xd, 0xf7, 0x0, 0x0, 0x0,
    0x3c, 0xf5, 0x22, 0x5, 0xbe, 0xfd, 0x81, 0x5,
    0x0, 0x9, 0xfe, 0xa9, 0xcf, 0xe2, 0x0, 0x0,
    0x37, 0x0, 0x0, 0x38, 0x0, 0x0, 0x0, 0x0,
    0x8c, 0x20, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x58, 0x10, 0x0,
    0x0,

    /* U+F240 "" */
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xf9, 0xf, 0x75,
    0x55, 0x55, 0x55, 0x5a, 0xf2, 0xf6, 0xff, 0xff,
    0xff, 0xfd, 0x4f, 0x5f, 0x6f, 0xff, 0xff, 0xff,
    0xd1, 0xf5, 0xf5, 0x77, 0x77, 0x77, 0x76, 0x8f,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0, 0x24,
    0x44, 0x44, 0x44, 0x44, 0x41, 0x0,

    /* U+F241 "" */
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xf9, 0xf, 0x75,
    0x55, 0x55, 0x54, 0x4a, 0xf2, 0xf6, 0xff, 0xff,
    0xff, 0x0, 0x4f, 0x5f, 0x6f, 0xff, 0xff, 0xf0,
    0x1, 0xf5, 0xf5, 0x77, 0x77, 0x77, 0x0, 0x8f,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0, 0x24,
    0x44, 0x44, 0x44, 0x44, 0x41, 0x0,

    /* U+F242 "" */
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xf9, 0xf, 0x75,
    0x55, 0x54, 0x44, 0x4a, 0xf2, 0xf6, 0xff, 0xff,
    0x20, 0x0, 0x4f, 0x5f, 0x6f, 0xff, 0xf2, 0x0,
    0x1, 0xf5, 0xf5, 0x77, 0x77, 0x10, 0x0, 0x8f,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0, 0x24,
    0x44, 0x44, 0x44, 0x44, 0x41, 0x0,

    /* U+F243 "" */
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xf9, 0xf, 0x75,
    0x54, 0x44, 0x44, 0x4a, 0xf2, 0xf6, 0xff, 0x50,
    0x0, 0x0, 0x4f, 0x5f, 0x6f, 0xf5, 0x0, 0x0,
    0x1, 0xf5, 0xf5, 0x77, 0x20, 0x0, 0x0, 0x8f,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0, 0x24,
    0x44, 0x44, 0x44, 0x44, 0x41, 0x0,

    /* U+F244 "" */
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xfa, 0xf, 0x74,
    0x44, 0x44, 0x44, 0x4a, 0xf2, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0x5f, 0x40, 0x0, 0x0, 0x0,
    0x1, 0xf5, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0, 0x24,
    0x44, 0x44, 0x44, 0x44, 0x41, 0x0,

    /* U+F287 "" */
    0x0, 0x0, 0x0, 0x5b, 0x10, 0x0, 0x0, 0x0,
    0x3, 0xbd, 0xf4, 0x0, 0x0, 0x0, 0x0, 0xa1,
    0x3, 0x0, 0x0, 0xa, 0xf7, 0x39, 0x0, 0x0,
    0x7, 0x60, 0xff, 0xea, 0xbf, 0xaa, 0xaa, 0xdf,
    0x45, 0xa3, 0x0, 0x93, 0x0, 0x4, 0x10, 0x0,
    0x0, 0x1, 0xb8, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x2, 0x9f, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x10, 0x0, 0x0,

    /* U+F293 "" */
    0x0, 0x6b, 0xca, 0x40, 0x0, 0x9f, 0xf6, 0xff,
    0x40, 0x1f, 0xff, 0x26, 0xfb, 0x4, 0xf6, 0xb4,
    0x6b, 0xf0, 0x6f, 0xf4, 0x6, 0xff, 0x6, 0xff,
    0x90, 0xbf, 0xf0, 0x5f, 0x95, 0x34, 0xcf, 0x2,
    0xfb, 0xf3, 0x4d, 0xc0, 0xc, 0xff, 0x3d, 0xf7,
    0x0, 0x1b, 0xfe, 0xf9, 0x0, 0x0, 0x0, 0x10,
    0x0, 0x0,

    /* U+F2ED "" */
    0x12, 0x3b, 0xca, 0x22, 0x1f, 0xff, 0xff, 0xff,
    0xfb, 0x36, 0x66, 0x66, 0x66, 0x16, 0xff, 0xff,
    0xff, 0xf2, 0x6f, 0x6f, 0x6f, 0x7f, 0x26, 0xf6,
    0xf6, 0xf7, 0xf2, 0x6f, 0x6f, 0x6f, 0x7f, 0x26,
    0xf6, 0xf6, 0xf7, 0xf2, 0x6f, 0x6f, 0x6f, 0x7f,
    0x24, 0xff, 0xff, 0xff, 0xf1, 0x3, 0x44, 0x44,
    0x42, 0x0,

    /* U+F304 "" */
    0x0, 0x0, 0x0, 0x0, 0x97, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xf8, 0x0, 0x0, 0x0, 0xa5, 0xef,
    0xe0, 0x0, 0x0, 0xbf, 0xe5, 0xd4, 0x0, 0x0,
    0xbf, 0xff, 0xe0, 0x0, 0x0, 0xbf, 0xff, 0xf4,
    0x0, 0x0, 0xbf, 0xff, 0xf4, 0x0, 0x0, 0xaf,
    0xff, 0xf4, 0x0, 0x0, 0xd, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x2,
    0x20, 0x0, 0x0, 0x0, 0x0,

    /* U+F55A "" */
    0x0, 0x5, 0x88, 0x88, 0x88, 0x86, 0x0, 0x8,
    0xff, 0xff, 0xff, 0xff, 0xf6, 0x8, 0xff, 0xf9,
    0x6f, 0x69, 0xff, 0x88, 0xff, 0xff, 0xc1, 0x21,
    0xcf, 0xf8, 0xdf, 0xff, 0xff, 0x50, 0x5f, 0xff,
    0x82, 0xef, 0xff, 0x71, 0x91, 0x7f, 0xf8, 0x2,
    0xef, 0xfe, 0xdf, 0xde, 0xff, 0x70, 0x2, 0xdf,
    0xff, 0xff, 0xff, 0xe3,

    /* U+F7C2 "" */
    0x1, 0xdf, 0xff, 0xe5, 0x1d, 0x6c, 0x5a, 0xab,
    0xdf, 0x3b, 0x18, 0x8b, 0xff, 0xdf, 0xde, 0xeb,
    0xff, 0xff, 0xff, 0xfb, 0xff, 0xff, 0xff, 0xfb,
    0xff, 0xff, 0xff, 0xfb, 0xff, 0xff, 0xff, 0xfb,
    0xff, 0xff, 0xff, 0xfb, 0xbf, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0,

    /* U+F8A2 "" */
    0x0, 0x0, 0x0, 0x0, 0xa, 0x0, 0x8, 0x10,
    0x0, 0x7, 0xf0, 0xb, 0xf2, 0x0, 0x0, 0x8f,
    0xb, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x7f, 0xfa,
    0x99, 0x99, 0x99, 0x0, 0x6f, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x30, 0x0, 0x0, 0x0, 0x0,

    /* U+FF1A "：" */
    0x0, 0x16, 0x0, 0x0, 0x0, 0x16
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 43, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 36, .box_w = 2, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7, .adv_w = 48, .box_w = 3, .box_h = 3, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 12, .adv_w = 99, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 37, .adv_w = 88, .box_w = 6, .box_h = 10, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 67, .adv_w = 120, .box_w = 8, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 95, .adv_w = 110, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 123, .adv_w = 27, .box_w = 2, .box_h = 3, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 126, .adv_w = 51, .box_w = 4, .box_h = 9, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 144, .adv_w = 51, .box_w = 3, .box_h = 9, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 158, .adv_w = 66, .box_w = 4, .box_h = 4, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 166, .adv_w = 88, .box_w = 5, .box_h = 5, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 179, .adv_w = 36, .box_w = 2, .box_h = 3, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 182, .adv_w = 79, .box_w = 5, .box_h = 1, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 185, .adv_w = 35, .box_w = 2, .box_h = 1, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 186, .adv_w = 59, .box_w = 4, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 200, .adv_w = 88, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 221, .adv_w = 88, .box_w = 3, .box_h = 7, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 232, .adv_w = 88, .box_w = 5, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 250, .adv_w = 88, .box_w = 5, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 268, .adv_w = 88, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 289, .adv_w = 88, .box_w = 5, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 307, .adv_w = 88, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 328, .adv_w = 88, .box_w = 5, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 346, .adv_w = 88, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 367, .adv_w = 88, .box_w = 5, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 385, .adv_w = 39, .box_w = 2, .box_h = 5, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 390, .adv_w = 40, .box_w = 2, .box_h = 7, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 397, .adv_w = 88, .box_w = 5, .box_h = 5, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 410, .adv_w = 88, .box_w = 5, .box_h = 3, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 418, .adv_w = 88, .box_w = 5, .box_h = 5, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 431, .adv_w = 67, .box_w = 4, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 447, .adv_w = 158, .box_w = 10, .box_h = 9, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 492, .adv_w = 104, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 517, .adv_w = 101, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 538, .adv_w = 102, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 563, .adv_w = 113, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 588, .adv_w = 93, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 609, .adv_w = 88, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 630, .adv_w = 110, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 655, .adv_w = 116, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 680, .adv_w = 39, .box_w = 2, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 687, .adv_w = 69, .box_w = 4, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 701, .adv_w = 104, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 726, .adv_w = 88, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 747, .adv_w = 136, .box_w = 8, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 775, .adv_w = 116, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 800, .adv_w = 120, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 825, .adv_w = 92, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 846, .adv_w = 120, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 882, .adv_w = 98, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 907, .adv_w = 88, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 928, .adv_w = 88, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 949, .adv_w = 113, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 974, .adv_w = 102, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 999, .adv_w = 153, .box_w = 10, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1034, .adv_w = 102, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1059, .adv_w = 95, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1080, .adv_w = 91, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1101, .adv_w = 50, .box_w = 3, .box_h = 9, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 1115, .adv_w = 59, .box_w = 4, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1129, .adv_w = 50, .box_w = 3, .box_h = 9, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 1143, .adv_w = 72, .box_w = 5, .box_h = 4, .ofs_x = 0, .ofs_y = 4},
    {.bitmap_index = 1153, .adv_w = 64, .box_w = 4, .box_h = 1, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1155, .adv_w = 42, .box_w = 3, .box_h = 2, .ofs_x = 0, .ofs_y = 6},
    {.bitmap_index = 1158, .adv_w = 86, .box_w = 5, .box_h = 5, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1171, .adv_w = 96, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1192, .adv_w = 78, .box_w = 5, .box_h = 5, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1205, .adv_w = 96, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1226, .adv_w = 86, .box_w = 5, .box_h = 5, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1239, .adv_w = 52, .box_w = 4, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1255, .adv_w = 96, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1276, .adv_w = 91, .box_w = 5, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1294, .adv_w = 37, .box_w = 2, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1301, .adv_w = 37, .box_w = 4, .box_h = 9, .ofs_x = -2, .ofs_y = -2},
    {.bitmap_index = 1319, .adv_w = 80, .box_w = 5, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1337, .adv_w = 36, .box_w = 2, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1344, .adv_w = 135, .box_w = 8, .box_h = 5, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1364, .adv_w = 91, .box_w = 5, .box_h = 5, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1377, .adv_w = 94, .box_w = 6, .box_h = 5, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1392, .adv_w = 96, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1413, .adv_w = 96, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1434, .adv_w = 57, .box_w = 4, .box_h = 5, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1444, .adv_w = 72, .box_w = 5, .box_h = 5, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1457, .adv_w = 55, .box_w = 4, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1471, .adv_w = 91, .box_w = 5, .box_h = 5, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1484, .adv_w = 79, .box_w = 5, .box_h = 5, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1497, .adv_w = 122, .box_w = 8, .box_h = 5, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1517, .adv_w = 76, .box_w = 5, .box_h = 5, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1530, .adv_w = 79, .box_w = 5, .box_h = 7, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1548, .adv_w = 74, .box_w = 5, .box_h = 5, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1561, .adv_w = 54, .box_w = 4, .box_h = 9, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 1579, .adv_w = 25, .box_w = 2, .box_h = 9, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 1588, .adv_w = 54, .box_w = 4, .box_h = 9, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 1606, .adv_w = 88, .box_w = 5, .box_h = 2, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 1611, .adv_w = 160, .box_w = 8, .box_h = 9, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 1647, .adv_w = 160, .box_w = 8, .box_h = 9, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 1683, .adv_w = 160, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1728, .adv_w = 160, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1783, .adv_w = 160, .box_w = 10, .box_h = 9, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 1828, .adv_w = 160, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 1878, .adv_w = 160, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 1928, .adv_w = 160, .box_w = 9, .box_h = 9, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 1969, .adv_w = 160, .box_w = 10, .box_h = 9, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2014, .adv_w = 160, .box_w = 10, .box_h = 9, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2059, .adv_w = 160, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2109, .adv_w = 160, .box_w = 10, .box_h = 9, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2154, .adv_w = 160, .box_w = 9, .box_h = 10, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 2199, .adv_w = 160, .box_w = 10, .box_h = 9, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2244, .adv_w = 160, .box_w = 10, .box_h = 9, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2289, .adv_w = 160, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2339, .adv_w = 160, .box_w = 10, .box_h = 9, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2384, .adv_w = 160, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2434, .adv_w = 160, .box_w = 10, .box_h = 9, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2479, .adv_w = 160, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2534, .adv_w = 160, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2589, .adv_w = 160, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2639, .adv_w = 160, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2700, .adv_w = 160, .box_w = 10, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2740, .adv_w = 160, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2790, .adv_w = 160, .box_w = 10, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2830, .adv_w = 110, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2858, .adv_w = 160, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2913, .adv_w = 160, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2968, .adv_w = 180, .box_w = 12, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3028, .adv_w = 160, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3083, .adv_w = 180, .box_w = 12, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3131, .adv_w = 160, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3186, .adv_w = 80, .box_w = 5, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3206, .adv_w = 120, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3238, .adv_w = 180, .box_w = 12, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3298, .adv_w = 160, .box_w = 10, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3338, .adv_w = 110, .box_w = 7, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3377, .adv_w = 140, .box_w = 7, .box_h = 10, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 3412, .adv_w = 140, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3462, .adv_w = 140, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3507, .adv_w = 140, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3552, .adv_w = 140, .box_w = 7, .box_h = 10, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 3587, .adv_w = 140, .box_w = 10, .box_h = 10, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 3637, .adv_w = 100, .box_w = 6, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3667, .adv_w = 100, .box_w = 6, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3697, .adv_w = 140, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3742, .adv_w = 140, .box_w = 9, .box_h = 3, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 3756, .adv_w = 180, .box_w = 12, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3804, .adv_w = 200, .box_w = 13, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3876, .adv_w = 180, .box_w = 13, .box_h = 11, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 3948, .adv_w = 160, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3998, .adv_w = 140, .box_w = 9, .box_h = 6, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 4025, .adv_w = 140, .box_w = 9, .box_h = 6, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 4052, .adv_w = 200, .box_w = 13, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4104, .adv_w = 160, .box_w = 10, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4144, .adv_w = 160, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4199, .adv_w = 160, .box_w = 11, .box_h = 11, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 4260, .adv_w = 140, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4305, .adv_w = 140, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4355, .adv_w = 140, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4400, .adv_w = 140, .box_w = 9, .box_h = 9, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4441, .adv_w = 160, .box_w = 10, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4481, .adv_w = 100, .box_w = 8, .box_h = 11, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 4525, .adv_w = 140, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4575, .adv_w = 140, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4625, .adv_w = 180, .box_w = 12, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4673, .adv_w = 160, .box_w = 12, .box_h = 11, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 4739, .adv_w = 120, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4783, .adv_w = 200, .box_w = 13, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4848, .adv_w = 200, .box_w = 13, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4894, .adv_w = 200, .box_w = 13, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4940, .adv_w = 200, .box_w = 13, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4986, .adv_w = 200, .box_w = 13, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5032, .adv_w = 200, .box_w = 13, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5078, .adv_w = 200, .box_w = 13, .box_h = 9, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5137, .adv_w = 140, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5187, .adv_w = 140, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5237, .adv_w = 160, .box_w = 11, .box_h = 11, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 5298, .adv_w = 200, .box_w = 13, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5350, .adv_w = 120, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5394, .adv_w = 161, .box_w = 11, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5433, .adv_w = 160, .box_w = 2, .box_h = 6, .ofs_x = 1, .ofs_y = 0}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_1[] = {
    0x0, 0x3cd, 0x3ea, 0x420, 0x55e, 0xada, 0x1079, 0x1126,
    0x18fd, 0x196f, 0x1f1e, 0x242c, 0x2708, 0x30a6, 0x36b0, 0x3a1f,
    0x3a3b, 0x3d91, 0x3da8, 0x4166, 0x41a3, 0x41f2, 0xa1d4, 0xa1db,
    0xa1de, 0xa1df, 0xa1e0, 0xa1e4, 0xa1e6, 0xa1e8, 0xa1ec, 0xa1ef,
    0xa1f4, 0xa1f9, 0xa1fa, 0xa1fb, 0xa211, 0xa216, 0xa21b, 0xa21e,
    0xa21f, 0xa220, 0xa224, 0xa225, 0xa226, 0xa227, 0xa23a, 0xa23b,
    0xa241, 0xa243, 0xa244, 0xa247, 0xa24a, 0xa24b, 0xa24c, 0xa24e,
    0xa266, 0xa268, 0xa297, 0xa298, 0xa29a, 0xa29c, 0xa2b3, 0xa2ba,
    0xa2bd, 0xa2c6, 0xa2ef, 0xa2f7, 0xa32e, 0xa3be, 0xa413, 0xa414,
    0xa415, 0xa416, 0xa417, 0xa45a, 0xa466, 0xa4c0, 0xa4d7, 0xa72d,
    0xa995, 0xaa75, 0xb0ed
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 20013, .range_length = 45294, .glyph_id_start = 96,
        .unicode_list = unicode_list_1, .glyph_id_ofs_list = NULL, .list_length = 83, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/


/*Map glyph_ids to kern left classes*/
static const uint8_t kern_left_class_mapping[] =
{
    0, 0, 0, 1, 0, 0, 0, 0,
    1, 2, 3, 0, 4, 0, 4, 0,
    5, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 4,
    6, 7, 8, 9, 10, 7, 11, 12,
    13, 14, 14, 15, 16, 17, 14, 14,
    7, 18, 0, 19, 20, 21, 15, 5,
    22, 23, 24, 25, 2, 8, 3, 0,
    0, 0, 26, 27, 28, 29, 30, 31,
    32, 26, 0, 33, 34, 29, 26, 26,
    27, 27, 0, 35, 36, 37, 32, 38,
    38, 39, 38, 40, 2, 0, 3, 4,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0
};

/*Map glyph_ids to kern right classes*/
static const uint8_t kern_right_class_mapping[] =
{
    0, 1, 0, 2, 0, 0, 0, 0,
    2, 0, 3, 0, 4, 5, 4, 5,
    6, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 4, 0, 0,
    7, 8, 6, 9, 8, 9, 9, 9,
    8, 9, 9, 10, 9, 9, 9, 9,
    8, 9, 8, 9, 11, 12, 13, 14,
    15, 16, 17, 18, 0, 14, 3, 0,
    5, 0, 19, 20, 21, 21, 21, 22,
    21, 20, 0, 23, 20, 20, 24, 24,
    21, 0, 21, 24, 25, 26, 27, 28,
    28, 29, 28, 30, 0, 0, 3, 4,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0
};

/*Kern values between classes*/
static const int8_t kern_class_values[] =
{
    0, 0, 0, 0, 0, 0, 2, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 3, 0, 3, 3, 2,
    0, 2, 0, 0, 8, 0, 0, 3,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 3, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -8, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -6, 4, 3, -6, -21, -13, 5, -4,
    0, -20, 0, 3, 0, 0, 0, 0,
    0, 0, -12, 0, -10, -3, 0, -7,
    -9, -1, -7, -5, -7, -5, 0, 0,
    0, -4, -16, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -4, 0, 0,
    -7, -4, 0, 0, 0, -6, 0, -5,
    0, -4, -3, -4, -8, -4, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -6, -17, 0, -9, 4, 0,
    -11, -4, 0, 0, 0, -13, -2, -13,
    -9, 0, -15, 3, 0, 0, -1, 0,
    0, 0, 0, 0, 0, -5, 0, 0,
    0, -1, 0, 0, 0, -2, 0, 0,
    0, 2, 0, -5, 0, -6, -2, 0,
    -8, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 4,
    0, -4, 3, 0, 5, -2, 0, 0,
    0, 1, 0, 1, 0, 0, 1, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -3, 0, 0, 0, 0, 0, 0,
    1, 0, 1, -2, 0, 1, 0, 0,
    0, -2, 0, 0, -2, 0, -3, 0,
    -2, -2, 0, 0, -2, -2, -2, -3,
    -1, 0, -2, 4, 0, 0, -21, -10,
    5, -2, 0, -26, 0, 3, 0, 0,
    0, 0, 0, 0, -9, 0, -6, -3,
    0, -5, 0, -3, 0, -4, -7, -6,
    0, 0, 0, 0, 2, 0, 1, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 1, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -5, -2, 0, 0, 0, -5, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -6, 0, 0, -16, 3, 0,
    1, -8, -1, 0, -1, 0, -3, 0,
    0, 0, 0, 0, -4, 0, -5, -6,
    0, -2, -2, -4, -5, -7, -4, 0,
    -7, -15, 0, -14, 4, 0, -12, -8,
    0, 3, -2, -21, -8, -24, -17, 0,
    -28, 0, -3, 0, -5, -4, 0, -1,
    -1, -5, -5, -14, 0, 0, -2, 3,
    0, 1, -22, -11, 2, 0, 0, -25,
    0, 0, 0, 1, 1, -2, 0, -5,
    -6, 0, -5, 0, 0, 0, 0, 0,
    0, 2, 0, 0, 0, -1, 0, -1,
    7, 0, -1, -2, 0, 0, 1, -1,
    -2, -3, -2, 0, -7, 0, 0, 0,
    -1, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    1, 0, 0, 0, 3, 0, 0, -2,
    0, 0, -2, 1, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -5, 4, 0, -14, -16, -13, 5, -5,
    0, -22, 0, 3, 0, 3, 3, 0,
    0, 0, -19, 0, -16, -7, 0, -14,
    -18, -7, -14, -14, -16, -14, -2, 3,
    0, -3, -12, -9, 0, -3, 0, -11,
    0, 3, 0, 0, 0, 0, 0, 0,
    -12, 0, -9, -2, 0, -7, -6, 0,
    -4, -2, -4, -5, 0, 0, 3, -13,
    2, 0, 4, -4, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -3, 0,
    -4, 0, 0, -2, -2, -3, -4, -6,
    0, 0, -5, 3, 3, -11, -19, -15,
    2, -8, 0, -22, -2, 0, 0, 0,
    0, 0, 0, 0, -17, 0, -15, -7,
    0, -12, -14, -4, -11, -10, -10, -11,
    0, 0, 2, -7, 3, 0, 1, -5,
    0, 0, -1, 0, 0, 0, 0, 0,
    0, 0, -1, 0, -3, 0, 0, 0,
    0, 0, 0, -5, 0, 0, 0, -6,
    0, 0, 0, 0, -4, 0, 0, 0,
    0, -13, 0, -10, -9, -1, -14, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -1, 0, 0, 0, -8, 0, -2,
    -5, 0, -5, 0, 0, 0, 0, -16,
    0, -10, -9, -4, -15, 0, -2, 0,
    0, -1, 0, 0, 0, -1, 0, -2,
    -3, -4, 0, 1, 0, 4, 4, 0,
    -2, 0, 0, 0, 0, -10, 0, -6,
    -3, 3, -10, 0, 0, 0, 0, 1,
    0, 0, 0, 3, 0, 0, 2, 2,
    0, 0, 2, 0, 0, 0, 2, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -1,
    0, 3, 0, 0, -4, 0, 0, 0,
    0, -10, 0, -8, -6, -1, -11, 0,
    0, 0, 0, 0, 0, 0, 2, 0,
    0, 1, 0, 0, 0, 7, 0, -1,
    -11, 0, 7, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -3, 0,
    -3, 0, 0, 0, 0, 1, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -4, 0, 0, 0, 0, -14, 0, -7,
    -7, 0, -12, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 2, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 4, 0,
    0, 0, 0, 0, 0, 0, -3, 0,
    0, -3, 4, 0, -6, 0, 0, 0,
    0, -13, 0, -7, -5, 0, -10, 0,
    -4, 0, -3, 0, 0, 0, -2, 0,
    0, 0, 0, 0, 0, 4, 0, 1,
    -15, -5, -4, 0, 0, -16, 0, 0,
    0, -5, 0, -5, -9, 0, -7, 0,
    -5, 0, 0, 0, -1, 4, 0, 0,
    0, 0, 0, -5, 0, 0, 0, 0,
    -5, 0, 0, 0, 0, -15, 0, -10,
    -8, 0, -15, 0, 0, 0, 0, 0,
    0, 0, 1, 0, 0, -1, 0, 2,
    0, 0, 1, -2, 4, 0, -4, 0,
    0, 0, 0, -13, 0, -6, 0, 0,
    -10, 0, 0, 0, -2, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -12, -5, -3, 0, 0, -11,
    0, -14, 0, -5, -2, -6, -10, 0,
    -2, 0, -2, 0, 0, 0, -1, 0,
    0, 0, 0, 0, 0, 0, 0, -3,
    2, 0, -6, 0, 0, 0, 0, -16,
    0, -7, -4, 0, -10, 0, -3, 0,
    -3, 0, 0, 0, 0, 2, 0, 0,
    0, 0, 0, 0, 0, 0, 2, 0,
    -6, 0, 0, 0, 0, -16, 0, -5,
    -4, 0, -12, 0, -3, 0, -4, 0,
    0, 0, 0, 0, 0, 0, 0, 0
};


/*Collect the kern class' data in one place*/
static const lv_font_fmt_txt_kern_classes_t kern_classes =
{
    .class_pair_values   = kern_class_values,
    .left_class_mapping  = kern_left_class_mapping,
    .right_class_mapping = kern_right_class_mapping,
    .left_class_cnt      = 40,
    .right_class_cnt     = 30,
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LVGL_VERSION_MAJOR == 8
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
#endif

#if LVGL_VERSION_MAJOR >= 8
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_classes,
    .kern_scale = 16,
    .cmap_num = 2,
    .bpp = 4,
    .kern_classes = 1,
    .bitmap_format = 0,
#if LVGL_VERSION_MAJOR == 8
    .cache = &cache
#endif
};



/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LVGL_VERSION_MAJOR >= 8
const lv_font_t lv_font_HarmonyOS_Sans_SC_Light_10 = {
#else
lv_font_t lv_font_HarmonyOS_Sans_SC_Light_10 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 10,          /*The maximum line height required by the font*/
    .base_line = 1,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -1,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc,          /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
#if LV_VERSION_CHECK(8, 2, 0) || LVGL_VERSION_MAJOR >= 9
    .fallback = NULL,
#endif
    .user_data = NULL,
};



#endif /*#if LV_FONT_HARMONYOS_SANS_SC_LIGHT_10*/

