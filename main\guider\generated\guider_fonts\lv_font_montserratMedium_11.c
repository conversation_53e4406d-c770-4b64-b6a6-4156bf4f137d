/*
* Copyright 2016 The Alatsi Project Authors (https://github.com/sorkintype/alatsi)
* This Font Software is licensed under the SIL Open Font License, Version 1.1.
* And is also available with a FAQ at: http://scripts.sil.org/OFL
*/
/*******************************************************************************
 * Size: 11 px
 * Bpp: 4
 * Opts: undefined
 ******************************************************************************/

#ifdef LV_LVGL_H_INCLUDE_SIMPLE
#include "lvgl.h"
#else
#include "lvgl.h"
#endif

#ifndef LV_FONT_MONTSERRATMEDIUM_11
#define LV_FONT_MONTSERRATMEDIUM_11 1
#endif

#if LV_FONT_MONTSERRATMEDIUM_11

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0x2f, 0x11, 0xf0, 0xf, 0x0, 0xf0, 0xe, 0x0,
    0x50, 0x5, 0x1, 0xe1,

    /* U+0022 "\"" */
    0x49, 0x49, 0x49, 0x49, 0x48, 0x48, 0x0, 0x0,

    /* U+0023 "#" */
    0x0, 0x83, 0xb, 0x10, 0x0, 0xa1, 0xc, 0x0,
    0x4d, 0xfd, 0xdf, 0xd5, 0x0, 0xc0, 0xb, 0x0,
    0x0, 0xc0, 0x2a, 0x0, 0x9d, 0xed, 0xde, 0xd1,
    0x4, 0x80, 0x66, 0x0, 0x5, 0x60, 0x84, 0x0,

    /* U+0024 "$" */
    0x0, 0x5, 0x0, 0x0, 0x0, 0xb0, 0x0, 0x6,
    0xdf, 0xe9, 0x2, 0xe2, 0xb1, 0x50, 0x4d, 0xb,
    0x0, 0x0, 0xcd, 0xd3, 0x0, 0x0, 0x4d, 0xeb,
    0x0, 0x0, 0xb0, 0xd5, 0x46, 0x1b, 0x1e, 0x32,
    0xae, 0xfe, 0x60, 0x0, 0xb, 0x0, 0x0, 0x0,
    0x50, 0x0,

    /* U+0025 "%" */
    0x1a, 0xb4, 0x0, 0x75, 0x7, 0x40, 0xb0, 0x2a,
    0x0, 0x92, 0xb, 0xb, 0x10, 0x6, 0x50, 0xb7,
    0x60, 0x0, 0x8, 0xa5, 0xb1, 0x9a, 0x20, 0x0,
    0xb1, 0x92, 0xa, 0x0, 0x76, 0xa, 0x10, 0xb0,
    0x2b, 0x0, 0x3a, 0xa4,

    /* U+0026 "&" */
    0x1, 0xcc, 0xc1, 0x0, 0x8, 0x80, 0x86, 0x0,
    0x5, 0xb2, 0xd2, 0x0, 0x0, 0xef, 0x30, 0x0,
    0xc, 0x7a, 0x90, 0x90, 0x6a, 0x0, 0xac, 0xb0,
    0x6c, 0x10, 0x4f, 0xa0, 0x8, 0xde, 0xc3, 0xa2,
    0x0, 0x0, 0x0, 0x0,

    /* U+0027 "'" */
    0x49, 0x49, 0x48, 0x0,

    /* U+0028 "(" */
    0xd, 0x25, 0xa0, 0xa5, 0xd, 0x20, 0xe1, 0xe,
    0x10, 0xd2, 0xa, 0x50, 0x5a, 0x0, 0xd2,

    /* U+0029 ")" */
    0x69, 0x0, 0xe1, 0xa, 0x50, 0x78, 0x6, 0xa0,
    0x6a, 0x7, 0x80, 0xa5, 0xe, 0x16, 0x90,

    /* U+002A "*" */
    0x12, 0x81, 0x6, 0xbd, 0xa0, 0x4c, 0xe8, 0x3,
    0x28, 0x30, 0x0, 0x10, 0x0,

    /* U+002B "+" */
    0x0, 0x39, 0x0, 0x0, 0x4a, 0x0, 0x3e, 0xef,
    0xe9, 0x0, 0x4a, 0x0, 0x0, 0x4a, 0x0,

    /* U+002C "," */
    0x0, 0x5e, 0x2c, 0x47,

    /* U+002D "-" */
    0x5f, 0xf8,

    /* U+002E "." */
    0x15, 0x5c,

    /* U+002F "/" */
    0x0, 0x0, 0x61, 0x0, 0x0, 0xe0, 0x0, 0x5,
    0x90, 0x0, 0xb, 0x30, 0x0, 0x1d, 0x0, 0x0,
    0x68, 0x0, 0x0, 0xc3, 0x0, 0x1, 0xd0, 0x0,
    0x7, 0x80, 0x0, 0xc, 0x20, 0x0, 0x2d, 0x0,
    0x0,

    /* U+0030 "0" */
    0x1, 0xbf, 0xd4, 0x0, 0xd8, 0x14, 0xe3, 0x4d,
    0x0, 0x8, 0x96, 0xa0, 0x0, 0x5c, 0x6a, 0x0,
    0x5, 0xc4, 0xd0, 0x0, 0x89, 0xd, 0x81, 0x4e,
    0x30, 0x2b, 0xfd, 0x50,

    /* U+0031 "1" */
    0xef, 0xe0, 0x2e, 0x2, 0xe0, 0x2e, 0x2, 0xe0,
    0x2e, 0x2, 0xe0, 0x2e,

    /* U+0032 "2" */
    0x2b, 0xfe, 0x90, 0x76, 0x11, 0xc7, 0x0, 0x0,
    0x89, 0x0, 0x0, 0xd4, 0x0, 0xc, 0x80, 0x0,
    0xc8, 0x0, 0xc, 0x80, 0x0, 0x9f, 0xff, 0xfe,

    /* U+0033 "3" */
    0x9f, 0xff, 0xf7, 0x0, 0x5, 0xd0, 0x0, 0x2e,
    0x20, 0x0, 0xcd, 0x50, 0x0, 0x25, 0xd7, 0x0,
    0x0, 0x5c, 0x84, 0x11, 0xb9, 0x5c, 0xff, 0xa0,

    /* U+0034 "4" */
    0x0, 0x1, 0xe3, 0x0, 0x0, 0xc, 0x60, 0x0,
    0x0, 0x8a, 0x0, 0x0, 0x4, 0xd0, 0x28, 0x0,
    0x1e, 0x30, 0x4c, 0x0, 0x8f, 0xff, 0xff, 0xf3,
    0x0, 0x0, 0x4c, 0x0, 0x0, 0x0, 0x4c, 0x0,

    /* U+0035 "5" */
    0xe, 0xff, 0xf7, 0xf, 0x0, 0x0, 0x1e, 0x0,
    0x0, 0x3f, 0xfe, 0xa1, 0x0, 0x1, 0xaa, 0x0,
    0x0, 0x3e, 0x76, 0x11, 0x9a, 0x3b, 0xef, 0xb1,

    /* U+0036 "6" */
    0x0, 0x9e, 0xfb, 0x0, 0xc9, 0x10, 0x20, 0x3d,
    0x0, 0x0, 0x6, 0xb9, 0xed, 0x60, 0x6f, 0x60,
    0x2e, 0x44, 0xf0, 0x0, 0xa7, 0xd, 0x50, 0x1e,
    0x30, 0x2c, 0xee, 0x60,

    /* U+0037 "7" */
    0xaf, 0xff, 0xff, 0x2a, 0x60, 0x5, 0xd0, 0x42,
    0x0, 0xc6, 0x0, 0x0, 0x3e, 0x0, 0x0, 0xa,
    0x80, 0x0, 0x1, 0xf1, 0x0, 0x0, 0x7a, 0x0,
    0x0, 0xe, 0x30, 0x0,

    /* U+0038 "8" */
    0x6, 0xde, 0xd7, 0x2, 0xf2, 0x1, 0xe3, 0x1e,
    0x20, 0x1e, 0x20, 0x8f, 0xef, 0x90, 0x4e, 0x20,
    0x2d, 0x57, 0xa0, 0x0, 0x88, 0x4e, 0x20, 0x1d,
    0x50, 0x6d, 0xee, 0x70,

    /* U+0039 "9" */
    0x8, 0xee, 0xa1, 0x7, 0xb0, 0x9, 0xa0, 0xa6,
    0x0, 0x2f, 0x17, 0xc1, 0x9, 0xf3, 0x8, 0xed,
    0x8e, 0x30, 0x0, 0x1, 0xf0, 0x2, 0x2, 0xb8,
    0x1, 0xdf, 0xe7, 0x0,

    /* U+003A ":" */
    0x5c, 0x25, 0x0, 0x0, 0x15, 0x5c,

    /* U+003B ";" */
    0x5c, 0x25, 0x0, 0x0, 0x0, 0x5e, 0x2c, 0x47,

    /* U+003C "<" */
    0x0, 0x0, 0x2, 0x0, 0x18, 0xd7, 0x1b, 0xc6,
    0x0, 0x2d, 0xa3, 0x0, 0x0, 0x4a, 0xd5, 0x0,
    0x0, 0x14,

    /* U+003D "=" */
    0x3e, 0xee, 0xe9, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3e, 0xee, 0xe9,

    /* U+003E ">" */
    0x11, 0x0, 0x0, 0x2d, 0xa4, 0x0, 0x0, 0x3a,
    0xd5, 0x0, 0x17, 0xd7, 0x1b, 0xd7, 0x10, 0x23,
    0x0, 0x0,

    /* U+003F "?" */
    0x3b, 0xfe, 0xa0, 0x76, 0x1, 0xd7, 0x0, 0x0,
    0xa7, 0x0, 0x6, 0xd1, 0x0, 0x3e, 0x10, 0x0,
    0x35, 0x0, 0x0, 0x23, 0x0, 0x0, 0x79, 0x0,

    /* U+0040 "@" */
    0x0, 0x18, 0xcb, 0xba, 0x40, 0x0, 0x2c, 0x40,
    0x0, 0x19, 0x70, 0xc, 0x22, 0xcd, 0xba, 0x7a,
    0x33, 0x90, 0xd5, 0x2, 0xe7, 0x29, 0x66, 0x2d,
    0x0, 0x9, 0x70, 0xb6, 0x62, 0xd0, 0x0, 0x97,
    0xb, 0x39, 0xd, 0x40, 0x2e, 0x83, 0x90, 0xc2,
    0x2c, 0xdc, 0x3e, 0xc1, 0x2, 0xc4, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x8c, 0xbc, 0xa0, 0x0,

    /* U+0041 "A" */
    0x0, 0x0, 0xcc, 0x0, 0x0, 0x0, 0x2, 0xdd,
    0x30, 0x0, 0x0, 0x9, 0x76, 0xa0, 0x0, 0x0,
    0x1e, 0x10, 0xe1, 0x0, 0x0, 0x79, 0x0, 0x98,
    0x0, 0x0, 0xee, 0xee, 0xee, 0x0, 0x5, 0xb0,
    0x0, 0xb, 0x60, 0xc, 0x50, 0x0, 0x4, 0xd0,

    /* U+0042 "B" */
    0xde, 0xee, 0xd6, 0xd, 0x40, 0x2, 0xf2, 0xd4,
    0x0, 0xc, 0x5d, 0x40, 0x3, 0xe2, 0xde, 0xee,
    0xfb, 0xd, 0x40, 0x0, 0x99, 0xd4, 0x0, 0x8,
    0xad, 0xee, 0xee, 0xb2,

    /* U+0043 "C" */
    0x0, 0x6d, 0xfe, 0x80, 0x9, 0xd3, 0x2, 0xa3,
    0x2f, 0x10, 0x0, 0x0, 0x6b, 0x0, 0x0, 0x0,
    0x6b, 0x0, 0x0, 0x0, 0x2f, 0x10, 0x0, 0x0,
    0x9, 0xd3, 0x2, 0xa3, 0x0, 0x7d, 0xfe, 0x80,

    /* U+0044 "D" */
    0xdf, 0xff, 0xd7, 0x0, 0xd4, 0x0, 0x2b, 0xb0,
    0xd4, 0x0, 0x0, 0xe4, 0xd4, 0x0, 0x0, 0x98,
    0xd4, 0x0, 0x0, 0x98, 0xd4, 0x0, 0x0, 0xe4,
    0xd4, 0x0, 0x2b, 0xb0, 0xdf, 0xff, 0xd7, 0x0,

    /* U+0045 "E" */
    0xdf, 0xff, 0xf9, 0xd4, 0x0, 0x0, 0xd4, 0x0,
    0x0, 0xd4, 0x0, 0x0, 0xdf, 0xff, 0xf1, 0xd4,
    0x0, 0x0, 0xd4, 0x0, 0x0, 0xdf, 0xff, 0xfb,

    /* U+0046 "F" */
    0xdf, 0xff, 0xf9, 0xd4, 0x0, 0x0, 0xd4, 0x0,
    0x0, 0xd4, 0x0, 0x0, 0xdf, 0xff, 0xf1, 0xd4,
    0x0, 0x0, 0xd4, 0x0, 0x0, 0xd4, 0x0, 0x0,

    /* U+0047 "G" */
    0x0, 0x6d, 0xfe, 0x90, 0x9, 0xd3, 0x2, 0x94,
    0x2f, 0x10, 0x0, 0x0, 0x6b, 0x0, 0x0, 0x0,
    0x6b, 0x0, 0x0, 0x88, 0x2f, 0x10, 0x0, 0x88,
    0x9, 0xd3, 0x1, 0xb8, 0x0, 0x6d, 0xfd, 0x91,

    /* U+0048 "H" */
    0xd4, 0x0, 0x5, 0xcd, 0x40, 0x0, 0x5c, 0xd4,
    0x0, 0x5, 0xcd, 0x40, 0x0, 0x5c, 0xdf, 0xff,
    0xff, 0xcd, 0x40, 0x0, 0x5c, 0xd4, 0x0, 0x5,
    0xcd, 0x40, 0x0, 0x5c,

    /* U+0049 "I" */
    0xd4, 0xd4, 0xd4, 0xd4, 0xd4, 0xd4, 0xd4, 0xd4,

    /* U+004A "J" */
    0x4, 0xff, 0xf8, 0x0, 0x0, 0x88, 0x0, 0x0,
    0x88, 0x0, 0x0, 0x88, 0x0, 0x0, 0x88, 0x0,
    0x0, 0x88, 0x9, 0x21, 0xd5, 0x6, 0xee, 0x90,

    /* U+004B "K" */
    0xd4, 0x0, 0x2d, 0x4d, 0x40, 0x1d, 0x50, 0xd4,
    0x1d, 0x60, 0xd, 0x4c, 0xa0, 0x0, 0xde, 0xcf,
    0x20, 0xd, 0xa0, 0x7d, 0x0, 0xd4, 0x0, 0xab,
    0xd, 0x40, 0x0, 0xc7,

    /* U+004C "L" */
    0xd4, 0x0, 0x0, 0xd4, 0x0, 0x0, 0xd4, 0x0,
    0x0, 0xd4, 0x0, 0x0, 0xd4, 0x0, 0x0, 0xd4,
    0x0, 0x0, 0xd4, 0x0, 0x0, 0xdf, 0xff, 0xf6,

    /* U+004D "M" */
    0xd5, 0x0, 0x0, 0xd, 0x5d, 0xe0, 0x0, 0x6,
    0xf5, 0xdd, 0x70, 0x0, 0xed, 0x5d, 0x4e, 0x10,
    0x88, 0xb5, 0xd3, 0x79, 0x2d, 0xb, 0x5d, 0x30,
    0xdc, 0x60, 0xb5, 0xd3, 0x4, 0xc0, 0xb, 0x5d,
    0x30, 0x0, 0x0, 0xb5,

    /* U+004E "N" */
    0xd7, 0x0, 0x5, 0xcd, 0xf3, 0x0, 0x5c, 0xda,
    0xe1, 0x5, 0xcd, 0x49, 0xc0, 0x5c, 0xd4, 0xc,
    0x85, 0xcd, 0x40, 0x2e, 0xac, 0xd4, 0x0, 0x4f,
    0xcd, 0x40, 0x0, 0x8c,

    /* U+004F "O" */
    0x0, 0x6d, 0xfe, 0x80, 0x0, 0x9d, 0x30, 0x2a,
    0xc0, 0x2f, 0x10, 0x0, 0xc, 0x66, 0xb0, 0x0,
    0x0, 0x7a, 0x6b, 0x0, 0x0, 0x7, 0xa2, 0xf1,
    0x0, 0x0, 0xc6, 0x9, 0xd3, 0x2, 0xac, 0x0,
    0x6, 0xdf, 0xe8, 0x0,

    /* U+0050 "P" */
    0xdf, 0xff, 0xb3, 0xd, 0x40, 0x6, 0xe1, 0xd4,
    0x0, 0xd, 0x4d, 0x40, 0x0, 0xd4, 0xd4, 0x0,
    0x6e, 0x1d, 0xff, 0xeb, 0x20, 0xd4, 0x0, 0x0,
    0xd, 0x40, 0x0, 0x0,

    /* U+0051 "Q" */
    0x0, 0x6d, 0xfe, 0x80, 0x0, 0x8, 0xd3, 0x2,
    0xac, 0x0, 0x2f, 0x10, 0x0, 0xc, 0x60, 0x6b,
    0x0, 0x0, 0x7, 0xa0, 0x6a, 0x0, 0x0, 0x7,
    0xa0, 0x3f, 0x10, 0x0, 0xc, 0x60, 0xa, 0xc3,
    0x1, 0xad, 0x0, 0x0, 0x7e, 0xff, 0x91, 0x0,
    0x0, 0x0, 0x4e, 0x30, 0x90, 0x0, 0x0, 0x4,
    0xcd, 0x50,

    /* U+0052 "R" */
    0xdf, 0xff, 0xb3, 0xd, 0x40, 0x6, 0xe1, 0xd4,
    0x0, 0xd, 0x4d, 0x40, 0x0, 0xd4, 0xd4, 0x0,
    0x6e, 0xd, 0xff, 0xfe, 0x30, 0xd4, 0x1, 0xd4,
    0xd, 0x40, 0x2, 0xe2,

    /* U+0053 "S" */
    0x6, 0xdf, 0xe8, 0x3, 0xe3, 0x1, 0x60, 0x5d,
    0x0, 0x0, 0x0, 0xcd, 0x83, 0x0, 0x0, 0x48,
    0xdb, 0x0, 0x0, 0x0, 0xd5, 0x47, 0x10, 0x2e,
    0x31, 0xae, 0xfd, 0x60,

    /* U+0054 "T" */
    0xef, 0xff, 0xff, 0x60, 0x5, 0xc0, 0x0, 0x0,
    0x5c, 0x0, 0x0, 0x5, 0xc0, 0x0, 0x0, 0x5c,
    0x0, 0x0, 0x5, 0xc0, 0x0, 0x0, 0x5c, 0x0,
    0x0, 0x5, 0xc0, 0x0,

    /* U+0055 "U" */
    0xe3, 0x0, 0x7, 0x9e, 0x30, 0x0, 0x79, 0xe3,
    0x0, 0x7, 0x9e, 0x30, 0x0, 0x79, 0xe3, 0x0,
    0x7, 0x9c, 0x50, 0x0, 0xa7, 0x6d, 0x20, 0x5f,
    0x10, 0x7d, 0xfc, 0x40,

    /* U+0056 "V" */
    0xc, 0x60, 0x0, 0x7, 0xa0, 0x6c, 0x0, 0x0,
    0xe3, 0x0, 0xe3, 0x0, 0x5c, 0x0, 0x8, 0xa0,
    0xb, 0x50, 0x0, 0x1f, 0x12, 0xe0, 0x0, 0x0,
    0xb7, 0x98, 0x0, 0x0, 0x4, 0xef, 0x10, 0x0,
    0x0, 0xd, 0xa0, 0x0,

    /* U+0057 "W" */
    0x7a, 0x0, 0x6, 0xe0, 0x0, 0x2d, 0x2, 0xf0,
    0x0, 0xcf, 0x30, 0x8, 0x80, 0xd, 0x40, 0x1e,
    0x88, 0x0, 0xd3, 0x0, 0x89, 0x7, 0x92, 0xe0,
    0x2e, 0x0, 0x3, 0xe0, 0xc4, 0xd, 0x37, 0x90,
    0x0, 0xe, 0x5e, 0x0, 0x88, 0xc4, 0x0, 0x0,
    0x9e, 0x90, 0x2, 0xee, 0x0, 0x0, 0x3, 0xf4,
    0x0, 0xd, 0x90, 0x0,

    /* U+0058 "X" */
    0x5d, 0x0, 0x7, 0xb0, 0xa, 0xa0, 0x3e, 0x10,
    0x0, 0xe5, 0xd4, 0x0, 0x0, 0x4f, 0x90, 0x0,
    0x0, 0x6f, 0xc0, 0x0, 0x2, 0xe2, 0xb8, 0x0,
    0xc, 0x70, 0x1e, 0x30, 0x8b, 0x0, 0x5, 0xd0,

    /* U+0059 "Y" */
    0xc, 0x60, 0x0, 0x3d, 0x0, 0x2e, 0x10, 0xc,
    0x40, 0x0, 0x99, 0x6, 0xb0, 0x0, 0x1, 0xe4,
    0xe2, 0x0, 0x0, 0x6, 0xf8, 0x0, 0x0, 0x0,
    0xf, 0x10, 0x0, 0x0, 0x0, 0xf1, 0x0, 0x0,
    0x0, 0xf, 0x10, 0x0,

    /* U+005A "Z" */
    0x7f, 0xff, 0xff, 0xb0, 0x0, 0x2, 0xe3, 0x0,
    0x0, 0xd6, 0x0, 0x0, 0xaa, 0x0, 0x0, 0x6d,
    0x0, 0x0, 0x3e, 0x20, 0x0, 0x1e, 0x50, 0x0,
    0x8, 0xff, 0xff, 0xfe,

    /* U+005B "[" */
    0xde, 0x6d, 0x30, 0xd3, 0xd, 0x30, 0xd3, 0xd,
    0x30, 0xd3, 0xd, 0x30, 0xd3, 0xd, 0xe6,

    /* U+005C "\\" */
    0x24, 0x0, 0x0, 0x1d, 0x0, 0x0, 0xb, 0x30,
    0x0, 0x6, 0x90, 0x0, 0x1, 0xe0, 0x0, 0x0,
    0xb4, 0x0, 0x0, 0x59, 0x0, 0x0, 0xe, 0x0,
    0x0, 0xa, 0x40, 0x0, 0x4, 0xa0, 0x0, 0x0,
    0xe0,

    /* U+005D "]" */
    0xbf, 0x80, 0x88, 0x8, 0x80, 0x88, 0x8, 0x80,
    0x88, 0x8, 0x80, 0x88, 0x8, 0x8b, 0xf8,

    /* U+005E "^" */
    0x0, 0x26, 0x0, 0x0, 0xac, 0x10, 0x1, 0xb4,
    0x80, 0x8, 0x40, 0xc0, 0xc, 0x0, 0x76,

    /* U+005F "_" */
    0xbb, 0xbb, 0xb5,

    /* U+0060 "`" */
    0x36, 0x0, 0x7, 0x90,

    /* U+0061 "a" */
    0x1a, 0xee, 0xa0, 0x3, 0x0, 0xa7, 0x8, 0xcc,
    0xe9, 0x5c, 0x0, 0x7a, 0x5b, 0x0, 0xca, 0xa,
    0xcc, 0xaa,

    /* U+0062 "b" */
    0xf1, 0x0, 0x0, 0xf, 0x10, 0x0, 0x0, 0xf7,
    0xdf, 0xa1, 0xf, 0xb0, 0xb, 0xa0, 0xf2, 0x0,
    0x1f, 0xf, 0x20, 0x1, 0xf0, 0xfa, 0x11, 0xaa,
    0xf, 0x6d, 0xfa, 0x10,

    /* U+0063 "c" */
    0x4, 0xcf, 0xd4, 0x2e, 0x40, 0x47, 0x7a, 0x0,
    0x0, 0x7a, 0x0, 0x0, 0x2e, 0x40, 0x47, 0x4,
    0xdf, 0xd4,

    /* U+0064 "d" */
    0x0, 0x0, 0x9, 0x80, 0x0, 0x0, 0x98, 0x5,
    0xde, 0xba, 0x82, 0xf4, 0x4, 0xf8, 0x7a, 0x0,
    0xa, 0x87, 0xa0, 0x0, 0xa8, 0x2e, 0x30, 0x3f,
    0x80, 0x5d, 0xdb, 0xa8,

    /* U+0065 "e" */
    0x4, 0xde, 0xc3, 0x2, 0xc1, 0x3, 0xd0, 0x7e,
    0xcc, 0xcd, 0x37, 0xa0, 0x0, 0x0, 0x2f, 0x50,
    0x37, 0x0, 0x4d, 0xfd, 0x50,

    /* U+0066 "f" */
    0x5, 0xee, 0x10, 0xd4, 0x0, 0xbf, 0xec, 0x0,
    0xd3, 0x0, 0xd, 0x30, 0x0, 0xd3, 0x0, 0xd,
    0x30, 0x0, 0xd3, 0x0,

    /* U+0067 "g" */
    0x4, 0xdf, 0xb8, 0x92, 0xf4, 0x3, 0xe9, 0x79,
    0x0, 0x8, 0x97, 0xa0, 0x0, 0x89, 0x2f, 0x40,
    0x3f, 0x90, 0x4d, 0xec, 0xa8, 0x5, 0x10, 0x1d,
    0x40, 0x9d, 0xed, 0x70,

    /* U+0068 "h" */
    0xf1, 0x0, 0x0, 0xf1, 0x0, 0x0, 0xf7, 0xde,
    0x90, 0xf9, 0x1, 0xd5, 0xf2, 0x0, 0x88, 0xf1,
    0x0, 0x88, 0xf1, 0x0, 0x88, 0xf1, 0x0, 0x88,

    /* U+0069 "i" */
    0x1e, 0x20, 0x40, 0xf, 0x10, 0xf1, 0xf, 0x10,
    0xf1, 0xf, 0x10, 0xf1,

    /* U+006A "j" */
    0x0, 0xe, 0x20, 0x0, 0x40, 0x0, 0xf, 0x10,
    0x0, 0xf1, 0x0, 0xf, 0x10, 0x0, 0xf1, 0x0,
    0xf, 0x10, 0x0, 0xf1, 0x0, 0x1f, 0x0, 0xce,
    0x80,

    /* U+006B "k" */
    0xf1, 0x0, 0x0, 0xf1, 0x0, 0x0, 0xf1, 0x5,
    0xd2, 0xf1, 0x5e, 0x20, 0xf7, 0xf4, 0x0, 0xfd,
    0x9c, 0x0, 0xf2, 0xb, 0x90, 0xf1, 0x1, 0xd5,

    /* U+006C "l" */
    0xf1, 0xf1, 0xf1, 0xf1, 0xf1, 0xf1, 0xf1, 0xf1,

    /* U+006D "m" */
    0xf7, 0xde, 0x76, 0xde, 0xa0, 0xf8, 0x1, 0xec,
    0x10, 0xb7, 0xf1, 0x0, 0xb6, 0x0, 0x6a, 0xf1,
    0x0, 0xb6, 0x0, 0x6b, 0xf1, 0x0, 0xb6, 0x0,
    0x6b, 0xf1, 0x0, 0xb6, 0x0, 0x6b,

    /* U+006E "n" */
    0xf7, 0xde, 0x90, 0xf9, 0x1, 0xd5, 0xf1, 0x0,
    0x88, 0xf1, 0x0, 0x88, 0xf1, 0x0, 0x88, 0xf1,
    0x0, 0x88,

    /* U+006F "o" */
    0x4, 0xdf, 0xd4, 0x2, 0xe4, 0x4, 0xe2, 0x7a,
    0x0, 0xa, 0x77, 0xa0, 0x0, 0xa7, 0x2e, 0x40,
    0x4e, 0x20, 0x4d, 0xfd, 0x40,

    /* U+0070 "p" */
    0xf7, 0xde, 0xa1, 0xf, 0xa0, 0xa, 0xa0, 0xf2,
    0x0, 0x1f, 0xf, 0x20, 0x1, 0xf0, 0xfb, 0x0,
    0xba, 0xf, 0x7d, 0xea, 0x10, 0xf1, 0x0, 0x0,
    0xf, 0x10, 0x0, 0x0,

    /* U+0071 "q" */
    0x5, 0xdf, 0xb9, 0x82, 0xf4, 0x4, 0xf8, 0x7a,
    0x0, 0xa, 0x87, 0xa0, 0x0, 0xa8, 0x2f, 0x40,
    0x4f, 0x80, 0x5d, 0xfb, 0xa8, 0x0, 0x0, 0x9,
    0x80, 0x0, 0x0, 0x98,

    /* U+0072 "r" */
    0xf7, 0xe2, 0xfa, 0x10, 0xf2, 0x0, 0xf1, 0x0,
    0xf1, 0x0, 0xf1, 0x0,

    /* U+0073 "s" */
    0x1b, 0xee, 0x90, 0x7a, 0x0, 0x20, 0x5e, 0x74,
    0x0, 0x2, 0x6a, 0xd0, 0x32, 0x1, 0xf1, 0x5d,
    0xee, 0x70,

    /* U+0074 "t" */
    0x6, 0x10, 0x0, 0xd3, 0x0, 0xbf, 0xec, 0x0,
    0xd3, 0x0, 0xd, 0x30, 0x0, 0xd3, 0x0, 0xc,
    0x60, 0x0, 0x5e, 0xe1,

    /* U+0075 "u" */
    0xf, 0x0, 0xa, 0x70, 0xf0, 0x0, 0xa7, 0xf,
    0x0, 0xa, 0x70, 0xf0, 0x0, 0xa7, 0xd, 0x60,
    0x2f, 0x70, 0x3c, 0xeb, 0xa7,

    /* U+0076 "v" */
    0xc, 0x40, 0x1, 0xe0, 0x6, 0xb0, 0x8, 0x80,
    0x0, 0xe2, 0xe, 0x10, 0x0, 0x88, 0x5a, 0x0,
    0x0, 0x1e, 0xc3, 0x0, 0x0, 0xa, 0xd0, 0x0,

    /* U+0077 "w" */
    0xc3, 0x0, 0xa9, 0x0, 0x4a, 0x69, 0x1, 0xee,
    0x0, 0xa4, 0x1e, 0x6, 0x8a, 0x40, 0xd0, 0xa,
    0x4c, 0x24, 0xa6, 0x80, 0x4, 0xcc, 0x0, 0xdc,
    0x20, 0x0, 0xe6, 0x0, 0x8d, 0x0,

    /* U+0078 "x" */
    0x5c, 0x0, 0xb6, 0x9, 0x87, 0xa0, 0x0, 0xdd,
    0x0, 0x1, 0xdd, 0x10, 0xb, 0x76, 0xc0, 0x7b,
    0x0, 0xa8,

    /* U+0079 "y" */
    0xc, 0x40, 0x1, 0xe0, 0x5, 0xb0, 0x8, 0x70,
    0x0, 0xe2, 0xe, 0x10, 0x0, 0x79, 0x69, 0x0,
    0x0, 0x1e, 0xd2, 0x0, 0x0, 0x9, 0xb0, 0x0,
    0x1, 0xc, 0x40, 0x0, 0x1d, 0xe8, 0x0, 0x0,

    /* U+007A "z" */
    0x7e, 0xee, 0xf3, 0x0, 0x9, 0x90, 0x0, 0x5c,
    0x0, 0x3, 0xe2, 0x0, 0x1d, 0x40, 0x0, 0x8f,
    0xee, 0xe4,

    /* U+007B "{" */
    0x3, 0xe9, 0x7, 0xa0, 0x8, 0x90, 0x8, 0x80,
    0x5f, 0x40, 0x8, 0x80, 0x8, 0x90, 0x8, 0x90,
    0x7, 0xa0, 0x2, 0xd9,

    /* U+007C "|" */
    0xd2, 0xd2, 0xd2, 0xd2, 0xd2, 0xd2, 0xd2, 0xd2,
    0xd2, 0xd2,

    /* U+007D "}" */
    0xbd, 0x10, 0xc, 0x50, 0xb, 0x50, 0xa, 0x60,
    0x6, 0xf3, 0xa, 0x60, 0xb, 0x50, 0xb, 0x50,
    0xc, 0x50, 0xbc, 0x0,

    /* U+007E "~" */
    0xb, 0xc4, 0x19, 0x46, 0x19, 0xc3,

    /* U+F001 "" */
    0x0, 0x0, 0x0, 0x0, 0x59, 0xb0, 0x0, 0x2,
    0x7b, 0xff, 0xff, 0x0, 0x1d, 0xff, 0xff, 0xff,
    0xf0, 0x4, 0xff, 0xff, 0xc7, 0x8f, 0x0, 0x4f,
    0xa5, 0x10, 0x6, 0xf0, 0x4, 0xf2, 0x0, 0x0,
    0x6f, 0x0, 0x4f, 0x20, 0x0, 0x6, 0xf0, 0x4,
    0xf2, 0x0, 0x8e, 0xff, 0x28, 0xaf, 0x20, 0xf,
    0xff, 0xfe, 0xff, 0xf1, 0x0, 0x5b, 0xb4, 0x9f,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x11, 0x0, 0x0,
    0x0, 0x0,

    /* U+F008 "" */
    0x10, 0x34, 0x44, 0x44, 0x30, 0x1d, 0x9f, 0xdc,
    0xcc, 0xdf, 0x9d, 0xb2, 0xf1, 0x0, 0x1, 0xf2,
    0xcd, 0x8f, 0x10, 0x0, 0x1f, 0x8d, 0xc3, 0xfb,
    0xaa, 0xab, 0xf3, 0xcd, 0x7f, 0x76, 0x66, 0x7f,
    0x7d, 0xc4, 0xf1, 0x0, 0x1, 0xf4, 0xcd, 0x6f,
    0x20, 0x0, 0x2f, 0x6d, 0xb5, 0xff, 0xff, 0xff,
    0xf5, 0xb0,

    /* U+F00B "" */
    0xef, 0xe3, 0xef, 0xff, 0xff, 0xdf, 0xff, 0x5f,
    0xff, 0xff, 0xff, 0xbc, 0xc2, 0xcc, 0xcc, 0xcc,
    0xb7, 0x87, 0x7, 0x88, 0x88, 0x86, 0xff, 0xf5,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x4f, 0xff, 0xff,
    0xff, 0x35, 0x40, 0x45, 0x55, 0x55, 0x3f, 0xff,
    0x3f, 0xff, 0xff, 0xff, 0xff, 0xf5, 0xff, 0xff,
    0xff, 0xfa, 0xba, 0x1a, 0xbb, 0xbb, 0xba,

    /* U+F00C "" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xf9, 0x0, 0x0, 0x0, 0x4, 0xff,
    0xc1, 0x50, 0x0, 0x4, 0xff, 0xc0, 0xcf, 0x80,
    0x4, 0xff, 0xc0, 0x9, 0xff, 0x84, 0xff, 0xc0,
    0x0, 0x9, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x9,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x8, 0xb0, 0x0,
    0x0, 0x0,

    /* U+F00D "" */
    0x7b, 0x0, 0x4, 0xc2, 0xdf, 0xc0, 0x4f, 0xf6,
    0x2e, 0xfd, 0xff, 0x90, 0x2, 0xef, 0xf9, 0x0,
    0x4, 0xff, 0xfc, 0x0, 0x5f, 0xfa, 0xef, 0xc1,
    0xef, 0x90, 0x2e, 0xf7, 0x47, 0x0, 0x2, 0x81,

    /* U+F011 "" */
    0x0, 0x0, 0x1d, 0x60, 0x0, 0x0, 0x0, 0x71,
    0x2f, 0x90, 0x62, 0x0, 0x9, 0xf7, 0x2f, 0x90,
    0xfe, 0x20, 0x3f, 0xb0, 0x2f, 0x90, 0x5f, 0xb0,
    0xaf, 0x20, 0x2f, 0x90, 0xb, 0xf1, 0xce, 0x0,
    0x2f, 0x90, 0x7, 0xf3, 0xbf, 0x0, 0x5, 0x20,
    0x8, 0xf3, 0x8f, 0x50, 0x0, 0x0, 0xe, 0xf0,
    0x1e, 0xf4, 0x0, 0x1, 0xbf, 0x70, 0x4, 0xff,
    0xc9, 0xaf, 0xfa, 0x0, 0x0, 0x2a, 0xff, 0xfd,
    0x60, 0x0, 0x0, 0x0, 0x1, 0x10, 0x0, 0x0,

    /* U+F013 "" */
    0x0, 0x0, 0x46, 0x40, 0x0, 0x0, 0x0, 0xe,
    0xfe, 0x0, 0x0, 0x8, 0x7a, 0xff, 0xfa, 0x78,
    0x3, 0xff, 0xff, 0xff, 0xff, 0xf3, 0x5f, 0xff,
    0x83, 0x9f, 0xff, 0x50, 0x9f, 0xd0, 0x0, 0xdf,
    0x90, 0xa, 0xfe, 0x0, 0xe, 0xfa, 0x7, 0xff,
    0xfc, 0x7c, 0xff, 0xf7, 0x2f, 0xff, 0xff, 0xff,
    0xff, 0x20, 0x43, 0x6f, 0xff, 0x63, 0x40, 0x0,
    0x0, 0xdf, 0xd0, 0x0, 0x0, 0x0, 0x1, 0x21,
    0x0, 0x0,

    /* U+F015 "" */
    0x0, 0x0, 0xa, 0xd3, 0x3f, 0x70, 0x0, 0x0,
    0x2d, 0xec, 0xf9, 0xf7, 0x0, 0x0, 0x4e, 0xd4,
    0x59, 0xff, 0x70, 0x0, 0x6f, 0xb4, 0xef, 0x86,
    0xfb, 0x0, 0x9f, 0x96, 0xff, 0xff, 0xa5, 0xed,
    0x19, 0x68, 0xff, 0xff, 0xff, 0xd3, 0xb1, 0x0,
    0xff, 0xff, 0xef, 0xff, 0x50, 0x0, 0xf, 0xff,
    0x20, 0xcf, 0xf5, 0x0, 0x0, 0xff, 0xf2, 0xc,
    0xff, 0x50, 0x0, 0x9, 0xaa, 0x10, 0x7a, 0xa2,
    0x0,

    /* U+F019 "" */
    0x0, 0x0, 0x7, 0x96, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xfd, 0x0, 0x0, 0x0, 0x0, 0xe, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0xe, 0xfd, 0x0, 0x0,
    0x0, 0x17, 0x7f, 0xfe, 0x77, 0x0, 0x0, 0xc,
    0xff, 0xff, 0xfb, 0x0, 0x0, 0x1, 0xdf, 0xff,
    0xc0, 0x0, 0x0, 0x0, 0x1d, 0xfc, 0x10, 0x0,
    0xf, 0xff, 0xe4, 0x94, 0xff, 0xfe, 0xf, 0xff,
    0xfe, 0x8e, 0xff, 0xff, 0xf, 0xff, 0xff, 0xff,
    0x8b, 0x7f, 0x8, 0x99, 0x99, 0x99, 0x99, 0x97,

    /* U+F01C "" */
    0x0, 0x3, 0x44, 0x44, 0x41, 0x0, 0x0, 0xb,
    0xff, 0xff, 0xff, 0xf2, 0x0, 0x6, 0xf4, 0x22,
    0x22, 0x2d, 0xc0, 0x2, 0xf9, 0x0, 0x0, 0x0,
    0x3f, 0x70, 0xbe, 0x22, 0x0, 0x0, 0x12, 0xaf,
    0x2f, 0xff, 0xf9, 0x0, 0x3f, 0xff, 0xf5, 0xff,
    0xff, 0xfc, 0xce, 0xff, 0xff, 0x6f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf5, 0xcf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x30,

    /* U+F021 "" */
    0x0, 0x1, 0x56, 0x40, 0x4, 0x90, 0x8, 0xff,
    0xff, 0xe6, 0x7f, 0x9, 0xfc, 0x42, 0x4c, 0xfe,
    0xf3, 0xfa, 0x0, 0x4, 0x4c, 0xff, 0xaf, 0x10,
    0x0, 0xcf, 0xff, 0xf1, 0x20, 0x0, 0x2, 0x33,
    0x32, 0x67, 0x77, 0x50, 0x0, 0x5, 0x4f, 0xff,
    0xfc, 0x0, 0x2, 0xf8, 0xff, 0xc2, 0x0, 0x1,
    0xdf, 0x2f, 0xcf, 0xf9, 0x79, 0xff, 0x50, 0xf7,
    0x2a, 0xff, 0xfb, 0x30, 0x5, 0x20, 0x0, 0x10,
    0x0, 0x0,

    /* U+F026 "" */
    0x0, 0x0, 0x10, 0x0, 0x1, 0xd7, 0x12, 0x3d,
    0xf8, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xf8, 0xff,
    0xff, 0xf8, 0xef, 0xff, 0xf8, 0x0, 0xb, 0xf8,
    0x0, 0x0, 0xb7, 0x0, 0x0, 0x0,

    /* U+F027 "" */
    0x0, 0x0, 0x10, 0x0, 0x0, 0x0, 0x1d, 0x70,
    0x0, 0x12, 0x3d, 0xf8, 0x0, 0xf, 0xff, 0xff,
    0x85, 0x80, 0xff, 0xff, 0xf8, 0x1e, 0x2f, 0xff,
    0xff, 0x82, 0xe1, 0xef, 0xff, 0xf8, 0x34, 0x0,
    0x0, 0xbf, 0x80, 0x0, 0x0, 0x0, 0xb7, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F028 "" */
    0x0, 0x0, 0x0, 0x0, 0x2, 0x0, 0x0, 0x0,
    0x1, 0x0, 0x2, 0xe5, 0x0, 0x0, 0x1, 0xd7,
    0x2, 0x52, 0xe3, 0x1, 0x23, 0xdf, 0x80, 0x1d,
    0x76, 0xb0, 0xff, 0xff, 0xf8, 0x58, 0x2f, 0x1f,
    0x1f, 0xff, 0xff, 0x80, 0xe2, 0xd3, 0xd3, 0xff,
    0xff, 0xf8, 0x2e, 0x1e, 0x2d, 0x3e, 0xff, 0xff,
    0x83, 0x45, 0xe1, 0xf0, 0x0, 0xb, 0xf8, 0x3,
    0xe4, 0x9a, 0x0, 0x0, 0xb, 0x70, 0x2, 0x6e,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x2d, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F03E "" */
    0x13, 0x44, 0x44, 0x44, 0x43, 0x1e, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0xf7, 0xa, 0xff, 0xff, 0xff,
    0xff, 0x40, 0x7f, 0xff, 0x6e, 0xff, 0xfe, 0xbf,
    0xff, 0x40, 0x2e, 0xff, 0xf7, 0x4e, 0x40, 0x0,
    0x6f, 0xf8, 0x0, 0x10, 0x0, 0x6, 0xff, 0x96,
    0x66, 0x66, 0x66, 0x9f, 0xcf, 0xff, 0xff, 0xff,
    0xff, 0xc0,

    /* U+F043 "" */
    0x0, 0x6, 0x20, 0x0, 0x0, 0x2f, 0xb0, 0x0,
    0x0, 0x8f, 0xf2, 0x0, 0x1, 0xff, 0xfa, 0x0,
    0xb, 0xff, 0xff, 0x40, 0x5f, 0xff, 0xff, 0xe0,
    0xdf, 0xff, 0xff, 0xf6, 0xf9, 0xff, 0xff, 0xf8,
    0xd9, 0x9f, 0xff, 0xf6, 0x7f, 0x65, 0xff, 0xe1,
    0x9, 0xff, 0xfe, 0x30, 0x0, 0x25, 0x40, 0x0,

    /* U+F048 "" */
    0x0, 0x0, 0x0, 0x0, 0x9d, 0x0, 0x3, 0xd2,
    0xae, 0x0, 0x4f, 0xf4, 0xae, 0x6, 0xff, 0xf4,
    0xae, 0x7f, 0xff, 0xf4, 0xaf, 0xff, 0xff, 0xf4,
    0xaf, 0xff, 0xff, 0xf4, 0xae, 0x3e, 0xff, 0xf4,
    0xae, 0x2, 0xef, 0xf4, 0xae, 0x0, 0x1d, 0xf3,
    0x69, 0x0, 0x1, 0x91,

    /* U+F04B "" */
    0x67, 0x0, 0x0, 0x0, 0x0, 0xff, 0xd5, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xb2, 0x0, 0x0, 0xff,
    0xff, 0xff, 0x80, 0x0, 0xff, 0xff, 0xff, 0xfe,
    0x50, 0xff, 0xff, 0xff, 0xff, 0xf7, 0xff, 0xff,
    0xff, 0xff, 0xf5, 0xff, 0xff, 0xff, 0xfb, 0x20,
    0xff, 0xff, 0xfd, 0x40, 0x0, 0xff, 0xff, 0x70,
    0x0, 0x0, 0xff, 0xa1, 0x0, 0x0, 0x0, 0x23,
    0x0, 0x0, 0x0, 0x0,

    /* U+F04C "" */
    0xbf, 0xfc, 0x1, 0xdf, 0xf9, 0xff, 0xff, 0x14,
    0xff, 0xfd, 0xff, 0xff, 0x24, 0xff, 0xfd, 0xff,
    0xff, 0x24, 0xff, 0xfd, 0xff, 0xff, 0x24, 0xff,
    0xfd, 0xff, 0xff, 0x24, 0xff, 0xfd, 0xff, 0xff,
    0x24, 0xff, 0xfd, 0xff, 0xff, 0x24, 0xff, 0xfd,
    0xff, 0xff, 0x14, 0xff, 0xfd, 0x7a, 0xa8, 0x0,
    0x9a, 0xa5,

    /* U+F04D "" */
    0xbf, 0xff, 0xff, 0xff, 0xf5, 0xff, 0xff, 0xff,
    0xff, 0xf9, 0xff, 0xff, 0xff, 0xff, 0xfa, 0xff,
    0xff, 0xff, 0xff, 0xfa, 0xff, 0xff, 0xff, 0xff,
    0xfa, 0xff, 0xff, 0xff, 0xff, 0xfa, 0xff, 0xff,
    0xff, 0xff, 0xfa, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0xff, 0xff, 0xff, 0xff, 0xf9, 0x7a, 0xaa, 0xaa,
    0xaa, 0xa3,

    /* U+F051 "" */
    0x0, 0x0, 0x0, 0x0, 0xa9, 0x0, 0x6, 0xf0,
    0xcf, 0xb0, 0x7, 0xf1, 0xcf, 0xfc, 0x17, 0xf1,
    0xcf, 0xff, 0xd9, 0xf1, 0xcf, 0xff, 0xff, 0xf1,
    0xcf, 0xff, 0xfe, 0xf1, 0xcf, 0xff, 0x97, 0xf1,
    0xcf, 0xf8, 0x7, 0xf1, 0xcf, 0x60, 0x7, 0xf1,
    0x65, 0x0, 0x4, 0xa0,

    /* U+F052 "" */
    0x0, 0x0, 0x1c, 0x90, 0x0, 0x0, 0x0, 0x1d,
    0xff, 0x80, 0x0, 0x0, 0xc, 0xff, 0xff, 0x70,
    0x0, 0xb, 0xff, 0xff, 0xff, 0x60, 0xa, 0xff,
    0xff, 0xff, 0xff, 0x40, 0xef, 0xff, 0xff, 0xff,
    0xf8, 0x1, 0x45, 0x55, 0x55, 0x54, 0x0, 0xef,
    0xff, 0xff, 0xff, 0xf8, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xa0, 0x9b, 0xbb, 0xbb, 0xbb, 0xb4,

    /* U+F053 "" */
    0x0, 0x0, 0x4b, 0x0, 0x0, 0x4f, 0xe1, 0x0,
    0x4f, 0xe3, 0x0, 0x4f, 0xe3, 0x0, 0x3f, 0xf3,
    0x0, 0x1, 0xdf, 0x70, 0x0, 0x1, 0xdf, 0x70,
    0x0, 0x1, 0xdf, 0x70, 0x0, 0x1, 0xdf, 0x20,
    0x0, 0x1, 0x50,

    /* U+F054 "" */
    0x1b, 0x30, 0x0, 0x3, 0xff, 0x30, 0x0, 0x4,
    0xff, 0x30, 0x0, 0x4, 0xff, 0x30, 0x0, 0x4,
    0xff, 0x20, 0x0, 0x9f, 0xc0, 0x0, 0x9f, 0xc0,
    0x0, 0x9f, 0xc0, 0x0, 0x4f, 0xc0, 0x0, 0x0,
    0x60, 0x0, 0x0,

    /* U+F067 "" */
    0x0, 0x1, 0xea, 0x0, 0x0, 0x0, 0x3, 0xfd,
    0x0, 0x0, 0x0, 0x3, 0xfd, 0x0, 0x0, 0x12,
    0x25, 0xfd, 0x22, 0x20, 0xff, 0xff, 0xff, 0xff,
    0xf9, 0xce, 0xee, 0xff, 0xee, 0xe6, 0x0, 0x3,
    0xfd, 0x0, 0x0, 0x0, 0x3, 0xfd, 0x0, 0x0,
    0x0, 0x3, 0xfd, 0x0, 0x0, 0x0, 0x0, 0xa7,
    0x0, 0x0,

    /* U+F068 "" */
    0x1, 0x11, 0x11, 0x11, 0x10, 0xef, 0xff, 0xff,
    0xff, 0xf8, 0xdf, 0xff, 0xff, 0xff, 0xf7,

    /* U+F06E "" */
    0x0, 0x0, 0x1, 0x20, 0x0, 0x0, 0x0, 0x1,
    0x9f, 0xfe, 0xfb, 0x40, 0x0, 0x4, 0xef, 0x60,
    0x13, 0xdf, 0x80, 0x3, 0xff, 0x70, 0x3f, 0xa2,
    0xff, 0x70, 0xdf, 0xf2, 0x7c, 0xff, 0x2d, 0xff,
    0x2c, 0xff, 0x3b, 0xff, 0xf1, 0xef, 0xf1, 0x2e,
    0xf9, 0x2c, 0xd6, 0x4f, 0xf5, 0x0, 0x2d, 0xf9,
    0x21, 0x6e, 0xf5, 0x0, 0x0, 0x6, 0xcf, 0xfd,
    0x92, 0x0, 0x0,

    /* U+F070 "" */
    0x52, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xce,
    0x40, 0x0, 0x21, 0x0, 0x0, 0x0, 0xa, 0xf9,
    0xaf, 0xef, 0xf9, 0x20, 0x0, 0x0, 0x6f, 0xf5,
    0x11, 0x5f, 0xf4, 0x0, 0x5, 0x3, 0xee, 0xcf,
    0x56, 0xff, 0x30, 0x2f, 0xd2, 0x1b, 0xff, 0xd1,
    0xff, 0xe0, 0x1e, 0xfd, 0x0, 0x7f, 0xe3, 0xff,
    0xc0, 0x5, 0xff, 0x40, 0x4, 0xef, 0xfe, 0x20,
    0x0, 0x5f, 0xe6, 0x10, 0x1c, 0xf8, 0x0, 0x0,
    0x1, 0x8d, 0xfd, 0x20, 0x9f, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x21,

    /* U+F071 "" */
    0x0, 0x0, 0x0, 0x69, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x50,
    0xef, 0x30, 0x0, 0x0, 0x6, 0xff, 0x50, 0xef,
    0xc0, 0x0, 0x0, 0x1e, 0xff, 0x50, 0xff, 0xf5,
    0x0, 0x0, 0x9f, 0xff, 0xa6, 0xff, 0xfe, 0x0,
    0x2, 0xff, 0xff, 0x61, 0xff, 0xff, 0x80, 0xb,
    0xff, 0xff, 0x72, 0xff, 0xff, 0xf1, 0xe, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf4, 0x2, 0x55, 0x55,
    0x55, 0x55, 0x55, 0x40,

    /* U+F074 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0x80, 0xaa, 0xa1, 0x0, 0x4a, 0xef,
    0x8f, 0xff, 0xc0, 0x4f, 0xff, 0xfc, 0x0, 0x8d,
    0x4f, 0xf3, 0xcd, 0x10, 0x0, 0x4f, 0xf3, 0x3,
    0x10, 0x0, 0x2e, 0xf5, 0x80, 0xa7, 0x9, 0xae,
    0xf4, 0x7f, 0xce, 0xf7, 0xff, 0xf5, 0x0, 0xaf,
    0xff, 0xd1, 0x11, 0x0, 0x0, 0x1c, 0xd1, 0x0,
    0x0, 0x0, 0x0, 0x41, 0x0,

    /* U+F077 "" */
    0x0, 0x1, 0xc7, 0x0, 0x0, 0x0, 0x1c, 0xff,
    0x70, 0x0, 0x1, 0xcf, 0x9d, 0xf7, 0x0, 0x1c,
    0xf8, 0x1, 0xdf, 0x70, 0xbf, 0x80, 0x0, 0x1d,
    0xf5, 0x47, 0x0, 0x0, 0x1, 0x91,

    /* U+F078 "" */
    0x8b, 0x0, 0x0, 0x3, 0xd3, 0x9f, 0xc0, 0x0,
    0x3f, 0xf3, 0x9, 0xfc, 0x3, 0xff, 0x40, 0x0,
    0x9f, 0xcf, 0xf4, 0x0, 0x0, 0x9, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x84, 0x0, 0x0,

    /* U+F079 "" */
    0x0, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xfb, 0x4, 0x66, 0x66, 0x61, 0x0, 0x4f, 0xff,
    0xb8, 0xff, 0xff, 0xf7, 0x0, 0xdb, 0xfb, 0xf5,
    0x0, 0x0, 0xe7, 0x0, 0x10, 0xf7, 0x10, 0x0,
    0x0, 0xe7, 0x0, 0x0, 0xf7, 0x0, 0x0, 0x20,
    0xe7, 0x20, 0x0, 0xf7, 0x11, 0x10, 0xcc, 0xec,
    0xf5, 0x0, 0xef, 0xff, 0xff, 0x5e, 0xff, 0xa0,
    0x0, 0x35, 0x55, 0x54, 0x3, 0xea, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x10, 0x0,

    /* U+F07B "" */
    0x13, 0x44, 0x20, 0x0, 0x0, 0xe, 0xff, 0xff,
    0x40, 0x0, 0x0, 0xff, 0xff, 0xff, 0xee, 0xed,
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xcf, 0xff, 0xff, 0xff,
    0xff, 0xc0,

    /* U+F093 "" */
    0x0, 0x0, 0x7, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xfb, 0x0, 0x0, 0x0, 0xb, 0xff, 0xfb, 0x0,
    0x0, 0xb, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x88,
    0xff, 0xf8, 0x80, 0x0, 0x0, 0xe, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0xef, 0xe0, 0x0, 0x0, 0x0,
    0xe, 0xfe, 0x0, 0x0, 0xff, 0xf8, 0x9b, 0x98,
    0xff, 0xff, 0xff, 0xf8, 0x78, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf9, 0xb6, 0xf8, 0x99, 0x99, 0x99,
    0x99, 0x98,

    /* U+F095 "" */
    0x0, 0x0, 0x0, 0x0, 0x18, 0x51, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xfe, 0x0, 0x0, 0x0, 0x2, 0xff, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xf2, 0x0, 0x0, 0x0, 0x1,
    0xef, 0x90, 0x0, 0x5c, 0x70, 0x2d, 0xfd, 0x0,
    0xd, 0xff, 0xfa, 0xff, 0xd1, 0x0, 0xc, 0xff,
    0xff, 0xfb, 0x10, 0x0, 0x9, 0xff, 0xfb, 0x40,
    0x0, 0x0, 0x1, 0x53, 0x10, 0x0, 0x0, 0x0,

    /* U+F0C4 "" */
    0x3c, 0xd4, 0x0, 0x5, 0x82, 0xdc, 0xbf, 0x0,
    0x9f, 0xf5, 0xea, 0x8f, 0x9, 0xff, 0x60, 0x6f,
    0xff, 0xdf, 0xf6, 0x0, 0x1, 0x6f, 0xff, 0x60,
    0x0, 0x4, 0xaf, 0xff, 0x70, 0x0, 0x9f, 0xff,
    0x9f, 0xf7, 0x0, 0xf8, 0x6f, 0x16, 0xff, 0x70,
    0xcd, 0xce, 0x0, 0x6f, 0xf6, 0x2b, 0xc3, 0x0,
    0x4, 0x71,

    /* U+F0C5 "" */
    0x0, 0x1d, 0xdd, 0xd3, 0x70, 0x0, 0x3f, 0xff,
    0xf4, 0xf7, 0x79, 0x4f, 0xff, 0xf2, 0x32, 0xff,
    0x4f, 0xff, 0xfe, 0xeb, 0xff, 0x4f, 0xff, 0xff,
    0xfd, 0xff, 0x4f, 0xff, 0xff, 0xfd, 0xff, 0x4f,
    0xff, 0xff, 0xfd, 0xff, 0x4f, 0xff, 0xff, 0xfd,
    0xff, 0x4f, 0xff, 0xff, 0xfd, 0xff, 0x64, 0x55,
    0x55, 0x53, 0xff, 0xff, 0xff, 0xf0, 0x0, 0x46,
    0x66, 0x66, 0x50, 0x0,

    /* U+F0C7 "" */
    0xbf, 0xff, 0xff, 0xfa, 0x0, 0xfa, 0x66, 0x66,
    0x8f, 0xa0, 0xf6, 0x0, 0x0, 0x1f, 0xf8, 0xf6,
    0x0, 0x0, 0x1f, 0xfa, 0xff, 0xff, 0xff, 0xff,
    0xfa, 0xff, 0xff, 0x8a, 0xff, 0xfa, 0xff, 0xf8,
    0x0, 0xdf, 0xfa, 0xff, 0xf9, 0x0, 0xef, 0xfa,
    0xff, 0xff, 0x9b, 0xff, 0xfa, 0xae, 0xee, 0xee,
    0xee, 0xe6,

    /* U+F0C9 "" */
    0x78, 0x88, 0x88, 0x88, 0x84, 0xff, 0xff, 0xff,
    0xff, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0xff, 0xff, 0xff,
    0xf8, 0x89, 0x99, 0x99, 0x99, 0x95, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x78, 0x88, 0x88, 0x88, 0x84,
    0xff, 0xff, 0xff, 0xff, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F0E0 "" */
    0x13, 0x44, 0x44, 0x44, 0x43, 0x1e, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0xc5, 0x9f, 0xff, 0xff, 0xff, 0x95, 0xf9, 0x5e,
    0xff, 0xfe, 0x58, 0xff, 0xfc, 0x4c, 0xfc, 0x4c,
    0xff, 0xff, 0xff, 0x64, 0x6f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xcf, 0xff, 0xff, 0xff,
    0xff, 0xc0,

    /* U+F0E7 "" */
    0x3, 0xdd, 0xd9, 0x0, 0x6, 0xff, 0xf9, 0x0,
    0x9, 0xff, 0xf3, 0x0, 0xb, 0xff, 0xf6, 0x64,
    0xd, 0xff, 0xff, 0xfb, 0xf, 0xff, 0xff, 0xf2,
    0x4, 0x55, 0xff, 0x90, 0x0, 0x3, 0xfe, 0x10,
    0x0, 0x7, 0xf6, 0x0, 0x0, 0xa, 0xd0, 0x0,
    0x0, 0xd, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F0EA "" */
    0x1, 0x5b, 0x41, 0x0, 0x0, 0xff, 0xf4, 0xff,
    0xf0, 0x0, 0xff, 0xff, 0xff, 0xf1, 0x0, 0xff,
    0xe4, 0x55, 0x51, 0x10, 0xff, 0xc8, 0xff, 0xf4,
    0xd1, 0xff, 0xc8, 0xff, 0xf3, 0xa7, 0xff, 0xc8,
    0xff, 0xfb, 0xa8, 0xff, 0xc8, 0xff, 0xff, 0xfd,
    0xff, 0xc8, 0xff, 0xff, 0xfd, 0x1, 0x18, 0xff,
    0xff, 0xfd, 0x0, 0x8, 0xff, 0xff, 0xfd, 0x0,
    0x0, 0x22, 0x22, 0x21,

    /* U+F0F3 "" */
    0x0, 0x0, 0x62, 0x0, 0x0, 0x0, 0x4, 0xeb,
    0x20, 0x0, 0x0, 0x9f, 0xff, 0xf4, 0x0, 0x5,
    0xff, 0xff, 0xfe, 0x0, 0x9, 0xff, 0xff, 0xff,
    0x30, 0xb, 0xff, 0xff, 0xff, 0x50, 0xd, 0xff,
    0xff, 0xff, 0x70, 0x4f, 0xff, 0xff, 0xff, 0xd0,
    0xef, 0xff, 0xff, 0xff, 0xf8, 0x35, 0x55, 0x55,
    0x55, 0x41, 0x0, 0x6, 0xff, 0x10, 0x0, 0x0,
    0x0, 0x42, 0x0, 0x0,

    /* U+F11C "" */
    0x13, 0x44, 0x44, 0x44, 0x44, 0x43, 0xe, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf4, 0xf7, 0x2b, 0x2c,
    0x2c, 0x2c, 0x2f, 0x6f, 0xdb, 0xeb, 0xeb, 0xeb,
    0xeb, 0xf6, 0xff, 0x60, 0xb0, 0xa0, 0xb0, 0xff,
    0x6f, 0xfd, 0xae, 0xae, 0xae, 0xaf, 0xf6, 0xf6,
    0x1a, 0x0, 0x0, 0xb, 0xf, 0x6f, 0xa6, 0xc6,
    0x66, 0x66, 0xd6, 0xf5, 0xcf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x30,

    /* U+F124 "" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0x75, 0x0, 0x0,
    0x0, 0x0, 0x29, 0xff, 0xe0, 0x0, 0x0, 0x3,
    0xaf, 0xff, 0xf9, 0x0, 0x0, 0x4c, 0xff, 0xff,
    0xff, 0x20, 0x5, 0xdf, 0xff, 0xff, 0xff, 0xa0,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xf3, 0x0, 0x5,
    0x89, 0x9a, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xff, 0x40, 0x0, 0x0, 0x0, 0x3, 0xff,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0x20, 0x0, 0x0,

    /* U+F15B "" */
    0x67, 0x77, 0x63, 0x0, 0xf, 0xff, 0xfd, 0x8c,
    0x0, 0xff, 0xff, 0xd8, 0xfc, 0xf, 0xff, 0xfd,
    0x35, 0x51, 0xff, 0xff, 0xff, 0xff, 0x4f, 0xff,
    0xff, 0xff, 0xf4, 0xff, 0xff, 0xff, 0xff, 0x4f,
    0xff, 0xff, 0xff, 0xf4, 0xff, 0xff, 0xff, 0xff,
    0x4f, 0xff, 0xff, 0xff, 0xf4, 0xff, 0xff, 0xff,
    0xff, 0x47, 0x88, 0x88, 0x88, 0x81,

    /* U+F1EB "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xbe, 0xff, 0xea, 0x50, 0x0, 0x6, 0xff,
    0xfd, 0xbc, 0xef, 0xfd, 0x40, 0xaf, 0xe6, 0x10,
    0x0, 0x2, 0x8f, 0xf6, 0x7b, 0x10, 0x26, 0x88,
    0x51, 0x2, 0xc4, 0x0, 0x1a, 0xff, 0xff, 0xff,
    0x70, 0x0, 0x0, 0x6f, 0xc5, 0x23, 0x6e, 0xf2,
    0x0, 0x0, 0x4, 0x0, 0x0, 0x0, 0x30, 0x0,
    0x0, 0x0, 0x3, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x97, 0x0, 0x0, 0x0,

    /* U+F240 "" */
    0x36, 0x66, 0x66, 0x66, 0x66, 0x66, 0x40, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0xf6, 0x67,
    0x77, 0x77, 0x77, 0x73, 0xfe, 0xf6, 0xef, 0xff,
    0xff, 0xff, 0xf3, 0xae, 0xf6, 0xef, 0xff, 0xff,
    0xff, 0xf3, 0xbe, 0xf6, 0x45, 0x55, 0x55, 0x55,
    0x53, 0xfc, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x24, 0x55, 0x55, 0x55, 0x55, 0x55, 0x20,

    /* U+F241 "" */
    0x36, 0x66, 0x66, 0x66, 0x66, 0x66, 0x40, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0xf6, 0x67,
    0x77, 0x77, 0x70, 0x2, 0xfe, 0xf6, 0xef, 0xff,
    0xff, 0xf1, 0x0, 0xae, 0xf6, 0xef, 0xff, 0xff,
    0xf1, 0x0, 0xbe, 0xf6, 0x45, 0x55, 0x55, 0x51,
    0x13, 0xfc, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x24, 0x55, 0x55, 0x55, 0x55, 0x55, 0x20,

    /* U+F242 "" */
    0x36, 0x66, 0x66, 0x66, 0x66, 0x66, 0x40, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0xf6, 0x67,
    0x77, 0x60, 0x0, 0x2, 0xfe, 0xf6, 0xef, 0xff,
    0xf0, 0x0, 0x0, 0xae, 0xf6, 0xef, 0xff, 0xf0,
    0x0, 0x0, 0xbe, 0xf6, 0x45, 0x55, 0x41, 0x11,
    0x13, 0xfc, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x24, 0x55, 0x55, 0x55, 0x55, 0x55, 0x20,

    /* U+F243 "" */
    0x36, 0x66, 0x66, 0x66, 0x66, 0x66, 0x40, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0xf6, 0x67,
    0x60, 0x0, 0x0, 0x2, 0xfe, 0xf6, 0xef, 0xe0,
    0x0, 0x0, 0x0, 0xae, 0xf6, 0xef, 0xe0, 0x0,
    0x0, 0x0, 0xbe, 0xf6, 0x45, 0x41, 0x11, 0x11,
    0x13, 0xfc, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x24, 0x55, 0x55, 0x55, 0x55, 0x55, 0x20,

    /* U+F244 "" */
    0x36, 0x66, 0x66, 0x66, 0x66, 0x66, 0x40, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xfd, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xae, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbe, 0xf6, 0x11, 0x11, 0x11, 0x11,
    0x13, 0xfc, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x24, 0x55, 0x55, 0x55, 0x55, 0x55, 0x20,

    /* U+F287 "" */
    0x0, 0x0, 0x0, 0x2, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x3, 0x8f, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x1c, 0x3b, 0xc0, 0x0, 0x0, 0x6d, 0x70, 0x93,
    0x0, 0x0, 0x5, 0x30, 0xff, 0xfb, 0xfb, 0xbb,
    0xbb, 0xbe, 0xf7, 0xaf, 0xb0, 0x5, 0x90, 0x0,
    0x8, 0x80, 0x2, 0x0, 0x0, 0xc0, 0x13, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x4a, 0xcf, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xbf, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x0, 0x0,

    /* U+F293 "" */
    0x0, 0x19, 0xcd, 0xa3, 0x0, 0x1, 0xef, 0xca,
    0xff, 0x30, 0x9, 0xff, 0xc0, 0xbf, 0xb0, 0xe,
    0xd8, 0xc5, 0x4d, 0xf0, 0xf, 0xf6, 0x43, 0x4f,
    0xf1, 0x1f, 0xff, 0x50, 0xef, 0xf2, 0xf, 0xfc,
    0x10, 0x8f, 0xf1, 0xf, 0xd2, 0xa6, 0x3b, 0xf0,
    0xb, 0xfe, 0xc2, 0x5f, 0xd0, 0x4, 0xff, 0xc5,
    0xff, 0x60, 0x0, 0x6e, 0xff, 0xf8, 0x0, 0x0,
    0x0, 0x12, 0x0, 0x0,

    /* U+F2ED "" */
    0x0, 0x8, 0xaa, 0x40, 0x0, 0xef, 0xff, 0xff,
    0xff, 0xf9, 0x56, 0x66, 0x66, 0x66, 0x63, 0x4f,
    0xff, 0xff, 0xff, 0xe0, 0x5f, 0xbf, 0xbe, 0xce,
    0xf0, 0x5f, 0x6e, 0x7d, 0x8c, 0xf0, 0x5f, 0x6e,
    0x7d, 0x8c, 0xf0, 0x5f, 0x6e, 0x7d, 0x8c, 0xf0,
    0x5f, 0x6e, 0x7d, 0x8c, 0xf0, 0x5f, 0x7e, 0x8d,
    0x9c, 0xf0, 0x4f, 0xff, 0xff, 0xff, 0xe0, 0x4,
    0x66, 0x66, 0x65, 0x20,

    /* U+F304 "" */
    0x0, 0x0, 0x0, 0x0, 0x18, 0x30, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xf4, 0x0, 0x0, 0x0, 0x15,
    0xaf, 0xfe, 0x0, 0x0, 0x1, 0xcf, 0x6a, 0xf9,
    0x0, 0x0, 0x1c, 0xff, 0xf6, 0x60, 0x0, 0x1,
    0xcf, 0xff, 0xf9, 0x0, 0x0, 0x1c, 0xff, 0xff,
    0x90, 0x0, 0x1, 0xcf, 0xff, 0xf9, 0x0, 0x0,
    0xa, 0xff, 0xff, 0x90, 0x0, 0x0, 0xd, 0xff,
    0xf9, 0x0, 0x0, 0x0, 0xf, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x4, 0x42, 0x0, 0x0, 0x0, 0x0,

    /* U+F55A "" */
    0x0, 0x0, 0x34, 0x44, 0x44, 0x44, 0x30, 0x0,
    0x1d, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x1, 0xdf,
    0xff, 0xaf, 0xfb, 0xef, 0xfb, 0x1d, 0xff, 0xff,
    0x13, 0x90, 0x8f, 0xfc, 0xcf, 0xff, 0xff, 0xd1,
    0x6, 0xff, 0xfc, 0xaf, 0xff, 0xff, 0xa0, 0x3,
    0xff, 0xfc, 0xa, 0xff, 0xff, 0x6, 0xd1, 0x7f,
    0xfc, 0x0, 0xaf, 0xff, 0xdf, 0xfe, 0xff, 0xfb,
    0x0, 0x9, 0xff, 0xff, 0xff, 0xff, 0xf5,

    /* U+F7C2 "" */
    0x0, 0x36, 0x66, 0x63, 0x0, 0x4e, 0xff, 0xff,
    0xf2, 0x4f, 0x6b, 0xb, 0x2f, 0x4f, 0xf6, 0xb0,
    0xb2, 0xf4, 0xff, 0xff, 0xff, 0xff, 0x4f, 0xff,
    0xff, 0xff, 0xf4, 0xff, 0xff, 0xff, 0xff, 0x4f,
    0xff, 0xff, 0xff, 0xf4, 0xff, 0xff, 0xff, 0xff,
    0x4f, 0xff, 0xff, 0xff, 0xf4, 0xef, 0xff, 0xff,
    0xff, 0x22, 0x88, 0x88, 0x88, 0x40,

    /* U+F8A2 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xf0, 0x0, 0xb6, 0x0, 0x0,
    0xa, 0xf0, 0xc, 0xf8, 0x11, 0x11, 0x1b, 0xf0,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x6f, 0xfd,
    0xaa, 0xaa, 0xaa, 0x90, 0x5, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x43, 0x0, 0x0, 0x0, 0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 47, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 47, .box_w = 3, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 12, .adv_w = 69, .box_w = 4, .box_h = 4, .ofs_x = 0, .ofs_y = 4},
    {.bitmap_index = 20, .adv_w = 124, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 52, .adv_w = 109, .box_w = 7, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 94, .adv_w = 148, .box_w = 9, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 130, .adv_w = 121, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 166, .adv_w = 37, .box_w = 2, .box_h = 4, .ofs_x = 0, .ofs_y = 4},
    {.bitmap_index = 170, .adv_w = 59, .box_w = 3, .box_h = 10, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 185, .adv_w = 59, .box_w = 3, .box_h = 10, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 200, .adv_w = 70, .box_w = 5, .box_h = 5, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 213, .adv_w = 102, .box_w = 6, .box_h = 5, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 228, .adv_w = 40, .box_w = 2, .box_h = 4, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 232, .adv_w = 67, .box_w = 4, .box_h = 1, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 234, .adv_w = 40, .box_w = 2, .box_h = 2, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 236, .adv_w = 62, .box_w = 6, .box_h = 11, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 269, .adv_w = 117, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 297, .adv_w = 65, .box_w = 3, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 309, .adv_w = 101, .box_w = 6, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 333, .adv_w = 101, .box_w = 6, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 357, .adv_w = 118, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 389, .adv_w = 101, .box_w = 6, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 413, .adv_w = 109, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 441, .adv_w = 105, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 469, .adv_w = 113, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 497, .adv_w = 109, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 525, .adv_w = 40, .box_w = 2, .box_h = 6, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 531, .adv_w = 40, .box_w = 2, .box_h = 8, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 539, .adv_w = 102, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 557, .adv_w = 102, .box_w = 6, .box_h = 4, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 569, .adv_w = 102, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 587, .adv_w = 101, .box_w = 6, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 611, .adv_w = 182, .box_w = 11, .box_h = 10, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 666, .adv_w = 129, .box_w = 10, .box_h = 8, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 706, .adv_w = 133, .box_w = 7, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 734, .adv_w = 127, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 766, .adv_w = 145, .box_w = 8, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 798, .adv_w = 118, .box_w = 6, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 822, .adv_w = 112, .box_w = 6, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 846, .adv_w = 136, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 878, .adv_w = 143, .box_w = 7, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 906, .adv_w = 55, .box_w = 2, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 914, .adv_w = 90, .box_w = 6, .box_h = 8, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 938, .adv_w = 127, .box_w = 7, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 966, .adv_w = 105, .box_w = 6, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 990, .adv_w = 168, .box_w = 9, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1026, .adv_w = 143, .box_w = 7, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1054, .adv_w = 148, .box_w = 9, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1090, .adv_w = 127, .box_w = 7, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1118, .adv_w = 148, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1168, .adv_w = 128, .box_w = 7, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1196, .adv_w = 109, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1224, .adv_w = 103, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1252, .adv_w = 139, .box_w = 7, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1280, .adv_w = 125, .box_w = 9, .box_h = 8, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 1316, .adv_w = 198, .box_w = 13, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1368, .adv_w = 118, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1400, .adv_w = 114, .box_w = 9, .box_h = 8, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 1436, .adv_w = 116, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1464, .adv_w = 59, .box_w = 3, .box_h = 10, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 1479, .adv_w = 62, .box_w = 6, .box_h = 11, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 1512, .adv_w = 59, .box_w = 3, .box_h = 10, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1527, .adv_w = 103, .box_w = 6, .box_h = 5, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 1542, .adv_w = 88, .box_w = 6, .box_h = 1, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 1545, .adv_w = 106, .box_w = 4, .box_h = 2, .ofs_x = 1, .ofs_y = 7},
    {.bitmap_index = 1549, .adv_w = 105, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1567, .adv_w = 120, .box_w = 7, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1595, .adv_w = 100, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1613, .adv_w = 120, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1641, .adv_w = 108, .box_w = 7, .box_h = 6, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1662, .adv_w = 62, .box_w = 5, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1682, .adv_w = 121, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1710, .adv_w = 120, .box_w = 6, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1734, .adv_w = 49, .box_w = 3, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1746, .adv_w = 50, .box_w = 5, .box_h = 10, .ofs_x = -2, .ofs_y = -2},
    {.bitmap_index = 1771, .adv_w = 108, .box_w = 6, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1795, .adv_w = 49, .box_w = 2, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1803, .adv_w = 186, .box_w = 10, .box_h = 6, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1833, .adv_w = 120, .box_w = 6, .box_h = 6, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1851, .adv_w = 112, .box_w = 7, .box_h = 6, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1872, .adv_w = 120, .box_w = 7, .box_h = 8, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 1900, .adv_w = 120, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1928, .adv_w = 72, .box_w = 4, .box_h = 6, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1940, .adv_w = 88, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1958, .adv_w = 73, .box_w = 5, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1978, .adv_w = 119, .box_w = 7, .box_h = 6, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1999, .adv_w = 98, .box_w = 8, .box_h = 6, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 2023, .adv_w = 158, .box_w = 10, .box_h = 6, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2053, .adv_w = 97, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2071, .adv_w = 98, .box_w = 8, .box_h = 8, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 2103, .adv_w = 92, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2121, .adv_w = 62, .box_w = 4, .box_h = 10, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2141, .adv_w = 53, .box_w = 2, .box_h = 10, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 2151, .adv_w = 62, .box_w = 4, .box_h = 10, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2171, .adv_w = 102, .box_w = 6, .box_h = 2, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 2177, .adv_w = 176, .box_w = 11, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2243, .adv_w = 176, .box_w = 11, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2293, .adv_w = 176, .box_w = 11, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2348, .adv_w = 176, .box_w = 11, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2398, .adv_w = 121, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2430, .adv_w = 176, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2502, .adv_w = 176, .box_w = 11, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2568, .adv_w = 198, .box_w = 13, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2633, .adv_w = 176, .box_w = 12, .box_h = 12, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 2705, .adv_w = 198, .box_w = 13, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2764, .adv_w = 176, .box_w = 11, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2830, .adv_w = 88, .box_w = 6, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2860, .adv_w = 132, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2905, .adv_w = 198, .box_w = 13, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2983, .adv_w = 176, .box_w = 11, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3033, .adv_w = 121, .box_w = 8, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3081, .adv_w = 154, .box_w = 8, .box_h = 11, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 3125, .adv_w = 154, .box_w = 10, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3185, .adv_w = 154, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3235, .adv_w = 154, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3285, .adv_w = 154, .box_w = 8, .box_h = 11, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 3329, .adv_w = 154, .box_w = 11, .box_h = 10, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 3384, .adv_w = 110, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3419, .adv_w = 110, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3454, .adv_w = 154, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3504, .adv_w = 154, .box_w = 10, .box_h = 3, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 3519, .adv_w = 198, .box_w = 13, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3578, .adv_w = 220, .box_w = 14, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3662, .adv_w = 198, .box_w = 14, .box_h = 12, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 3746, .adv_w = 176, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3807, .adv_w = 154, .box_w = 10, .box_h = 6, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 3837, .adv_w = 154, .box_w = 10, .box_h = 6, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 3867, .adv_w = 220, .box_w = 14, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3937, .adv_w = 176, .box_w = 11, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3987, .adv_w = 176, .box_w = 11, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4053, .adv_w = 176, .box_w = 12, .box_h = 12, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 4125, .adv_w = 154, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4175, .adv_w = 154, .box_w = 10, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4235, .adv_w = 154, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4285, .adv_w = 154, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4335, .adv_w = 176, .box_w = 11, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4385, .adv_w = 110, .box_w = 8, .box_h = 12, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 4433, .adv_w = 154, .box_w = 10, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4493, .adv_w = 154, .box_w = 10, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4553, .adv_w = 198, .box_w = 13, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4612, .adv_w = 176, .box_w = 13, .box_h = 12, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 4690, .adv_w = 132, .box_w = 9, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4744, .adv_w = 220, .box_w = 14, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4821, .adv_w = 220, .box_w = 14, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4877, .adv_w = 220, .box_w = 14, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4933, .adv_w = 220, .box_w = 14, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4989, .adv_w = 220, .box_w = 14, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5045, .adv_w = 220, .box_w = 14, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5101, .adv_w = 220, .box_w = 14, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5171, .adv_w = 154, .box_w = 10, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5231, .adv_w = 154, .box_w = 10, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5291, .adv_w = 176, .box_w = 12, .box_h = 12, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 5363, .adv_w = 220, .box_w = 14, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5426, .adv_w = 132, .box_w = 9, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5480, .adv_w = 177, .box_w = 12, .box_h = 8, .ofs_x = 0, .ofs_y = 0}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_1[] = {
    0x0, 0x7, 0xa, 0xb, 0xc, 0x10, 0x12, 0x14,
    0x18, 0x1b, 0x20, 0x25, 0x26, 0x27, 0x3d, 0x42,
    0x47, 0x4a, 0x4b, 0x4c, 0x50, 0x51, 0x52, 0x53,
    0x66, 0x67, 0x6d, 0x6f, 0x70, 0x73, 0x76, 0x77,
    0x78, 0x7a, 0x92, 0x94, 0xc3, 0xc4, 0xc6, 0xc8,
    0xdf, 0xe6, 0xe9, 0xf2, 0x11b, 0x123, 0x15a, 0x1ea,
    0x23f, 0x240, 0x241, 0x242, 0x243, 0x286, 0x292, 0x2ec,
    0x303, 0x559, 0x7c1, 0x8a1
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 61441, .range_length = 2210, .glyph_id_start = 96,
        .unicode_list = unicode_list_1, .glyph_id_ofs_list = NULL, .list_length = 60, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/


/*Map glyph_ids to kern left classes*/
static const uint8_t kern_left_class_mapping[] =
{
    0, 0, 1, 2, 0, 3, 4, 5,
    2, 6, 7, 8, 9, 10, 9, 10,
    11, 12, 0, 13, 14, 15, 16, 17,
    18, 19, 12, 20, 20, 0, 0, 0,
    21, 22, 23, 24, 25, 22, 26, 27,
    28, 29, 29, 30, 31, 32, 29, 29,
    22, 33, 34, 35, 3, 36, 30, 37,
    37, 38, 39, 40, 41, 42, 43, 0,
    44, 0, 45, 46, 47, 48, 49, 50,
    51, 45, 52, 52, 53, 48, 45, 45,
    46, 46, 54, 55, 56, 57, 51, 58,
    58, 59, 58, 60, 41, 0, 0, 9,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0
};

/*Map glyph_ids to kern right classes*/
static const uint8_t kern_right_class_mapping[] =
{
    0, 0, 1, 2, 0, 3, 4, 5,
    2, 6, 7, 8, 9, 10, 9, 10,
    11, 12, 13, 14, 15, 16, 17, 12,
    18, 19, 20, 21, 21, 0, 0, 0,
    22, 23, 24, 25, 23, 25, 25, 25,
    23, 25, 25, 26, 25, 25, 25, 25,
    23, 25, 23, 25, 3, 27, 28, 29,
    29, 30, 31, 32, 33, 34, 35, 0,
    36, 0, 37, 38, 39, 39, 39, 0,
    39, 38, 40, 41, 38, 38, 42, 42,
    39, 42, 39, 42, 43, 44, 45, 46,
    46, 47, 46, 48, 0, 0, 35, 9,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0
};

/*Kern values between classes*/
static const int8_t kern_class_values[] =
{
    0, 1, 0, 0, 0, 0, 0, 0,
    0, 1, 0, 0, 2, 0, 0, 0,
    0, 1, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 1, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    1, 8, 0, 5, -4, 0, 0, 0,
    0, -10, -11, 1, 8, 4, 3, -7,
    1, 9, 1, 7, 2, 6, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 11, 1, -1, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -5, 0, 0, 0, 0, 0, -4,
    3, 4, 0, 0, -2, 0, -1, 2,
    0, -2, 0, -2, -1, -4, 0, 0,
    0, 0, -2, 0, 0, -2, -3, 0,
    0, -2, 0, -4, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -2, -2, 0,
    0, -5, 0, -21, 0, 0, -4, 0,
    4, 5, 0, 0, -4, 2, 2, 6,
    4, -3, 4, 0, 0, -10, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -7, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -2, -9, 0, -7, -1, 0, 0, 0,
    0, 0, 7, 0, -5, -1, -1, 1,
    0, -3, 0, 0, -1, -13, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -14, -1, 7, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 6, 0, 2, 0, 0, -4,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 7, 1, 1, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -7, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    1, 4, 2, 5, -2, 0, 0, 4,
    -2, -6, -24, 1, 5, 4, 0, -2,
    0, 6, 0, 6, 0, 6, 0, -16,
    0, -2, 5, 0, 6, -2, 4, 2,
    0, 0, 1, -2, 0, 0, -3, 14,
    0, 14, 0, 5, 0, 7, 2, 3,
    0, 0, 0, -7, 0, 0, 0, 0,
    1, -1, 0, 1, -3, -2, -4, 1,
    0, -2, 0, 0, 0, -7, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -11, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    1, -10, 0, -11, 0, 0, 0, 0,
    -1, 0, 17, -2, -2, 2, 2, -2,
    0, -2, 2, 0, 0, -9, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -17, 0, 2, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 11, 0, 0, -7, 0, 6, 0,
    -12, -17, -12, -4, 5, 0, 0, -12,
    0, 2, -4, 0, -3, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 5, 5, -21, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 1, 0, 0, 0, 0, 0, 1,
    1, -2, -4, 0, -1, -1, -2, 0,
    0, -1, 0, 0, 0, -4, 0, -1,
    0, -4, -4, 0, -4, -6, -6, -3,
    0, -4, 0, -4, 0, 0, 0, 0,
    -1, 0, 0, 2, 0, 1, -2, 0,
    0, 0, 0, 2, -1, 0, 0, 0,
    -1, 2, 2, -1, 0, 0, 0, -3,
    0, -1, 0, 0, 0, 0, 0, 1,
    0, 2, -1, 0, -2, 0, -3, 0,
    0, -1, 0, 5, 0, 0, -2, 0,
    0, 0, 0, 0, -1, 1, -1, -1,
    0, -2, 0, -2, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -1, -1, 0,
    -2, -2, 0, 0, 0, 0, 0, 1,
    0, 0, -1, 0, -2, -2, -2, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -1, 0, 0, 0, 0, -1, -2, 0,
    0, -5, -1, -5, 4, 0, 0, -4,
    2, 4, 5, 0, -4, -1, -2, 0,
    -1, -8, 2, -1, 1, -9, 2, 0,
    0, 1, -9, 0, -9, -1, -15, -1,
    0, -9, 0, 4, 5, 0, 2, 0,
    0, 0, 0, 0, 0, -3, -2, 0,
    0, 0, 0, -2, 0, 0, 0, -2,
    0, 0, 0, 0, 0, -1, -1, 0,
    -1, -2, 0, 0, 0, 0, 0, 0,
    0, -2, -2, 0, -1, -2, -1, 0,
    0, -2, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -1, -1, 0,
    0, -1, 0, -4, 2, 0, 0, -2,
    1, 2, 2, 0, 0, 0, 0, 0,
    0, -1, 0, 0, 0, 0, 0, 1,
    0, 0, -2, 0, -2, -1, -2, 0,
    0, 0, 0, 0, 0, 0, 1, 0,
    -1, 0, 0, 0, 0, -2, -3, 0,
    0, 5, -1, 1, -6, 0, 0, 5,
    -9, -9, -7, -4, 2, 0, -1, -11,
    -3, 0, -3, 0, -4, 3, -3, -11,
    0, -5, 0, 0, 1, -1, 1, -1,
    0, 2, 0, -5, -7, 0, -9, -4,
    -4, -4, -5, -2, -5, 0, -3, -5,
    0, 1, 0, -2, 0, 0, 0, 1,
    0, 2, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -2, 0, -1,
    0, -1, -2, 0, -3, -4, -4, -1,
    0, -5, 0, 0, 0, 0, 0, 0,
    -1, 0, 0, 0, 0, 1, -1, 0,
    0, 2, 0, 0, 0, 0, 0, 0,
    0, 0, 8, 0, 0, 0, 0, 0,
    0, 1, 0, 0, 0, -2, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -3, 0, 2, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -1, 0, 0, 0, -3, 0, 0, 0,
    0, -9, -5, 0, 0, 0, -3, -9,
    0, 0, -2, 2, 0, -5, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -3, 0, 0, -3, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -3, 0, 0, 0, 0, 2, 0,
    1, -4, -4, 0, -2, -2, -2, 0,
    0, 0, 0, 0, 0, -5, 0, -2,
    0, -3, -2, 0, -4, -4, -5, -1,
    0, -4, 0, -5, 0, 0, 0, 0,
    14, 0, 0, 1, 0, 0, -2, 0,
    0, -8, 0, 0, 0, 0, 0, -16,
    -3, 6, 5, -1, -7, 0, 2, -3,
    0, -9, -1, -2, 2, -12, -2, 2,
    0, 3, -6, -3, -7, -6, -7, 0,
    0, -11, 0, 10, 0, 0, -1, 0,
    0, 0, -1, -1, -2, -5, -6, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 1, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -2, 0, -1, -2, -3, 0,
    0, -4, 0, -2, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -1, 0, -4, 0, 0, 4,
    -1, 2, 0, -4, 2, -1, -1, -5,
    -2, 0, -2, -2, -1, 0, -3, -3,
    0, 0, -1, -1, -1, -3, -2, 0,
    0, -2, 0, 2, -1, 0, -4, 0,
    0, 0, -4, 0, -3, 0, -3, -3,
    0, 0, 0, 0, 0, 0, 0, 0,
    -4, 2, 0, -2, 0, -1, -2, -5,
    -1, -1, -1, -1, -1, -2, -1, 0,
    0, 0, 0, 0, -2, -1, -1, 0,
    0, 0, 0, 2, -1, 0, -1, 0,
    0, 0, -1, -2, -1, -2, -2, -2,
    1, 7, -1, 0, -5, 0, -1, 4,
    0, -2, -7, -2, 3, 0, 0, -8,
    -3, 2, -3, 1, 0, -1, -1, -6,
    0, -3, 1, 0, 0, -3, 0, 0,
    0, 2, 2, -4, -3, 0, -3, -2,
    -3, -2, -2, 0, -3, 1, -3, -3,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 2, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -3, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -1, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -1, -2, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -3,
    0, 0, -2, 0, 0, -2, -2, 0,
    0, 0, 0, -2, 0, 0, 0, 0,
    -1, 0, 0, 0, 0, 0, -1, 0,
    0, 0, -3, 0, -4, 0, 0, 0,
    -6, 0, 1, -4, 4, 0, -1, -8,
    0, 0, -4, -2, 0, -7, -4, -5,
    0, 0, -8, -2, -7, -7, -8, 0,
    -5, 0, 1, 12, -2, 0, -4, -2,
    -1, -2, -3, -5, -3, -7, -7, -4,
    0, 0, -1, 0, 1, 0, 0, -12,
    -2, 5, 4, -4, -7, 0, 1, -5,
    0, -9, -1, -2, 4, -16, -2, 1,
    0, 0, -11, -2, -9, -2, -13, 0,
    0, -12, 0, 10, 1, 0, -1, 0,
    0, 0, 0, -1, -1, -7, -1, 0,
    0, 0, 0, 0, -6, 0, -2, 0,
    -1, -5, -8, 0, 0, -1, -3, -5,
    -2, 0, -1, 0, 0, 0, 0, -8,
    -2, -6, -6, -1, -3, -4, -2, -3,
    0, -4, -2, -6, -3, 0, -2, -3,
    -2, -3, 0, 1, 0, -1, -6, 0,
    0, -3, 0, 0, 0, 0, 2, 0,
    1, -4, 7, 0, -2, -2, -2, 0,
    0, 0, 0, 0, 0, -5, 0, -2,
    0, -3, -2, 0, -4, -4, -5, -1,
    0, -4, 1, 7, 0, 0, 0, 0,
    14, 0, 0, 1, 0, 0, -2, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -1, -4,
    0, 0, 0, 0, 0, -1, 0, 0,
    0, -2, -2, 0, 0, -4, -2, 0,
    0, -4, 0, 3, -1, 0, 0, 0,
    0, 0, 0, 1, 0, 0, 0, 0,
    4, 1, -2, 0, -6, -3, 0, 5,
    -6, -6, -4, -4, 7, 3, 2, -15,
    -1, 4, -2, 0, -2, 2, -2, -6,
    0, -2, 2, -2, -1, -5, -1, 0,
    0, 5, 4, 0, -5, 0, -10, -2,
    5, -2, -7, 1, -2, -6, -6, -2,
    2, 0, -3, 0, -5, 0, 1, 6,
    -4, -7, -7, -4, 5, 0, 1, -13,
    -1, 2, -3, -1, -4, 0, -4, -7,
    -3, -3, -1, 0, 0, -4, -4, -2,
    0, 5, 4, -2, -10, 0, -10, -2,
    0, -6, -10, -1, -6, -3, -6, -5,
    0, 0, -2, 0, -4, -2, 0, -2,
    -3, 0, 3, -6, 2, 0, 0, -9,
    0, -2, -4, -3, -1, -5, -4, -6,
    -4, 0, -5, -2, -4, -3, -5, -2,
    0, 0, 1, 8, -3, 0, -5, -2,
    0, -2, -4, -4, -5, -5, -7, -2,
    4, 0, -3, 0, -9, -2, 1, 4,
    -6, -7, -4, -6, 6, -2, 1, -16,
    -3, 4, -4, -3, -7, 0, -5, -7,
    -2, -2, -1, -2, -4, -5, -1, 0,
    0, 5, 5, -1, -11, 0, -11, -4,
    4, -7, -12, -4, -6, -7, -9, -6,
    0, 0, 0, 0, -2, 0, 0, 2,
    -2, 4, 1, -3, 4, 0, 0, -5,
    -1, 0, -1, 0, 1, 1, -1, 0,
    0, 0, 0, 0, 0, -2, 0, 0,
    0, 0, 1, 5, 0, 0, -2, 0,
    0, 0, 0, -1, -1, -2, 0, 0,
    1, 1, 0, 0, 0, 0, 1, 0,
    -1, 0, 7, 0, 3, 1, 1, -2,
    0, 4, 0, 0, 0, 1, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 5, 0, 5, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -11, 0, -2, 3, 0, 5, 0,
    0, 17, 2, -4, -4, 2, 2, -1,
    1, -9, 0, 0, 8, -11, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -12, 7, 25, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -3, 0, 0, -3, -2, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -1, 0, -5, 0, 0, 1, 0,
    0, 2, 23, -4, -1, 6, 5, -5,
    2, 0, 0, 2, 2, -2, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -23, 5, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -5, 0, 0, 0, -5,
    0, 0, 0, 0, -4, -1, 0, 0,
    0, -4, 0, -2, 0, -8, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -12, 0, 0, 0, 0, 1, 0,
    0, 0, 0, 0, 0, -2, 0, 0,
    0, -3, 0, -5, 0, 0, 0, -3,
    2, -2, 0, 0, -5, -2, -4, 0,
    0, -5, 0, -2, 0, -8, 0, -2,
    0, 0, -14, -3, -7, -2, -6, 0,
    0, -12, 0, -5, -1, 0, 0, 0,
    0, 0, 0, 0, 0, -3, -3, -1,
    0, 0, 0, 0, -4, 0, -4, 2,
    -2, 4, 0, -1, -4, -1, -3, -3,
    0, -2, -1, -1, 1, -5, -1, 0,
    0, 0, -15, -1, -2, 0, -4, 0,
    -1, -8, -2, 0, 0, -1, -1, 0,
    0, 0, 0, 1, 0, -1, -3, -1,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 2, 0, 0, 0, 0,
    0, -4, 0, -1, 0, 0, 0, -4,
    2, 0, 0, 0, -5, -2, -4, 0,
    0, -5, 0, -2, 0, -8, 0, 0,
    0, 0, -17, 0, -4, -7, -9, 0,
    0, -12, 0, -1, -3, 0, 0, 0,
    0, 0, 0, 0, 0, -2, -3, -1,
    1, 0, 0, 3, -2, 0, 5, 9,
    -2, -2, -5, 2, 9, 3, 4, -5,
    2, 7, 2, 5, 4, 5, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 11, 8, -3, -2, 0, -1, 14,
    8, 14, 0, 0, 0, 2, 0, 0,
    0, 0, -3, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -1, 0, 0, 0,
    0, 0, 0, 0, 0, 2, 0, 0,
    0, 0, -15, -2, -1, -7, -9, 0,
    0, -12, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -3, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -1, 0, 0, 0,
    0, 0, 0, 0, 0, 2, 0, 0,
    0, 0, -15, -2, -1, -7, -9, 0,
    0, -7, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -1, 0, 0, 0,
    -4, 2, 0, -2, 1, 3, 2, -5,
    0, 0, -1, 2, 0, 1, 0, 0,
    0, 0, -4, 0, -2, -1, -4, 0,
    -2, -7, 0, 11, -2, 0, -4, -1,
    0, -1, -3, 0, -2, -5, -4, -2,
    0, 0, -3, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -1, 0, 0, 0,
    0, 0, 0, 0, 0, 2, 0, 0,
    0, 0, -15, -2, -1, -7, -9, 0,
    0, -12, 0, 0, 0, 0, 0, 0,
    9, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -3, 0, -6, -2, -2, 5,
    -2, -2, -7, 1, -1, 1, -1, -5,
    0, 4, 0, 1, 1, 1, -4, -7,
    -2, 0, -7, -3, -5, -7, -7, 0,
    -3, -4, -2, -2, -1, -1, -2, -1,
    0, -1, -1, 3, 0, 3, -1, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -1, -2, -2, 0,
    0, -5, 0, -1, 0, -3, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -11, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -2, -2, 0,
    0, 0, 0, 0, -1, 0, 0, -3,
    -2, 2, 0, -3, -3, -1, 0, -5,
    -1, -4, -1, -2, 0, -3, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -12, 0, 6, 0, 0, -3, 0,
    0, 0, 0, -2, 0, -2, 0, 0,
    0, 0, -1, 0, -4, 0, 0, 7,
    -2, -6, -5, 1, 2, 2, 0, -5,
    1, 3, 1, 5, 1, 6, -1, -5,
    0, 0, -7, 0, 0, -5, -5, 0,
    0, -4, 0, -2, -3, 0, -3, 0,
    -3, 0, -1, 3, 0, -1, -5, -2,
    0, 0, -2, 0, -4, 0, 0, 2,
    -4, 0, 2, -2, 1, 0, 0, -6,
    0, -1, -1, 0, -2, 2, -1, 0,
    0, 0, -7, -2, -4, 0, -5, 0,
    0, -8, 0, 7, -2, 0, -3, 0,
    1, 0, -2, 0, -2, -5, 0, -2,
    0, 0, 0, 0, -1, 0, 0, 2,
    -2, 1, 0, 0, -2, -1, 0, -2,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -11, 0, 4, 0, 0, -1, 0,
    0, 0, 0, 0, 0, -2, -2, 0
};


/*Collect the kern class' data in one place*/
static const lv_font_fmt_txt_kern_classes_t kern_classes =
{
    .class_pair_values   = kern_class_values,
    .left_class_mapping  = kern_left_class_mapping,
    .right_class_mapping = kern_right_class_mapping,
    .left_class_cnt      = 60,
    .right_class_cnt     = 48,
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LVGL_VERSION_MAJOR == 8
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
#endif

#if LVGL_VERSION_MAJOR >= 8
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_classes,
    .kern_scale = 16,
    .cmap_num = 2,
    .bpp = 4,
    .kern_classes = 1,
    .bitmap_format = 0,
#if LVGL_VERSION_MAJOR == 8
    .cache = &cache
#endif
};



/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LVGL_VERSION_MAJOR >= 8
const lv_font_t lv_font_montserratMedium_11 = {
#else
lv_font_t lv_font_montserratMedium_11 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 11,          /*The maximum line height required by the font*/
    .base_line = 1,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -1,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc,          /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
#if LV_VERSION_CHECK(8, 2, 0) || LVGL_VERSION_MAJOR >= 9
    .fallback = NULL,
#endif
    .user_data = NULL,
};



#endif /*#if LV_FONT_MONTSERRATMEDIUM_11*/

