/**
 * @file lv_others.h
 *
 */

#ifndef LV_OTHERS_H
#define LV_OTHERS_H

#ifdef __cplusplus
extern "C" {
#endif

/*********************
 *      INCLUDES
 *********************/
#include "snapshot/lv_snapshot.h"
#include "monkey/lv_monkey.h"
#include "gridnav/lv_gridnav.h"
#include "fragment/lv_fragment.h"
#include "imgfont/lv_imgfont.h"
#include "msg/lv_msg.h"
#include "ime/lv_ime_pinyin.h"

/*********************
 *      DEFINES
 *********************/

/**********************
 *      TYPEDEFS
 **********************/

/**********************
 * GLOBAL PROTOTYPES
 **********************/

/**********************
 *      MACROS
 **********************/

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif /*LV_OTHERS_H*/
