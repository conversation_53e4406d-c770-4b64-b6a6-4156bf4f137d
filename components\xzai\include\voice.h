#ifndef _VOICE_H
#define _VOICE_H

#include <stdio.h>
#include <stdint.h>
#include <string.h>
#include "recorder.h"

typedef struct voice_t *voice_handle_t;

typedef void (*voice_wakeup_cb)(voice_handle_t voice);
typedef void (*voice_rec_ready_cb)(voice_handle_t voice);

typedef struct
{
    recorder_handle_t recorder_handle;
    int sample_rate;
    int channels;
    int rec_duration;
    const char *model;
} voice_cfg_t;

voice_handle_t voice_create(voice_cfg_t *cfg);
void voice_destroy(voice_handle_t voice);
esp_err_t voice_recorder_start(voice_handle_t voice);
void voice_recorder_stop(voice_handle_t voice);
void voice_input_audio_start(voice_handle_t voice);
void voice_input_audio_stop(voice_handle_t voice);
bool voice_read_audio_data(voice_handle_t voice, int16_t **buff, int * size);

void voice_set_read_handle(voice_handle_t voice, recorder_handle_t handle);
void voice_set_user_data(voice_handle_t voice, void *user_data);
void voice_set_wakeup_cb(voice_handle_t voice, voice_wakeup_cb cb);
void voice_set_rec_ready_cb(voice_handle_t voice, voice_rec_ready_cb cb);
void voice_set_rec_duration(voice_handle_t voice, int duration);

void *voice_get_user_data(voice_handle_t voice);

#endif
