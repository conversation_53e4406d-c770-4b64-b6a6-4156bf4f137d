/*
* Copyright 2025 NXP
* NXP Proprietary. This software is owned or controlled by NXP and may only be used strictly in
* accordance with the applicable license terms. By expressly accepting such terms or by downloading, installing,
* activating and/or otherwise using the software, you are agreeing that you have read, and that you agree to
* comply with and are bound by, such license terms.  If you do not agree to be bound by the applicable license
* terms, then you may not retain, install, activate or otherwise use the software.
*/

#ifndef GUI_GUIDER_H
#define GUI_GUIDER_H
#ifdef __cplusplus
extern "C" {
#endif

#include "lvgl.h"

typedef struct
{
  
	lv_obj_t *Start_Page;
	bool Start_Page_del;
	lv_obj_t *Start_Page_ProgressBar;
	lv_obj_t *Start_Page_textView;
	lv_obj_t *Menu_page;
	bool Menu_page_del;
	lv_obj_t *Menu_page_navigation_bar;
	lv_obj_t *Menu_page_label_16;
	lv_obj_t *Menu_page_tileview;
	lv_obj_t *Menu_page_tileview_first_page;
	lv_obj_t *Menu_page_tileview_second_page;
	lv_obj_t *Menu_page_cont_7;
	lv_obj_t *Menu_page_btn_wave;
	lv_obj_t *Menu_page_btn_wave_label;
	lv_obj_t *Menu_page_label_7;
	lv_obj_t *Menu_page_cont_6;
	lv_obj_t *Menu_page_btn_ai;
	lv_obj_t *Menu_page_btn_ai_label;
	lv_obj_t *Menu_page_label_6;
	lv_obj_t *Menu_page_cont_5;
	lv_obj_t *Menu_page_btn_pid;
	lv_obj_t *Menu_page_btn_pid_label;
	lv_obj_t *Menu_page_label_5;
	lv_obj_t *Menu_page_cont_4;
	lv_obj_t *Menu_page_btn_setting;
	lv_obj_t *Menu_page_btn_setting_label;
	lv_obj_t *Menu_page_label_4;
	lv_obj_t *Menu_page_cont_3;
	lv_obj_t *Menu_page_btn_bt;
	lv_obj_t *Menu_page_btn_bt_label;
	lv_obj_t *Menu_page_label_3;
	lv_obj_t *Menu_page_cont_2;
	lv_obj_t *Menu_page_btn_can;
	lv_obj_t *Menu_page_btn_can_label;
	lv_obj_t *Menu_page_label_2;
	lv_obj_t *Menu_page_cont_8;
	lv_obj_t *Menu_page_label_8;
	lv_obj_t *Menu_page_cont_9;
	lv_obj_t *Menu_page_cont_10;
	lv_obj_t *Menu_page_label_14;
	lv_obj_t *Menu_page_btn_wifi;
	lv_obj_t *Menu_page_btn_wifi_label;
	lv_obj_t *Menu_page_btn_device;
	lv_obj_t *Menu_page_btn_device_label;
	lv_obj_t *Menu_page_label_15;
	lv_obj_t *Menu_page_btn_update;
	lv_obj_t *Menu_page_btn_update_label;
	lv_obj_t *Menu_page_label_17;
	lv_obj_t *Menu_page_label_18;
	lv_obj_t *PID_page;
	bool PID_page_del;
	lv_obj_t *PID_page_Curren_loop;
	lv_obj_t *PID_page_label_3;
	lv_obj_t *PID_page_label_kp1;
	lv_obj_t *PID_page_label_ki1;
	lv_obj_t *PID_page_slider_kcp;
	lv_obj_t *PID_page_label_kcp;
	lv_obj_t *PID_page_slider_kci;
	lv_obj_t *PID_page_title_bar;
	lv_obj_t *PID_page_label_4;
	lv_obj_t *PID_page_imgbtn_1;
	lv_obj_t *PID_page_imgbtn_1_label;
	lv_obj_t *PID_page_label_kci;
	lv_obj_t *PID_page_Speed_loop;
	lv_obj_t *PID_page_label_40;
	lv_obj_t *PID_page_label_kp2;
	lv_obj_t *PID_page_label_ki2;
	lv_obj_t *PID_page_slider_kc;
	lv_obj_t *PID_page_label_kvp;
	lv_obj_t *PID_page_slider_kvi;
	lv_obj_t *PID_page_label_kvi;
	lv_obj_t *PID_page_cont_1;
	lv_obj_t *PID_page_label_44;
	lv_obj_t *PID_page_label_kp3;
	lv_obj_t *PID_page_label_ki3;
	lv_obj_t *PID_page_slider_kpp;
	lv_obj_t *PID_page_label_kpp;
	lv_obj_t *PID_page_slider_kpi;
	lv_obj_t *PID_page_label_kpi;
	lv_obj_t *PID_page_btn_write;
	lv_obj_t *PID_page_btn_write_label;
	lv_obj_t *PID_page_ddlist_choose;
	lv_obj_t *Can_Connet_page;
	bool Can_Connet_page_del;
	lv_obj_t *Can_Connet_page_title_bar;
	lv_obj_t *Can_Connet_page_label_state_true;
	lv_obj_t *Can_Connet_page_control_meter;
	lv_obj_t *Can_Connet_page_Speed;
	lv_obj_t *Can_Connet_page_meter_speed;
	lv_meter_scale_t *Can_Connet_page_meter_speed_scale_0;
	lv_meter_indicator_t *Can_Connet_page_meter_speed_scale_0_ndline_0;
	lv_meter_indicator_t *Can_Connet_page_meter_speed_scale_0_scaleline_0;
	lv_meter_indicator_t *Can_Connet_page_meter_speed_scale_0_scaleline_1;
	lv_meter_indicator_t *Can_Connet_page_meter_speed_scale_0_scaleline_2;
	lv_obj_t *Can_Connet_page_label_title1;
	lv_obj_t *Can_Connet_page_label_voltage;
	lv_obj_t *Can_Connet_page_label_speed;
	lv_obj_t *Can_Connet_page_label_current;
	lv_obj_t *Can_Connet_page_label_temper;
	lv_obj_t *Can_Connet_page_voltage;
	lv_obj_t *Can_Connet_page_temper;
	lv_obj_t *Can_Connet_page_current;
	lv_obj_t *Can_Connet_page_Position;
	lv_obj_t *Can_Connet_page_label_position;
	lv_obj_t *Can_Connet_page_state;
	lv_obj_t *Can_Connet_page_back;
	lv_obj_t *Can_Connet_page_back_label;
	lv_obj_t *Can_Connet_page_line_1;
	lv_obj_t *Can_Connet_page_line_2;
	lv_obj_t *BT_page;
	bool BT_page_del;
	lv_obj_t *BT_page_cont_1;
	lv_obj_t *BT_page_BT_list;
	lv_obj_t *BT_page_BT_list_item0;
	lv_obj_t *BT_page_BT_list_item1;
	lv_obj_t *BT_page_BT_list_item2;
	lv_obj_t *BT_page_BT_list_item3;
	lv_obj_t *BT_page_BT_list_item4;
	lv_obj_t *BT_page_BT_list_item5;
	lv_obj_t *BT_page_label_1;
	lv_obj_t *BT_page_btn_1;
	lv_obj_t *BT_page_btn_1_label;
	lv_obj_t *BT_page_btn_2;
	lv_obj_t *BT_page_btn_2_label;
	lv_obj_t *BT_page_cont_2;
	lv_obj_t *BT_page_label_2;
	lv_obj_t *BT_page_label_3;
	lv_obj_t *BT_page_label_4;
	lv_obj_t *BT_page_cont_3;
	lv_obj_t *BT_page_label_5;
	lv_obj_t *BT_page_label_6;
	lv_obj_t *BT_page_label_7;
	lv_obj_t *BT_page_imgbtn_1;
	lv_obj_t *BT_page_imgbtn_1_label;
	lv_obj_t *Settings_Page;
	bool Settings_Page_del;
	lv_obj_t *Settings_Page_imgbtn_1;
	lv_obj_t *Settings_Page_imgbtn_1_label;
	lv_obj_t *Settings_Page_list_1;
	lv_obj_t *Settings_Page_list_1_item0;
	lv_obj_t *Wave_Page;
	bool Wave_Page_del;
	lv_obj_t *Wave_Page_chart_1;
	lv_obj_t *Wave_Page_imgbtn_1;
	lv_obj_t *Wave_Page_imgbtn_1_label;
	lv_obj_t *Wave_Page_ddlist_1;
	lv_obj_t *Wave_Page_btn_smaller;
	lv_obj_t *Wave_Page_btn_smaller_label;
	lv_obj_t *Wave_Page_btn_biggeer;
	lv_obj_t *Wave_Page_btn_biggeer_label;
	lv_obj_t *Wave_Page_btn_reset;
	lv_obj_t *Wave_Page_btn_reset_label;
	lv_obj_t *Wave_Page_btn_auto;
	lv_obj_t *Wave_Page_btn_auto_label;
	lv_obj_t *Wave_Page_slider_target;
	lv_obj_t *Wave_Page_btn_send;
	lv_obj_t *Wave_Page_btn_send_label;
	lv_obj_t *devoce_info_page;
	bool devoce_info_page_del;
	lv_obj_t *devoce_info_page_imgbtn_1;
	lv_obj_t *devoce_info_page_imgbtn_1_label;
	lv_obj_t *devoce_info_page_cont_1;
	lv_obj_t *devoce_info_page_label_23;
	lv_obj_t *devoce_info_page_label_24;
	lv_obj_t *devoce_info_page_label_25;
	lv_obj_t *devoce_info_page_label_26;
	lv_obj_t *devoce_info_page_label_version;
	lv_obj_t *ai_page;
	bool ai_page_del;
	lv_obj_t *ai_page_label_assistant;
	lv_obj_t *ai_page_img_emoji;
	lv_obj_t *ai_page_label_state;
	lv_obj_t *ai_page_label_user;
	lv_obj_t *WIFI_page;
	bool WIFI_page_del;
	lv_obj_t *WIFI_page_label_state;
	lv_obj_t *WIFI_page_qrcode_ip;
	lv_obj_t *WIFI_page_label_ip;
	lv_obj_t *WIFI_page_btn_sw;
	lv_obj_t *WIFI_page_btn_sw_label;
	lv_obj_t *WIFI_page_btn_back;
	lv_obj_t *WIFI_page_btn_back_label;
	lv_obj_t *update_page;
	bool update_page_del;
	lv_obj_t *update_page_label_1;
	lv_obj_t *update_page_label_2;
	lv_obj_t *update_page_bar_1;
	lv_obj_t *update_page_label_3;
	lv_obj_t *update_page_btn_1;
	lv_obj_t *update_page_btn_1_label;
	lv_obj_t *g_kb_top_layer;
}lv_ui;

typedef void (*ui_setup_scr_t)(lv_ui * ui);

void ui_init_style(lv_style_t * style);

void ui_load_scr_animation(lv_ui *ui, lv_obj_t ** new_scr, bool new_scr_del, bool * old_scr_del, ui_setup_scr_t setup_scr,
                           lv_scr_load_anim_t anim_type, uint32_t time, uint32_t delay, bool is_clean, bool auto_del);

void ui_animation(void * var, int32_t duration, int32_t delay, int32_t start_value, int32_t end_value, lv_anim_path_cb_t path_cb,
                       uint16_t repeat_cnt, uint32_t repeat_delay, uint32_t playback_time, uint32_t playback_delay,
                       lv_anim_exec_xcb_t exec_cb, lv_anim_start_cb_t start_cb, lv_anim_ready_cb_t ready_cb, lv_anim_deleted_cb_t deleted_cb);


void init_scr_del_flag(lv_ui *ui);

void setup_ui(lv_ui *ui);

void init_keyboard(lv_ui *ui);

extern lv_ui guider_ui;


void setup_scr_Start_Page(lv_ui *ui);
void setup_scr_Menu_page(lv_ui *ui);
void setup_scr_PID_page(lv_ui *ui);
void setup_scr_Can_Connet_page(lv_ui *ui);
void setup_scr_BT_page(lv_ui *ui);
void setup_scr_Settings_Page(lv_ui *ui);
void setup_scr_Wave_Page(lv_ui *ui);
void setup_scr_devoce_info_page(lv_ui *ui);
void setup_scr_ai_page(lv_ui *ui);
void setup_scr_WIFI_page(lv_ui *ui);
void setup_scr_update_page(lv_ui *ui);

LV_IMG_DECLARE(_Start_bg_320x240);

LV_IMG_DECLARE(_bg05_320x240);

LV_IMG_DECLARE(_temperature_56x48);

LV_IMG_DECLARE(_AI_56x48);

LV_IMG_DECLARE(_PID_43x43);

LV_IMG_DECLARE(_set_ico_43x43);

LV_IMG_DECLARE(_bt01_43x43);

LV_IMG_DECLARE(_merter01_43x43);

LV_IMG_DECLARE(_wifi_56x48);

LV_IMG_DECLARE(_feedback01_56x48);

LV_IMG_DECLARE(_update_56x48);

LV_IMG_DECLARE(_bg01_320x240);
LV_IMG_DECLARE(_back01_ico_alpha_29x26);
LV_IMG_DECLARE(_needle_alpha_210x210);
LV_IMG_DECLARE(_back01_ico_alpha_26x26);

LV_IMG_DECLARE(_bg03_320x240);
LV_IMG_DECLARE(_bt01_alpha_20x20);
LV_IMG_DECLARE(_bt01_alpha_20x20);
LV_IMG_DECLARE(_bt01_alpha_20x20);
LV_IMG_DECLARE(_bt01_alpha_20x20);
LV_IMG_DECLARE(_bt01_alpha_20x20);
LV_IMG_DECLARE(_bt01_alpha_20x20);
LV_IMG_DECLARE(_back01_ico_alpha_26x26);
LV_IMG_DECLARE(_back01_ico_alpha_26x26);
LV_IMG_DECLARE(_bt01_alpha_20x20);
LV_IMG_DECLARE(_back01_ico_alpha_29x30);

LV_IMG_DECLARE(_kmbg_45x40);
LV_IMG_DECLARE(_back01_ico_alpha_26x26);
LV_IMG_DECLARE(_neutral_48_alpha_48x48);

LV_FONT_DECLARE(lv_font_HarmonyOS_Sans_SC_Medium_10)
LV_FONT_DECLARE(lv_font_montserratMedium_10)
LV_FONT_DECLARE(lv_font_montserratMedium_16)
LV_FONT_DECLARE(lv_font_montserratMedium_15)
LV_FONT_DECLARE(lv_font_HarmonyOS_Sans_SC_Light_16)
LV_FONT_DECLARE(lv_font_montserratMedium_32)
LV_FONT_DECLARE(lv_font_HarmonyOS_Sans_SC_Medium_14)
LV_FONT_DECLARE(lv_font_montserratMedium_12)
LV_FONT_DECLARE(lv_font_HarmonyOS_Sans_SC_Medium_16)
LV_FONT_DECLARE(lv_font_HarmonyOS_Sans_SC_Light_10)
LV_FONT_DECLARE(lv_font_HarmonyOS_Sans_SC_Light_12)
LV_FONT_DECLARE(lv_font_HarmonyOS_Sans_SC_Regular_14)
LV_FONT_DECLARE(lv_font_montserratMedium_11)
LV_FONT_DECLARE(lv_font_montserratMedium_14)
LV_FONT_DECLARE(lv_font_SourceHanSerifSC_Regular_15)


#ifdef __cplusplus
}
#endif
#endif
