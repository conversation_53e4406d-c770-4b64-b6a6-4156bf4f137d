/**
 * @file lv_example_get_started.h
 *
 */

#ifndef LV_EX_GET_STARTED_H
#define LV_EX_GET_STARTED_H

#ifdef __cplusplus
extern "C" {
#endif

/*********************
 *      INCLUDES
 *********************/

/*********************
 *      DEFINES
 *********************/

/**********************
 *      TYPEDEFS
 **********************/

/**********************
 * GLOBAL PROTOTYPES
 **********************/
void lv_example_get_started_1(void);
void lv_example_get_started_2(void);
void lv_example_get_started_3(void);

/**********************
 *      MACROS
 **********************/

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif /*LV_EX_GET_STARTED_H*/
