#include "xzai.h"
#include <stdio.h>
#include "http_rest_client.h"
#include "system_util.h"

#define TAG "xzai_check_ver"

static void check_ver_client_header_handle(esp_http_client_handle_t client)
{
    esp_http_client_set_header(client, "Device-Id", get_mac_address());
    esp_http_client_set_header(client, "Client-Id", generate_uuid());
    esp_http_client_set_header(client, "Accept-Language", "zh-CN");
}

bool xzai_check_ver(void *arg)
{
    bool res = false;

    if (arg == NULL)
        return res;

    xzai_handle_t xzai = (xzai_handle_t)arg;
    http_rest_recv_buffer_t response_buffer = {0};

    char *board_info = xzai_get_boardinfo(xzai);
    xzai_activation_t *activation_data = xzai_get_activation(xzai);
    xzai_mqtt_t *mqtt_cfg = xzai_get_mqtt_cfg(xzai);
    xzai_callbacks_t *callbacks = xzai_get_callbacks(xzai);

    cJSON *root = NULL;
    cJSON *obj_activation = NULL;
    cJSON *obj_mqtt = NULL;
    cJSON *obj_item = NULL;

    http_rest_client_post(XZAI_API_OTA, board_info, &response_buffer, check_ver_client_header_handle);
    if (response_buffer.status_code == 200 && response_buffer.buffer != NULL)
    {
        char *json_buff = (char *)response_buffer.buffer;
        ESP_LOGI(TAG, "%s", json_buff);

        do
        {
            // activation
            root = cJSON_Parse(json_buff);
            if (root == NULL)
            {
                ESP_LOGE(TAG, "Failed to parse JSON response");
                break;
            }

            activation_data->has_activation_code = false;
            obj_activation = cJSON_GetObjectItem(root, "activation");
            if (obj_activation)
            {
                // --code
                obj_item = cJSON_GetObjectItem(obj_activation, "code");
                if (obj_item)
                {
                    memset(activation_data->code, 0x0, sizeof(activation_data->code));
                    memcpy(activation_data->code, obj_item->valuestring, sizeof(activation_data->code));
                }

                // --message
                obj_item = cJSON_GetObjectItem(obj_activation, "message");
                if (obj_item)
                {
                    memset(activation_data->message, 0x0, sizeof(activation_data->message));
                    memcpy(activation_data->message, obj_item->valuestring, sizeof(activation_data->message));
                }

                activation_data->has_activation_code = true;
                xzai_set_state(xzai, XZAI_STATE_ACTIVATING);
            }

            if (callbacks->check_ver)
            {
                callbacks->check_ver(xzai);
            }

            // mqtt config
            mqtt_cfg->has_mqtt_config = false;
            obj_mqtt = cJSON_GetObjectItem(root, "mqtt");
            if (obj_mqtt)
            {
                // endpoint
                obj_item = cJSON_GetObjectItem(obj_mqtt, "endpoint");
                if (obj_item)
                {
                    memset(mqtt_cfg->endpoint, 0x0, sizeof(mqtt_cfg->endpoint));
                    memcpy(mqtt_cfg->endpoint, obj_item->valuestring, sizeof(mqtt_cfg->endpoint));
                }

                // client_id
                obj_item = cJSON_GetObjectItem(obj_mqtt, "client_id");
                if (obj_item)
                {
                    memset(mqtt_cfg->client_id, 0x0, sizeof(mqtt_cfg->client_id));
                    memcpy(mqtt_cfg->client_id, obj_item->valuestring, sizeof(mqtt_cfg->client_id));
                }

                // username
                obj_item = cJSON_GetObjectItem(obj_mqtt, "username");
                if (obj_item)
                {
                    memset(mqtt_cfg->username, 0x0, sizeof(mqtt_cfg->username));
                    memcpy(mqtt_cfg->username, obj_item->valuestring, sizeof(mqtt_cfg->username));
                }

                // password
                obj_item = cJSON_GetObjectItem(obj_mqtt, "password");
                if (obj_item)
                {
                    memset(mqtt_cfg->password, 0x0, sizeof(mqtt_cfg->password));
                    memcpy(mqtt_cfg->password, obj_item->valuestring, sizeof(mqtt_cfg->password));
                }

                // publish_topic
                obj_item = cJSON_GetObjectItem(obj_mqtt, "publish_topic");
                if (obj_item)
                {
                    memset(mqtt_cfg->publish_topic, 0x0, sizeof(mqtt_cfg->publish_topic));
                    memcpy(mqtt_cfg->publish_topic, obj_item->valuestring, sizeof(mqtt_cfg->publish_topic));
                }

                // subscribe_topic
                obj_item = cJSON_GetObjectItem(obj_mqtt, "subscribe_topic");
                if (obj_item)
                {
                    memset(mqtt_cfg->subscribe_topic, 0x0, sizeof(mqtt_cfg->subscribe_topic));
                    memcpy(mqtt_cfg->subscribe_topic, obj_item->valuestring, sizeof(mqtt_cfg->subscribe_topic));
                }

                mqtt_cfg->has_mqtt_config = true;

                if (callbacks->mqtt_cfg_update)
                    callbacks->mqtt_cfg_update(xzai);
            }

            cJSON *firmware = cJSON_GetObjectItem(root, "firmware");
            if (firmware == NULL)
            {
                ESP_LOGE(TAG, "Failed to get firmware object");
                break;
            }
            cJSON *version = cJSON_GetObjectItem(firmware, "version");
            if (version == NULL)
            {
                ESP_LOGE(TAG, "Failed to get version object");
                break;
            }
            cJSON *url = cJSON_GetObjectItem(firmware, "url");
            if (url == NULL)
            {
                ESP_LOGE(TAG, "Failed to get url object");
                break;
            }

            res = true;

        } while (0);
    }

    if (root)
        cJSON_Delete(root);
    http_rest_client_cleanup(&response_buffer);

    return res;
}