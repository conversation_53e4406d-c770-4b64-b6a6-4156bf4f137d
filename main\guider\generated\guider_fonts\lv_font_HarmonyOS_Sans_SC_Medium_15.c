/*******************************************************************************
 * Size: 15 px
 * Bpp: 4
 * Opts: undefined
 ******************************************************************************/

#ifdef LV_LVGL_H_INCLUDE_SIMPLE
#include "lvgl.h"
#else
#include "lvgl.h"
#endif

#ifndef LV_FONT_HARMONYOS_SANS_SC_MEDIUM_15
#define LV_FONT_HARMONYOS_SANS_SC_MEDIUM_15 1
#endif

#if LV_FONT_HARMONYOS_SANS_SC_MEDIUM_15

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0xf, 0x90, 0xf9, 0xe, 0x80, 0xe8, 0xd, 0x80,
    0xd7, 0xc, 0x70, 0xc6, 0x2, 0x10, 0xd9, 0xd,
    0x90,

    /* U+0022 "\"" */
    0x2f, 0x28, 0xc2, 0xf2, 0x8c, 0x2f, 0x28, 0xc1,
    0xf2, 0x7c,

    /* U+0023 "#" */
    0x0, 0x6, 0xf0, 0xb, 0xb0, 0x0, 0x9, 0xc0,
    0xf, 0x70, 0x1, 0x1d, 0xa1, 0x3f, 0x51, 0xc,
    0xff, 0xff, 0xff, 0xfb, 0x3, 0x8f, 0x54, 0xbd,
    0x43, 0x0, 0x9d, 0x0, 0xd8, 0x0, 0x11, 0xda,
    0x13, 0xf5, 0x10, 0xdf, 0xff, 0xff, 0xff, 0xb0,
    0x38, 0xf5, 0x4c, 0xd4, 0x30, 0x8, 0xe0, 0xd,
    0x80, 0x0, 0xc, 0xa0, 0x1f, 0x50, 0x0,

    /* U+0024 "$" */
    0x0, 0x3, 0xf3, 0x0, 0x0, 0x0, 0x3f, 0x30,
    0x0, 0x1, 0xbf, 0xff, 0xa1, 0x0, 0xde, 0x8f,
    0xbf, 0xb0, 0x2f, 0x63, 0xf2, 0x7a, 0x2, 0xf8,
    0x3f, 0x20, 0x0, 0xa, 0xfc, 0xf2, 0x0, 0x0,
    0x7, 0xdf, 0xe8, 0x0, 0x0, 0x3, 0xfb, 0xfc,
    0x0, 0x50, 0x3f, 0x28, 0xf2, 0x7f, 0x33, 0xf2,
    0x7f, 0x11, 0xdf, 0xbf, 0xaf, 0xb0, 0x1, 0x9e,
    0xfe, 0x91, 0x0, 0x0, 0x3f, 0x30, 0x0, 0x0,
    0x3, 0xf3, 0x0, 0x0,

    /* U+0025 "%" */
    0x9, 0xfd, 0x40, 0x0, 0xdc, 0x0, 0x7f, 0x59,
    0xf1, 0x7, 0xf3, 0x0, 0xbb, 0x1, 0xf4, 0x1f,
    0x90, 0x0, 0x8e, 0x26, 0xf2, 0xae, 0x10, 0x0,
    0x1c, 0xff, 0x63, 0xf6, 0x0, 0x0, 0x0, 0x21,
    0xc, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0x36,
    0xef, 0xa0, 0x0, 0x1, 0xe9, 0x2f, 0x95, 0xf7,
    0x0, 0x9, 0xe1, 0x4f, 0x10, 0xba, 0x0, 0x3f,
    0x60, 0x2f, 0x73, 0xe7, 0x0, 0xcd, 0x0, 0x4,
    0xef, 0x90,

    /* U+0026 "&" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1b,
    0xff, 0xb1, 0x0, 0x0, 0x0, 0xaf, 0x66, 0xfa,
    0x0, 0x0, 0x0, 0xfa, 0x0, 0xbe, 0x0, 0x0,
    0x0, 0xdd, 0x1, 0xec, 0x0, 0x0, 0x0, 0x8f,
    0x8d, 0xf3, 0x0, 0x0, 0x0, 0x2f, 0xfc, 0x20,
    0x57, 0x0, 0x5, 0xfd, 0xde, 0x20, 0xcb, 0x0,
    0x1f, 0xc0, 0x2e, 0xd2, 0xf7, 0x0, 0x5f, 0x50,
    0x3, 0xff, 0xf1, 0x0, 0x4f, 0x60, 0x0, 0x6f,
    0xd0, 0x0, 0xd, 0xe6, 0x47, 0xfd, 0xfa, 0x0,
    0x1, 0xae, 0xfd, 0x70, 0x6f, 0x90,

    /* U+0027 "'" */
    0x2f, 0x22, 0xf2, 0x2f, 0x21, 0xf2,

    /* U+0028 "(" */
    0x0, 0xb, 0xb0, 0x0, 0x7f, 0x20, 0x1, 0xf9,
    0x0, 0x6, 0xf2, 0x0, 0xb, 0xe0, 0x0, 0xe,
    0xb0, 0x0, 0xf, 0x90, 0x0, 0xf, 0x90, 0x0,
    0xe, 0xb0, 0x0, 0xb, 0xd0, 0x0, 0x7, 0xf2,
    0x0, 0x1, 0xf8, 0x0, 0x0, 0x8f, 0x10, 0x0,
    0xc, 0xb0,

    /* U+0029 ")" */
    0x6e, 0x20, 0x0, 0xbc, 0x0, 0x3, 0xf5, 0x0,
    0xc, 0xc0, 0x0, 0x8f, 0x0, 0x5, 0xf3, 0x0,
    0x4f, 0x50, 0x4, 0xf5, 0x0, 0x5f, 0x30, 0x8,
    0xf1, 0x0, 0xcc, 0x0, 0x3f, 0x60, 0xb, 0xd0,
    0x6, 0xf2, 0x0,

    /* U+002A "*" */
    0x0, 0x4e, 0x0, 0x3, 0xd6, 0xd6, 0xc0, 0x7,
    0xff, 0xc4, 0x2, 0xbe, 0xfe, 0x70, 0x29, 0x4d,
    0x38, 0x0, 0x3, 0xb0, 0x0,

    /* U+002B "+" */
    0x0, 0x1, 0x40, 0x0, 0x0, 0x5, 0xf0, 0x0,
    0x0, 0x5, 0xf0, 0x0, 0x1, 0x16, 0xf2, 0x11,
    0x4f, 0xff, 0xff, 0xff, 0x14, 0x48, 0xf5, 0x44,
    0x0, 0x5, 0xf0, 0x0, 0x0, 0x5, 0xf0, 0x0,

    /* U+002C "," */
    0x0, 0x0, 0xc, 0xb0, 0xc, 0xf0, 0x1, 0xd0,
    0x1c, 0x40, 0x1, 0x0,

    /* U+002D "-" */
    0x1, 0x11, 0x11, 0x1, 0xff, 0xff, 0xf6, 0x3,
    0x33, 0x33, 0x10,

    /* U+002E "." */
    0x0, 0x0, 0xe9, 0xe, 0x80,

    /* U+002F "/" */
    0x0, 0x0, 0xda, 0x0, 0x4, 0xf4, 0x0, 0x9,
    0xe0, 0x0, 0xf, 0x80, 0x0, 0x5f, 0x20, 0x0,
    0xbc, 0x0, 0x1, 0xf6, 0x0, 0x7, 0xf0, 0x0,
    0xd, 0xa0, 0x0, 0x3f, 0x40, 0x0, 0x9e, 0x0,
    0x0,

    /* U+0030 "0" */
    0x0, 0x6d, 0xfc, 0x30, 0x0, 0x6f, 0xc8, 0xef,
    0x20, 0xd, 0xd0, 0x3, 0xf9, 0x2, 0xf9, 0x0,
    0xd, 0xd0, 0x4f, 0x70, 0x0, 0xbf, 0x4, 0xf6,
    0x0, 0xb, 0xf0, 0x4f, 0x70, 0x0, 0xbf, 0x2,
    0xf9, 0x0, 0xd, 0xd0, 0xd, 0xe0, 0x3, 0xf9,
    0x0, 0x6f, 0xc8, 0xef, 0x20, 0x0, 0x7e, 0xfc,
    0x30, 0x0,

    /* U+0031 "1" */
    0x0, 0x5d, 0xe2, 0xbf, 0xfe, 0x9d, 0x4b, 0xe2,
    0x0, 0xbe, 0x0, 0xb, 0xe0, 0x0, 0xbe, 0x0,
    0xb, 0xe0, 0x0, 0xbe, 0x0, 0xb, 0xe0, 0x0,
    0xbe, 0x0, 0xb, 0xe0,

    /* U+0032 "2" */
    0x0, 0x7d, 0xfc, 0x40, 0xa, 0xfa, 0x8e, 0xf2,
    0x1e, 0x70, 0x4, 0xf8, 0x1, 0x0, 0x2, 0xf8,
    0x0, 0x0, 0x8, 0xf4, 0x0, 0x0, 0x3f, 0xb0,
    0x0, 0x2, 0xed, 0x10, 0x0, 0x1e, 0xe2, 0x0,
    0x1, 0xdf, 0x30, 0x0, 0xc, 0xfa, 0x77, 0x76,
    0x4f, 0xff, 0xff, 0xff,

    /* U+0033 "3" */
    0x0, 0x8e, 0xfc, 0x40, 0xa, 0xf9, 0x8e, 0xf2,
    0xe, 0x70, 0x5, 0xf7, 0x0, 0x0, 0x4, 0xf7,
    0x0, 0x1, 0x4d, 0xf1, 0x0, 0xd, 0xff, 0x70,
    0x0, 0x4, 0x6b, 0xf6, 0x0, 0x0, 0x0, 0xed,
    0x1b, 0x30, 0x0, 0xed, 0xd, 0xf9, 0x7d, 0xf7,
    0x1, 0x9e, 0xfd, 0x60,

    /* U+0034 "4" */
    0x0, 0x0, 0xbe, 0x10, 0x0, 0x0, 0x4f, 0x70,
    0x0, 0x0, 0xd, 0xd0, 0x0, 0x0, 0x6, 0xf5,
    0x0, 0x0, 0x0, 0xec, 0x7, 0xa0, 0x0, 0x8f,
    0x30, 0xaf, 0x0, 0x1f, 0xb1, 0x1a, 0xf1, 0x7,
    0xff, 0xff, 0xff, 0xf8, 0x24, 0x44, 0x4b, 0xf4,
    0x20, 0x0, 0x0, 0xaf, 0x0, 0x0, 0x0, 0xa,
    0xf0, 0x0,

    /* U+0035 "5" */
    0x1f, 0xff, 0xff, 0x53, 0xf9, 0x66, 0x62, 0x5f,
    0x30, 0x0, 0x7, 0xf1, 0x10, 0x0, 0x9f, 0xef,
    0xe7, 0xa, 0xf7, 0x5c, 0xf5, 0x2, 0x0, 0x1f,
    0xc0, 0x0, 0x0, 0xdd, 0x77, 0x0, 0x1f, 0xb9,
    0xf9, 0x8d, 0xf3, 0x8, 0xef, 0xc4, 0x0,

    /* U+0036 "6" */
    0x0, 0x0, 0xaf, 0x20, 0x0, 0x0, 0x4f, 0x70,
    0x0, 0x0, 0xd, 0xc0, 0x0, 0x0, 0x8, 0xf3,
    0x0, 0x0, 0x2, 0xff, 0xff, 0x90, 0x0, 0xbf,
    0x95, 0xaf, 0x90, 0x1f, 0xb0, 0x0, 0xdf, 0x2,
    0xf8, 0x0, 0xa, 0xf1, 0xf, 0xc0, 0x0, 0xde,
    0x0, 0x8f, 0xb8, 0xcf, 0x60, 0x0, 0x7d, 0xfd,
    0x60, 0x0,

    /* U+0037 "7" */
    0x2f, 0xff, 0xff, 0xfd, 0x16, 0x66, 0x67, 0xfa,
    0x0, 0x0, 0x6, 0xf3, 0x0, 0x0, 0xd, 0xd0,
    0x0, 0x0, 0x4f, 0x70, 0x0, 0x0, 0xaf, 0x10,
    0x0, 0x1, 0xfa, 0x0, 0x0, 0x7, 0xf3, 0x0,
    0x0, 0xe, 0xd0, 0x0, 0x0, 0x5f, 0x70, 0x0,
    0x0, 0xbf, 0x10, 0x0,

    /* U+0038 "8" */
    0x0, 0x8e, 0xfd, 0x60, 0x0, 0xaf, 0x96, 0xcf,
    0x50, 0xe, 0xd0, 0x1, 0xf9, 0x0, 0xaf, 0x52,
    0x8f, 0x50, 0x0, 0xdf, 0xff, 0x90, 0x0, 0x9f,
    0x85, 0xbf, 0x50, 0x2f, 0x90, 0x0, 0xdd, 0x4,
    0xf7, 0x0, 0xb, 0xf0, 0x2f, 0xa0, 0x0, 0xed,
    0x0, 0xbf, 0xa7, 0xcf, 0x60, 0x0, 0x8e, 0xfd,
    0x60, 0x0,

    /* U+0039 "9" */
    0x0, 0x8e, 0xfc, 0x40, 0xa, 0xfa, 0x8d, 0xf4,
    0x2f, 0xa0, 0x1, 0xfc, 0x5f, 0x60, 0x0, 0xce,
    0x4f, 0x80, 0x0, 0xec, 0xe, 0xf5, 0x29, 0xf6,
    0x3, 0xef, 0xff, 0xd0, 0x0, 0x3, 0x8f, 0x40,
    0x0, 0x1, 0xea, 0x0, 0x0, 0xa, 0xe1, 0x0,
    0x0, 0x5f, 0x60, 0x0,

    /* U+003A ":" */
    0xcb, 0xcc, 0x0, 0x0, 0x0, 0x0, 0xcc, 0xcb,

    /* U+003B ";" */
    0x9, 0xd0, 0xa, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xd0, 0xa, 0xf2,
    0x0, 0xe0, 0xc, 0x60, 0x1, 0x0,

    /* U+003C "<" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x8d,
    0x0, 0x4, 0xaf, 0xf8, 0x6, 0xdf, 0xd6, 0x0,
    0x4f, 0xf6, 0x0, 0x0, 0x18, 0xef, 0xb5, 0x0,
    0x0, 0x5, 0xcf, 0xe7, 0x0, 0x0, 0x3, 0x9e,
    0x0, 0x0, 0x0, 0x0,

    /* U+003D "=" */
    0x4f, 0xff, 0xff, 0xff, 0x15, 0x55, 0x55, 0x55,
    0x1, 0x11, 0x11, 0x11, 0x4f, 0xff, 0xff, 0xff,
    0x14, 0x44, 0x44, 0x44,

    /* U+003E ">" */
    0x0, 0x0, 0x0, 0x0, 0x4c, 0x60, 0x0, 0x0,
    0x1b, 0xfe, 0x82, 0x0, 0x0, 0x28, 0xef, 0xb4,
    0x0, 0x0, 0xa, 0xff, 0x0, 0x17, 0xdf, 0xd6,
    0x19, 0xff, 0xa3, 0x0, 0x4e, 0x71, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+003F "?" */
    0x0, 0x7d, 0xfd, 0x60, 0xa, 0xfa, 0x8d, 0xf5,
    0xa, 0x70, 0x2, 0xf8, 0x0, 0x0, 0x6, 0xf4,
    0x0, 0x0, 0x7f, 0x60, 0x0, 0x5, 0xf5, 0x0,
    0x0, 0xa, 0xc0, 0x0, 0x0, 0x6, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xe1, 0x0,
    0x0, 0x8, 0xe1, 0x0,

    /* U+0040 "@" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xbe, 0xff, 0xc7, 0x0, 0x0, 0x0,
    0x1c, 0xf9, 0x53, 0x58, 0xed, 0x30, 0x0, 0xd,
    0xc1, 0x0, 0x0, 0x12, 0xbe, 0x10, 0x8, 0xe1,
    0x8, 0xff, 0xba, 0xa0, 0xe8, 0x0, 0xe7, 0x6,
    0xf9, 0x5b, 0xfa, 0x7, 0xe0, 0x2f, 0x20, 0xdb,
    0x0, 0xf, 0xa0, 0x3f, 0x14, 0xf0, 0xe, 0x90,
    0x0, 0xca, 0x2, 0xf2, 0x2f, 0x20, 0xda, 0x0,
    0xe, 0xa0, 0x3f, 0x10, 0xf5, 0x8, 0xf4, 0x18,
    0xfd, 0x1a, 0xc0, 0xa, 0xc0, 0xa, 0xff, 0xc4,
    0xef, 0xf4, 0x0, 0x2e, 0xa0, 0x1, 0x20, 0x0,
    0x30, 0x0, 0x0, 0x4e, 0xd6, 0x21, 0x26, 0x90,
    0x0, 0x0, 0x0, 0x19, 0xef, 0xff, 0xe8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x13, 0x20, 0x0, 0x0,
    0x0,

    /* U+0041 "A" */
    0x0, 0x0, 0xde, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xf5, 0x0, 0x0, 0x0, 0x9, 0xde, 0xb0, 0x0,
    0x0, 0x0, 0xf8, 0x8f, 0x20, 0x0, 0x0, 0x6f,
    0x22, 0xf8, 0x0, 0x0, 0xc, 0xd0, 0xc, 0xe0,
    0x0, 0x2, 0xf8, 0x11, 0x8f, 0x50, 0x0, 0x8f,
    0xff, 0xff, 0xfb, 0x0, 0xe, 0xc4, 0x44, 0x4b,
    0xf2, 0x5, 0xf5, 0x0, 0x0, 0x3f, 0x80, 0xbe,
    0x0, 0x0, 0x0, 0xde, 0x0,

    /* U+0042 "B" */
    0xbf, 0xff, 0xeb, 0x30, 0xb, 0xe6, 0x68, 0xef,
    0x40, 0xbd, 0x0, 0x2, 0xfa, 0xb, 0xd0, 0x0,
    0xf, 0xb0, 0xbd, 0x11, 0x3b, 0xf5, 0xb, 0xff,
    0xff, 0xfc, 0x10, 0xbe, 0x44, 0x48, 0xfd, 0xb,
    0xd0, 0x0, 0x7, 0xf4, 0xbd, 0x0, 0x0, 0x8f,
    0x4b, 0xe6, 0x67, 0xaf, 0xe0, 0xbf, 0xff, 0xfd,
    0x91, 0x0,

    /* U+0043 "C" */
    0x0, 0x6, 0xcf, 0xea, 0x20, 0x0, 0xbf, 0xc8,
    0x9f, 0xe2, 0x8, 0xf7, 0x0, 0x2, 0xd3, 0xe,
    0xd0, 0x0, 0x0, 0x0, 0x3f, 0x80, 0x0, 0x0,
    0x0, 0x4f, 0x70, 0x0, 0x0, 0x0, 0x3f, 0x80,
    0x0, 0x0, 0x0, 0xe, 0xd0, 0x0, 0x0, 0x0,
    0x8, 0xf7, 0x0, 0x2, 0xd4, 0x0, 0xbf, 0xc8,
    0x9f, 0xe2, 0x0, 0x6, 0xcf, 0xea, 0x20,

    /* U+0044 "D" */
    0xbf, 0xff, 0xea, 0x30, 0x0, 0xbe, 0x66, 0x8e,
    0xf8, 0x0, 0xbd, 0x0, 0x0, 0xaf, 0x50, 0xbd,
    0x0, 0x0, 0xe, 0xc0, 0xbd, 0x0, 0x0, 0xa,
    0xf0, 0xbd, 0x0, 0x0, 0x9, 0xf1, 0xbd, 0x0,
    0x0, 0xa, 0xf0, 0xbd, 0x0, 0x0, 0xe, 0xc0,
    0xbd, 0x0, 0x0, 0xaf, 0x50, 0xbe, 0x67, 0x9e,
    0xf7, 0x0, 0xbf, 0xff, 0xea, 0x30, 0x0,

    /* U+0045 "E" */
    0xbf, 0xff, 0xff, 0xf2, 0xbe, 0x66, 0x66, 0x60,
    0xbd, 0x0, 0x0, 0x0, 0xbd, 0x0, 0x0, 0x0,
    0xbd, 0x11, 0x11, 0x10, 0xbf, 0xff, 0xff, 0x90,
    0xbe, 0x44, 0x44, 0x20, 0xbd, 0x0, 0x0, 0x0,
    0xbd, 0x0, 0x0, 0x0, 0xbe, 0x77, 0x77, 0x72,
    0xbf, 0xff, 0xff, 0xf4,

    /* U+0046 "F" */
    0xbf, 0xff, 0xff, 0xf2, 0xbe, 0x66, 0x66, 0x60,
    0xbd, 0x0, 0x0, 0x0, 0xbd, 0x0, 0x0, 0x0,
    0xbd, 0x11, 0x11, 0x10, 0xbf, 0xff, 0xff, 0x90,
    0xbe, 0x44, 0x44, 0x20, 0xbd, 0x0, 0x0, 0x0,
    0xbd, 0x0, 0x0, 0x0, 0xbd, 0x0, 0x0, 0x0,
    0xbd, 0x0, 0x0, 0x0,

    /* U+0047 "G" */
    0x0, 0x6, 0xcf, 0xfc, 0x50, 0x0, 0xaf, 0xc8,
    0x8d, 0xf7, 0x7, 0xf8, 0x0, 0x0, 0x84, 0xe,
    0xd0, 0x0, 0x0, 0x0, 0x3f, 0x80, 0x0, 0x12,
    0x21, 0x4f, 0x60, 0x0, 0xef, 0xff, 0x2f, 0x80,
    0x0, 0x45, 0xcf, 0xe, 0xd0, 0x0, 0x0, 0xaf,
    0x7, 0xf9, 0x0, 0x0, 0xbf, 0x0, 0xaf, 0xd9,
    0x8c, 0xfb, 0x0, 0x6, 0xcf, 0xfc, 0x60,

    /* U+0048 "H" */
    0xbd, 0x0, 0x0, 0x8, 0xf1, 0xbd, 0x0, 0x0,
    0x8, 0xf1, 0xbd, 0x0, 0x0, 0x8, 0xf1, 0xbd,
    0x0, 0x0, 0x8, 0xf1, 0xbe, 0x22, 0x22, 0x29,
    0xf1, 0xbf, 0xff, 0xff, 0xff, 0xf1, 0xbe, 0x55,
    0x55, 0x5b, 0xf1, 0xbd, 0x0, 0x0, 0x8, 0xf1,
    0xbd, 0x0, 0x0, 0x8, 0xf1, 0xbd, 0x0, 0x0,
    0x8, 0xf1, 0xbd, 0x0, 0x0, 0x8, 0xf1,

    /* U+0049 "I" */
    0xbd, 0xbd, 0xbd, 0xbd, 0xbd, 0xbd, 0xbd, 0xbd,
    0xbd, 0xbd, 0xbd,

    /* U+004A "J" */
    0x0, 0x0, 0x9f, 0x0, 0x0, 0x9, 0xf0, 0x0,
    0x0, 0x9f, 0x0, 0x0, 0x9, 0xf0, 0x0, 0x0,
    0x9f, 0x0, 0x0, 0x9, 0xf0, 0x0, 0x0, 0x9f,
    0x0, 0x0, 0x9, 0xf0, 0x55, 0x0, 0xce, 0xb,
    0xf9, 0xaf, 0x90, 0x1b, 0xfe, 0x80, 0x0,

    /* U+004B "K" */
    0xbd, 0x0, 0x0, 0x7f, 0x80, 0xbd, 0x0, 0x6,
    0xf9, 0x0, 0xbd, 0x0, 0x5f, 0xa0, 0x0, 0xbd,
    0x3, 0xfb, 0x0, 0x0, 0xbd, 0x3f, 0xd0, 0x0,
    0x0, 0xbe, 0xef, 0xf4, 0x0, 0x0, 0xbf, 0xd2,
    0xee, 0x10, 0x0, 0xbe, 0x10, 0x4f, 0xc0, 0x0,
    0xbd, 0x0, 0x8, 0xf8, 0x0, 0xbd, 0x0, 0x0,
    0xbf, 0x40, 0xbd, 0x0, 0x0, 0x1e, 0xe1,

    /* U+004C "L" */
    0xbd, 0x0, 0x0, 0x0, 0xbd, 0x0, 0x0, 0x0,
    0xbd, 0x0, 0x0, 0x0, 0xbd, 0x0, 0x0, 0x0,
    0xbd, 0x0, 0x0, 0x0, 0xbd, 0x0, 0x0, 0x0,
    0xbd, 0x0, 0x0, 0x0, 0xbd, 0x0, 0x0, 0x0,
    0xbd, 0x0, 0x0, 0x0, 0xbe, 0x77, 0x77, 0x71,
    0xbf, 0xff, 0xff, 0xf2,

    /* U+004D "M" */
    0xbe, 0x0, 0x0, 0x0, 0xc, 0xcb, 0xf8, 0x0,
    0x0, 0x7, 0xfc, 0xbf, 0xf3, 0x0, 0x1, 0xff,
    0xcb, 0xdd, 0xc0, 0x0, 0xbd, 0xdc, 0xbc, 0x4f,
    0x70, 0x5f, 0x4c, 0xcb, 0xc0, 0x9f, 0x2e, 0xa0,
    0xcc, 0xbc, 0x1, 0xef, 0xe1, 0xc, 0xcb, 0xc0,
    0x5, 0xf5, 0x0, 0xcc, 0xbc, 0x0, 0x1, 0x0,
    0xc, 0xcb, 0xc0, 0x0, 0x0, 0x0, 0xcc, 0xbc,
    0x0, 0x0, 0x0, 0xc, 0xc0,

    /* U+004E "N" */
    0xbe, 0x10, 0x0, 0x9, 0xfb, 0xfa, 0x0, 0x0,
    0x9f, 0xbf, 0xf6, 0x0, 0x9, 0xfb, 0xcb, 0xf2,
    0x0, 0x9f, 0xbc, 0x1e, 0xc0, 0x9, 0xfb, 0xc0,
    0x5f, 0x80, 0x9f, 0xbc, 0x0, 0x9f, 0x39, 0xfb,
    0xc0, 0x0, 0xde, 0xaf, 0xbc, 0x0, 0x3, 0xff,
    0xfb, 0xc0, 0x0, 0x7, 0xff, 0xbc, 0x0, 0x0,
    0xb, 0xf0,

    /* U+004F "O" */
    0x0, 0x7, 0xdf, 0xea, 0x30, 0x0, 0xc, 0xfc,
    0x89, 0xef, 0x50, 0x8, 0xf7, 0x0, 0x1, 0xdf,
    0x10, 0xfd, 0x0, 0x0, 0x4, 0xf7, 0x3f, 0x80,
    0x0, 0x0, 0xf, 0xb4, 0xf7, 0x0, 0x0, 0x0,
    0xec, 0x3f, 0x80, 0x0, 0x0, 0xf, 0xb0, 0xfd,
    0x0, 0x0, 0x4, 0xf7, 0x8, 0xf7, 0x0, 0x1,
    0xdf, 0x10, 0xc, 0xfc, 0x89, 0xef, 0x50, 0x0,
    0x7, 0xdf, 0xea, 0x30, 0x0,

    /* U+0050 "P" */
    0xbf, 0xff, 0xea, 0x20, 0xbe, 0x66, 0x8f, 0xe1,
    0xbd, 0x0, 0x4, 0xf9, 0xbd, 0x0, 0x0, 0xfb,
    0xbd, 0x0, 0x2, 0xfa, 0xbd, 0x11, 0x4c, 0xf4,
    0xbf, 0xff, 0xfe, 0x60, 0xbe, 0x44, 0x30, 0x0,
    0xbd, 0x0, 0x0, 0x0, 0xbd, 0x0, 0x0, 0x0,
    0xbd, 0x0, 0x0, 0x0,

    /* U+0051 "Q" */
    0x0, 0x7, 0xdf, 0xea, 0x30, 0x0, 0x0, 0xcf,
    0xc8, 0x9e, 0xf5, 0x0, 0x8, 0xf7, 0x0, 0x1,
    0xdf, 0x10, 0xf, 0xd0, 0x0, 0x0, 0x4f, 0x70,
    0x3f, 0x80, 0x0, 0x0, 0xf, 0xb0, 0x4f, 0x70,
    0x0, 0x0, 0xe, 0xc0, 0x3f, 0x80, 0x0, 0x0,
    0xf, 0xb0, 0xf, 0xd0, 0x0, 0x0, 0x4f, 0x70,
    0x8, 0xf7, 0x0, 0x1, 0xdf, 0x10, 0x0, 0xcf,
    0xc8, 0x9e, 0xf5, 0x0, 0x0, 0x7, 0xdf, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xfb, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x5e, 0xe3,

    /* U+0052 "R" */
    0xbf, 0xff, 0xeb, 0x30, 0xb, 0xe6, 0x68, 0xef,
    0x30, 0xbd, 0x0, 0x2, 0xfa, 0xb, 0xd0, 0x0,
    0xf, 0xb0, 0xbd, 0x11, 0x3b, 0xf6, 0xb, 0xff,
    0xff, 0xf7, 0x0, 0xbe, 0x44, 0xed, 0x0, 0xb,
    0xd0, 0x5, 0xf8, 0x0, 0xbd, 0x0, 0xb, 0xf3,
    0xb, 0xd0, 0x0, 0x1f, 0xd0, 0xbd, 0x0, 0x0,
    0x6f, 0x80,

    /* U+0053 "S" */
    0x1, 0x9e, 0xfd, 0x80, 0x0, 0xdf, 0xa7, 0xaf,
    0xb0, 0x4f, 0x80, 0x0, 0x7a, 0x4, 0xf9, 0x0,
    0x0, 0x0, 0xb, 0xfc, 0x61, 0x0, 0x0, 0x6,
    0xcf, 0xfb, 0x20, 0x0, 0x0, 0x17, 0xfe, 0x0,
    0x20, 0x0, 0x7, 0xf4, 0x9f, 0x30, 0x0, 0x8f,
    0x32, 0xef, 0xa8, 0xaf, 0xc0, 0x1, 0x9e, 0xfe,
    0x91, 0x0,

    /* U+0054 "T" */
    0xdf, 0xff, 0xff, 0xff, 0x85, 0x66, 0xaf, 0x86,
    0x63, 0x0, 0x7, 0xf2, 0x0, 0x0, 0x0, 0x7f,
    0x20, 0x0, 0x0, 0x7, 0xf2, 0x0, 0x0, 0x0,
    0x7f, 0x20, 0x0, 0x0, 0x7, 0xf2, 0x0, 0x0,
    0x0, 0x7f, 0x20, 0x0, 0x0, 0x7, 0xf2, 0x0,
    0x0, 0x0, 0x7f, 0x20, 0x0, 0x0, 0x7, 0xf2,
    0x0, 0x0,

    /* U+0055 "U" */
    0xdc, 0x0, 0x0, 0xa, 0xfd, 0xc0, 0x0, 0x0,
    0xaf, 0xdc, 0x0, 0x0, 0xa, 0xfd, 0xc0, 0x0,
    0x0, 0xaf, 0xdc, 0x0, 0x0, 0xa, 0xfd, 0xc0,
    0x0, 0x0, 0xaf, 0xdd, 0x0, 0x0, 0xb, 0xeb,
    0xf0, 0x0, 0x0, 0xdd, 0x7f, 0x70, 0x0, 0x5f,
    0x80, 0xdf, 0xb8, 0xaf, 0xe1, 0x1, 0x9e, 0xfe,
    0x91, 0x0,

    /* U+0056 "V" */
    0xbf, 0x10, 0x0, 0x0, 0xcd, 0x4, 0xf7, 0x0,
    0x0, 0x2f, 0x70, 0xe, 0xd0, 0x0, 0x8, 0xf1,
    0x0, 0x8f, 0x30, 0x0, 0xdb, 0x0, 0x1, 0xf9,
    0x0, 0x3f, 0x50, 0x0, 0xb, 0xf0, 0x9, 0xe0,
    0x0, 0x0, 0x4f, 0x50, 0xe9, 0x0, 0x0, 0x0,
    0xeb, 0x5f, 0x20, 0x0, 0x0, 0x8, 0xfc, 0xc0,
    0x0, 0x0, 0x0, 0x1f, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0x10, 0x0, 0x0,

    /* U+0057 "W" */
    0xbf, 0x0, 0x0, 0x6f, 0x30, 0x0, 0x2f, 0x66,
    0xf4, 0x0, 0xa, 0xf8, 0x0, 0x6, 0xf1, 0x1f,
    0x90, 0x0, 0xff, 0xd0, 0x0, 0xbc, 0x0, 0xce,
    0x0, 0x4f, 0xaf, 0x20, 0xf, 0x70, 0x6, 0xf2,
    0x8, 0xe2, 0xf7, 0x4, 0xf3, 0x0, 0x1f, 0x70,
    0xd9, 0xc, 0xc0, 0x8e, 0x0, 0x0, 0xcc, 0x1f,
    0x40, 0x7f, 0x1d, 0x90, 0x0, 0x7, 0xf8, 0xf0,
    0x2, 0xf8, 0xf4, 0x0, 0x0, 0x2f, 0xfa, 0x0,
    0xd, 0xff, 0x0, 0x0, 0x0, 0xdf, 0x50, 0x0,
    0x8f, 0xa0, 0x0, 0x0, 0x8, 0xf1, 0x0, 0x3,
    0xf5, 0x0, 0x0,

    /* U+0058 "X" */
    0x6f, 0x90, 0x0, 0x5, 0xf8, 0xa, 0xf4, 0x0,
    0x1e, 0xc0, 0x1, 0xee, 0x10, 0xbf, 0x20, 0x0,
    0x3f, 0xa7, 0xf5, 0x0, 0x0, 0x8, 0xff, 0xa0,
    0x0, 0x0, 0x1, 0xff, 0x20, 0x0, 0x0, 0xa,
    0xff, 0xc0, 0x0, 0x0, 0x5f, 0x76, 0xf7, 0x0,
    0x1, 0xec, 0x0, 0xbf, 0x30, 0xc, 0xf1, 0x0,
    0x1e, 0xd0, 0x7f, 0x50, 0x0, 0x5, 0xfa,

    /* U+0059 "Y" */
    0x9f, 0x50, 0x0, 0x9, 0xf2, 0x1e, 0xe0, 0x0,
    0x2f, 0x90, 0x5, 0xf8, 0x0, 0xbe, 0x10, 0x0,
    0xbf, 0x24, 0xf6, 0x0, 0x0, 0x2f, 0xbd, 0xc0,
    0x0, 0x0, 0x8, 0xff, 0x30, 0x0, 0x0, 0x0,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0xfa, 0x0, 0x0, 0x0, 0x0, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0xfa, 0x0, 0x0,

    /* U+005A "Z" */
    0x3f, 0xff, 0xff, 0xff, 0x21, 0x66, 0x66, 0x8f,
    0xb0, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x6,
    0xf7, 0x0, 0x0, 0x1, 0xec, 0x0, 0x0, 0x0,
    0xaf, 0x30, 0x0, 0x0, 0x4f, 0x80, 0x0, 0x0,
    0xe, 0xd0, 0x0, 0x0, 0x9, 0xf3, 0x0, 0x0,
    0x4, 0xfd, 0x77, 0x77, 0x72, 0xaf, 0xff, 0xff,
    0xff, 0x60,

    /* U+005B "[" */
    0x11, 0x11, 0xb, 0xff, 0xf0, 0xbd, 0x44, 0xb,
    0xc0, 0x0, 0xbc, 0x0, 0xb, 0xc0, 0x0, 0xbc,
    0x0, 0xb, 0xc0, 0x0, 0xbc, 0x0, 0xb, 0xc0,
    0x0, 0xbc, 0x0, 0xb, 0xc0, 0x0, 0xbc, 0x0,
    0xb, 0xd2, 0x20, 0xbf, 0xff, 0x3, 0x44, 0x40,

    /* U+005C "\\" */
    0x9e, 0x0, 0x0, 0x3f, 0x40, 0x0, 0xd, 0xa0,
    0x0, 0x7, 0xf0, 0x0, 0x1, 0xf6, 0x0, 0x0,
    0xbc, 0x0, 0x0, 0x5f, 0x20, 0x0, 0xf, 0x80,
    0x0, 0x9, 0xe0, 0x0, 0x4, 0xf4, 0x0, 0x0,
    0xda,

    /* U+005D "]" */
    0x11, 0x11, 0xb, 0xff, 0xf1, 0x34, 0xaf, 0x10,
    0x7, 0xf1, 0x0, 0x7f, 0x10, 0x7, 0xf1, 0x0,
    0x7f, 0x10, 0x7, 0xf1, 0x0, 0x7f, 0x10, 0x7,
    0xf1, 0x0, 0x7f, 0x10, 0x7, 0xf1, 0x0, 0x7f,
    0x11, 0x28, 0xf1, 0xbf, 0xff, 0x13, 0x44, 0x40,

    /* U+005E "^" */
    0x0, 0x3f, 0x90, 0x0, 0xa, 0xff, 0x0, 0x1,
    0xf8, 0xf7, 0x0, 0x8f, 0xa, 0xd0, 0xe, 0x90,
    0x3f, 0x45, 0xf2, 0x0, 0xcb,

    /* U+005F "_" */
    0x11, 0x11, 0x11, 0xf, 0xff, 0xff, 0xf8, 0x33,
    0x33, 0x33, 0x20,

    /* U+0060 "`" */
    0x7, 0x50, 0x6, 0xf1, 0x0, 0xc9,

    /* U+0061 "a" */
    0x0, 0x9e, 0xfb, 0x20, 0xb, 0xe7, 0x6d, 0xd0,
    0x0, 0x10, 0x5, 0xf3, 0x2, 0xbe, 0xfd, 0xf4,
    0xe, 0xd4, 0x38, 0xf5, 0x3f, 0x50, 0x5, 0xf5,
    0x1f, 0xb2, 0x3d, 0xf5, 0x4, 0xdf, 0xd6, 0xf5,

    /* U+0062 "b" */
    0xea, 0x0, 0x0, 0x0, 0xea, 0x0, 0x0, 0x0,
    0xea, 0x0, 0x0, 0x0, 0xea, 0x7e, 0xfa, 0x10,
    0xef, 0xd8, 0x9f, 0xc0, 0xef, 0x10, 0x6, 0xf4,
    0xeb, 0x0, 0x1, 0xf8, 0xeb, 0x0, 0x1, 0xf8,
    0xee, 0x10, 0x6, 0xf4, 0xee, 0xd7, 0x8f, 0xc0,
    0xe7, 0x8e, 0xfa, 0x10,

    /* U+0063 "c" */
    0x0, 0x6d, 0xfd, 0x40, 0x8, 0xfb, 0x7c, 0xf1,
    0x1f, 0xa0, 0x0, 0x20, 0x4f, 0x50, 0x0, 0x0,
    0x4f, 0x50, 0x0, 0x0, 0x1f, 0xa0, 0x0, 0x20,
    0x8, 0xfa, 0x7c, 0xf1, 0x0, 0x7d, 0xfd, 0x40,

    /* U+0064 "d" */
    0x0, 0x0, 0x0, 0x6f, 0x10, 0x0, 0x0, 0x6,
    0xf1, 0x0, 0x0, 0x0, 0x6f, 0x10, 0x8, 0xef,
    0x97, 0xf1, 0x9, 0xfa, 0x7c, 0xff, 0x11, 0xfa,
    0x0, 0xd, 0xf1, 0x4f, 0x50, 0x0, 0x8f, 0x14,
    0xf4, 0x0, 0x7, 0xf1, 0x1f, 0x80, 0x0, 0xbf,
    0x10, 0x9f, 0x74, 0x9f, 0xf1, 0x0, 0x8e, 0xfa,
    0x5f, 0x10,

    /* U+0065 "e" */
    0x0, 0x7d, 0xfc, 0x40, 0x9, 0xf8, 0x6b, 0xf3,
    0x1f, 0x80, 0x0, 0xea, 0x4f, 0xff, 0xff, 0xfc,
    0x4f, 0x73, 0x33, 0x32, 0x1f, 0xa0, 0x0, 0x30,
    0x9, 0xfa, 0x69, 0xf4, 0x0, 0x7d, 0xfd, 0x60,

    /* U+0066 "f" */
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xfa, 0x1, 0xfb,
    0x54, 0x4, 0xf3, 0x0, 0x6, 0xf2, 0x0, 0xcf,
    0xff, 0xf2, 0x39, 0xf6, 0x50, 0x6, 0xf2, 0x0,
    0x6, 0xf2, 0x0, 0x6, 0xf2, 0x0, 0x6, 0xf2,
    0x0, 0x6, 0xf2, 0x0, 0x6, 0xf2, 0x0,

    /* U+0067 "g" */
    0x0, 0x8e, 0xfa, 0x5f, 0x10, 0x9f, 0xa7, 0xce,
    0xf1, 0x1f, 0x90, 0x0, 0xcf, 0x14, 0xf5, 0x0,
    0x7, 0xf1, 0x4f, 0x50, 0x0, 0x7f, 0x11, 0xf9,
    0x0, 0xc, 0xf1, 0x9, 0xfa, 0x7c, 0xff, 0x10,
    0x8, 0xef, 0xa8, 0xf1, 0x1, 0x10, 0x0, 0xbe,
    0x0, 0xce, 0x87, 0xaf, 0x60, 0x1, 0xae, 0xfc,
    0x50, 0x0,

    /* U+0068 "h" */
    0xea, 0x0, 0x0, 0xe, 0xa0, 0x0, 0x0, 0xea,
    0x0, 0x0, 0xe, 0xa8, 0xee, 0x80, 0xef, 0xd7,
    0xbf, 0x7e, 0xe0, 0x0, 0xeb, 0xea, 0x0, 0xb,
    0xde, 0xa0, 0x0, 0xbd, 0xea, 0x0, 0xb, 0xde,
    0xa0, 0x0, 0xbd, 0xea, 0x0, 0xb, 0xd0,

    /* U+0069 "i" */
    0xd, 0x90, 0xd9, 0x0, 0x0, 0xea, 0xe, 0xa0,
    0xea, 0xe, 0xa0, 0xea, 0xe, 0xa0, 0xea, 0xe,
    0xa0,

    /* U+006A "j" */
    0x0, 0xd, 0x90, 0x0, 0xd9, 0x0, 0x0, 0x0,
    0x0, 0xea, 0x0, 0xe, 0xa0, 0x0, 0xea, 0x0,
    0xe, 0xa0, 0x0, 0xea, 0x0, 0xe, 0xa0, 0x0,
    0xea, 0x0, 0xe, 0xa0, 0x0, 0xf9, 0x47, 0xaf,
    0x48, 0xee, 0x70,

    /* U+006B "k" */
    0xea, 0x0, 0x0, 0x0, 0xea, 0x0, 0x0, 0x0,
    0xea, 0x0, 0x0, 0x0, 0xea, 0x0, 0x6f, 0x60,
    0xea, 0x4, 0xf8, 0x0, 0xea, 0x2f, 0xa0, 0x0,
    0xeb, 0xdf, 0x30, 0x0, 0xef, 0xdf, 0xc0, 0x0,
    0xee, 0x26, 0xf7, 0x0, 0xea, 0x0, 0xbf, 0x20,
    0xea, 0x0, 0x1f, 0xc0,

    /* U+006C "l" */
    0xea, 0xea, 0xea, 0xea, 0xea, 0xea, 0xea, 0xea,
    0xea, 0xea, 0xea,

    /* U+006D "m" */
    0xe8, 0xaf, 0xd5, 0x3c, 0xfa, 0x1e, 0xf9, 0x5d,
    0xfd, 0x66, 0xf9, 0xed, 0x0, 0x6f, 0x70, 0xb,
    0xde, 0xa0, 0x3, 0xf4, 0x0, 0x9e, 0xea, 0x0,
    0x3f, 0x40, 0x9, 0xee, 0xa0, 0x3, 0xf4, 0x0,
    0x9e, 0xea, 0x0, 0x3f, 0x40, 0x9, 0xee, 0xa0,
    0x3, 0xf4, 0x0, 0x9e,

    /* U+006E "n" */
    0xe8, 0x9e, 0xe9, 0xe, 0xfa, 0x49, 0xf7, 0xed,
    0x0, 0xd, 0xbe, 0xa0, 0x0, 0xbd, 0xea, 0x0,
    0xb, 0xde, 0xa0, 0x0, 0xbd, 0xea, 0x0, 0xb,
    0xde, 0xa0, 0x0, 0xbd,

    /* U+006F "o" */
    0x0, 0x6d, 0xfd, 0x50, 0x0, 0x8f, 0xb7, 0xbf,
    0x60, 0x1f, 0xa0, 0x0, 0xce, 0x4, 0xf5, 0x0,
    0x6, 0xf3, 0x4f, 0x50, 0x0, 0x6f, 0x31, 0xfa,
    0x0, 0xc, 0xe0, 0x8, 0xfb, 0x7b, 0xf6, 0x0,
    0x6, 0xdf, 0xd5, 0x0,

    /* U+0070 "p" */
    0xe8, 0x8e, 0xfa, 0x10, 0xef, 0xb4, 0x6e, 0xc0,
    0xee, 0x0, 0x5, 0xf4, 0xeb, 0x0, 0x1, 0xf8,
    0xeb, 0x0, 0x2, 0xf8, 0xef, 0x10, 0x7, 0xf4,
    0xef, 0xd7, 0x9f, 0xc0, 0xea, 0x8e, 0xfa, 0x10,
    0xea, 0x0, 0x0, 0x0, 0xea, 0x0, 0x0, 0x0,
    0xea, 0x0, 0x0, 0x0,

    /* U+0071 "q" */
    0x0, 0x8e, 0xfa, 0x5f, 0x10, 0x9f, 0xa7, 0xce,
    0xf1, 0x1f, 0x90, 0x0, 0xcf, 0x14, 0xf5, 0x0,
    0x7, 0xf1, 0x4f, 0x50, 0x0, 0x7f, 0x11, 0xf9,
    0x0, 0xc, 0xf1, 0x9, 0xfa, 0x7c, 0xff, 0x10,
    0x8, 0xef, 0xa7, 0xf1, 0x0, 0x0, 0x0, 0x6f,
    0x10, 0x0, 0x0, 0x6, 0xf1, 0x0, 0x0, 0x0,
    0x6f, 0x10,

    /* U+0072 "r" */
    0xe8, 0xaf, 0xae, 0xfa, 0x42, 0xee, 0x0, 0xe,
    0xb0, 0x0, 0xea, 0x0, 0xe, 0xa0, 0x0, 0xea,
    0x0, 0xe, 0xa0, 0x0,

    /* U+0073 "s" */
    0x5, 0xdf, 0xd6, 0x2, 0xfb, 0x6a, 0xf3, 0x5f,
    0x60, 0x2, 0x0, 0xcf, 0xc7, 0x10, 0x0, 0x49,
    0xee, 0x21, 0x40, 0x3, 0xf7, 0x6f, 0x95, 0xaf,
    0x40, 0x7d, 0xfd, 0x60,

    /* U+0074 "t" */
    0x4, 0x70, 0x0, 0x8, 0xf0, 0x0, 0x8, 0xf0,
    0x0, 0xdf, 0xff, 0xf4, 0x4b, 0xf5, 0x51, 0x8,
    0xf0, 0x0, 0x8, 0xf0, 0x0, 0x8, 0xf0, 0x0,
    0x8, 0xf0, 0x0, 0x5, 0xfa, 0x73, 0x0, 0x9e,
    0xe6,

    /* U+0075 "u" */
    0xf, 0x80, 0x0, 0xdb, 0xf, 0x80, 0x0, 0xdb,
    0xf, 0x80, 0x0, 0xdb, 0xf, 0x80, 0x0, 0xdb,
    0xf, 0x80, 0x0, 0xdb, 0xe, 0xa0, 0x1, 0xfb,
    0xa, 0xf7, 0x5c, 0xfb, 0x1, 0xaf, 0xe7, 0xab,

    /* U+0076 "v" */
    0xaf, 0x10, 0x0, 0xea, 0x4f, 0x60, 0x4, 0xf4,
    0xd, 0xc0, 0xa, 0xd0, 0x6, 0xf3, 0xf, 0x70,
    0x1, 0xf9, 0x5f, 0x10, 0x0, 0x9e, 0xba, 0x0,
    0x0, 0x3f, 0xf4, 0x0, 0x0, 0xc, 0xd0, 0x0,

    /* U+0077 "w" */
    0xbe, 0x0, 0xc, 0xc0, 0x0, 0xd9, 0x5f, 0x30,
    0x1f, 0xf2, 0x2, 0xf4, 0xf, 0x80, 0x6e, 0xf7,
    0x7, 0xe0, 0x9, 0xe0, 0xb9, 0xac, 0xc, 0x80,
    0x4, 0xf4, 0xf4, 0x4f, 0x3f, 0x30, 0x0, 0xed,
    0xe0, 0xe, 0xdd, 0x0, 0x0, 0x8f, 0x90, 0x9,
    0xf8, 0x0, 0x0, 0x2f, 0x30, 0x3, 0xf2, 0x0,

    /* U+0078 "x" */
    0x8f, 0x50, 0x8, 0xf3, 0xc, 0xe1, 0x4f, 0x70,
    0x2, 0xfc, 0xeb, 0x0, 0x0, 0x6f, 0xe1, 0x0,
    0x0, 0x5f, 0xe1, 0x0, 0x2, 0xfa, 0xec, 0x0,
    0xc, 0xd0, 0x5f, 0x70, 0x8f, 0x30, 0xa, 0xf3,

    /* U+0079 "y" */
    0xbf, 0x10, 0x0, 0xdb, 0x4f, 0x60, 0x4, 0xf4,
    0xd, 0xd0, 0xa, 0xe0, 0x6, 0xf3, 0xf, 0x70,
    0x0, 0xea, 0x6f, 0x10, 0x0, 0x8f, 0xda, 0x0,
    0x0, 0x1f, 0xf3, 0x0, 0x0, 0xc, 0xd0, 0x0,
    0x0, 0x2f, 0x60, 0x0, 0x57, 0xce, 0x0, 0x0,
    0xaf, 0xd3, 0x0, 0x0,

    /* U+007A "z" */
    0x4f, 0xff, 0xff, 0x81, 0x44, 0x4d, 0xf1, 0x0,
    0x6, 0xf6, 0x0, 0x1, 0xfb, 0x0, 0x0, 0xbe,
    0x10, 0x0, 0x6f, 0x50, 0x0, 0x2f, 0xd4, 0x44,
    0x38, 0xff, 0xff, 0xfb,

    /* U+007B "{" */
    0x0, 0x0, 0x0, 0x0, 0x2c, 0xf7, 0x0, 0xaf,
    0x62, 0x0, 0xcc, 0x0, 0x0, 0xdc, 0x0, 0x0,
    0xdc, 0x0, 0x0, 0xfa, 0x0, 0x4d, 0xf4, 0x0,
    0x7f, 0xc1, 0x0, 0x4, 0xf9, 0x0, 0x0, 0xdb,
    0x0, 0x0, 0xdc, 0x0, 0x0, 0xdc, 0x0, 0x0,
    0xbf, 0x40, 0x0, 0x4f, 0xf7, 0x0, 0x1, 0x32,

    /* U+007C "|" */
    0x3f, 0x33, 0xf3, 0x3f, 0x33, 0xf3, 0x3f, 0x33,
    0xf3, 0x3f, 0x33, 0xf3, 0x3f, 0x33, 0xf3, 0x3f,
    0x33, 0xf3, 0x3f, 0x33, 0xf3,

    /* U+007D "}" */
    0x10, 0x0, 0x0, 0xbf, 0xa0, 0x0, 0x39, 0xf6,
    0x0, 0x1, 0xf8, 0x0, 0x0, 0xf9, 0x0, 0x0,
    0xf9, 0x0, 0x0, 0xeb, 0x0, 0x0, 0x7f, 0xc1,
    0x0, 0x3e, 0xf3, 0x0, 0xdd, 0x10, 0x0, 0xf9,
    0x0, 0x0, 0xf9, 0x0, 0x0, 0xf8, 0x0, 0x16,
    0xf6, 0x0, 0xbf, 0xd1, 0x0, 0x33, 0x0, 0x0,

    /* U+007E "~" */
    0x0, 0x0, 0x0, 0x1, 0x4, 0xff, 0x30, 0x8c,
    0xd, 0xb9, 0xe3, 0xd9, 0x1f, 0x40, 0xaf, 0xf2,
    0x4, 0x0, 0x3, 0x10,

    /* U+F001 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x15, 0xae, 0xe0,
    0x0, 0x0, 0x0, 0x2, 0x7c, 0xff, 0xff, 0xf1,
    0x0, 0x0, 0x39, 0xef, 0xff, 0xff, 0xff, 0xf1,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0x0, 0x1, 0xff, 0xff, 0xff, 0xc7, 0x2c, 0xf1,
    0x0, 0x1, 0xff, 0xea, 0x51, 0x0, 0xc, 0xf1,
    0x0, 0x1, 0xfd, 0x0, 0x0, 0x0, 0xc, 0xf1,
    0x0, 0x1, 0xfc, 0x0, 0x0, 0x0, 0xc, 0xf1,
    0x0, 0x1, 0xfc, 0x0, 0x0, 0x0, 0xc, 0xf1,
    0x0, 0x1, 0xfc, 0x0, 0x0, 0x4a, 0xbe, 0xf1,
    0x0, 0x1, 0xfc, 0x0, 0x5, 0xff, 0xff, 0xf1,
    0x8, 0xdd, 0xfc, 0x0, 0x6, 0xff, 0xff, 0xf0,
    0xaf, 0xff, 0xfc, 0x0, 0x0, 0x8e, 0xfc, 0x30,
    0xaf, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x19, 0xdd, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F008 "" */
    0x20, 0x4, 0x44, 0x44, 0x44, 0x44, 0x0, 0x2e,
    0x57, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x5e, 0xfa,
    0xbf, 0x52, 0x22, 0x22, 0x5f, 0xba, 0xff, 0x3,
    0xf3, 0x0, 0x0, 0x3, 0xf3, 0xf, 0xf8, 0x9f,
    0x30, 0x0, 0x0, 0x3f, 0xa8, 0xff, 0x79, 0xf8,
    0x55, 0x55, 0x58, 0xf9, 0x7f, 0xf0, 0x3f, 0xff,
    0xff, 0xff, 0xff, 0x30, 0xff, 0xbc, 0xf4, 0x11,
    0x11, 0x14, 0xfc, 0xbf, 0xf4, 0x6f, 0x30, 0x0,
    0x0, 0x3f, 0x64, 0xff, 0x3, 0xf3, 0x0, 0x0,
    0x3, 0xf3, 0xf, 0xfe, 0xef, 0x96, 0x66, 0x66,
    0x9f, 0xee, 0xfd, 0x14, 0xff, 0xff, 0xff, 0xff,
    0xf4, 0x1d,

    /* U+F00B "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xff, 0xf4, 0x9f, 0xff, 0xff, 0xff, 0xfe, 0xff,
    0xff, 0x5b, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0xbf, 0xff, 0xff, 0xff, 0xff, 0x8b, 0xba,
    0x25, 0xbb, 0xbb, 0xbb, 0xbb, 0x84, 0x66, 0x50,
    0x26, 0x66, 0x66, 0x66, 0x64, 0xff, 0xff, 0x5a,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0xbf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x5a, 0xff,
    0xff, 0xff, 0xff, 0xf2, 0x44, 0x40, 0x14, 0x44,
    0x44, 0x44, 0x42, 0xac, 0xcc, 0x26, 0xcc, 0xcc,
    0xcc, 0xcc, 0xaf, 0xff, 0xf5, 0xbf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x5b, 0xff, 0xff, 0xff,
    0xff, 0xfc, 0xff, 0xe3, 0x8f, 0xff, 0xff, 0xff,
    0xfc,

    /* U+F00C "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xff, 0xf6, 0x4, 0x20,
    0x0, 0x0, 0xc, 0xff, 0xf6, 0x7, 0xfe, 0x20,
    0x0, 0xc, 0xff, 0xf6, 0x0, 0xef, 0xfe, 0x20,
    0xc, 0xff, 0xf6, 0x0, 0x3, 0xff, 0xfe, 0x3c,
    0xff, 0xf6, 0x0, 0x0, 0x3, 0xff, 0xff, 0xff,
    0xf6, 0x0, 0x0, 0x0, 0x3, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xd6, 0x0, 0x0,
    0x0, 0x0,

    /* U+F00D "" */
    0x3c, 0x50, 0x0, 0x2, 0xc7, 0xe, 0xff, 0x50,
    0x2, 0xef, 0xf3, 0x9f, 0xff, 0x52, 0xef, 0xfd,
    0x0, 0x9f, 0xff, 0xef, 0xfd, 0x10, 0x0, 0x9f,
    0xff, 0xfd, 0x10, 0x0, 0x2, 0xff, 0xff, 0x60,
    0x0, 0x2, 0xef, 0xff, 0xff, 0x50, 0x2, 0xef,
    0xfd, 0xaf, 0xff, 0x50, 0xdf, 0xfd, 0x10, 0x9f,
    0xff, 0x2b, 0xfd, 0x10, 0x0, 0x9f, 0xe1, 0x6,
    0x10, 0x0, 0x0, 0x52, 0x0,

    /* U+F011 "" */
    0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x2b, 0x30, 0xef, 0x70, 0x97, 0x0, 0x0, 0x1e,
    0xfc, 0xe, 0xf7, 0x3f, 0xf8, 0x0, 0xb, 0xff,
    0x50, 0xef, 0x70, 0xbf, 0xf4, 0x3, 0xff, 0x60,
    0xe, 0xf7, 0x0, 0xdf, 0xb0, 0x8f, 0xe0, 0x0,
    0xef, 0x70, 0x5, 0xff, 0x1b, 0xfa, 0x0, 0xe,
    0xf7, 0x0, 0x1f, 0xf3, 0xbf, 0x90, 0x0, 0xdf,
    0x60, 0x0, 0xff, 0x49, 0xfc, 0x0, 0x2, 0x40,
    0x0, 0x3f, 0xf3, 0x5f, 0xf2, 0x0, 0x0, 0x0,
    0x9, 0xff, 0x0, 0xef, 0xc0, 0x0, 0x0, 0x4,
    0xff, 0x80, 0x5, 0xff, 0xc3, 0x0, 0x7, 0xff,
    0xe1, 0x0, 0x8, 0xff, 0xff, 0xef, 0xff, 0xe2,
    0x0, 0x0, 0x4, 0xdf, 0xff, 0xff, 0xa1, 0x0,
    0x0, 0x0, 0x0, 0x36, 0x75, 0x10, 0x0, 0x0,

    /* U+F013 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3c, 0xdc, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x8,
    0x25, 0xdf, 0xff, 0xd5, 0x28, 0x0, 0x9, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf9, 0x2, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf2, 0x4f, 0xff, 0xfe,
    0x41, 0x4e, 0xff, 0xff, 0x40, 0x2f, 0xff, 0x60,
    0x0, 0x6f, 0xff, 0x20, 0x1, 0xff, 0xf3, 0x0,
    0x3, 0xff, 0xf1, 0x0, 0x5f, 0xff, 0x70, 0x0,
    0x8f, 0xff, 0x50, 0x5f, 0xff, 0xff, 0x85, 0x8f,
    0xff, 0xff, 0x51, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0x6, 0xfd, 0xff, 0xff, 0xff, 0xfd,
    0xf6, 0x0, 0x4, 0x2, 0xbf, 0xff, 0xb2, 0x4,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x28, 0x98, 0x20, 0x0, 0x0,

    /* U+F015 "" */
    0x0, 0x0, 0x0, 0x0, 0x20, 0x0, 0x22, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xcf, 0xa0, 0xf, 0xf4,
    0x0, 0x0, 0x0, 0x3, 0xef, 0xff, 0xd2, 0xff,
    0x40, 0x0, 0x0, 0x5, 0xff, 0x91, 0xbf, 0xef,
    0xf4, 0x0, 0x0, 0x8, 0xff, 0x63, 0xb2, 0x8f,
    0xff, 0x40, 0x0, 0xb, 0xfe, 0x45, 0xff, 0xf4,
    0x5f, 0xf9, 0x0, 0x2d, 0xfd, 0x28, 0xff, 0xff,
    0xf6, 0x3e, 0xfc, 0x1d, 0xfb, 0x1b, 0xff, 0xff,
    0xff, 0xf9, 0x2c, 0xfb, 0x48, 0x1d, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x9, 0x30, 0x3, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0x0, 0x0, 0x3f, 0xff,
    0xf4, 0x36, 0xff, 0xff, 0x10, 0x0, 0x3, 0xff,
    0xff, 0x0, 0x2f, 0xff, 0xf1, 0x0, 0x0, 0x3f,
    0xff, 0xf0, 0x2, 0xff, 0xff, 0x10, 0x0, 0x2,
    0xff, 0xfe, 0x0, 0x1e, 0xff, 0xe0, 0x0,

    /* U+F019 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xff, 0x60, 0x0, 0x0, 0x0, 0x26, 0x6a,
    0xff, 0xfa, 0x66, 0x20, 0x0, 0x5, 0xff, 0xff,
    0xff, 0xff, 0xf5, 0x0, 0x0, 0x6, 0xff, 0xff,
    0xff, 0xf6, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xf6,
    0x0, 0x0, 0x9, 0xbb, 0xbb, 0x46, 0xf6, 0x4b,
    0xbb, 0xb9, 0xff, 0xff, 0xff, 0x50, 0x5f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x5d, 0x2e,
    0xfb, 0xee, 0xee, 0xee, 0xee, 0xed, 0xed, 0xeb,

    /* U+F01C "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0x60,
    0x0, 0x0, 0x4f, 0xfd, 0xdd, 0xdd, 0xdd, 0xff,
    0x30, 0x0, 0x1e, 0xf4, 0x0, 0x0, 0x0, 0x6,
    0xfd, 0x0, 0xa, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xf8, 0x5, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1e, 0xf3, 0xdf, 0xca, 0xa9, 0x0, 0x0,
    0x19, 0xaa, 0xdf, 0xbf, 0xff, 0xff, 0xf6, 0x0,
    0x8, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xec,
    0xcc, 0xff, 0xff, 0xff, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xd9, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,

    /* U+F021 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0x50,
    0x0, 0x4, 0x9c, 0xdc, 0x82, 0x0, 0xff, 0x0,
    0x1b, 0xff, 0xff, 0xff, 0xf9, 0x1f, 0xf0, 0x1d,
    0xff, 0xa5, 0x35, 0xaf, 0xfd, 0xff, 0xb, 0xfe,
    0x30, 0x0, 0x0, 0x3e, 0xff, 0xf3, 0xff, 0x30,
    0x0, 0x7, 0xdc, 0xdf, 0xff, 0x8f, 0xa0, 0x0,
    0x0, 0x8f, 0xff, 0xff, 0xf1, 0x31, 0x0, 0x0,
    0x1, 0x33, 0x33, 0x32, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xee, 0xee, 0xe7, 0x0,
    0x0, 0x7, 0xe8, 0xff, 0xff, 0xff, 0x80, 0x0,
    0x0, 0xdf, 0x5f, 0xff, 0x91, 0x21, 0x0, 0x0,
    0x7f, 0xf0, 0xff, 0xff, 0x80, 0x0, 0x0, 0x7f,
    0xf7, 0xf, 0xfa, 0xff, 0xd7, 0x46, 0xcf, 0xfa,
    0x0, 0xff, 0x7, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0xa, 0xa0, 0x1, 0x7b, 0xdc, 0x82, 0x0, 0x0,

    /* U+F026 "" */
    0x0, 0x0, 0x0, 0x31, 0x0, 0x0, 0x6, 0xf7,
    0x0, 0x0, 0x6f, 0xf8, 0x79, 0x9a, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xf8,
    0xef, 0xff, 0xff, 0xf8, 0x0, 0x1, 0xcf, 0xf8,
    0x0, 0x0, 0x1c, 0xf8, 0x0, 0x0, 0x1, 0xb5,

    /* U+F027 "" */
    0x0, 0x0, 0x0, 0x31, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf8,
    0x0, 0x0, 0x79, 0x9a, 0xff, 0xf8, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xf8, 0xd, 0x40, 0xff, 0xff,
    0xff, 0xf8, 0xa, 0xf1, 0xff, 0xff, 0xff, 0xf8,
    0x3, 0xf3, 0xff, 0xff, 0xff, 0xf8, 0xc, 0xe0,
    0xef, 0xff, 0xff, 0xf8, 0xa, 0x20, 0x0, 0x1,
    0xcf, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x1c, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xb4, 0x0, 0x0,

    /* U+F028 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x73, 0x0,
    0x0, 0x0, 0x0, 0x3, 0x10, 0x0, 0xb, 0xf5,
    0x0, 0x0, 0x0, 0x6, 0xf7, 0x0, 0x2, 0xa,
    0xf3, 0x0, 0x0, 0x6, 0xff, 0x80, 0x6, 0xf6,
    0xc, 0xd0, 0x79, 0x9a, 0xff, 0xf8, 0x0, 0x9,
    0xf3, 0x3f, 0x4f, 0xff, 0xff, 0xff, 0x80, 0xd5,
    0xd, 0xb0, 0xda, 0xff, 0xff, 0xff, 0xf8, 0x9,
    0xf1, 0x7f, 0xa, 0xcf, 0xff, 0xff, 0xff, 0x80,
    0x3f, 0x36, 0xf0, 0x8d, 0xff, 0xff, 0xff, 0xf8,
    0xc, 0xe0, 0x8e, 0xa, 0xbe, 0xff, 0xff, 0xff,
    0x80, 0x92, 0x1e, 0x90, 0xe8, 0x0, 0x1, 0xcf,
    0xf8, 0x0, 0x1c, 0xe1, 0x5f, 0x30, 0x0, 0x1,
    0xcf, 0x80, 0x6, 0xe3, 0x1e, 0xb0, 0x0, 0x0,
    0x1, 0xb5, 0x0, 0x0, 0x1c, 0xe1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xe2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x41, 0x0, 0x0,

    /* U+F03E "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0xff,
    0xda, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0,
    0x4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x0,
    0x2f, 0xff, 0xfc, 0x5f, 0xff, 0xff, 0xf9, 0x6d,
    0xff, 0xfc, 0x0, 0x4f, 0xff, 0xff, 0xfe, 0xbf,
    0xfc, 0x0, 0x0, 0x4f, 0xff, 0xfe, 0x20, 0xac,
    0x0, 0x0, 0x0, 0xef, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xff, 0xee, 0xee, 0xee, 0xee,
    0xee, 0xef, 0xf9, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf9,

    /* U+F043 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xe0, 0x0, 0x0, 0x0, 0x1, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x7f, 0xfc, 0x0, 0x0, 0x0, 0x1e,
    0xff, 0xf5, 0x0, 0x0, 0x9, 0xff, 0xff, 0xd0,
    0x0, 0x4, 0xff, 0xff, 0xff, 0x90, 0x0, 0xdf,
    0xff, 0xff, 0xff, 0x40, 0x7f, 0xff, 0xff, 0xff,
    0xfc, 0xd, 0xff, 0xff, 0xff, 0xff, 0xf2, 0xff,
    0x7f, 0xff, 0xff, 0xff, 0x4e, 0xf1, 0xff, 0xff,
    0xff, 0xf3, 0xaf, 0x75, 0xef, 0xff, 0xff, 0x2,
    0xff, 0x82, 0x8f, 0xff, 0x70, 0x5, 0xff, 0xff,
    0xff, 0x90, 0x0, 0x2, 0x9d, 0xda, 0x40, 0x0,

    /* U+F048 "" */
    0x2, 0x20, 0x0, 0x0, 0x1, 0x1, 0xff, 0x0,
    0x0, 0xb, 0xf2, 0x2f, 0xf0, 0x0, 0x1c, 0xff,
    0x42, 0xff, 0x0, 0x1d, 0xff, 0xf4, 0x2f, 0xf0,
    0x2e, 0xff, 0xff, 0x42, 0xff, 0x3e, 0xff, 0xff,
    0xf4, 0x2f, 0xff, 0xff, 0xff, 0xff, 0x42, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0x2f, 0xfd, 0xff, 0xff,
    0xff, 0x42, 0xff, 0x1c, 0xff, 0xff, 0xf4, 0x2f,
    0xf0, 0xb, 0xff, 0xff, 0x42, 0xff, 0x0, 0xa,
    0xff, 0xf4, 0x2f, 0xf0, 0x0, 0x9, 0xff, 0x41,
    0xfe, 0x0, 0x0, 0x8, 0xd1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F04B "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xb2, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xfe,
    0x50, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xfb,
    0x20, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe5,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xb2, 0x0, 0xff, 0xff, 0xff,
    0xff, 0xd4, 0x0, 0x0, 0xff, 0xff, 0xff, 0xf7,
    0x0, 0x0, 0x0, 0xff, 0xff, 0xfa, 0x10, 0x0,
    0x0, 0x0, 0xff, 0xfd, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x7c, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F04C "" */
    0x2, 0x22, 0x10, 0x0, 0x12, 0x22, 0x0, 0xbf,
    0xff, 0xf5, 0x4, 0xff, 0xff, 0xc0, 0xff, 0xff,
    0xf9, 0x8, 0xff, 0xff, 0xf1, 0xff, 0xff, 0xfa,
    0x8, 0xff, 0xff, 0xf1, 0xff, 0xff, 0xfa, 0x8,
    0xff, 0xff, 0xf1, 0xff, 0xff, 0xfa, 0x8, 0xff,
    0xff, 0xf1, 0xff, 0xff, 0xfa, 0x8, 0xff, 0xff,
    0xf1, 0xff, 0xff, 0xfa, 0x8, 0xff, 0xff, 0xf1,
    0xff, 0xff, 0xfa, 0x8, 0xff, 0xff, 0xf1, 0xff,
    0xff, 0xfa, 0x8, 0xff, 0xff, 0xf1, 0xff, 0xff,
    0xfa, 0x8, 0xff, 0xff, 0xf1, 0xff, 0xff, 0xfa,
    0x8, 0xff, 0xff, 0xf1, 0xff, 0xff, 0xf9, 0x8,
    0xff, 0xff, 0xf0, 0x8f, 0xff, 0xe3, 0x2, 0xdf,
    0xff, 0x90,

    /* U+F04D "" */
    0x2, 0x22, 0x22, 0x22, 0x22, 0x22, 0x0, 0xbf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xd0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf2, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf1, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xa0,

    /* U+F051 "" */
    0x1, 0x0, 0x0, 0x0, 0x22, 0x1, 0xfc, 0x10,
    0x0, 0xe, 0xf2, 0x3f, 0xfd, 0x10, 0x0, 0xef,
    0x33, 0xff, 0xfe, 0x20, 0xe, 0xf3, 0x3f, 0xff,
    0xfe, 0x30, 0xef, 0x33, 0xff, 0xff, 0xff, 0x4e,
    0xf3, 0x3f, 0xff, 0xff, 0xff, 0xff, 0x33, 0xff,
    0xff, 0xff, 0xff, 0xf3, 0x3f, 0xff, 0xff, 0xfd,
    0xff, 0x33, 0xff, 0xff, 0xfd, 0x1e, 0xf3, 0x3f,
    0xff, 0xfc, 0x10, 0xef, 0x33, 0xff, 0xfb, 0x0,
    0xe, 0xf3, 0x3f, 0xfa, 0x0, 0x0, 0xef, 0x31,
    0xd9, 0x0, 0x0, 0xd, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F052 "" */
    0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x6,
    0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x5, 0xff,
    0xff, 0xff, 0xff, 0xf7, 0x0, 0x4, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf5, 0x0, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0x0, 0x3, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x0, 0x9, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcc, 0xca, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x20, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xd0,

    /* U+F053 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xd1, 0x0, 0x0, 0x9, 0xff, 0x70, 0x0, 0x9,
    0xff, 0xa0, 0x0, 0x9, 0xff, 0xa0, 0x0, 0x9,
    0xff, 0xb0, 0x0, 0x9, 0xff, 0xb0, 0x0, 0x1,
    0xff, 0xf2, 0x0, 0x0, 0x5, 0xff, 0xd1, 0x0,
    0x0, 0x5, 0xff, 0xd1, 0x0, 0x0, 0x5, 0xff,
    0xd1, 0x0, 0x0, 0x5, 0xff, 0xd1, 0x0, 0x0,
    0x5, 0xff, 0x70, 0x0, 0x0, 0x5, 0x90,

    /* U+F054 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9d, 0x10, 0x0,
    0x0, 0x1f, 0xfd, 0x10, 0x0, 0x0, 0x5f, 0xfd,
    0x10, 0x0, 0x0, 0x5f, 0xfd, 0x10, 0x0, 0x0,
    0x5f, 0xfd, 0x10, 0x0, 0x0, 0x5f, 0xfd, 0x10,
    0x0, 0x0, 0xbf, 0xf7, 0x0, 0x0, 0x9f, 0xfb,
    0x0, 0x0, 0x9f, 0xfb, 0x0, 0x0, 0x9f, 0xfb,
    0x0, 0x0, 0x9f, 0xfb, 0x0, 0x0, 0x1f, 0xfb,
    0x0, 0x0, 0x0, 0x59, 0x0, 0x0, 0x0,

    /* U+F067 "" */
    0x0, 0x0, 0x0, 0x21, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0x0,
    0x0, 0x0, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0x9c, 0xcc, 0xcf, 0xff, 0xcc, 0xcc, 0xa0, 0x0,
    0x0, 0xd, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xeb, 0x0,
    0x0, 0x0,

    /* U+F068 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0x9c, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xa0,

    /* U+F06E "" */
    0x0, 0x0, 0x0, 0x2, 0x32, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xcf, 0xff, 0xff, 0xb4, 0x0,
    0x0, 0x0, 0x1c, 0xff, 0xa4, 0x24, 0xbf, 0xfb,
    0x10, 0x0, 0x2d, 0xff, 0x60, 0x16, 0x30, 0x8f,
    0xfd, 0x10, 0xd, 0xff, 0xa0, 0x1, 0xff, 0x70,
    0xcf, 0xfc, 0x9, 0xff, 0xf5, 0x12, 0x8f, 0xff,
    0x17, 0xff, 0xf8, 0xef, 0xff, 0x35, 0xff, 0xff,
    0xf2, 0x5f, 0xff, 0xc7, 0xff, 0xf6, 0x1f, 0xff,
    0xfe, 0x8, 0xff, 0xf5, 0xb, 0xff, 0xc0, 0x5e,
    0xfd, 0x30, 0xef, 0xfa, 0x0, 0xc, 0xff, 0x90,
    0x1, 0x0, 0xbf, 0xfa, 0x0, 0x0, 0x8, 0xff,
    0xd8, 0x68, 0xef, 0xf7, 0x0, 0x0, 0x0, 0x1,
    0x7c, 0xef, 0xec, 0x71, 0x0, 0x0,

    /* U+F070 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xd2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xf5, 0x0, 0x0, 0x23,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xf9, 0x5b,
    0xff, 0xff, 0xfa, 0x30, 0x0, 0x0, 0x0, 0x4e,
    0xff, 0xfa, 0x42, 0x5b, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x1c, 0xff, 0x41, 0x62, 0x9, 0xff, 0xd1,
    0x0, 0x1, 0x50, 0x9, 0xff, 0x9f, 0xf6, 0xd,
    0xff, 0xc0, 0x0, 0xbf, 0x90, 0x5, 0xff, 0xff,
    0xf0, 0x8f, 0xff, 0x70, 0xf, 0xff, 0xc1, 0x2,
    0xdf, 0xff, 0x26, 0xff, 0xfb, 0x0, 0x7f, 0xff,
    0x50, 0x0, 0xaf, 0xf6, 0x9f, 0xff, 0x40, 0x0,
    0xcf, 0xfc, 0x0, 0x0, 0x6f, 0xff, 0xff, 0x80,
    0x0, 0x1, 0xbf, 0xf9, 0x0, 0x0, 0x3e, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0x9f, 0xfd, 0x87, 0x30,
    0x1b, 0xfe, 0x40, 0x0, 0x0, 0x0, 0x28, 0xcf,
    0xfe, 0x30, 0x8, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xef, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xb4,

    /* U+F071 "" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xff, 0xff, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xff, 0x30, 0x5f, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xf3, 0x5,
    0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff,
    0x40, 0x6f, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x5f,
    0xff, 0xf5, 0x7, 0xff, 0xff, 0x30, 0x0, 0x0,
    0xe, 0xff, 0xff, 0xb8, 0xcf, 0xff, 0xfc, 0x0,
    0x0, 0x8, 0xff, 0xff, 0xf8, 0x1a, 0xff, 0xff,
    0xf6, 0x0, 0x1, 0xff, 0xff, 0xff, 0x20, 0x4f,
    0xff, 0xff, 0xe0, 0x0, 0xaf, 0xff, 0xff, 0xf8,
    0x1a, 0xff, 0xff, 0xff, 0x80, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x0, 0x7d,
    0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 0xed, 0x50,

    /* U+F074 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbd, 0x10, 0x45,
    0x54, 0x0, 0x0, 0x3, 0x5d, 0xfd, 0x1f, 0xff,
    0xf8, 0x0, 0x6, 0xff, 0xff, 0xfc, 0xff, 0xff,
    0xf7, 0x5, 0xff, 0xff, 0xff, 0x90, 0x0, 0xbf,
    0x74, 0xff, 0xc1, 0xcf, 0xa0, 0x0, 0x0, 0x63,
    0xff, 0xd1, 0x8, 0x90, 0x0, 0x0, 0x2, 0xef,
    0xe1, 0x0, 0x0, 0x0, 0x0, 0x2, 0xef, 0xe2,
    0x93, 0xb, 0xd1, 0x4, 0x44, 0xef, 0xf3, 0x8f,
    0xe5, 0xdf, 0xd1, 0xff, 0xff, 0xf3, 0x2, 0xef,
    0xff, 0xff, 0xcf, 0xff, 0xf4, 0x0, 0x3, 0xff,
    0xff, 0xfa, 0x1, 0x10, 0x0, 0x0, 0x0, 0x1c,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9a,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F077 "" */
    0x0, 0x0, 0x0, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xc0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0xbf, 0xfc, 0xff,
    0xc0, 0x0, 0x0, 0xbf, 0xf9, 0x7, 0xff, 0xc0,
    0x0, 0xbf, 0xf9, 0x0, 0x7, 0xff, 0xc1, 0x9f,
    0xf9, 0x0, 0x0, 0x7, 0xff, 0xb7, 0xf9, 0x0,
    0x0, 0x0, 0x7, 0xf9, 0x2, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x0,

    /* U+F078 "" */
    0x6, 0x0, 0x0, 0x0, 0x0, 0x6, 0x1a, 0xfc,
    0x0, 0x0, 0x0, 0xa, 0xfc, 0x7f, 0xfc, 0x0,
    0x0, 0xa, 0xff, 0x90, 0x7f, 0xfc, 0x0, 0xb,
    0xff, 0x90, 0x0, 0x7f, 0xfc, 0x1b, 0xff, 0x90,
    0x0, 0x0, 0x7f, 0xff, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x20,
    0x0, 0x0, 0x0,

    /* U+F079 "" */
    0x0, 0x2, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xfe, 0x10, 0x1, 0x11, 0x11,
    0x11, 0x0, 0x0, 0x4, 0xff, 0xfd, 0x16, 0xff,
    0xff, 0xff, 0xff, 0x0, 0x3, 0xff, 0xff, 0xfd,
    0x2a, 0xcc, 0xcc, 0xcf, 0xf0, 0x0, 0xbf, 0x9f,
    0xdb, 0xf6, 0x0, 0x0, 0x0, 0xdf, 0x0, 0x1,
    0x51, 0xfc, 0x5, 0x0, 0x0, 0x0, 0xd, 0xf0,
    0x0, 0x0, 0x1f, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0x0, 0x0, 0x1, 0xfc, 0x0, 0x0, 0x0,
    0x6, 0xd, 0xf0, 0x62, 0x0, 0x1f, 0xc0, 0x0,
    0x0, 0x7, 0xfc, 0xdf, 0x9f, 0xa0, 0x1, 0xff,
    0xdd, 0xdd, 0xdb, 0x2d, 0xff, 0xff, 0xe3, 0x0,
    0xe, 0xff, 0xff, 0xff, 0xf5, 0x1d, 0xff, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1d,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x12, 0x0, 0x0,

    /* U+F07B "" */
    0x3, 0x44, 0x44, 0x0, 0x0, 0x0, 0x0, 0xc,
    0xff, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xfc, 0x66, 0x66, 0x65, 0x1f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf9, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf9,

    /* U+F093 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xe4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x4,
    0xff, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x4, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x39, 0x9b,
    0xff, 0xfb, 0x99, 0x30, 0x0, 0x0, 0x0, 0x6f,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xf6,
    0x0, 0x0, 0x9, 0xbb, 0xb8, 0x4f, 0xff, 0x48,
    0xbb, 0xb9, 0xff, 0xff, 0xf3, 0x22, 0x23, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x5d, 0x2e,
    0xfb, 0xee, 0xee, 0xee, 0xee, 0xed, 0xed, 0xeb,

    /* U+F095 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xfd, 0x95,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xfe, 0x0,
    0x0, 0x6, 0xda, 0x0, 0xa, 0xff, 0xf3, 0x0,
    0x7, 0xef, 0xff, 0x84, 0xdf, 0xff, 0x50, 0x0,
    0xe, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x0, 0x0,
    0xa, 0xff, 0xff, 0xff, 0xfd, 0x30, 0x0, 0x0,
    0x7, 0xff, 0xff, 0xfe, 0x60, 0x0, 0x0, 0x0,
    0x2, 0xdc, 0xa8, 0x30, 0x0, 0x0, 0x0, 0x0,

    /* U+F0C4 "" */
    0x0, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1c,
    0xff, 0x80, 0x0, 0x0, 0x79, 0x40, 0xbf, 0xdf,
    0xf6, 0x0, 0x1d, 0xff, 0xe0, 0xff, 0x5, 0xf9,
    0x2, 0xef, 0xfe, 0x20, 0xcf, 0xce, 0xf9, 0x3e,
    0xff, 0xe2, 0x0, 0x2d, 0xff, 0xff, 0xff, 0xfe,
    0x20, 0x0, 0x0, 0x28, 0xff, 0xff, 0xe2, 0x0,
    0x0, 0x0, 0x2, 0xff, 0xff, 0x60, 0x0, 0x0,
    0x5, 0xae, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x7f,
    0xff, 0xfd, 0xaf, 0xff, 0x40, 0x0, 0xef, 0x48,
    0xf8, 0xa, 0xff, 0xf4, 0x0, 0xef, 0x16, 0xf8,
    0x0, 0xaf, 0xff, 0x50, 0x9f, 0xff, 0xf3, 0x0,
    0xa, 0xff, 0xe0, 0x8, 0xdc, 0x50, 0x0, 0x0,
    0x46, 0x20,

    /* U+F0C5 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xff, 0xff, 0xf5, 0xb4, 0x0, 0x0, 0x4,
    0xff, 0xff, 0xf5, 0xbf, 0x40, 0x35, 0x44, 0xff,
    0xff, 0xf5, 0x8c, 0xb0, 0xff, 0xd4, 0xff, 0xff,
    0xfb, 0x66, 0x60, 0xff, 0xd4, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0xff, 0xd4, 0xff, 0xff, 0xff, 0xff,
    0xf1, 0xff, 0xd4, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0xff, 0xd4, 0xff, 0xff, 0xff, 0xff, 0xf1, 0xff,
    0xd4, 0xff, 0xff, 0xff, 0xff, 0xf1, 0xff, 0xd4,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0xff, 0xd4, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0xff, 0xd2, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0xff, 0xf6, 0x1, 0x11, 0x11,
    0x11, 0x0, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x0,
    0x0, 0xbe, 0xee, 0xee, 0xee, 0xd2, 0x0, 0x0,

    /* U+F0C7 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xff, 0xff, 0xff, 0xff, 0x50, 0x0, 0xff, 0xdd,
    0xdd, 0xdd, 0xdf, 0xf5, 0x0, 0xfe, 0x0, 0x0,
    0x0, 0xa, 0xff, 0x50, 0xfe, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xf0, 0xfe, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xf1, 0xff, 0xcc, 0xcc, 0xcc, 0xcf, 0xff,
    0xf1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0xff, 0xff, 0xf7, 0x27, 0xff, 0xff, 0xf1, 0xff,
    0xff, 0xc0, 0x0, 0xbf, 0xff, 0xf1, 0xff, 0xff,
    0xc0, 0x0, 0xaf, 0xff, 0xf1, 0xff, 0xff, 0xf5,
    0x4, 0xff, 0xff, 0xf1, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x90,

    /* U+F0C9 "" */
    0x12, 0x22, 0x22, 0x22, 0x22, 0x22, 0x20, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xde, 0xee, 0xee, 0xee, 0xee,
    0xee, 0xe1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf1, 0x23, 0x33, 0x33, 0x33, 0x33, 0x33, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0x12, 0x22, 0x22, 0x22,
    0x22, 0x22, 0x10,

    /* U+F0E0 "" */
    0x3, 0x44, 0x44, 0x44, 0x44, 0x44, 0x43, 0xc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x53, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xe3, 0x5f, 0xa1, 0xaf,
    0xff, 0xff, 0xff, 0xa1, 0x9f, 0xff, 0xd3, 0x6f,
    0xff, 0xff, 0x62, 0xdf, 0xff, 0xff, 0xf7, 0x2d,
    0xfd, 0x26, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x12,
    0x1a, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf9, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf9,

    /* U+F0E7 "" */
    0x0, 0x12, 0x22, 0x10, 0x0, 0x0, 0xf, 0xff,
    0xff, 0x70, 0x0, 0x3, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0x5f, 0xff, 0xff, 0x0, 0x0, 0x7, 0xff,
    0xff, 0xa0, 0x0, 0x0, 0x9f, 0xff, 0xfa, 0x77,
    0x61, 0xb, 0xff, 0xff, 0xff, 0xff, 0x40, 0xdf,
    0xff, 0xff, 0xff, 0xc0, 0xf, 0xff, 0xff, 0xff,
    0xf3, 0x0, 0x35, 0x55, 0xef, 0xf9, 0x0, 0x0,
    0x0, 0x1f, 0xff, 0x10, 0x0, 0x0, 0x5, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x9f, 0xd0, 0x0, 0x0,
    0x0, 0xc, 0xf4, 0x0, 0x0, 0x0, 0x0, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x1c, 0x20, 0x0, 0x0,

    /* U+F0EA "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x13,
    0x3a, 0xfb, 0x33, 0x20, 0x0, 0x0, 0xff, 0xff,
    0x3e, 0xff, 0xf1, 0x0, 0x0, 0xff, 0xff, 0xcf,
    0xff, 0xf2, 0x0, 0x0, 0xff, 0xff, 0xc7, 0x77,
    0x71, 0x0, 0x0, 0xff, 0xfe, 0x2a, 0xaa, 0xa1,
    0x61, 0x0, 0xff, 0xfc, 0x6f, 0xff, 0xf2, 0xbc,
    0x10, 0xff, 0xfc, 0x6f, 0xff, 0xf2, 0xbf, 0xc0,
    0xff, 0xfc, 0x6f, 0xff, 0xf2, 0x12, 0x20, 0xff,
    0xfc, 0x6f, 0xff, 0xfc, 0x99, 0x90, 0xff, 0xfc,
    0x6f, 0xff, 0xff, 0xff, 0xf1, 0xff, 0xfc, 0x6f,
    0xff, 0xff, 0xff, 0xf1, 0xef, 0xfc, 0x6f, 0xff,
    0xff, 0xff, 0xf1, 0x12, 0x21, 0x6f, 0xff, 0xff,
    0xff, 0xf1, 0x0, 0x0, 0x6f, 0xff, 0xff, 0xff,
    0xf1, 0x0, 0x0, 0x4e, 0xff, 0xff, 0xff, 0xd0,

    /* U+F0F3 "" */
    0x0, 0x0, 0x0, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2a, 0xfc, 0x30, 0x0, 0x0, 0x0, 0x7, 0xff,
    0xff, 0xf9, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xff,
    0xff, 0x80, 0x0, 0x0, 0xef, 0xff, 0xff, 0xff,
    0xf0, 0x0, 0x1, 0xff, 0xff, 0xff, 0xff, 0xf3,
    0x0, 0x2, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0,
    0x4, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x8,
    0xff, 0xff, 0xff, 0xff, 0xfa, 0x0, 0xe, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x10, 0xbf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xd0, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xe0, 0x1, 0x11, 0x11, 0x11, 0x11,
    0x11, 0x0, 0x0, 0x0, 0x3f, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xd9, 0x0, 0x0, 0x0,

    /* U+F11C "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0xff, 0xdd, 0xfd, 0xef, 0xdf, 0xdd, 0xfd,
    0xdf, 0xdf, 0xe0, 0x1d, 0x3, 0xb0, 0xd1, 0xd,
    0x10, 0xfe, 0xff, 0xbc, 0xfb, 0xcf, 0xbf, 0xcb,
    0xfc, 0xbf, 0xef, 0xff, 0x73, 0xc7, 0x3d, 0x38,
    0xb3, 0x9f, 0xfe, 0xff, 0xf4, 0xa, 0x40, 0xb0,
    0x6a, 0x6, 0xff, 0xef, 0xff, 0xfe, 0xff, 0xef,
    0xef, 0xfe, 0xff, 0xfe, 0xfe, 0x2, 0xe0, 0x0,
    0x0, 0x0, 0xe2, 0x1f, 0xef, 0xe0, 0x1d, 0x0,
    0x0, 0x0, 0xd, 0x10, 0xfe, 0xff, 0xee, 0xfe,
    0xee, 0xee, 0xee, 0xfe, 0xef, 0xd9, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,

    /* U+F124 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xcf,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6d, 0xff,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x7, 0xef, 0xff,
    0xff, 0x90, 0x0, 0x0, 0x1, 0x8f, 0xff, 0xff,
    0xff, 0xf2, 0x0, 0x0, 0x2a, 0xff, 0xff, 0xff,
    0xff, 0xfa, 0x0, 0x2, 0xbf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x30, 0x0, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xb0, 0x0, 0xd, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf4, 0x0, 0x0, 0x14, 0x55, 0x55,
    0x9f, 0xff, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1c, 0xb1, 0x0, 0x0, 0x0,

    /* U+F15B "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xff,
    0xff, 0x97, 0x80, 0x0, 0xff, 0xff, 0xff, 0x98,
    0xf8, 0x0, 0xff, 0xff, 0xff, 0x98, 0xff, 0x80,
    0xff, 0xff, 0xff, 0x95, 0xbb, 0xb1, 0xff, 0xff,
    0xff, 0xd5, 0x55, 0x51, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xe2,

    /* U+F1EB "" */
    0x0, 0x0, 0x0, 0x0, 0x22, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0x9d, 0xff, 0xff, 0xfc,
    0x82, 0x0, 0x0, 0x0, 0x4d, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0x20, 0x0, 0xaf, 0xff, 0xd9,
    0x54, 0x34, 0x6a, 0xef, 0xff, 0x70, 0xcf, 0xfc,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x6e, 0xff, 0x87,
    0xf8, 0x0, 0x0, 0x46, 0x76, 0x30, 0x0, 0x1b,
    0xf4, 0x1, 0x0, 0x19, 0xff, 0xff, 0xff, 0xe7,
    0x0, 0x1, 0x0, 0x0, 0x4e, 0xff, 0xff, 0xef,
    0xff, 0xfd, 0x20, 0x0, 0x0, 0x7, 0xff, 0x82,
    0x0, 0x3, 0xaf, 0xf3, 0x0, 0x0, 0x0, 0x6,
    0x20, 0x0, 0x0, 0x0, 0x44, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8c, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xe8, 0x0, 0x0, 0x0, 0x0,

    /* U+F240 "" */
    0x1, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
    0x0, 0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x70, 0xff, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xfd, 0x1f, 0xe0, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0xf, 0xfb, 0xfe, 0x3f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0xaf, 0xcf,
    0xe3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x1,
    0xfc, 0xfe, 0x3f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0xbf, 0xcf, 0xe0, 0x33, 0x33, 0x33, 0x33,
    0x33, 0x33, 0xf, 0xfb, 0xff, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xfd, 0x9, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F241 "" */
    0x1, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
    0x0, 0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x70, 0xff, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xfd, 0x1f, 0xe0, 0x44, 0x44,
    0x44, 0x44, 0x40, 0x0, 0xf, 0xfb, 0xfe, 0x3f,
    0xff, 0xff, 0xff, 0xff, 0x30, 0x0, 0xaf, 0xcf,
    0xe3, 0xff, 0xff, 0xff, 0xff, 0xf3, 0x0, 0x1,
    0xfc, 0xfe, 0x3f, 0xff, 0xff, 0xff, 0xff, 0x30,
    0x0, 0xbf, 0xcf, 0xe0, 0x33, 0x33, 0x33, 0x33,
    0x30, 0x0, 0xf, 0xfb, 0xff, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xfd, 0x9, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F242 "" */
    0x1, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
    0x0, 0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x70, 0xff, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xfd, 0x1f, 0xe0, 0x44, 0x44,
    0x44, 0x10, 0x0, 0x0, 0xf, 0xfb, 0xfe, 0x3f,
    0xff, 0xff, 0xf6, 0x0, 0x0, 0x0, 0xaf, 0xcf,
    0xe3, 0xff, 0xff, 0xff, 0x60, 0x0, 0x0, 0x1,
    0xfc, 0xfe, 0x3f, 0xff, 0xff, 0xf6, 0x0, 0x0,
    0x0, 0xbf, 0xcf, 0xe0, 0x33, 0x33, 0x33, 0x10,
    0x0, 0x0, 0xf, 0xfb, 0xff, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xfd, 0x9, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F243 "" */
    0x1, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
    0x0, 0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x70, 0xff, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xfd, 0x1f, 0xe0, 0x44, 0x42,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xfb, 0xfe, 0x3f,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xcf,
    0xe3, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xfc, 0xfe, 0x3f, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xcf, 0xe0, 0x33, 0x32, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xfb, 0xff, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xfd, 0x9, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F244 "" */
    0x1, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
    0x0, 0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x70, 0xff, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xfd, 0x1f, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xfb, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xcf,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xfc, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xcf, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xfb, 0xff, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xfd, 0x9, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F287 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x22, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0x6f, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xee,
    0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xc0, 0x1b, 0xb1, 0x0, 0x0, 0x0, 0x18, 0x92,
    0x0, 0xd4, 0x0, 0x0, 0x0, 0x1, 0x20, 0xb,
    0xff, 0xe1, 0x7d, 0x11, 0x11, 0x11, 0x11, 0x6f,
    0x70, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x8a, 0xff, 0xc0, 0x0, 0x4e, 0x0,
    0x0, 0x0, 0x5e, 0x50, 0x5, 0x60, 0x0, 0x0,
    0xb6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xe1, 0x8f, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x22, 0x10, 0x0, 0x0,

    /* U+F293 "" */
    0x0, 0x0, 0x0, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x5c, 0xff, 0xfd, 0x70, 0x0, 0x0, 0x7f, 0xff,
    0x8f, 0xff, 0x90, 0x0, 0x2f, 0xff, 0xf3, 0x6f,
    0xff, 0x30, 0x8, 0xff, 0xff, 0x31, 0x7f, 0xf9,
    0x0, 0xcf, 0x96, 0xf3, 0xb2, 0x9f, 0xd0, 0xf,
    0xfe, 0x26, 0x39, 0x1c, 0xff, 0x0, 0xff, 0xfe,
    0x20, 0xa, 0xff, 0xf0, 0xf, 0xff, 0xfb, 0x5,
    0xff, 0xff, 0x10, 0xff, 0xfd, 0x10, 0x9, 0xff,
    0xf0, 0xf, 0xfd, 0x17, 0x39, 0x1a, 0xff, 0x0,
    0xcf, 0x97, 0xf3, 0xa2, 0x8f, 0xd0, 0x8, 0xff,
    0xff, 0x30, 0x8f, 0xf9, 0x0, 0x1f, 0xff, 0xf4,
    0x7f, 0xff, 0x20, 0x0, 0x5f, 0xff, 0xaf, 0xff,
    0x70, 0x0, 0x0, 0x29, 0xcd, 0xda, 0x40, 0x0,

    /* U+F2ED "" */
    0x0, 0x0, 0xbf, 0xff, 0xd1, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0xcd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xc1, 0x2, 0x22, 0x22,
    0x22, 0x22, 0x22, 0x0, 0x1f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x30, 0x1f, 0xfd, 0xff, 0xdf, 0xfd,
    0xff, 0x30, 0x1f, 0xf4, 0xcf, 0x1f, 0xe2, 0xff,
    0x30, 0x1f, 0xf4, 0xcf, 0x1f, 0xe2, 0xff, 0x30,
    0x1f, 0xf4, 0xcf, 0x1f, 0xe2, 0xff, 0x30, 0x1f,
    0xf4, 0xcf, 0x1f, 0xe2, 0xff, 0x30, 0x1f, 0xf4,
    0xcf, 0x1f, 0xe2, 0xff, 0x30, 0x1f, 0xf4, 0xcf,
    0x1f, 0xe2, 0xff, 0x30, 0x1f, 0xf5, 0xdf, 0x3f,
    0xe3, 0xff, 0x30, 0xf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x20, 0x8, 0xde, 0xee, 0xee, 0xee, 0xd9,
    0x0,

    /* U+F304 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xfe, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xe2,
    0x0, 0x0, 0x0, 0x0, 0x2, 0x4f, 0xff, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0x64, 0xff, 0xfd,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xf6, 0x4f, 0xe3,
    0x0, 0x0, 0x0, 0x6f, 0xff, 0xff, 0x64, 0x30,
    0x0, 0x0, 0x6, 0xff, 0xff, 0xff, 0xe1, 0x0,
    0x0, 0x0, 0x6f, 0xff, 0xff, 0xfe, 0x30, 0x0,
    0x0, 0x6, 0xff, 0xff, 0xff, 0xe3, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0xff, 0xfe, 0x30, 0x0, 0x0,
    0x5, 0xff, 0xff, 0xff, 0xe3, 0x0, 0x0, 0x0,
    0xb, 0xff, 0xff, 0xfe, 0x30, 0x0, 0x0, 0x0,
    0xd, 0xff, 0xff, 0xe3, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xff, 0xfe, 0x30, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xca, 0x93, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F55A "" */
    0x0, 0x0, 0x2, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x31, 0x0, 0x0, 0x7, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf5, 0x0, 0x8, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xb0, 0x8, 0xff, 0xff,
    0xf8, 0x5f, 0xfc, 0x3e, 0xff, 0xfc, 0x8, 0xff,
    0xff, 0xff, 0x20, 0x5b, 0x0, 0xaf, 0xff, 0xc8,
    0xff, 0xff, 0xff, 0xfe, 0x20, 0x0, 0x8f, 0xff,
    0xfc, 0xef, 0xff, 0xff, 0xff, 0xfa, 0x0, 0x3f,
    0xff, 0xff, 0xc4, 0xff, 0xff, 0xff, 0xfc, 0x0,
    0x10, 0x5f, 0xff, 0xfc, 0x4, 0xff, 0xff, 0xff,
    0x10, 0x8e, 0x20, 0x9f, 0xff, 0xc0, 0x4, 0xff,
    0xff, 0xfb, 0x9f, 0xfe, 0x7f, 0xff, 0xfc, 0x0,
    0x4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xa0, 0x0, 0x4, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xd2,

    /* U+F7C2 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xff, 0xff, 0xff, 0x70, 0x0, 0xbe, 0xdf, 0xdf,
    0xdf, 0xf0, 0xb, 0xf4, 0x1d, 0xf, 0xc, 0xf1,
    0xbf, 0xf4, 0x1d, 0xf, 0xc, 0xf1, 0xff, 0xf8,
    0x6e, 0x5f, 0x5d, 0xf1, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x4e, 0xff, 0xff, 0xff, 0xfe, 0x50,

    /* U+F8A2 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xf1,
    0x0, 0x4, 0x40, 0x0, 0x0, 0x0, 0x2f, 0xf1,
    0x0, 0x5f, 0xb0, 0x0, 0x0, 0x0, 0x4f, 0xf1,
    0x6, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x4f, 0xf1,
    0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x1c, 0xff, 0xd6, 0x66, 0x66, 0x66, 0x66, 0x40,
    0x0, 0xcf, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 65, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 59, .box_w = 3, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 17, .adv_w = 90, .box_w = 5, .box_h = 4, .ofs_x = 0, .ofs_y = 8},
    {.bitmap_index = 27, .adv_w = 158, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 82, .adv_w = 139, .box_w = 9, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 150, .adv_w = 192, .box_w = 12, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 216, .adv_w = 173, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 294, .adv_w = 48, .box_w = 3, .box_h = 4, .ofs_x = 0, .ofs_y = 8},
    {.bitmap_index = 300, .adv_w = 86, .box_w = 6, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 342, .adv_w = 86, .box_w = 5, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 377, .adv_w = 106, .box_w = 7, .box_h = 6, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 398, .adv_w = 139, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 430, .adv_w = 61, .box_w = 4, .box_h = 6, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 442, .adv_w = 117, .box_w = 7, .box_h = 3, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 453, .adv_w = 58, .box_w = 3, .box_h = 3, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 458, .adv_w = 97, .box_w = 6, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 491, .adv_w = 139, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 541, .adv_w = 139, .box_w = 5, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 569, .adv_w = 139, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 613, .adv_w = 139, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 657, .adv_w = 139, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 707, .adv_w = 139, .box_w = 7, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 746, .adv_w = 139, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 796, .adv_w = 139, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 840, .adv_w = 139, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 890, .adv_w = 139, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 934, .adv_w = 64, .box_w = 2, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 942, .adv_w = 66, .box_w = 4, .box_h = 11, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 964, .adv_w = 139, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 1000, .adv_w = 139, .box_w = 8, .box_h = 5, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 1020, .adv_w = 139, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 1056, .adv_w = 108, .box_w = 8, .box_h = 11, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 1100, .adv_w = 235, .box_w = 15, .box_h = 15, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 1213, .adv_w = 164, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1274, .adv_w = 158, .box_w = 9, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1324, .adv_w = 159, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1379, .adv_w = 174, .box_w = 10, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1434, .adv_w = 144, .box_w = 8, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1478, .adv_w = 137, .box_w = 8, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1522, .adv_w = 170, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1577, .adv_w = 181, .box_w = 10, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1632, .adv_w = 66, .box_w = 2, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1643, .adv_w = 115, .box_w = 7, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1682, .adv_w = 168, .box_w = 10, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1737, .adv_w = 137, .box_w = 8, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1781, .adv_w = 209, .box_w = 11, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1842, .adv_w = 179, .box_w = 9, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1892, .adv_w = 185, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1953, .adv_w = 146, .box_w = 8, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1997, .adv_w = 185, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2075, .adv_w = 157, .box_w = 9, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2125, .adv_w = 139, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2175, .adv_w = 139, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2225, .adv_w = 178, .box_w = 9, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2275, .adv_w = 162, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2336, .adv_w = 235, .box_w = 15, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2419, .adv_w = 162, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2474, .adv_w = 153, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2529, .adv_w = 139, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2579, .adv_w = 85, .box_w = 5, .box_h = 16, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 2619, .adv_w = 97, .box_w = 6, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2652, .adv_w = 85, .box_w = 5, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2692, .adv_w = 118, .box_w = 7, .box_h = 6, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 2713, .adv_w = 105, .box_w = 7, .box_h = 3, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 2724, .adv_w = 76, .box_w = 4, .box_h = 3, .ofs_x = 0, .ofs_y = 9},
    {.bitmap_index = 2730, .adv_w = 132, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2762, .adv_w = 147, .box_w = 8, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2806, .adv_w = 119, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2838, .adv_w = 147, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2888, .adv_w = 132, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2920, .adv_w = 86, .box_w = 6, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2959, .adv_w = 147, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 3009, .adv_w = 141, .box_w = 7, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3048, .adv_w = 60, .box_w = 3, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3065, .adv_w = 60, .box_w = 5, .box_h = 14, .ofs_x = -2, .ofs_y = -3},
    {.bitmap_index = 3100, .adv_w = 130, .box_w = 8, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3144, .adv_w = 60, .box_w = 2, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3155, .adv_w = 206, .box_w = 11, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3199, .adv_w = 141, .box_w = 7, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3227, .adv_w = 143, .box_w = 9, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3263, .adv_w = 147, .box_w = 8, .box_h = 11, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 3307, .adv_w = 147, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 3357, .adv_w = 94, .box_w = 5, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3377, .adv_w = 110, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3405, .adv_w = 93, .box_w = 6, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3438, .adv_w = 141, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3470, .adv_w = 128, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3502, .adv_w = 190, .box_w = 12, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3550, .adv_w = 123, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3582, .adv_w = 129, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 3626, .adv_w = 114, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3654, .adv_w = 92, .box_w = 6, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3702, .adv_w = 48, .box_w = 3, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3723, .adv_w = 92, .box_w = 6, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3771, .adv_w = 139, .box_w = 8, .box_h = 5, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 3791, .adv_w = 240, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3919, .adv_w = 240, .box_w = 15, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4009, .adv_w = 240, .box_w = 15, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4114, .adv_w = 240, .box_w = 15, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4204, .adv_w = 165, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4265, .adv_w = 240, .box_w = 15, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4385, .adv_w = 240, .box_w = 15, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4505, .adv_w = 270, .box_w = 17, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4624, .adv_w = 240, .box_w = 15, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4744, .adv_w = 270, .box_w = 17, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4846, .adv_w = 240, .box_w = 15, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4966, .adv_w = 120, .box_w = 8, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5014, .adv_w = 180, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5086, .adv_w = 270, .box_w = 17, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5214, .adv_w = 240, .box_w = 15, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5304, .adv_w = 165, .box_w = 11, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5392, .adv_w = 210, .box_w = 11, .box_h = 15, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 5475, .adv_w = 210, .box_w = 14, .box_h = 17, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 5594, .adv_w = 210, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5692, .adv_w = 210, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5790, .adv_w = 210, .box_w = 11, .box_h = 15, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 5873, .adv_w = 210, .box_w = 15, .box_h = 14, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 5978, .adv_w = 150, .box_w = 9, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 6041, .adv_w = 150, .box_w = 9, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 6104, .adv_w = 210, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 6202, .adv_w = 210, .box_w = 14, .box_h = 4, .ofs_x = 0, .ofs_y = 4},
    {.bitmap_index = 6230, .adv_w = 270, .box_w = 17, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6332, .adv_w = 300, .box_w = 19, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6484, .adv_w = 270, .box_w = 19, .box_h = 16, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 6636, .adv_w = 240, .box_w = 15, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6749, .adv_w = 210, .box_w = 13, .box_h = 9, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 6808, .adv_w = 210, .box_w = 13, .box_h = 9, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 6867, .adv_w = 300, .box_w = 19, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 6991, .adv_w = 240, .box_w = 15, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7081, .adv_w = 240, .box_w = 15, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7201, .adv_w = 240, .box_w = 16, .box_h = 16, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 7329, .adv_w = 210, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 7427, .adv_w = 210, .box_w = 14, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7539, .adv_w = 210, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 7637, .adv_w = 210, .box_w = 14, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 7728, .adv_w = 240, .box_w = 15, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7818, .adv_w = 150, .box_w = 11, .box_h = 16, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 7906, .adv_w = 210, .box_w = 14, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 8018, .adv_w = 210, .box_w = 14, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 8130, .adv_w = 270, .box_w = 17, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8232, .adv_w = 240, .box_w = 17, .box_h = 16, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 8368, .adv_w = 180, .box_w = 12, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 8464, .adv_w = 300, .box_w = 19, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 8597, .adv_w = 300, .box_w = 19, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8702, .adv_w = 300, .box_w = 19, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8807, .adv_w = 300, .box_w = 19, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8912, .adv_w = 300, .box_w = 19, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9017, .adv_w = 300, .box_w = 19, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9122, .adv_w = 300, .box_w = 19, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 9246, .adv_w = 210, .box_w = 13, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 9350, .adv_w = 210, .box_w = 14, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 9455, .adv_w = 240, .box_w = 16, .box_h = 16, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 9583, .adv_w = 300, .box_w = 19, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9697, .adv_w = 180, .box_w = 12, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 9793, .adv_w = 241, .box_w = 16, .box_h = 10, .ofs_x = 0, .ofs_y = 1}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_1[] = {
    0x0, 0x7, 0xa, 0xb, 0xc, 0x10, 0x12, 0x14,
    0x18, 0x1b, 0x20, 0x25, 0x26, 0x27, 0x3d, 0x42,
    0x47, 0x4a, 0x4b, 0x4c, 0x50, 0x51, 0x52, 0x53,
    0x66, 0x67, 0x6d, 0x6f, 0x70, 0x73, 0x76, 0x77,
    0x78, 0x7a, 0x92, 0x94, 0xc3, 0xc4, 0xc6, 0xc8,
    0xdf, 0xe6, 0xe9, 0xf2, 0x11b, 0x123, 0x15a, 0x1ea,
    0x23f, 0x240, 0x241, 0x242, 0x243, 0x286, 0x292, 0x2ec,
    0x303, 0x559, 0x7c1, 0x8a1
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 61441, .range_length = 2210, .glyph_id_start = 96,
        .unicode_list = unicode_list_1, .glyph_id_ofs_list = NULL, .list_length = 60, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/


/*Map glyph_ids to kern left classes*/
static const uint8_t kern_left_class_mapping[] =
{
    0, 0, 0, 1, 0, 0, 0, 0,
    1, 2, 3, 0, 4, 0, 4, 0,
    5, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 4,
    6, 7, 8, 9, 10, 7, 11, 12,
    13, 14, 14, 15, 16, 17, 14, 14,
    7, 18, 0, 19, 20, 21, 15, 5,
    22, 23, 24, 25, 2, 8, 3, 0,
    0, 0, 26, 27, 28, 29, 30, 31,
    32, 26, 0, 33, 34, 29, 26, 26,
    27, 27, 0, 35, 36, 37, 32, 38,
    38, 39, 38, 40, 2, 0, 3, 4,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0
};

/*Map glyph_ids to kern right classes*/
static const uint8_t kern_right_class_mapping[] =
{
    0, 1, 0, 2, 0, 0, 0, 0,
    2, 0, 3, 0, 4, 5, 4, 5,
    6, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 4, 0, 0,
    7, 8, 6, 9, 8, 9, 9, 9,
    8, 9, 9, 10, 9, 9, 9, 9,
    8, 9, 8, 9, 11, 12, 13, 14,
    15, 16, 17, 18, 0, 14, 3, 0,
    5, 0, 19, 20, 21, 21, 21, 22,
    21, 20, 0, 23, 20, 20, 24, 24,
    21, 0, 21, 24, 25, 26, 27, 28,
    28, 29, 30, 31, 0, 0, 3, 4,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0
};

/*Kern values between classes*/
static const int8_t kern_class_values[] =
{
    0, 0, 0, 0, 0, 0, 4, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 5, 0, 5, 5,
    2, 0, 3, 0, 0, 17, 0, 0,
    4, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 6, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -13, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -10, 4, 5, -11,
    -34, -22, 6, -9, 0, -29, -2, 5,
    0, 0, 0, 0, 0, 0, -18, 0,
    -18, -6, 0, -12, -14, -2, -12, -11,
    -13, -11, -13, 0, 0, 0, -8, -24,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -6, 0, 0, -10, -9,
    0, 0, 0, -9, 0, -7, 0, -9,
    -5, -8, -13, -5, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -10, -28, 0, -15, 6, 0, -16,
    -9, 0, 0, 0, -20, -4, -22, -17,
    0, -27, 5, 0, 0, -3, 0, 0,
    0, 0, 0, 0, -10, 0, -10, 0,
    0, -3, 0, 0, 0, -4, 0, 0,
    0, 3, 0, -8, 0, -11, -4, 0,
    -14, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    2, 0, -7, 6, 0, 8, -4, 0,
    0, 0, 1, 0, -1, 0, 0, -1,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -6, 0, -6, 0, 0, 0,
    0, 0, 2, 0, 3, -2, 0, 3,
    0, 0, 0, -3, 0, 0, -3, 0,
    -2, 0, -3, -3, 0, 0, -2, -2,
    -2, -5, -2, -5, 0, -2, 6, 0,
    1, -31, -14, 10, -1, 0, -33, 0,
    5, 0, 0, 0, 0, 0, 0, -10,
    0, -7, -2, 0, -4, 0, -3, 0,
    -6, -9, -6, -6, 0, 0, 0, 0,
    4, 0, 3, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 1, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -7, -4,
    0, 0, 0, -7, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -10, 0, 0, -26, 4, 0, -1,
    -14, -4, 0, -4, 0, -6, 0, 0,
    0, 0, 0, -7, 0, -8, -10, 0,
    -4, -4, -10, -10, -16, -8, -16, 0,
    -12, -25, 0, -22, 6, 0, -17, -12,
    0, 4, -2, -31, -10, -35, -26, 0,
    -43, 0, -2, 0, -5, -5, 0, 0,
    0, -7, -6, -23, 0, -23, 0, -2,
    2, 0, 3, -35, -19, 4, 0, 0,
    -38, 0, 0, 0, -1, -1, -6, 0,
    -7, -8, 0, -7, 0, 0, 0, 0,
    0, 0, 3, 0, 3, 0, 0, -3,
    0, -2, 9, 0, -1, -2, 0, 0,
    2, -3, -3, -7, -4, 0, -12, 0,
    0, 0, -3, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 2, 0, 0, 0, 5,
    0, 0, -4, 0, 0, -6, 1, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -7, 7, 0, -19,
    -27, -20, 9, -7, 0, -34, 0, 5,
    0, 5, 5, 0, 0, 0, -29, 0,
    -27, -11, 0, -22, -27, -8, -21, -25,
    -26, -25, -21, -2, 4, 0, -6, -18,
    -17, 0, -5, 0, -18, 0, 5, 0,
    0, 0, 0, 0, 0, -18, 0, -15,
    -4, 0, -10, -11, 0, -8, -6, -8,
    -6, -8, 0, 0, 5, -22, 2, 0,
    3, -8, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -6, 0, -8, 0,
    0, -4, -4, -7, -7, -15, 0, -15,
    0, -7, 3, 5, -17, -33, -27, 2,
    -13, 0, -33, -6, 0, 0, 0, 0,
    0, 0, 0, -27, 0, -26, -12, 0,
    -20, -23, -8, -19, -18, -17, -18, -18,
    0, 0, 2, -11, 5, 0, 2, -7,
    0, 0, -2, 0, 0, 0, 0, 0,
    0, 0, -1, 0, -4, 0, 0, 0,
    0, 0, 0, -8, 0, -8, 0, 0,
    -10, 0, 0, 0, 0, -7, 0, 0,
    0, 0, -21, 0, -18, -16, -2, -24,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -3, 0, -3, 0, 0, -12,
    0, -2, -8, 0, -10, 0, 0, 0,
    0, -27, 0, -18, -15, -8, -26, 0,
    -2, 0, 0, -2, 0, 0, 0, -1,
    0, -5, -6, -5, -6, 0, 1, 0,
    4, 6, 0, -3, 0, 0, 0, 0,
    -19, 0, -12, -8, 4, -18, 0, 0,
    0, -1, 3, 0, 0, 0, 6, 0,
    0, 2, 0, 3, 0, 0, 3, 0,
    0, 0, 3, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -1, 0, 4, -1,
    0, -6, 0, 0, 0, 0, -17, 0,
    -16, -12, -3, -22, 0, 0, 0, 0,
    0, 0, 0, 3, 0, 0, 0, -2,
    0, 0, 0, 11, 0, -2, -18, 0,
    11, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -6, 0, -6, 0,
    0, 0, 0, 3, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -6,
    0, 0, 0, 0, -22, 0, -12, -10,
    0, -20, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 3, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 10, 0,
    0, 0, 0, 0, 0, 0, 0, -5,
    0, 0, -6, 6, 0, -12, 0, 0,
    0, 0, -22, 0, -15, -13, 0, -20,
    0, -7, 0, -6, 0, 0, 0, -3,
    0, -2, 0, 0, 0, 0, 0, 6,
    0, 2, -26, -11, -7, 0, 0, -28,
    0, 0, 0, -10, 0, -12, -18, 0,
    -10, 0, -8, 0, 0, 0, -1, 7,
    0, 0, 0, 0, 0, 0, -7, 0,
    0, 0, 0, -7, 0, 0, 0, 0,
    -24, 0, -17, -12, -1, -25, 0, 0,
    0, 0, 0, 0, 0, 2, 0, 0,
    -2, 0, -2, 3, 0, -2, 2, 0,
    7, 0, -6, 0, 0, 0, 0, -17,
    0, -11, 0, 0, -16, 0, 0, 0,
    -2, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -22,
    -10, -6, 0, 0, -19, 0, -25, 0,
    -11, -6, -15, -18, 0, -5, 0, -5,
    0, 0, 0, -2, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -5, 2, 0,
    -10, 0, 0, 0, 0, -26, 0, -13,
    -8, 0, -17, 0, -4, 0, -6, 0,
    0, 0, 0, 3, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 2, 0, -8,
    0, 0, 0, 0, -24, 0, -13, -7,
    0, -18, 0, -4, 0, -5, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0
};


/*Collect the kern class' data in one place*/
static const lv_font_fmt_txt_kern_classes_t kern_classes =
{
    .class_pair_values   = kern_class_values,
    .left_class_mapping  = kern_left_class_mapping,
    .right_class_mapping = kern_right_class_mapping,
    .left_class_cnt      = 40,
    .right_class_cnt     = 31,
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LVGL_VERSION_MAJOR == 8
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
#endif

#if LVGL_VERSION_MAJOR >= 8
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_classes,
    .kern_scale = 16,
    .cmap_num = 2,
    .bpp = 4,
    .kern_classes = 1,
    .bitmap_format = 0,
#if LVGL_VERSION_MAJOR == 8
    .cache = &cache
#endif
};



/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LVGL_VERSION_MAJOR >= 8
const lv_font_t lv_font_HarmonyOS_Sans_SC_Medium_15 = {
#else
lv_font_t lv_font_HarmonyOS_Sans_SC_Medium_15 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 15,          /*The maximum line height required by the font*/
    .base_line = 2,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -1,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc,          /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
#if LV_VERSION_CHECK(8, 2, 0) || LVGL_VERSION_MAJOR >= 9
    .fallback = NULL,
#endif
    .user_data = NULL,
};



#endif /*#if LV_FONT_HARMONYOS_SANS_SC_MEDIUM_15*/

