/*
* Copyright 2016 The Alatsi Project Authors (https://github.com/sorkintype/alatsi)
* This Font Software is licensed under the SIL Open Font License, Version 1.1.
* And is also available with a FAQ at: http://scripts.sil.org/OFL
*/
/*******************************************************************************
 * Size: 15 px
 * Bpp: 4
 * Opts: undefined
 ******************************************************************************/

#ifdef LV_LVGL_H_INCLUDE_SIMPLE
#include "lvgl.h"
#else
#include "lvgl.h"
#endif

#ifndef LV_FONT_MONTSERRATMEDIUM_15
#define LV_FONT_MONTSERRATMEDIUM_15 1
#endif

#if LV_FONT_MONTSERRATMEDIUM_15

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0xdd, 0xcc, 0xbc, 0xbb, 0xaa, 0xaa, 0x99, 0x11,
    0xab, 0xcc,

    /* U+0022 "\"" */
    0xf, 0x35, 0xe0, 0xf2, 0x5e, 0xf, 0x24, 0xd0,
    0xf1, 0x4d, 0x0, 0x0, 0x0,

    /* U+0023 "#" */
    0x0, 0xa, 0x60, 0xc, 0x50, 0x0, 0x0, 0xc4,
    0x0, 0xe2, 0x0, 0x1f, 0xff, 0xff, 0xff, 0xff,
    0x20, 0x12, 0xf1, 0x14, 0xe1, 0x10, 0x0, 0x3d,
    0x0, 0x4c, 0x0, 0x0, 0x5, 0xb0, 0x6, 0xa0,
    0x0, 0x9f, 0xff, 0xff, 0xff, 0xfa, 0x1, 0x2a,
    0x92, 0x2b, 0x72, 0x10, 0x0, 0xb6, 0x0, 0xc4,
    0x0, 0x0, 0xc, 0x40, 0xe, 0x20, 0x0,

    /* U+0024 "$" */
    0x0, 0x0, 0x51, 0x0, 0x0, 0x0, 0xb, 0x30,
    0x0, 0x0, 0x0, 0xb3, 0x0, 0x0, 0x8, 0xdf,
    0xfc, 0x70, 0xb, 0xe7, 0xd7, 0x8d, 0x1, 0xf6,
    0xb, 0x30, 0x0, 0xf, 0xa0, 0xb3, 0x0, 0x0,
    0x5f, 0xff, 0x82, 0x0, 0x0, 0x5, 0xde, 0xfa,
    0x0, 0x0, 0xb, 0x34, 0xf8, 0x2, 0x0, 0xb3,
    0xd, 0xa3, 0xfa, 0x5c, 0x7a, 0xf4, 0x4, 0xbe,
    0xff, 0xc4, 0x0, 0x0, 0xb, 0x30, 0x0, 0x0,
    0x0, 0x51, 0x0, 0x0,

    /* U+0025 "%" */
    0x7, 0xdd, 0x70, 0x0, 0x1e, 0x20, 0x3, 0xc0,
    0xc, 0x30, 0xb, 0x60, 0x0, 0x69, 0x0, 0x96,
    0x7, 0xb0, 0x0, 0x3, 0xd1, 0x1d, 0x33, 0xd1,
    0x0, 0x0, 0x6, 0xdd, 0x60, 0xd4, 0x8d, 0xb1,
    0x0, 0x0, 0x0, 0x98, 0x6a, 0x6, 0xb0, 0x0,
    0x0, 0x5c, 0xb, 0x30, 0xf, 0x0, 0x0, 0x1e,
    0x20, 0xb3, 0x0, 0xe0, 0x0, 0xc, 0x60, 0x7,
    0x80, 0x4c, 0x0, 0x7, 0xa0, 0x0, 0xa, 0xcb,
    0x20,

    /* U+0026 "&" */
    0x0, 0x2c, 0xfe, 0x80, 0x0, 0x0, 0xd, 0xa1,
    0x3f, 0x40, 0x0, 0x0, 0xf6, 0x0, 0xe4, 0x0,
    0x0, 0x9, 0xe5, 0xcb, 0x0, 0x0, 0x0, 0x2f,
    0xf8, 0x0, 0x0, 0x0, 0x5f, 0x79, 0xe3, 0x8,
    0x40, 0x1f, 0x50, 0x8, 0xf5, 0xf4, 0x4, 0xf2,
    0x0, 0x7, 0xfd, 0x0, 0x1e, 0xd5, 0x36, 0xce,
    0xf7, 0x0, 0x2a, 0xef, 0xd8, 0x15, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0027 "'" */
    0xf, 0x30, 0xf2, 0xf, 0x20, 0xf1, 0x0, 0x0,

    /* U+0028 "(" */
    0x1, 0xf5, 0x8, 0xd0, 0xe, 0x80, 0x2f, 0x30,
    0x5f, 0x0, 0x7e, 0x0, 0x8d, 0x0, 0x8d, 0x0,
    0x7e, 0x0, 0x5f, 0x0, 0x2f, 0x30, 0xe, 0x80,
    0x8, 0xd0, 0x1, 0xf5,

    /* U+0029 ")" */
    0x4f, 0x20, 0xc, 0x90, 0x7, 0xe0, 0x2, 0xf3,
    0x0, 0xf6, 0x0, 0xd8, 0x0, 0xc9, 0x0, 0xc9,
    0x0, 0xd8, 0x0, 0xf6, 0x2, 0xf3, 0x7, 0xe0,
    0xc, 0x90, 0x4f, 0x20,

    /* U+002A "*" */
    0x0, 0x77, 0x0, 0x79, 0x88, 0x97, 0x7, 0xff,
    0x80, 0x4d, 0xdd, 0xd4, 0x32, 0x77, 0x23, 0x0,
    0x44, 0x0,

    /* U+002B "+" */
    0x0, 0x28, 0x0, 0x0, 0x4, 0xf0, 0x0, 0x0,
    0x4f, 0x0, 0xf, 0xff, 0xff, 0xfb, 0x33, 0x6f,
    0x33, 0x20, 0x4, 0xf0, 0x0, 0x0, 0x4f, 0x0,
    0x0,

    /* U+002C "," */
    0x1, 0x2, 0xf9, 0x1e, 0x90, 0xd3, 0x1e, 0x0,

    /* U+002D "-" */
    0x0, 0x0, 0x2, 0xff, 0xfe, 0x3, 0x33, 0x20,

    /* U+002E "." */
    0x0, 0x2, 0xf8, 0x2e, 0x70,

    /* U+002F "/" */
    0x0, 0x0, 0xa, 0xa0, 0x0, 0x0, 0xf4, 0x0,
    0x0, 0x5e, 0x0, 0x0, 0xb, 0x90, 0x0, 0x1,
    0xf3, 0x0, 0x0, 0x6e, 0x0, 0x0, 0xc, 0x80,
    0x0, 0x2, 0xf2, 0x0, 0x0, 0x7d, 0x0, 0x0,
    0xd, 0x70, 0x0, 0x2, 0xf2, 0x0, 0x0, 0x8c,
    0x0, 0x0, 0xe, 0x60, 0x0, 0x3, 0xf1, 0x0,
    0x0,

    /* U+0030 "0" */
    0x0, 0x3b, 0xee, 0xb3, 0x0, 0x3, 0xfd, 0x66,
    0xdf, 0x30, 0xc, 0xd0, 0x0, 0xd, 0xc0, 0x1f,
    0x60, 0x0, 0x6, 0xf1, 0x3f, 0x40, 0x0, 0x4,
    0xf3, 0x3f, 0x40, 0x0, 0x4, 0xf3, 0x1f, 0x60,
    0x0, 0x6, 0xf1, 0xc, 0xd0, 0x0, 0xd, 0xc0,
    0x3, 0xfd, 0x66, 0xdf, 0x30, 0x0, 0x3b, 0xff,
    0xb3, 0x0,

    /* U+0031 "1" */
    0xef, 0xff, 0x44, 0xaf, 0x0, 0x8f, 0x0, 0x8f,
    0x0, 0x8f, 0x0, 0x8f, 0x0, 0x8f, 0x0, 0x8f,
    0x0, 0x8f, 0x0, 0x8f,

    /* U+0032 "2" */
    0x6, 0xcf, 0xfc, 0x50, 0x8, 0xf9, 0x56, 0xcf,
    0x40, 0x2, 0x0, 0x0, 0xe9, 0x0, 0x0, 0x0,
    0xf, 0x80, 0x0, 0x0, 0x8, 0xf2, 0x0, 0x0,
    0x8, 0xf4, 0x0, 0x0, 0xa, 0xe3, 0x0, 0x0,
    0xb, 0xe2, 0x0, 0x0, 0x1c, 0xf6, 0x44, 0x44,
    0x7, 0xff, 0xff, 0xff, 0xf1,

    /* U+0033 "3" */
    0x7f, 0xff, 0xff, 0xf7, 0x24, 0x44, 0x4c, 0xe2,
    0x0, 0x0, 0x7f, 0x30, 0x0, 0x5, 0xf5, 0x0,
    0x0, 0xf, 0xfa, 0x40, 0x0, 0x4, 0x59, 0xf6,
    0x0, 0x0, 0x0, 0xbd, 0x10, 0x0, 0x0, 0xcd,
    0xbe, 0x85, 0x6b, 0xf6, 0x18, 0xdf, 0xfc, 0x50,

    /* U+0034 "4" */
    0x0, 0x0, 0x8, 0xf2, 0x0, 0x0, 0x0, 0x5f,
    0x40, 0x0, 0x0, 0x4, 0xf6, 0x0, 0x0, 0x0,
    0x2e, 0x80, 0x1, 0x0, 0x1, 0xea, 0x0, 0x8d,
    0x0, 0xc, 0xc1, 0x0, 0x9d, 0x0, 0x6f, 0xff,
    0xff, 0xff, 0xfd, 0x13, 0x33, 0x33, 0xbe, 0x33,
    0x0, 0x0, 0x0, 0x9d, 0x0, 0x0, 0x0, 0x0,
    0x9d, 0x0,

    /* U+0035 "5" */
    0x7, 0xff, 0xff, 0xf8, 0x0, 0x9d, 0x44, 0x44,
    0x20, 0xb, 0xb0, 0x0, 0x0, 0x0, 0xc9, 0x0,
    0x0, 0x0, 0xe, 0xff, 0xfe, 0x80, 0x0, 0x34,
    0x45, 0x8f, 0xa0, 0x0, 0x0, 0x0, 0x8f, 0x0,
    0x10, 0x0, 0x9, 0xf0, 0x8e, 0x96, 0x59, 0xf9,
    0x0, 0x7c, 0xff, 0xd7, 0x0,

    /* U+0036 "6" */
    0x0, 0x18, 0xdf, 0xeb, 0x10, 0x1e, 0xe7, 0x55,
    0x80, 0xb, 0xe1, 0x0, 0x0, 0x1, 0xf7, 0x0,
    0x0, 0x0, 0x3f, 0x6a, 0xef, 0xd6, 0x4, 0xff,
    0x94, 0x39, 0xf6, 0x2f, 0xb0, 0x0, 0xb, 0xc0,
    0xdb, 0x0, 0x0, 0xbc, 0x4, 0xf9, 0x54, 0x9f,
    0x50, 0x4, 0xbf, 0xfc, 0x50,

    /* U+0037 "7" */
    0x8f, 0xff, 0xff, 0xff, 0x68, 0xe4, 0x44, 0x4a,
    0xf2, 0x8e, 0x0, 0x0, 0xda, 0x0, 0x0, 0x0,
    0x5f, 0x30, 0x0, 0x0, 0xd, 0xb0, 0x0, 0x0,
    0x5, 0xf4, 0x0, 0x0, 0x0, 0xcc, 0x0, 0x0,
    0x0, 0x4f, 0x40, 0x0, 0x0, 0xc, 0xd0, 0x0,
    0x0, 0x3, 0xf5, 0x0, 0x0,

    /* U+0038 "8" */
    0x0, 0x8d, 0xff, 0xc5, 0x0, 0xaf, 0x73, 0x4a,
    0xf5, 0xe, 0x90, 0x0, 0xe, 0x90, 0xbd, 0x30,
    0x6, 0xf5, 0x1, 0xdf, 0xff, 0xf9, 0x0, 0xce,
    0x63, 0x38, 0xf7, 0x4f, 0x40, 0x0, 0x9, 0xe4,
    0xf4, 0x0, 0x0, 0xae, 0xd, 0xe7, 0x44, 0x9f,
    0x70, 0x18, 0xdf, 0xfc, 0x60,

    /* U+0039 "9" */
    0x2, 0xae, 0xfc, 0x60, 0x1, 0xec, 0x43, 0x7f,
    0x80, 0x7f, 0x0, 0x0, 0x7f, 0x18, 0xf0, 0x0,
    0x6, 0xf6, 0x3f, 0x91, 0x4, 0xef, 0x80, 0x5e,
    0xff, 0xe6, 0xf7, 0x0, 0x2, 0x20, 0x3f, 0x50,
    0x0, 0x0, 0xb, 0xe0, 0x6, 0x65, 0x7c, 0xf4,
    0x0, 0x9e, 0xfe, 0xa2, 0x0,

    /* U+003A ":" */
    0x2e, 0x72, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0x82, 0xe7,

    /* U+003B ";" */
    0x2e, 0x72, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2e, 0x71, 0xe9, 0xc, 0x40, 0xe0, 0x2,
    0x0,

    /* U+003C "<" */
    0x0, 0x0, 0x1, 0x40, 0x0, 0x3a, 0xf9, 0x17,
    0xde, 0x93, 0xf, 0xd5, 0x0, 0x0, 0x9f, 0xc6,
    0x10, 0x0, 0x6, 0xcf, 0xa3, 0x0, 0x0, 0x39,
    0xa0, 0x0, 0x0, 0x0,

    /* U+003D "=" */
    0xff, 0xff, 0xff, 0xb3, 0x33, 0x33, 0x32, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff,
    0xff, 0xb2, 0x22, 0x22, 0x21,

    /* U+003E ">" */
    0x50, 0x0, 0x0, 0xe, 0xe8, 0x20, 0x0, 0x4,
    0xaf, 0xb5, 0x0, 0x0, 0x17, 0xfb, 0x0, 0x28,
    0xee, 0x66, 0xcf, 0xa4, 0x0, 0xd7, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+003F "?" */
    0x6, 0xcf, 0xfc, 0x50, 0x8f, 0x84, 0x5b, 0xf4,
    0x2, 0x0, 0x0, 0xf8, 0x0, 0x0, 0x3, 0xf4,
    0x0, 0x0, 0x4f, 0x80, 0x0, 0x3, 0xf7, 0x0,
    0x0, 0x5, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xc0, 0x0, 0x0, 0x9, 0xe0, 0x0,

    /* U+0040 "@" */
    0x0, 0x0, 0x4a, 0xdf, 0xed, 0x82, 0x0, 0x0,
    0x1, 0xbc, 0x61, 0x0, 0x38, 0xe7, 0x0, 0x0,
    0xd8, 0x6, 0xdf, 0xd5, 0xc9, 0xc8, 0x0, 0x8b,
    0x6, 0xf7, 0x35, 0xdf, 0x81, 0xe2, 0xe, 0x30,
    0xe8, 0x0, 0x2, 0xf8, 0x8, 0x82, 0xe0, 0x2f,
    0x30, 0x0, 0xc, 0x80, 0x5b, 0x4d, 0x2, 0xf3,
    0x0, 0x0, 0xc8, 0x4, 0xc2, 0xe0, 0xe, 0x80,
    0x0, 0x2f, 0x80, 0x6a, 0xe, 0x30, 0x6f, 0x73,
    0x4d, 0xec, 0x3d, 0x40, 0x8b, 0x0, 0x6d, 0xfd,
    0x53, 0xdf, 0x80, 0x0, 0xd8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xcc, 0x62, 0x1, 0x47,
    0x0, 0x0, 0x0, 0x0, 0x5a, 0xdf, 0xec, 0x60,
    0x0, 0x0,

    /* U+0041 "A" */
    0x0, 0x0, 0x8, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xce, 0x0, 0x0, 0x0, 0x0, 0x7e, 0x1e,
    0x60, 0x0, 0x0, 0x0, 0xe6, 0x7, 0xd0, 0x0,
    0x0, 0x6, 0xe0, 0x0, 0xf5, 0x0, 0x0, 0xd,
    0x70, 0x0, 0x8d, 0x0, 0x0, 0x5f, 0xff, 0xff,
    0xff, 0x50, 0x0, 0xd9, 0x33, 0x33, 0x3a, 0xc0,
    0x4, 0xf2, 0x0, 0x0, 0x3, 0xf4, 0xc, 0xb0,
    0x0, 0x0, 0x0, 0xcc,

    /* U+0042 "B" */
    0x6f, 0xff, 0xff, 0xd9, 0x10, 0x6f, 0x43, 0x33,
    0x6e, 0xc0, 0x6f, 0x10, 0x0, 0x8, 0xf0, 0x6f,
    0x10, 0x0, 0x3d, 0xb0, 0x6f, 0xff, 0xff, 0xfe,
    0x30, 0x6f, 0x43, 0x33, 0x5c, 0xf2, 0x6f, 0x10,
    0x0, 0x0, 0xf8, 0x6f, 0x10, 0x0, 0x0, 0xf8,
    0x6f, 0x43, 0x33, 0x5b, 0xf3, 0x6f, 0xff, 0xff,
    0xeb, 0x40,

    /* U+0043 "C" */
    0x0, 0x5, 0xbe, 0xfd, 0x91, 0x0, 0xb, 0xfb,
    0x65, 0x7d, 0xe1, 0x8, 0xf5, 0x0, 0x0, 0x3,
    0x0, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x3f, 0x40,
    0x0, 0x0, 0x0, 0x3, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0xf, 0x90, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0x50, 0x0, 0x0, 0x30, 0x0, 0xbf, 0xb6, 0x57,
    0xde, 0x10, 0x0, 0x5b, 0xef, 0xd9, 0x10,

    /* U+0044 "D" */
    0x6f, 0xff, 0xff, 0xd8, 0x10, 0x6, 0xf5, 0x44,
    0x58, 0xee, 0x30, 0x6f, 0x10, 0x0, 0x1, 0xce,
    0x6, 0xf1, 0x0, 0x0, 0x2, 0xf6, 0x6f, 0x10,
    0x0, 0x0, 0xe, 0x96, 0xf1, 0x0, 0x0, 0x0,
    0xe9, 0x6f, 0x10, 0x0, 0x0, 0x2f, 0x66, 0xf1,
    0x0, 0x0, 0x1c, 0xe0, 0x6f, 0x54, 0x45, 0x8e,
    0xe3, 0x6, 0xff, 0xff, 0xfd, 0x81, 0x0,

    /* U+0045 "E" */
    0x6f, 0xff, 0xff, 0xff, 0x6, 0xf5, 0x44, 0x44,
    0x40, 0x6f, 0x10, 0x0, 0x0, 0x6, 0xf1, 0x0,
    0x0, 0x0, 0x6f, 0xff, 0xff, 0xf5, 0x6, 0xf4,
    0x33, 0x33, 0x10, 0x6f, 0x10, 0x0, 0x0, 0x6,
    0xf1, 0x0, 0x0, 0x0, 0x6f, 0x54, 0x44, 0x44,
    0x6, 0xff, 0xff, 0xff, 0xf3,

    /* U+0046 "F" */
    0x6f, 0xff, 0xff, 0xff, 0x6f, 0x54, 0x44, 0x44,
    0x6f, 0x10, 0x0, 0x0, 0x6f, 0x10, 0x0, 0x0,
    0x6f, 0x10, 0x0, 0x0, 0x6f, 0xff, 0xff, 0xf5,
    0x6f, 0x43, 0x33, 0x31, 0x6f, 0x10, 0x0, 0x0,
    0x6f, 0x10, 0x0, 0x0, 0x6f, 0x10, 0x0, 0x0,

    /* U+0047 "G" */
    0x0, 0x5, 0xbe, 0xfe, 0x92, 0x0, 0xb, 0xfb,
    0x65, 0x7d, 0xf1, 0x8, 0xf5, 0x0, 0x0, 0x3,
    0x0, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x3f, 0x40,
    0x0, 0x0, 0x1, 0x3, 0xf4, 0x0, 0x0, 0x2,
    0xf4, 0xf, 0x90, 0x0, 0x0, 0x2f, 0x40, 0x8f,
    0x50, 0x0, 0x2, 0xf4, 0x0, 0xbf, 0xb6, 0x57,
    0xcf, 0x30, 0x0, 0x5b, 0xef, 0xe9, 0x30,

    /* U+0048 "H" */
    0x6f, 0x10, 0x0, 0x0, 0xe9, 0x6f, 0x10, 0x0,
    0x0, 0xe9, 0x6f, 0x10, 0x0, 0x0, 0xe9, 0x6f,
    0x21, 0x11, 0x11, 0xe9, 0x6f, 0xff, 0xff, 0xff,
    0xf9, 0x6f, 0x44, 0x44, 0x44, 0xe9, 0x6f, 0x10,
    0x0, 0x0, 0xe9, 0x6f, 0x10, 0x0, 0x0, 0xe9,
    0x6f, 0x10, 0x0, 0x0, 0xe9, 0x6f, 0x10, 0x0,
    0x0, 0xe9,

    /* U+0049 "I" */
    0x6f, 0x16, 0xf1, 0x6f, 0x16, 0xf1, 0x6f, 0x16,
    0xf1, 0x6f, 0x16, 0xf1, 0x6f, 0x16, 0xf1,

    /* U+004A "J" */
    0x1, 0xff, 0xff, 0xf3, 0x0, 0x44, 0x47, 0xf3,
    0x0, 0x0, 0x4, 0xf3, 0x0, 0x0, 0x4, 0xf3,
    0x0, 0x0, 0x4, 0xf3, 0x0, 0x0, 0x4, 0xf3,
    0x0, 0x0, 0x4, 0xf3, 0x2, 0x0, 0x6, 0xf1,
    0xe, 0xb5, 0x6e, 0xc0, 0x3, 0xbf, 0xea, 0x10,

    /* U+004B "K" */
    0x6f, 0x10, 0x0, 0xb, 0xd1, 0x6f, 0x10, 0x0,
    0xbd, 0x10, 0x6f, 0x10, 0xb, 0xd1, 0x0, 0x6f,
    0x10, 0xcd, 0x10, 0x0, 0x6f, 0x2c, 0xf3, 0x0,
    0x0, 0x6f, 0xdd, 0xed, 0x0, 0x0, 0x6f, 0xd1,
    0x2e, 0xb0, 0x0, 0x6f, 0x20, 0x3, 0xf9, 0x0,
    0x6f, 0x10, 0x0, 0x5f, 0x70, 0x6f, 0x10, 0x0,
    0x6, 0xf5,

    /* U+004C "L" */
    0x6f, 0x10, 0x0, 0x0, 0x6f, 0x10, 0x0, 0x0,
    0x6f, 0x10, 0x0, 0x0, 0x6f, 0x10, 0x0, 0x0,
    0x6f, 0x10, 0x0, 0x0, 0x6f, 0x10, 0x0, 0x0,
    0x6f, 0x10, 0x0, 0x0, 0x6f, 0x10, 0x0, 0x0,
    0x6f, 0x54, 0x44, 0x43, 0x6f, 0xff, 0xff, 0xfc,

    /* U+004D "M" */
    0x6f, 0x20, 0x0, 0x0, 0x0, 0xcb, 0x6f, 0xb0,
    0x0, 0x0, 0x6, 0xfb, 0x6f, 0xf5, 0x0, 0x0,
    0x1f, 0xfb, 0x6f, 0x8e, 0x10, 0x0, 0xac, 0xbb,
    0x6f, 0xd, 0x90, 0x4, 0xf3, 0xbb, 0x6f, 0x4,
    0xf3, 0xd, 0x80, 0xbb, 0x6f, 0x0, 0xad, 0x8e,
    0x0, 0xbb, 0x6f, 0x0, 0x1e, 0xf4, 0x0, 0xbb,
    0x6f, 0x0, 0x5, 0x90, 0x0, 0xbb, 0x6f, 0x0,
    0x0, 0x0, 0x0, 0xbb,

    /* U+004E "N" */
    0x6f, 0x40, 0x0, 0x0, 0xe9, 0x6f, 0xe2, 0x0,
    0x0, 0xe9, 0x6f, 0xed, 0x10, 0x0, 0xe9, 0x6f,
    0x4f, 0xb0, 0x0, 0xe9, 0x6f, 0x14, 0xf9, 0x0,
    0xe9, 0x6f, 0x10, 0x6f, 0x70, 0xe9, 0x6f, 0x10,
    0x9, 0xf4, 0xe9, 0x6f, 0x10, 0x0, 0xbf, 0xf9,
    0x6f, 0x10, 0x0, 0xd, 0xf9, 0x6f, 0x10, 0x0,
    0x2, 0xe9,

    /* U+004F "O" */
    0x0, 0x5, 0xbe, 0xfd, 0x92, 0x0, 0x0, 0xaf,
    0xb6, 0x58, 0xdf, 0x50, 0x8, 0xf5, 0x0, 0x0,
    0xa, 0xf2, 0xf, 0x90, 0x0, 0x0, 0x0, 0xf9,
    0x3f, 0x40, 0x0, 0x0, 0x0, 0xbc, 0x3f, 0x40,
    0x0, 0x0, 0x0, 0xbc, 0xf, 0x90, 0x0, 0x0,
    0x0, 0xf9, 0x8, 0xf5, 0x0, 0x0, 0xa, 0xf2,
    0x0, 0xbf, 0xb6, 0x57, 0xdf, 0x50, 0x0, 0x5,
    0xbe, 0xfd, 0x92, 0x0,

    /* U+0050 "P" */
    0x6f, 0xff, 0xfe, 0xb3, 0x0, 0x6f, 0x54, 0x46,
    0xcf, 0x50, 0x6f, 0x10, 0x0, 0xc, 0xd0, 0x6f,
    0x10, 0x0, 0x8, 0xf0, 0x6f, 0x10, 0x0, 0xa,
    0xe0, 0x6f, 0x10, 0x2, 0x8f, 0x80, 0x6f, 0xff,
    0xff, 0xe7, 0x0, 0x6f, 0x43, 0x32, 0x0, 0x0,
    0x6f, 0x10, 0x0, 0x0, 0x0, 0x6f, 0x10, 0x0,
    0x0, 0x0,

    /* U+0051 "Q" */
    0x0, 0x5, 0xbe, 0xfd, 0x92, 0x0, 0x0, 0xa,
    0xfb, 0x65, 0x8e, 0xf5, 0x0, 0x8, 0xf5, 0x0,
    0x0, 0xb, 0xf2, 0x0, 0xf9, 0x0, 0x0, 0x0,
    0xf, 0x90, 0x3f, 0x40, 0x0, 0x0, 0x0, 0xbc,
    0x3, 0xf4, 0x0, 0x0, 0x0, 0xb, 0xd0, 0xf,
    0x90, 0x0, 0x0, 0x0, 0xe9, 0x0, 0x9f, 0x40,
    0x0, 0x0, 0xaf, 0x30, 0x0, 0xbf, 0xa5, 0x47,
    0xdf, 0x50, 0x0, 0x0, 0x6c, 0xff, 0xfa, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0x71, 0x2b, 0x20,
    0x0, 0x0, 0x0, 0x6e, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0x10, 0x0,

    /* U+0052 "R" */
    0x6f, 0xff, 0xfe, 0xb3, 0x0, 0x6f, 0x54, 0x46,
    0xcf, 0x50, 0x6f, 0x10, 0x0, 0xc, 0xd0, 0x6f,
    0x10, 0x0, 0x8, 0xf0, 0x6f, 0x10, 0x0, 0xa,
    0xe0, 0x6f, 0x10, 0x2, 0x7f, 0x70, 0x6f, 0xff,
    0xff, 0xf7, 0x0, 0x6f, 0x43, 0x37, 0xf3, 0x0,
    0x6f, 0x10, 0x0, 0x9e, 0x10, 0x6f, 0x10, 0x0,
    0xc, 0xc0,

    /* U+0053 "S" */
    0x0, 0x8d, 0xfe, 0xc7, 0x0, 0xbf, 0x74, 0x58,
    0xd0, 0x1f, 0x70, 0x0, 0x0, 0x0, 0xfa, 0x0,
    0x0, 0x0, 0x5, 0xee, 0xa6, 0x20, 0x0, 0x0,
    0x48, 0xcf, 0xb1, 0x0, 0x0, 0x0, 0x3f, 0x80,
    0x20, 0x0, 0x0, 0xda, 0x3f, 0xa6, 0x45, 0xbf,
    0x40, 0x4a, 0xef, 0xeb, 0x40,

    /* U+0054 "T" */
    0xff, 0xff, 0xff, 0xff, 0xc4, 0x44, 0x8f, 0x64,
    0x43, 0x0, 0x5, 0xf2, 0x0, 0x0, 0x0, 0x5f,
    0x20, 0x0, 0x0, 0x5, 0xf2, 0x0, 0x0, 0x0,
    0x5f, 0x20, 0x0, 0x0, 0x5, 0xf2, 0x0, 0x0,
    0x0, 0x5f, 0x20, 0x0, 0x0, 0x5, 0xf2, 0x0,
    0x0, 0x0, 0x5f, 0x20, 0x0,

    /* U+0055 "U" */
    0x8f, 0x0, 0x0, 0x1, 0xf5, 0x8f, 0x0, 0x0,
    0x1, 0xf5, 0x8f, 0x0, 0x0, 0x1, 0xf5, 0x8f,
    0x0, 0x0, 0x1, 0xf5, 0x8f, 0x0, 0x0, 0x1,
    0xf5, 0x8f, 0x0, 0x0, 0x1, 0xf5, 0x7f, 0x10,
    0x0, 0x3, 0xf4, 0x3f, 0x80, 0x0, 0xa, 0xf1,
    0xa, 0xfa, 0x66, 0xbf, 0x70, 0x0, 0x7d, 0xff,
    0xc5, 0x0,

    /* U+0056 "V" */
    0xc, 0xd0, 0x0, 0x0, 0x1, 0xf7, 0x5, 0xf4,
    0x0, 0x0, 0x8, 0xf0, 0x0, 0xdc, 0x0, 0x0,
    0xe, 0x80, 0x0, 0x6f, 0x30, 0x0, 0x6f, 0x10,
    0x0, 0xe, 0xa0, 0x0, 0xe9, 0x0, 0x0, 0x7,
    0xf2, 0x5, 0xf2, 0x0, 0x0, 0x1, 0xf9, 0xd,
    0xb0, 0x0, 0x0, 0x0, 0x8f, 0x5f, 0x30, 0x0,
    0x0, 0x0, 0x1f, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xf5, 0x0, 0x0,

    /* U+0057 "W" */
    0x5f, 0x30, 0x0, 0x5, 0xf5, 0x0, 0x0, 0x3f,
    0x30, 0xf8, 0x0, 0x0, 0xbf, 0xb0, 0x0, 0x8,
    0xe0, 0xa, 0xe0, 0x0, 0x1f, 0xaf, 0x10, 0x0,
    0xe8, 0x0, 0x4f, 0x30, 0x7, 0xe1, 0xf6, 0x0,
    0x4f, 0x20, 0x0, 0xe9, 0x0, 0xd9, 0xa, 0xc0,
    0x9, 0xd0, 0x0, 0x9, 0xe0, 0x3f, 0x30, 0x4f,
    0x20, 0xe7, 0x0, 0x0, 0x3f, 0x48, 0xd0, 0x0,
    0xe7, 0x5f, 0x20, 0x0, 0x0, 0xea, 0xe7, 0x0,
    0x9, 0xda, 0xc0, 0x0, 0x0, 0x8, 0xff, 0x20,
    0x0, 0x3f, 0xf6, 0x0, 0x0, 0x0, 0x3f, 0xc0,
    0x0, 0x0, 0xdf, 0x10, 0x0,

    /* U+0058 "X" */
    0x3f, 0x80, 0x0, 0x6, 0xf4, 0x6, 0xf4, 0x0,
    0x2f, 0x70, 0x0, 0xae, 0x10, 0xdb, 0x0, 0x0,
    0xd, 0xca, 0xd1, 0x0, 0x0, 0x2, 0xff, 0x30,
    0x0, 0x0, 0x5, 0xff, 0x70, 0x0, 0x0, 0x2f,
    0x87, 0xf3, 0x0, 0x0, 0xdc, 0x0, 0xbe, 0x10,
    0xa, 0xe1, 0x0, 0x1e, 0xb0, 0x6f, 0x40, 0x0,
    0x3, 0xf8,

    /* U+0059 "Y" */
    0xb, 0xd0, 0x0, 0x0, 0x1f, 0x70, 0x2f, 0x80,
    0x0, 0xa, 0xc0, 0x0, 0x7f, 0x20, 0x5, 0xf3,
    0x0, 0x0, 0xcc, 0x0, 0xe8, 0x0, 0x0, 0x3,
    0xf6, 0x9d, 0x0, 0x0, 0x0, 0x8, 0xff, 0x40,
    0x0, 0x0, 0x0, 0xf, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0xe9, 0x0, 0x0, 0x0, 0x0, 0xe, 0x90,
    0x0, 0x0, 0x0, 0x0, 0xe9, 0x0, 0x0,

    /* U+005A "Z" */
    0x4f, 0xff, 0xff, 0xff, 0xf3, 0x14, 0x44, 0x44,
    0x5f, 0xc0, 0x0, 0x0, 0x0, 0xcd, 0x10, 0x0,
    0x0, 0xa, 0xe2, 0x0, 0x0, 0x0, 0x8f, 0x40,
    0x0, 0x0, 0x5, 0xf6, 0x0, 0x0, 0x0, 0x3f,
    0x80, 0x0, 0x0, 0x2, 0xea, 0x0, 0x0, 0x0,
    0x1d, 0xe5, 0x44, 0x44, 0x42, 0x5f, 0xff, 0xff,
    0xff, 0xf6,

    /* U+005B "[" */
    0x6f, 0xfb, 0x6f, 0x32, 0x6f, 0x0, 0x6f, 0x0,
    0x6f, 0x0, 0x6f, 0x0, 0x6f, 0x0, 0x6f, 0x0,
    0x6f, 0x0, 0x6f, 0x0, 0x6f, 0x0, 0x6f, 0x0,
    0x6f, 0x32, 0x6f, 0xfb,

    /* U+005C "\\" */
    0x6e, 0x0, 0x0, 0x1, 0xf4, 0x0, 0x0, 0xa,
    0x90, 0x0, 0x0, 0x5f, 0x0, 0x0, 0x0, 0xf5,
    0x0, 0x0, 0xa, 0xa0, 0x0, 0x0, 0x4f, 0x0,
    0x0, 0x0, 0xe6, 0x0, 0x0, 0x9, 0xb0, 0x0,
    0x0, 0x3f, 0x10, 0x0, 0x0, 0xd7, 0x0, 0x0,
    0x8, 0xc0, 0x0, 0x0, 0x2f, 0x20, 0x0, 0x0,
    0xc8,

    /* U+005D "]" */
    0xbf, 0xf6, 0x23, 0xf6, 0x0, 0xf6, 0x0, 0xf6,
    0x0, 0xf6, 0x0, 0xf6, 0x0, 0xf6, 0x0, 0xf6,
    0x0, 0xf6, 0x0, 0xf6, 0x0, 0xf6, 0x0, 0xf6,
    0x23, 0xf6, 0xbf, 0xf6,

    /* U+005E "^" */
    0x0, 0x7f, 0x30, 0x0, 0xd, 0xaa, 0x0, 0x5,
    0xc1, 0xf1, 0x0, 0xc5, 0x9, 0x80, 0x3e, 0x0,
    0x2e, 0xa, 0x70, 0x0, 0xb6,

    /* U+005F "_" */
    0xee, 0xee, 0xee, 0xe7,

    /* U+0060 "`" */
    0x9, 0xe2, 0x0, 0x6, 0xe3,

    /* U+0061 "a" */
    0x3, 0xbe, 0xfc, 0x50, 0xa, 0x85, 0x4a, 0xf3,
    0x0, 0x0, 0x0, 0xd8, 0x3, 0xbd, 0xee, 0xfa,
    0xe, 0xa2, 0x11, 0xca, 0x3f, 0x30, 0x0, 0xda,
    0xe, 0x91, 0x19, 0xfa, 0x3, 0xcf, 0xe9, 0xba,

    /* U+0062 "b" */
    0xad, 0x0, 0x0, 0x0, 0xa, 0xd0, 0x0, 0x0,
    0x0, 0xad, 0x0, 0x0, 0x0, 0xa, 0xd5, 0xdf,
    0xe8, 0x0, 0xaf, 0xe7, 0x47, 0xfb, 0xa, 0xf3,
    0x0, 0x4, 0xf4, 0xad, 0x0, 0x0, 0xe, 0x8a,
    0xd0, 0x0, 0x0, 0xe8, 0xaf, 0x30, 0x0, 0x4f,
    0x4a, 0xfe, 0x74, 0x7f, 0xb0, 0xac, 0x5d, 0xfe,
    0x80, 0x0,

    /* U+0063 "c" */
    0x0, 0x5c, 0xfe, 0xa1, 0x0, 0x7f, 0x94, 0x6d,
    0xc0, 0x1f, 0x80, 0x0, 0x11, 0x5, 0xf2, 0x0,
    0x0, 0x0, 0x5f, 0x20, 0x0, 0x0, 0x1, 0xf8,
    0x0, 0x1, 0x10, 0x7, 0xf9, 0x46, 0xdc, 0x0,
    0x5, 0xcf, 0xea, 0x10,

    /* U+0064 "d" */
    0x0, 0x0, 0x0, 0x9, 0xd0, 0x0, 0x0, 0x0,
    0x9d, 0x0, 0x0, 0x0, 0x9, 0xd0, 0x6, 0xdf,
    0xe7, 0x9d, 0x8, 0xf9, 0x46, 0xdf, 0xd1, 0xf8,
    0x0, 0x1, 0xed, 0x5f, 0x20, 0x0, 0xa, 0xd5,
    0xf2, 0x0, 0x0, 0x9d, 0x1f, 0x70, 0x0, 0xe,
    0xd0, 0x8f, 0x83, 0x4c, 0xfd, 0x0, 0x6d, 0xfe,
    0x88, 0xd0,

    /* U+0065 "e" */
    0x0, 0x6d, 0xfe, 0x90, 0x0, 0x8e, 0x74, 0x5d,
    0xc0, 0x1f, 0x40, 0x0, 0x1f, 0x55, 0xfe, 0xee,
    0xee, 0xf8, 0x5f, 0x41, 0x11, 0x11, 0x1, 0xf9,
    0x0, 0x0, 0x10, 0x7, 0xfa, 0x55, 0xbc, 0x0,
    0x5, 0xcf, 0xfb, 0x30,

    /* U+0066 "f" */
    0x0, 0x7e, 0xf8, 0x3, 0xf7, 0x33, 0x6, 0xf0,
    0x0, 0xcf, 0xff, 0xf4, 0x28, 0xf3, 0x30, 0x6,
    0xf0, 0x0, 0x6, 0xf0, 0x0, 0x6, 0xf0, 0x0,
    0x6, 0xf0, 0x0, 0x6, 0xf0, 0x0, 0x6, 0xf0,
    0x0,

    /* U+0067 "g" */
    0x0, 0x6c, 0xfe, 0x86, 0xf0, 0x8f, 0x95, 0x6d,
    0xff, 0x1f, 0x80, 0x0, 0xd, 0xf5, 0xf2, 0x0,
    0x0, 0x8f, 0x5f, 0x20, 0x0, 0x8, 0xf1, 0xf8,
    0x0, 0x0, 0xdf, 0x8, 0xf9, 0x45, 0xcf, 0xf0,
    0x6, 0xdf, 0xe8, 0x8e, 0x0, 0x0, 0x0, 0xb,
    0xc0, 0xac, 0x64, 0x5a, 0xf4, 0x2, 0x9d, 0xff,
    0xc4, 0x0,

    /* U+0068 "h" */
    0xad, 0x0, 0x0, 0x0, 0xad, 0x0, 0x0, 0x0,
    0xad, 0x0, 0x0, 0x0, 0xad, 0x6d, 0xfd, 0x70,
    0xaf, 0xe7, 0x5a, 0xf6, 0xaf, 0x20, 0x0, 0xcc,
    0xae, 0x0, 0x0, 0x8e, 0xad, 0x0, 0x0, 0x8e,
    0xad, 0x0, 0x0, 0x8e, 0xad, 0x0, 0x0, 0x8e,
    0xad, 0x0, 0x0, 0x8e,

    /* U+0069 "i" */
    0xad, 0x9, 0xb0, 0x0, 0xa, 0xd0, 0xad, 0xa,
    0xd0, 0xad, 0xa, 0xd0, 0xad, 0xa, 0xd0, 0xad,
    0x0,

    /* U+006A "j" */
    0x0, 0x9, 0xe0, 0x0, 0x8, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xe0, 0x0, 0x9, 0xe0, 0x0,
    0x9, 0xe0, 0x0, 0x9, 0xe0, 0x0, 0x9, 0xe0,
    0x0, 0x9, 0xe0, 0x0, 0x9, 0xe0, 0x0, 0x9,
    0xe0, 0x0, 0x9, 0xd0, 0x5, 0x4e, 0xa0, 0x2e,
    0xfb, 0x10,

    /* U+006B "k" */
    0xad, 0x0, 0x0, 0x0, 0xa, 0xd0, 0x0, 0x0,
    0x0, 0xad, 0x0, 0x0, 0x0, 0xa, 0xd0, 0x0,
    0x6f, 0x60, 0xad, 0x0, 0x7f, 0x50, 0xa, 0xd0,
    0x9f, 0x50, 0x0, 0xad, 0xaf, 0xc0, 0x0, 0xa,
    0xff, 0x9f, 0x70, 0x0, 0xaf, 0x30, 0x9f, 0x40,
    0xa, 0xd0, 0x0, 0xce, 0x10, 0xad, 0x0, 0x1,
    0xec, 0x0,

    /* U+006C "l" */
    0xad, 0xad, 0xad, 0xad, 0xad, 0xad, 0xad, 0xad,
    0xad, 0xad, 0xad,

    /* U+006D "m" */
    0xac, 0x7d, 0xfd, 0x61, 0x9e, 0xfc, 0x30, 0xaf,
    0xd5, 0x4a, 0xfd, 0xc5, 0x4c, 0xf1, 0xaf, 0x20,
    0x0, 0xee, 0x10, 0x1, 0xf6, 0xad, 0x0, 0x0,
    0xcb, 0x0, 0x0, 0xe8, 0xad, 0x0, 0x0, 0xcb,
    0x0, 0x0, 0xe9, 0xad, 0x0, 0x0, 0xcb, 0x0,
    0x0, 0xe9, 0xad, 0x0, 0x0, 0xcb, 0x0, 0x0,
    0xe9, 0xad, 0x0, 0x0, 0xcb, 0x0, 0x0, 0xe9,

    /* U+006E "n" */
    0xac, 0x6d, 0xfd, 0x70, 0xaf, 0xd5, 0x48, 0xf6,
    0xaf, 0x20, 0x0, 0xbc, 0xad, 0x0, 0x0, 0x8e,
    0xad, 0x0, 0x0, 0x8e, 0xad, 0x0, 0x0, 0x8e,
    0xad, 0x0, 0x0, 0x8e, 0xad, 0x0, 0x0, 0x8e,

    /* U+006F "o" */
    0x0, 0x5c, 0xfe, 0xa1, 0x0, 0x7f, 0x94, 0x6d,
    0xe1, 0x1f, 0x80, 0x0, 0x1e, 0x95, 0xf2, 0x0,
    0x0, 0xad, 0x5f, 0x20, 0x0, 0xa, 0xd1, 0xf8,
    0x0, 0x1, 0xe9, 0x7, 0xf9, 0x46, 0xde, 0x10,
    0x5, 0xcf, 0xea, 0x10,

    /* U+0070 "p" */
    0xac, 0x6d, 0xfe, 0x80, 0xa, 0xfe, 0x63, 0x6e,
    0xb0, 0xaf, 0x30, 0x0, 0x4f, 0x4a, 0xd0, 0x0,
    0x0, 0xe8, 0xad, 0x0, 0x0, 0xe, 0x8a, 0xf3,
    0x0, 0x5, 0xf4, 0xaf, 0xe7, 0x47, 0xfb, 0xa,
    0xd5, 0xdf, 0xe8, 0x0, 0xad, 0x0, 0x0, 0x0,
    0xa, 0xd0, 0x0, 0x0, 0x0, 0xad, 0x0, 0x0,
    0x0, 0x0,

    /* U+0071 "q" */
    0x0, 0x6d, 0xfe, 0x88, 0xd0, 0x8f, 0x94, 0x6d,
    0xfd, 0x1f, 0x80, 0x0, 0x1e, 0xd5, 0xf2, 0x0,
    0x0, 0xad, 0x5f, 0x20, 0x0, 0xa, 0xd1, 0xf8,
    0x0, 0x1, 0xed, 0x8, 0xf9, 0x46, 0xdf, 0xd0,
    0x6, 0xdf, 0xe7, 0x9d, 0x0, 0x0, 0x0, 0x9,
    0xd0, 0x0, 0x0, 0x0, 0x9d, 0x0, 0x0, 0x0,
    0x9, 0xd0,

    /* U+0072 "r" */
    0xac, 0x6d, 0x9a, 0xff, 0x84, 0xaf, 0x30, 0xa,
    0xe0, 0x0, 0xad, 0x0, 0xa, 0xd0, 0x0, 0xad,
    0x0, 0xa, 0xd0, 0x0,

    /* U+0073 "s" */
    0x4, 0xcf, 0xec, 0x60, 0x2f, 0x94, 0x48, 0x50,
    0x5f, 0x20, 0x0, 0x0, 0x1d, 0xea, 0x73, 0x0,
    0x0, 0x69, 0xcf, 0x90, 0x0, 0x0, 0x7, 0xf0,
    0x5c, 0x74, 0x5c, 0xd0, 0x2a, 0xef, 0xea, 0x20,

    /* U+0074 "t" */
    0x6, 0xf0, 0x0, 0x6, 0xf0, 0x0, 0xcf, 0xff,
    0xf4, 0x28, 0xf3, 0x30, 0x6, 0xf0, 0x0, 0x6,
    0xf0, 0x0, 0x6, 0xf0, 0x0, 0x6, 0xf0, 0x0,
    0x3, 0xf8, 0x44, 0x0, 0x7e, 0xf8,

    /* U+0075 "u" */
    0xbb, 0x0, 0x0, 0xac, 0xbb, 0x0, 0x0, 0xac,
    0xbb, 0x0, 0x0, 0xac, 0xbb, 0x0, 0x0, 0xac,
    0xbc, 0x0, 0x0, 0xbc, 0x8e, 0x0, 0x0, 0xec,
    0x3f, 0xa4, 0x4c, 0xfc, 0x4, 0xcf, 0xe8, 0x9c,

    /* U+0076 "v" */
    0xd, 0xa0, 0x0, 0x3, 0xf3, 0x6, 0xf1, 0x0,
    0xa, 0xc0, 0x0, 0xe8, 0x0, 0x1f, 0x50, 0x0,
    0x8e, 0x0, 0x8d, 0x0, 0x0, 0x1f, 0x60, 0xe7,
    0x0, 0x0, 0xa, 0xc6, 0xf0, 0x0, 0x0, 0x3,
    0xfe, 0x90, 0x0, 0x0, 0x0, 0xcf, 0x20, 0x0,

    /* U+0077 "w" */
    0xba, 0x0, 0x0, 0xf8, 0x0, 0x1, 0xf3, 0x5f,
    0x0, 0x6, 0xfe, 0x0, 0x7, 0xd0, 0xf, 0x50,
    0xc, 0x9f, 0x40, 0xd, 0x70, 0x9, 0xb0, 0x2f,
    0x2a, 0xa0, 0x3f, 0x10, 0x3, 0xf1, 0x8c, 0x4,
    0xf1, 0x8b, 0x0, 0x0, 0xd7, 0xe6, 0x0, 0xe6,
    0xe5, 0x0, 0x0, 0x8f, 0xf0, 0x0, 0x8f, 0xf0,
    0x0, 0x0, 0x2f, 0x90, 0x0, 0x2f, 0x90, 0x0,

    /* U+0078 "x" */
    0x4f, 0x50, 0x1, 0xe8, 0x0, 0x7f, 0x20, 0xcb,
    0x0, 0x0, 0xbd, 0x9e, 0x10, 0x0, 0x1, 0xef,
    0x30, 0x0, 0x0, 0x2f, 0xf5, 0x0, 0x0, 0xd,
    0xb7, 0xf2, 0x0, 0xa, 0xe1, 0xb, 0xd0, 0x6,
    0xf3, 0x0, 0x1d, 0xa0,

    /* U+0079 "y" */
    0xc, 0xa0, 0x0, 0x3, 0xf2, 0x5, 0xf2, 0x0,
    0xa, 0xb0, 0x0, 0xe8, 0x0, 0x1f, 0x40, 0x0,
    0x7f, 0x0, 0x8d, 0x0, 0x0, 0x1f, 0x60, 0xe6,
    0x0, 0x0, 0x9, 0xd5, 0xf0, 0x0, 0x0, 0x2,
    0xfe, 0x80, 0x0, 0x0, 0x0, 0xbf, 0x20, 0x0,
    0x0, 0x0, 0xca, 0x0, 0x0, 0x9, 0x48, 0xf2,
    0x0, 0x0, 0x1b, 0xfd, 0x50, 0x0, 0x0,

    /* U+007A "z" */
    0x5f, 0xff, 0xff, 0xf2, 0x3, 0x33, 0x4f, 0xb0,
    0x0, 0x0, 0xbd, 0x0, 0x0, 0x8, 0xf2, 0x0,
    0x0, 0x5f, 0x40, 0x0, 0x3, 0xf7, 0x0, 0x0,
    0x1e, 0xc3, 0x33, 0x30, 0x6f, 0xff, 0xff, 0xf4,

    /* U+007B "{" */
    0x0, 0x4d, 0xf0, 0xd, 0xc3, 0x0, 0xe8, 0x0,
    0xf, 0x80, 0x0, 0xf8, 0x0, 0x1f, 0x70, 0x2f,
    0xe1, 0x0, 0x4f, 0x60, 0x0, 0xf7, 0x0, 0xf,
    0x80, 0x0, 0xf8, 0x0, 0xe, 0x80, 0x0, 0xcc,
    0x30, 0x3, 0xdf,

    /* U+007C "|" */
    0x6e, 0x6e, 0x6e, 0x6e, 0x6e, 0x6e, 0x6e, 0x6e,
    0x6e, 0x6e, 0x6e, 0x6e, 0x6e, 0x6e,

    /* U+007D "}" */
    0xbe, 0x70, 0x2, 0x9f, 0x10, 0x3, 0xf3, 0x0,
    0x3f, 0x30, 0x3, 0xf3, 0x0, 0x3f, 0x50, 0x0,
    0xcf, 0x60, 0x2f, 0x71, 0x3, 0xf3, 0x0, 0x3f,
    0x30, 0x3, 0xf3, 0x0, 0x3f, 0x30, 0x29, 0xf1,
    0xb, 0xe6, 0x0,

    /* U+007E "~" */
    0x6, 0xed, 0x71, 0x5b, 0xf, 0x22, 0xaf, 0xe4,

    /* U+F001 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x15, 0xae, 0xe0,
    0x0, 0x0, 0x0, 0x2, 0x7c, 0xff, 0xff, 0xf1,
    0x0, 0x0, 0x39, 0xef, 0xff, 0xff, 0xff, 0xf1,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0x0, 0x1, 0xff, 0xff, 0xff, 0xc7, 0x2c, 0xf1,
    0x0, 0x1, 0xff, 0xea, 0x51, 0x0, 0xc, 0xf1,
    0x0, 0x1, 0xfd, 0x0, 0x0, 0x0, 0xc, 0xf1,
    0x0, 0x1, 0xfc, 0x0, 0x0, 0x0, 0xc, 0xf1,
    0x0, 0x1, 0xfc, 0x0, 0x0, 0x0, 0xc, 0xf1,
    0x0, 0x1, 0xfc, 0x0, 0x0, 0x4a, 0xbe, 0xf1,
    0x0, 0x1, 0xfc, 0x0, 0x5, 0xff, 0xff, 0xf1,
    0x8, 0xdd, 0xfc, 0x0, 0x6, 0xff, 0xff, 0xf0,
    0xaf, 0xff, 0xfc, 0x0, 0x0, 0x8e, 0xfc, 0x30,
    0xaf, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x19, 0xdd, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F008 "" */
    0x20, 0x4, 0x44, 0x44, 0x44, 0x44, 0x0, 0x2e,
    0x57, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x5e, 0xfa,
    0xbf, 0x52, 0x22, 0x22, 0x5f, 0xba, 0xff, 0x3,
    0xf3, 0x0, 0x0, 0x3, 0xf3, 0xf, 0xf8, 0x9f,
    0x30, 0x0, 0x0, 0x3f, 0xa8, 0xff, 0x79, 0xf8,
    0x55, 0x55, 0x58, 0xf9, 0x7f, 0xf0, 0x3f, 0xff,
    0xff, 0xff, 0xff, 0x30, 0xff, 0xbc, 0xf4, 0x11,
    0x11, 0x14, 0xfc, 0xbf, 0xf4, 0x6f, 0x30, 0x0,
    0x0, 0x3f, 0x64, 0xff, 0x3, 0xf3, 0x0, 0x0,
    0x3, 0xf3, 0xf, 0xfe, 0xef, 0x96, 0x66, 0x66,
    0x9f, 0xee, 0xfd, 0x14, 0xff, 0xff, 0xff, 0xff,
    0xf4, 0x1d,

    /* U+F00B "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xff, 0xf4, 0x9f, 0xff, 0xff, 0xff, 0xfe, 0xff,
    0xff, 0x5b, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0xbf, 0xff, 0xff, 0xff, 0xff, 0x8b, 0xba,
    0x25, 0xbb, 0xbb, 0xbb, 0xbb, 0x84, 0x66, 0x50,
    0x26, 0x66, 0x66, 0x66, 0x64, 0xff, 0xff, 0x5a,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0xbf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x5a, 0xff,
    0xff, 0xff, 0xff, 0xf2, 0x44, 0x40, 0x14, 0x44,
    0x44, 0x44, 0x42, 0xac, 0xcc, 0x26, 0xcc, 0xcc,
    0xcc, 0xcc, 0xaf, 0xff, 0xf5, 0xbf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x5b, 0xff, 0xff, 0xff,
    0xff, 0xfc, 0xff, 0xe3, 0x8f, 0xff, 0xff, 0xff,
    0xfc,

    /* U+F00C "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xff, 0xf6, 0x4, 0x20,
    0x0, 0x0, 0xc, 0xff, 0xf6, 0x7, 0xfe, 0x20,
    0x0, 0xc, 0xff, 0xf6, 0x0, 0xef, 0xfe, 0x20,
    0xc, 0xff, 0xf6, 0x0, 0x3, 0xff, 0xfe, 0x3c,
    0xff, 0xf6, 0x0, 0x0, 0x3, 0xff, 0xff, 0xff,
    0xf6, 0x0, 0x0, 0x0, 0x3, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xd6, 0x0, 0x0,
    0x0, 0x0,

    /* U+F00D "" */
    0x3c, 0x50, 0x0, 0x2, 0xc7, 0xe, 0xff, 0x50,
    0x2, 0xef, 0xf3, 0x9f, 0xff, 0x52, 0xef, 0xfd,
    0x0, 0x9f, 0xff, 0xef, 0xfd, 0x10, 0x0, 0x9f,
    0xff, 0xfd, 0x10, 0x0, 0x2, 0xff, 0xff, 0x60,
    0x0, 0x2, 0xef, 0xff, 0xff, 0x50, 0x2, 0xef,
    0xfd, 0xaf, 0xff, 0x50, 0xdf, 0xfd, 0x10, 0x9f,
    0xff, 0x2b, 0xfd, 0x10, 0x0, 0x9f, 0xe1, 0x6,
    0x10, 0x0, 0x0, 0x52, 0x0,

    /* U+F011 "" */
    0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x2b, 0x30, 0xef, 0x70, 0x97, 0x0, 0x0, 0x1e,
    0xfc, 0xe, 0xf7, 0x3f, 0xf8, 0x0, 0xb, 0xff,
    0x50, 0xef, 0x70, 0xbf, 0xf4, 0x3, 0xff, 0x60,
    0xe, 0xf7, 0x0, 0xdf, 0xb0, 0x8f, 0xe0, 0x0,
    0xef, 0x70, 0x5, 0xff, 0x1b, 0xfa, 0x0, 0xe,
    0xf7, 0x0, 0x1f, 0xf3, 0xbf, 0x90, 0x0, 0xdf,
    0x60, 0x0, 0xff, 0x49, 0xfc, 0x0, 0x2, 0x40,
    0x0, 0x3f, 0xf3, 0x5f, 0xf2, 0x0, 0x0, 0x0,
    0x9, 0xff, 0x0, 0xef, 0xc0, 0x0, 0x0, 0x4,
    0xff, 0x80, 0x5, 0xff, 0xc3, 0x0, 0x7, 0xff,
    0xe1, 0x0, 0x8, 0xff, 0xff, 0xef, 0xff, 0xe2,
    0x0, 0x0, 0x4, 0xdf, 0xff, 0xff, 0xa1, 0x0,
    0x0, 0x0, 0x0, 0x36, 0x75, 0x10, 0x0, 0x0,

    /* U+F013 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3c, 0xdc, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x8,
    0x25, 0xdf, 0xff, 0xd5, 0x28, 0x0, 0x9, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf9, 0x2, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf2, 0x4f, 0xff, 0xfe,
    0x41, 0x4e, 0xff, 0xff, 0x40, 0x2f, 0xff, 0x60,
    0x0, 0x6f, 0xff, 0x20, 0x1, 0xff, 0xf3, 0x0,
    0x3, 0xff, 0xf1, 0x0, 0x5f, 0xff, 0x70, 0x0,
    0x8f, 0xff, 0x50, 0x5f, 0xff, 0xff, 0x85, 0x8f,
    0xff, 0xff, 0x51, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0x6, 0xfd, 0xff, 0xff, 0xff, 0xfd,
    0xf6, 0x0, 0x4, 0x2, 0xbf, 0xff, 0xb2, 0x4,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x28, 0x98, 0x20, 0x0, 0x0,

    /* U+F015 "" */
    0x0, 0x0, 0x0, 0x0, 0x20, 0x0, 0x22, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xcf, 0xa0, 0xf, 0xf4,
    0x0, 0x0, 0x0, 0x3, 0xef, 0xff, 0xd2, 0xff,
    0x40, 0x0, 0x0, 0x5, 0xff, 0x91, 0xbf, 0xef,
    0xf4, 0x0, 0x0, 0x8, 0xff, 0x63, 0xb2, 0x8f,
    0xff, 0x40, 0x0, 0xb, 0xfe, 0x45, 0xff, 0xf4,
    0x5f, 0xf9, 0x0, 0x2d, 0xfd, 0x28, 0xff, 0xff,
    0xf6, 0x3e, 0xfc, 0x1d, 0xfb, 0x1b, 0xff, 0xff,
    0xff, 0xf9, 0x2c, 0xfb, 0x48, 0x1d, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x9, 0x30, 0x3, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0x0, 0x0, 0x3f, 0xff,
    0xf4, 0x36, 0xff, 0xff, 0x10, 0x0, 0x3, 0xff,
    0xff, 0x0, 0x2f, 0xff, 0xf1, 0x0, 0x0, 0x3f,
    0xff, 0xf0, 0x2, 0xff, 0xff, 0x10, 0x0, 0x2,
    0xff, 0xfe, 0x0, 0x1e, 0xff, 0xe0, 0x0,

    /* U+F019 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xff, 0x60, 0x0, 0x0, 0x0, 0x26, 0x6a,
    0xff, 0xfa, 0x66, 0x20, 0x0, 0x5, 0xff, 0xff,
    0xff, 0xff, 0xf5, 0x0, 0x0, 0x6, 0xff, 0xff,
    0xff, 0xf6, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xf6,
    0x0, 0x0, 0x9, 0xbb, 0xbb, 0x46, 0xf6, 0x4b,
    0xbb, 0xb9, 0xff, 0xff, 0xff, 0x50, 0x5f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x5d, 0x2e,
    0xfb, 0xee, 0xee, 0xee, 0xee, 0xed, 0xed, 0xeb,

    /* U+F01C "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0x60,
    0x0, 0x0, 0x4f, 0xfd, 0xdd, 0xdd, 0xdd, 0xff,
    0x30, 0x0, 0x1e, 0xf4, 0x0, 0x0, 0x0, 0x6,
    0xfd, 0x0, 0xa, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xf8, 0x5, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1e, 0xf3, 0xdf, 0xca, 0xa9, 0x0, 0x0,
    0x19, 0xaa, 0xdf, 0xbf, 0xff, 0xff, 0xf6, 0x0,
    0x8, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xec,
    0xcc, 0xff, 0xff, 0xff, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xd9, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,

    /* U+F021 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0x50,
    0x0, 0x4, 0x9c, 0xdc, 0x82, 0x0, 0xff, 0x0,
    0x1b, 0xff, 0xff, 0xff, 0xf9, 0x1f, 0xf0, 0x1d,
    0xff, 0xa5, 0x35, 0xaf, 0xfd, 0xff, 0xb, 0xfe,
    0x30, 0x0, 0x0, 0x3e, 0xff, 0xf3, 0xff, 0x30,
    0x0, 0x7, 0xdc, 0xdf, 0xff, 0x8f, 0xa0, 0x0,
    0x0, 0x8f, 0xff, 0xff, 0xf1, 0x31, 0x0, 0x0,
    0x1, 0x33, 0x33, 0x32, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xee, 0xee, 0xe7, 0x0,
    0x0, 0x7, 0xe8, 0xff, 0xff, 0xff, 0x80, 0x0,
    0x0, 0xdf, 0x5f, 0xff, 0x91, 0x21, 0x0, 0x0,
    0x7f, 0xf0, 0xff, 0xff, 0x80, 0x0, 0x0, 0x7f,
    0xf7, 0xf, 0xfa, 0xff, 0xd7, 0x46, 0xcf, 0xfa,
    0x0, 0xff, 0x7, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0xa, 0xa0, 0x1, 0x7b, 0xdc, 0x82, 0x0, 0x0,

    /* U+F026 "" */
    0x0, 0x0, 0x0, 0x31, 0x0, 0x0, 0x6, 0xf7,
    0x0, 0x0, 0x6f, 0xf8, 0x79, 0x9a, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xf8,
    0xef, 0xff, 0xff, 0xf8, 0x0, 0x1, 0xcf, 0xf8,
    0x0, 0x0, 0x1c, 0xf8, 0x0, 0x0, 0x1, 0xb5,

    /* U+F027 "" */
    0x0, 0x0, 0x0, 0x31, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf8,
    0x0, 0x0, 0x79, 0x9a, 0xff, 0xf8, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xf8, 0xd, 0x40, 0xff, 0xff,
    0xff, 0xf8, 0xa, 0xf1, 0xff, 0xff, 0xff, 0xf8,
    0x3, 0xf3, 0xff, 0xff, 0xff, 0xf8, 0xc, 0xe0,
    0xef, 0xff, 0xff, 0xf8, 0xa, 0x20, 0x0, 0x1,
    0xcf, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x1c, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xb4, 0x0, 0x0,

    /* U+F028 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x73, 0x0,
    0x0, 0x0, 0x0, 0x3, 0x10, 0x0, 0xb, 0xf5,
    0x0, 0x0, 0x0, 0x6, 0xf7, 0x0, 0x2, 0xa,
    0xf3, 0x0, 0x0, 0x6, 0xff, 0x80, 0x6, 0xf6,
    0xc, 0xd0, 0x79, 0x9a, 0xff, 0xf8, 0x0, 0x9,
    0xf3, 0x3f, 0x4f, 0xff, 0xff, 0xff, 0x80, 0xd5,
    0xd, 0xb0, 0xda, 0xff, 0xff, 0xff, 0xf8, 0x9,
    0xf1, 0x7f, 0xa, 0xcf, 0xff, 0xff, 0xff, 0x80,
    0x3f, 0x36, 0xf0, 0x8d, 0xff, 0xff, 0xff, 0xf8,
    0xc, 0xe0, 0x8e, 0xa, 0xbe, 0xff, 0xff, 0xff,
    0x80, 0x92, 0x1e, 0x90, 0xe8, 0x0, 0x1, 0xcf,
    0xf8, 0x0, 0x1c, 0xe1, 0x5f, 0x30, 0x0, 0x1,
    0xcf, 0x80, 0x6, 0xe3, 0x1e, 0xb0, 0x0, 0x0,
    0x1, 0xb5, 0x0, 0x0, 0x1c, 0xe1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xe2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x41, 0x0, 0x0,

    /* U+F03E "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0xff,
    0xda, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0,
    0x4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x0,
    0x2f, 0xff, 0xfc, 0x5f, 0xff, 0xff, 0xf9, 0x6d,
    0xff, 0xfc, 0x0, 0x4f, 0xff, 0xff, 0xfe, 0xbf,
    0xfc, 0x0, 0x0, 0x4f, 0xff, 0xfe, 0x20, 0xac,
    0x0, 0x0, 0x0, 0xef, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xff, 0xee, 0xee, 0xee, 0xee,
    0xee, 0xef, 0xf9, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf9,

    /* U+F043 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xe0, 0x0, 0x0, 0x0, 0x1, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x7f, 0xfc, 0x0, 0x0, 0x0, 0x1e,
    0xff, 0xf5, 0x0, 0x0, 0x9, 0xff, 0xff, 0xd0,
    0x0, 0x4, 0xff, 0xff, 0xff, 0x90, 0x0, 0xdf,
    0xff, 0xff, 0xff, 0x40, 0x7f, 0xff, 0xff, 0xff,
    0xfc, 0xd, 0xff, 0xff, 0xff, 0xff, 0xf2, 0xff,
    0x7f, 0xff, 0xff, 0xff, 0x4e, 0xf1, 0xff, 0xff,
    0xff, 0xf3, 0xaf, 0x75, 0xef, 0xff, 0xff, 0x2,
    0xff, 0x82, 0x8f, 0xff, 0x70, 0x5, 0xff, 0xff,
    0xff, 0x90, 0x0, 0x2, 0x9d, 0xda, 0x40, 0x0,

    /* U+F048 "" */
    0x2, 0x20, 0x0, 0x0, 0x1, 0x1, 0xff, 0x0,
    0x0, 0xb, 0xf2, 0x2f, 0xf0, 0x0, 0x1c, 0xff,
    0x42, 0xff, 0x0, 0x1d, 0xff, 0xf4, 0x2f, 0xf0,
    0x2e, 0xff, 0xff, 0x42, 0xff, 0x3e, 0xff, 0xff,
    0xf4, 0x2f, 0xff, 0xff, 0xff, 0xff, 0x42, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0x2f, 0xfd, 0xff, 0xff,
    0xff, 0x42, 0xff, 0x1c, 0xff, 0xff, 0xf4, 0x2f,
    0xf0, 0xb, 0xff, 0xff, 0x42, 0xff, 0x0, 0xa,
    0xff, 0xf4, 0x2f, 0xf0, 0x0, 0x9, 0xff, 0x41,
    0xfe, 0x0, 0x0, 0x8, 0xd1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F04B "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xb2, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xfe,
    0x50, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xfb,
    0x20, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe5,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xb2, 0x0, 0xff, 0xff, 0xff,
    0xff, 0xd4, 0x0, 0x0, 0xff, 0xff, 0xff, 0xf7,
    0x0, 0x0, 0x0, 0xff, 0xff, 0xfa, 0x10, 0x0,
    0x0, 0x0, 0xff, 0xfd, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x7c, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F04C "" */
    0x2, 0x22, 0x10, 0x0, 0x12, 0x22, 0x0, 0xbf,
    0xff, 0xf5, 0x4, 0xff, 0xff, 0xc0, 0xff, 0xff,
    0xf9, 0x8, 0xff, 0xff, 0xf1, 0xff, 0xff, 0xfa,
    0x8, 0xff, 0xff, 0xf1, 0xff, 0xff, 0xfa, 0x8,
    0xff, 0xff, 0xf1, 0xff, 0xff, 0xfa, 0x8, 0xff,
    0xff, 0xf1, 0xff, 0xff, 0xfa, 0x8, 0xff, 0xff,
    0xf1, 0xff, 0xff, 0xfa, 0x8, 0xff, 0xff, 0xf1,
    0xff, 0xff, 0xfa, 0x8, 0xff, 0xff, 0xf1, 0xff,
    0xff, 0xfa, 0x8, 0xff, 0xff, 0xf1, 0xff, 0xff,
    0xfa, 0x8, 0xff, 0xff, 0xf1, 0xff, 0xff, 0xfa,
    0x8, 0xff, 0xff, 0xf1, 0xff, 0xff, 0xf9, 0x8,
    0xff, 0xff, 0xf0, 0x8f, 0xff, 0xe3, 0x2, 0xdf,
    0xff, 0x90,

    /* U+F04D "" */
    0x2, 0x22, 0x22, 0x22, 0x22, 0x22, 0x0, 0xbf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xd0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf2, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf1, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xa0,

    /* U+F051 "" */
    0x1, 0x0, 0x0, 0x0, 0x22, 0x1, 0xfc, 0x10,
    0x0, 0xe, 0xf2, 0x3f, 0xfd, 0x10, 0x0, 0xef,
    0x33, 0xff, 0xfe, 0x20, 0xe, 0xf3, 0x3f, 0xff,
    0xfe, 0x30, 0xef, 0x33, 0xff, 0xff, 0xff, 0x4e,
    0xf3, 0x3f, 0xff, 0xff, 0xff, 0xff, 0x33, 0xff,
    0xff, 0xff, 0xff, 0xf3, 0x3f, 0xff, 0xff, 0xfd,
    0xff, 0x33, 0xff, 0xff, 0xfd, 0x1e, 0xf3, 0x3f,
    0xff, 0xfc, 0x10, 0xef, 0x33, 0xff, 0xfb, 0x0,
    0xe, 0xf3, 0x3f, 0xfa, 0x0, 0x0, 0xef, 0x31,
    0xd9, 0x0, 0x0, 0xd, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F052 "" */
    0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x6,
    0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x5, 0xff,
    0xff, 0xff, 0xff, 0xf7, 0x0, 0x4, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf5, 0x0, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0x0, 0x3, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x0, 0x9, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcc, 0xca, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x20, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xd0,

    /* U+F053 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xd1, 0x0, 0x0, 0x9, 0xff, 0x70, 0x0, 0x9,
    0xff, 0xa0, 0x0, 0x9, 0xff, 0xa0, 0x0, 0x9,
    0xff, 0xb0, 0x0, 0x9, 0xff, 0xb0, 0x0, 0x1,
    0xff, 0xf2, 0x0, 0x0, 0x5, 0xff, 0xd1, 0x0,
    0x0, 0x5, 0xff, 0xd1, 0x0, 0x0, 0x5, 0xff,
    0xd1, 0x0, 0x0, 0x5, 0xff, 0xd1, 0x0, 0x0,
    0x5, 0xff, 0x70, 0x0, 0x0, 0x5, 0x90,

    /* U+F054 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9d, 0x10, 0x0,
    0x0, 0x1f, 0xfd, 0x10, 0x0, 0x0, 0x5f, 0xfd,
    0x10, 0x0, 0x0, 0x5f, 0xfd, 0x10, 0x0, 0x0,
    0x5f, 0xfd, 0x10, 0x0, 0x0, 0x5f, 0xfd, 0x10,
    0x0, 0x0, 0xbf, 0xf7, 0x0, 0x0, 0x9f, 0xfb,
    0x0, 0x0, 0x9f, 0xfb, 0x0, 0x0, 0x9f, 0xfb,
    0x0, 0x0, 0x9f, 0xfb, 0x0, 0x0, 0x1f, 0xfb,
    0x0, 0x0, 0x0, 0x59, 0x0, 0x0, 0x0,

    /* U+F067 "" */
    0x0, 0x0, 0x0, 0x21, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0x0,
    0x0, 0x0, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0x9c, 0xcc, 0xcf, 0xff, 0xcc, 0xcc, 0xa0, 0x0,
    0x0, 0xd, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xeb, 0x0,
    0x0, 0x0,

    /* U+F068 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0x9c, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xa0,

    /* U+F06E "" */
    0x0, 0x0, 0x0, 0x2, 0x32, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xcf, 0xff, 0xff, 0xb4, 0x0,
    0x0, 0x0, 0x1c, 0xff, 0xa4, 0x24, 0xbf, 0xfb,
    0x10, 0x0, 0x2d, 0xff, 0x60, 0x16, 0x30, 0x8f,
    0xfd, 0x10, 0xd, 0xff, 0xa0, 0x1, 0xff, 0x70,
    0xcf, 0xfc, 0x9, 0xff, 0xf5, 0x12, 0x8f, 0xff,
    0x17, 0xff, 0xf8, 0xef, 0xff, 0x35, 0xff, 0xff,
    0xf2, 0x5f, 0xff, 0xc7, 0xff, 0xf6, 0x1f, 0xff,
    0xfe, 0x8, 0xff, 0xf5, 0xb, 0xff, 0xc0, 0x5e,
    0xfd, 0x30, 0xef, 0xfa, 0x0, 0xc, 0xff, 0x90,
    0x1, 0x0, 0xbf, 0xfa, 0x0, 0x0, 0x8, 0xff,
    0xd8, 0x68, 0xef, 0xf7, 0x0, 0x0, 0x0, 0x1,
    0x7c, 0xef, 0xec, 0x71, 0x0, 0x0,

    /* U+F070 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xd2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xf5, 0x0, 0x0, 0x23,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xf9, 0x5b,
    0xff, 0xff, 0xfa, 0x30, 0x0, 0x0, 0x0, 0x4e,
    0xff, 0xfa, 0x42, 0x5b, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x1c, 0xff, 0x41, 0x62, 0x9, 0xff, 0xd1,
    0x0, 0x1, 0x50, 0x9, 0xff, 0x9f, 0xf6, 0xd,
    0xff, 0xc0, 0x0, 0xbf, 0x90, 0x5, 0xff, 0xff,
    0xf0, 0x8f, 0xff, 0x70, 0xf, 0xff, 0xc1, 0x2,
    0xdf, 0xff, 0x26, 0xff, 0xfb, 0x0, 0x7f, 0xff,
    0x50, 0x0, 0xaf, 0xf6, 0x9f, 0xff, 0x40, 0x0,
    0xcf, 0xfc, 0x0, 0x0, 0x6f, 0xff, 0xff, 0x80,
    0x0, 0x1, 0xbf, 0xf9, 0x0, 0x0, 0x3e, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0x9f, 0xfd, 0x87, 0x30,
    0x1b, 0xfe, 0x40, 0x0, 0x0, 0x0, 0x28, 0xcf,
    0xfe, 0x30, 0x8, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xef, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xb4,

    /* U+F071 "" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xff, 0xff, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xff, 0x30, 0x5f, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xf3, 0x5,
    0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff,
    0x40, 0x6f, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x5f,
    0xff, 0xf5, 0x7, 0xff, 0xff, 0x30, 0x0, 0x0,
    0xe, 0xff, 0xff, 0xb8, 0xcf, 0xff, 0xfc, 0x0,
    0x0, 0x8, 0xff, 0xff, 0xf8, 0x1a, 0xff, 0xff,
    0xf6, 0x0, 0x1, 0xff, 0xff, 0xff, 0x20, 0x4f,
    0xff, 0xff, 0xe0, 0x0, 0xaf, 0xff, 0xff, 0xf8,
    0x1a, 0xff, 0xff, 0xff, 0x80, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x0, 0x7d,
    0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 0xed, 0x50,

    /* U+F074 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbd, 0x10, 0x45,
    0x54, 0x0, 0x0, 0x3, 0x5d, 0xfd, 0x1f, 0xff,
    0xf8, 0x0, 0x6, 0xff, 0xff, 0xfc, 0xff, 0xff,
    0xf7, 0x5, 0xff, 0xff, 0xff, 0x90, 0x0, 0xbf,
    0x74, 0xff, 0xc1, 0xcf, 0xa0, 0x0, 0x0, 0x63,
    0xff, 0xd1, 0x8, 0x90, 0x0, 0x0, 0x2, 0xef,
    0xe1, 0x0, 0x0, 0x0, 0x0, 0x2, 0xef, 0xe2,
    0x93, 0xb, 0xd1, 0x4, 0x44, 0xef, 0xf3, 0x8f,
    0xe5, 0xdf, 0xd1, 0xff, 0xff, 0xf3, 0x2, 0xef,
    0xff, 0xff, 0xcf, 0xff, 0xf4, 0x0, 0x3, 0xff,
    0xff, 0xfa, 0x1, 0x10, 0x0, 0x0, 0x0, 0x1c,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9a,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F077 "" */
    0x0, 0x0, 0x0, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xc0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0xbf, 0xfc, 0xff,
    0xc0, 0x0, 0x0, 0xbf, 0xf9, 0x7, 0xff, 0xc0,
    0x0, 0xbf, 0xf9, 0x0, 0x7, 0xff, 0xc1, 0x9f,
    0xf9, 0x0, 0x0, 0x7, 0xff, 0xb7, 0xf9, 0x0,
    0x0, 0x0, 0x7, 0xf9, 0x2, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x0,

    /* U+F078 "" */
    0x6, 0x0, 0x0, 0x0, 0x0, 0x6, 0x1a, 0xfc,
    0x0, 0x0, 0x0, 0xa, 0xfc, 0x7f, 0xfc, 0x0,
    0x0, 0xa, 0xff, 0x90, 0x7f, 0xfc, 0x0, 0xb,
    0xff, 0x90, 0x0, 0x7f, 0xfc, 0x1b, 0xff, 0x90,
    0x0, 0x0, 0x7f, 0xff, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x20,
    0x0, 0x0, 0x0,

    /* U+F079 "" */
    0x0, 0x2, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xfe, 0x10, 0x1, 0x11, 0x11,
    0x11, 0x0, 0x0, 0x4, 0xff, 0xfd, 0x16, 0xff,
    0xff, 0xff, 0xff, 0x0, 0x3, 0xff, 0xff, 0xfd,
    0x2a, 0xcc, 0xcc, 0xcf, 0xf0, 0x0, 0xbf, 0x9f,
    0xdb, 0xf6, 0x0, 0x0, 0x0, 0xdf, 0x0, 0x1,
    0x51, 0xfc, 0x5, 0x0, 0x0, 0x0, 0xd, 0xf0,
    0x0, 0x0, 0x1f, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0x0, 0x0, 0x1, 0xfc, 0x0, 0x0, 0x0,
    0x6, 0xd, 0xf0, 0x62, 0x0, 0x1f, 0xc0, 0x0,
    0x0, 0x7, 0xfc, 0xdf, 0x9f, 0xa0, 0x1, 0xff,
    0xdd, 0xdd, 0xdb, 0x2d, 0xff, 0xff, 0xe3, 0x0,
    0xe, 0xff, 0xff, 0xff, 0xf5, 0x1d, 0xff, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1d,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x12, 0x0, 0x0,

    /* U+F07B "" */
    0x3, 0x44, 0x44, 0x0, 0x0, 0x0, 0x0, 0xc,
    0xff, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xfc, 0x66, 0x66, 0x65, 0x1f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf9, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf9,

    /* U+F093 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xe4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x4,
    0xff, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x4, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x39, 0x9b,
    0xff, 0xfb, 0x99, 0x30, 0x0, 0x0, 0x0, 0x6f,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xf6,
    0x0, 0x0, 0x9, 0xbb, 0xb8, 0x4f, 0xff, 0x48,
    0xbb, 0xb9, 0xff, 0xff, 0xf3, 0x22, 0x23, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x5d, 0x2e,
    0xfb, 0xee, 0xee, 0xee, 0xee, 0xed, 0xed, 0xeb,

    /* U+F095 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xfd, 0x95,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xfe, 0x0,
    0x0, 0x6, 0xda, 0x0, 0xa, 0xff, 0xf3, 0x0,
    0x7, 0xef, 0xff, 0x84, 0xdf, 0xff, 0x50, 0x0,
    0xe, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x0, 0x0,
    0xa, 0xff, 0xff, 0xff, 0xfd, 0x30, 0x0, 0x0,
    0x7, 0xff, 0xff, 0xfe, 0x60, 0x0, 0x0, 0x0,
    0x2, 0xdc, 0xa8, 0x30, 0x0, 0x0, 0x0, 0x0,

    /* U+F0C4 "" */
    0x0, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1c,
    0xff, 0x80, 0x0, 0x0, 0x79, 0x40, 0xbf, 0xdf,
    0xf6, 0x0, 0x1d, 0xff, 0xe0, 0xff, 0x5, 0xf9,
    0x2, 0xef, 0xfe, 0x20, 0xcf, 0xce, 0xf9, 0x3e,
    0xff, 0xe2, 0x0, 0x2d, 0xff, 0xff, 0xff, 0xfe,
    0x20, 0x0, 0x0, 0x28, 0xff, 0xff, 0xe2, 0x0,
    0x0, 0x0, 0x2, 0xff, 0xff, 0x60, 0x0, 0x0,
    0x5, 0xae, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x7f,
    0xff, 0xfd, 0xaf, 0xff, 0x40, 0x0, 0xef, 0x48,
    0xf8, 0xa, 0xff, 0xf4, 0x0, 0xef, 0x16, 0xf8,
    0x0, 0xaf, 0xff, 0x50, 0x9f, 0xff, 0xf3, 0x0,
    0xa, 0xff, 0xe0, 0x8, 0xdc, 0x50, 0x0, 0x0,
    0x46, 0x20,

    /* U+F0C5 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xff, 0xff, 0xf5, 0xb4, 0x0, 0x0, 0x4,
    0xff, 0xff, 0xf5, 0xbf, 0x40, 0x35, 0x44, 0xff,
    0xff, 0xf5, 0x8c, 0xb0, 0xff, 0xd4, 0xff, 0xff,
    0xfb, 0x66, 0x60, 0xff, 0xd4, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0xff, 0xd4, 0xff, 0xff, 0xff, 0xff,
    0xf1, 0xff, 0xd4, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0xff, 0xd4, 0xff, 0xff, 0xff, 0xff, 0xf1, 0xff,
    0xd4, 0xff, 0xff, 0xff, 0xff, 0xf1, 0xff, 0xd4,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0xff, 0xd4, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0xff, 0xd2, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0xff, 0xf6, 0x1, 0x11, 0x11,
    0x11, 0x0, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x0,
    0x0, 0xbe, 0xee, 0xee, 0xee, 0xd2, 0x0, 0x0,

    /* U+F0C7 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xff, 0xff, 0xff, 0xff, 0x50, 0x0, 0xff, 0xdd,
    0xdd, 0xdd, 0xdf, 0xf5, 0x0, 0xfe, 0x0, 0x0,
    0x0, 0xa, 0xff, 0x50, 0xfe, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xf0, 0xfe, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xf1, 0xff, 0xcc, 0xcc, 0xcc, 0xcf, 0xff,
    0xf1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0xff, 0xff, 0xf7, 0x27, 0xff, 0xff, 0xf1, 0xff,
    0xff, 0xc0, 0x0, 0xbf, 0xff, 0xf1, 0xff, 0xff,
    0xc0, 0x0, 0xaf, 0xff, 0xf1, 0xff, 0xff, 0xf5,
    0x4, 0xff, 0xff, 0xf1, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x90,

    /* U+F0C9 "" */
    0x12, 0x22, 0x22, 0x22, 0x22, 0x22, 0x20, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xde, 0xee, 0xee, 0xee, 0xee,
    0xee, 0xe1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf1, 0x23, 0x33, 0x33, 0x33, 0x33, 0x33, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0x12, 0x22, 0x22, 0x22,
    0x22, 0x22, 0x10,

    /* U+F0E0 "" */
    0x3, 0x44, 0x44, 0x44, 0x44, 0x44, 0x43, 0xc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x53, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xe3, 0x5f, 0xa1, 0xaf,
    0xff, 0xff, 0xff, 0xa1, 0x9f, 0xff, 0xd3, 0x6f,
    0xff, 0xff, 0x62, 0xdf, 0xff, 0xff, 0xf7, 0x2d,
    0xfd, 0x26, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x12,
    0x1a, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf9, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf9,

    /* U+F0E7 "" */
    0x0, 0x12, 0x22, 0x10, 0x0, 0x0, 0xf, 0xff,
    0xff, 0x70, 0x0, 0x3, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0x5f, 0xff, 0xff, 0x0, 0x0, 0x7, 0xff,
    0xff, 0xa0, 0x0, 0x0, 0x9f, 0xff, 0xfa, 0x77,
    0x61, 0xb, 0xff, 0xff, 0xff, 0xff, 0x40, 0xdf,
    0xff, 0xff, 0xff, 0xc0, 0xf, 0xff, 0xff, 0xff,
    0xf3, 0x0, 0x35, 0x55, 0xef, 0xf9, 0x0, 0x0,
    0x0, 0x1f, 0xff, 0x10, 0x0, 0x0, 0x5, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x9f, 0xd0, 0x0, 0x0,
    0x0, 0xc, 0xf4, 0x0, 0x0, 0x0, 0x0, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x1c, 0x20, 0x0, 0x0,

    /* U+F0EA "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x13,
    0x3a, 0xfb, 0x33, 0x20, 0x0, 0x0, 0xff, 0xff,
    0x3e, 0xff, 0xf1, 0x0, 0x0, 0xff, 0xff, 0xcf,
    0xff, 0xf2, 0x0, 0x0, 0xff, 0xff, 0xc7, 0x77,
    0x71, 0x0, 0x0, 0xff, 0xfe, 0x2a, 0xaa, 0xa1,
    0x61, 0x0, 0xff, 0xfc, 0x6f, 0xff, 0xf2, 0xbc,
    0x10, 0xff, 0xfc, 0x6f, 0xff, 0xf2, 0xbf, 0xc0,
    0xff, 0xfc, 0x6f, 0xff, 0xf2, 0x12, 0x20, 0xff,
    0xfc, 0x6f, 0xff, 0xfc, 0x99, 0x90, 0xff, 0xfc,
    0x6f, 0xff, 0xff, 0xff, 0xf1, 0xff, 0xfc, 0x6f,
    0xff, 0xff, 0xff, 0xf1, 0xef, 0xfc, 0x6f, 0xff,
    0xff, 0xff, 0xf1, 0x12, 0x21, 0x6f, 0xff, 0xff,
    0xff, 0xf1, 0x0, 0x0, 0x6f, 0xff, 0xff, 0xff,
    0xf1, 0x0, 0x0, 0x4e, 0xff, 0xff, 0xff, 0xd0,

    /* U+F0F3 "" */
    0x0, 0x0, 0x0, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2a, 0xfc, 0x30, 0x0, 0x0, 0x0, 0x7, 0xff,
    0xff, 0xf9, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xff,
    0xff, 0x80, 0x0, 0x0, 0xef, 0xff, 0xff, 0xff,
    0xf0, 0x0, 0x1, 0xff, 0xff, 0xff, 0xff, 0xf3,
    0x0, 0x2, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0,
    0x4, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x8,
    0xff, 0xff, 0xff, 0xff, 0xfa, 0x0, 0xe, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x10, 0xbf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xd0, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xe0, 0x1, 0x11, 0x11, 0x11, 0x11,
    0x11, 0x0, 0x0, 0x0, 0x3f, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xd9, 0x0, 0x0, 0x0,

    /* U+F11C "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0xff, 0xdd, 0xfd, 0xef, 0xdf, 0xdd, 0xfd,
    0xdf, 0xdf, 0xe0, 0x1d, 0x3, 0xb0, 0xd1, 0xd,
    0x10, 0xfe, 0xff, 0xbc, 0xfb, 0xcf, 0xbf, 0xcb,
    0xfc, 0xbf, 0xef, 0xff, 0x73, 0xc7, 0x3d, 0x38,
    0xb3, 0x9f, 0xfe, 0xff, 0xf4, 0xa, 0x40, 0xb0,
    0x6a, 0x6, 0xff, 0xef, 0xff, 0xfe, 0xff, 0xef,
    0xef, 0xfe, 0xff, 0xfe, 0xfe, 0x2, 0xe0, 0x0,
    0x0, 0x0, 0xe2, 0x1f, 0xef, 0xe0, 0x1d, 0x0,
    0x0, 0x0, 0xd, 0x10, 0xfe, 0xff, 0xee, 0xfe,
    0xee, 0xee, 0xee, 0xfe, 0xef, 0xd9, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,

    /* U+F124 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xcf,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6d, 0xff,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x7, 0xef, 0xff,
    0xff, 0x90, 0x0, 0x0, 0x1, 0x8f, 0xff, 0xff,
    0xff, 0xf2, 0x0, 0x0, 0x2a, 0xff, 0xff, 0xff,
    0xff, 0xfa, 0x0, 0x2, 0xbf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x30, 0x0, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xb0, 0x0, 0xd, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf4, 0x0, 0x0, 0x14, 0x55, 0x55,
    0x9f, 0xff, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1c, 0xb1, 0x0, 0x0, 0x0,

    /* U+F15B "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xff,
    0xff, 0x97, 0x80, 0x0, 0xff, 0xff, 0xff, 0x98,
    0xf8, 0x0, 0xff, 0xff, 0xff, 0x98, 0xff, 0x80,
    0xff, 0xff, 0xff, 0x95, 0xbb, 0xb1, 0xff, 0xff,
    0xff, 0xd5, 0x55, 0x51, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xe2,

    /* U+F1EB "" */
    0x0, 0x0, 0x0, 0x0, 0x22, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0x9d, 0xff, 0xff, 0xfc,
    0x82, 0x0, 0x0, 0x0, 0x4d, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0x20, 0x0, 0xaf, 0xff, 0xd9,
    0x54, 0x34, 0x6a, 0xef, 0xff, 0x70, 0xcf, 0xfc,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x6e, 0xff, 0x87,
    0xf8, 0x0, 0x0, 0x46, 0x76, 0x30, 0x0, 0x1b,
    0xf4, 0x1, 0x0, 0x19, 0xff, 0xff, 0xff, 0xe7,
    0x0, 0x1, 0x0, 0x0, 0x4e, 0xff, 0xff, 0xef,
    0xff, 0xfd, 0x20, 0x0, 0x0, 0x7, 0xff, 0x82,
    0x0, 0x3, 0xaf, 0xf3, 0x0, 0x0, 0x0, 0x6,
    0x20, 0x0, 0x0, 0x0, 0x44, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8c, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xe8, 0x0, 0x0, 0x0, 0x0,

    /* U+F240 "" */
    0x1, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
    0x0, 0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x70, 0xff, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xfd, 0x1f, 0xe0, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0xf, 0xfb, 0xfe, 0x3f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0xaf, 0xcf,
    0xe3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x1,
    0xfc, 0xfe, 0x3f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0xbf, 0xcf, 0xe0, 0x33, 0x33, 0x33, 0x33,
    0x33, 0x33, 0xf, 0xfb, 0xff, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xfd, 0x9, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F241 "" */
    0x1, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
    0x0, 0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x70, 0xff, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xfd, 0x1f, 0xe0, 0x44, 0x44,
    0x44, 0x44, 0x40, 0x0, 0xf, 0xfb, 0xfe, 0x3f,
    0xff, 0xff, 0xff, 0xff, 0x30, 0x0, 0xaf, 0xcf,
    0xe3, 0xff, 0xff, 0xff, 0xff, 0xf3, 0x0, 0x1,
    0xfc, 0xfe, 0x3f, 0xff, 0xff, 0xff, 0xff, 0x30,
    0x0, 0xbf, 0xcf, 0xe0, 0x33, 0x33, 0x33, 0x33,
    0x30, 0x0, 0xf, 0xfb, 0xff, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xfd, 0x9, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F242 "" */
    0x1, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
    0x0, 0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x70, 0xff, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xfd, 0x1f, 0xe0, 0x44, 0x44,
    0x44, 0x10, 0x0, 0x0, 0xf, 0xfb, 0xfe, 0x3f,
    0xff, 0xff, 0xf6, 0x0, 0x0, 0x0, 0xaf, 0xcf,
    0xe3, 0xff, 0xff, 0xff, 0x60, 0x0, 0x0, 0x1,
    0xfc, 0xfe, 0x3f, 0xff, 0xff, 0xf6, 0x0, 0x0,
    0x0, 0xbf, 0xcf, 0xe0, 0x33, 0x33, 0x33, 0x10,
    0x0, 0x0, 0xf, 0xfb, 0xff, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xfd, 0x9, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F243 "" */
    0x1, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
    0x0, 0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x70, 0xff, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xfd, 0x1f, 0xe0, 0x44, 0x42,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xfb, 0xfe, 0x3f,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xcf,
    0xe3, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xfc, 0xfe, 0x3f, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xcf, 0xe0, 0x33, 0x32, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xfb, 0xff, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xfd, 0x9, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F244 "" */
    0x1, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
    0x0, 0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x70, 0xff, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xfd, 0x1f, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xfb, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xcf,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xfc, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xcf, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xfb, 0xff, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xfd, 0x9, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F287 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x22, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0x6f, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xee,
    0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xc0, 0x1b, 0xb1, 0x0, 0x0, 0x0, 0x18, 0x92,
    0x0, 0xd4, 0x0, 0x0, 0x0, 0x1, 0x20, 0xb,
    0xff, 0xe1, 0x7d, 0x11, 0x11, 0x11, 0x11, 0x6f,
    0x70, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x8a, 0xff, 0xc0, 0x0, 0x4e, 0x0,
    0x0, 0x0, 0x5e, 0x50, 0x5, 0x60, 0x0, 0x0,
    0xb6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xe1, 0x8f, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x22, 0x10, 0x0, 0x0,

    /* U+F293 "" */
    0x0, 0x0, 0x0, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x5c, 0xff, 0xfd, 0x70, 0x0, 0x0, 0x7f, 0xff,
    0x8f, 0xff, 0x90, 0x0, 0x2f, 0xff, 0xf3, 0x6f,
    0xff, 0x30, 0x8, 0xff, 0xff, 0x31, 0x7f, 0xf9,
    0x0, 0xcf, 0x96, 0xf3, 0xb2, 0x9f, 0xd0, 0xf,
    0xfe, 0x26, 0x39, 0x1c, 0xff, 0x0, 0xff, 0xfe,
    0x20, 0xa, 0xff, 0xf0, 0xf, 0xff, 0xfb, 0x5,
    0xff, 0xff, 0x10, 0xff, 0xfd, 0x10, 0x9, 0xff,
    0xf0, 0xf, 0xfd, 0x17, 0x39, 0x1a, 0xff, 0x0,
    0xcf, 0x97, 0xf3, 0xa2, 0x8f, 0xd0, 0x8, 0xff,
    0xff, 0x30, 0x8f, 0xf9, 0x0, 0x1f, 0xff, 0xf4,
    0x7f, 0xff, 0x20, 0x0, 0x5f, 0xff, 0xaf, 0xff,
    0x70, 0x0, 0x0, 0x29, 0xcd, 0xda, 0x40, 0x0,

    /* U+F2ED "" */
    0x0, 0x0, 0xbf, 0xff, 0xd1, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0xcd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xc1, 0x2, 0x22, 0x22,
    0x22, 0x22, 0x22, 0x0, 0x1f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x30, 0x1f, 0xfd, 0xff, 0xdf, 0xfd,
    0xff, 0x30, 0x1f, 0xf4, 0xcf, 0x1f, 0xe2, 0xff,
    0x30, 0x1f, 0xf4, 0xcf, 0x1f, 0xe2, 0xff, 0x30,
    0x1f, 0xf4, 0xcf, 0x1f, 0xe2, 0xff, 0x30, 0x1f,
    0xf4, 0xcf, 0x1f, 0xe2, 0xff, 0x30, 0x1f, 0xf4,
    0xcf, 0x1f, 0xe2, 0xff, 0x30, 0x1f, 0xf4, 0xcf,
    0x1f, 0xe2, 0xff, 0x30, 0x1f, 0xf5, 0xdf, 0x3f,
    0xe3, 0xff, 0x30, 0xf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x20, 0x8, 0xde, 0xee, 0xee, 0xee, 0xd9,
    0x0,

    /* U+F304 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xfe, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xe2,
    0x0, 0x0, 0x0, 0x0, 0x2, 0x4f, 0xff, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0x64, 0xff, 0xfd,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xf6, 0x4f, 0xe3,
    0x0, 0x0, 0x0, 0x6f, 0xff, 0xff, 0x64, 0x30,
    0x0, 0x0, 0x6, 0xff, 0xff, 0xff, 0xe1, 0x0,
    0x0, 0x0, 0x6f, 0xff, 0xff, 0xfe, 0x30, 0x0,
    0x0, 0x6, 0xff, 0xff, 0xff, 0xe3, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0xff, 0xfe, 0x30, 0x0, 0x0,
    0x5, 0xff, 0xff, 0xff, 0xe3, 0x0, 0x0, 0x0,
    0xb, 0xff, 0xff, 0xfe, 0x30, 0x0, 0x0, 0x0,
    0xd, 0xff, 0xff, 0xe3, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xff, 0xfe, 0x30, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xca, 0x93, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F55A "" */
    0x0, 0x0, 0x2, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x31, 0x0, 0x0, 0x7, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf5, 0x0, 0x8, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xb0, 0x8, 0xff, 0xff,
    0xf8, 0x5f, 0xfc, 0x3e, 0xff, 0xfc, 0x8, 0xff,
    0xff, 0xff, 0x20, 0x5b, 0x0, 0xaf, 0xff, 0xc8,
    0xff, 0xff, 0xff, 0xfe, 0x20, 0x0, 0x8f, 0xff,
    0xfc, 0xef, 0xff, 0xff, 0xff, 0xfa, 0x0, 0x3f,
    0xff, 0xff, 0xc4, 0xff, 0xff, 0xff, 0xfc, 0x0,
    0x10, 0x5f, 0xff, 0xfc, 0x4, 0xff, 0xff, 0xff,
    0x10, 0x8e, 0x20, 0x9f, 0xff, 0xc0, 0x4, 0xff,
    0xff, 0xfb, 0x9f, 0xfe, 0x7f, 0xff, 0xfc, 0x0,
    0x4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xa0, 0x0, 0x4, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xd2,

    /* U+F7C2 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xff, 0xff, 0xff, 0x70, 0x0, 0xbe, 0xdf, 0xdf,
    0xdf, 0xf0, 0xb, 0xf4, 0x1d, 0xf, 0xc, 0xf1,
    0xbf, 0xf4, 0x1d, 0xf, 0xc, 0xf1, 0xff, 0xf8,
    0x6e, 0x5f, 0x5d, 0xf1, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x4e, 0xff, 0xff, 0xff, 0xfe, 0x50,

    /* U+F8A2 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xf1,
    0x0, 0x4, 0x40, 0x0, 0x0, 0x0, 0x2f, 0xf1,
    0x0, 0x5f, 0xb0, 0x0, 0x0, 0x0, 0x4f, 0xf1,
    0x6, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x4f, 0xf1,
    0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x1c, 0xff, 0xd6, 0x66, 0x66, 0x66, 0x66, 0x40,
    0x0, 0xcf, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 65, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 64, .box_w = 2, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 10, .adv_w = 94, .box_w = 5, .box_h = 5, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 23, .adv_w = 169, .box_w = 11, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 78, .adv_w = 149, .box_w = 9, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 146, .adv_w = 202, .box_w = 13, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 211, .adv_w = 165, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 272, .adv_w = 50, .box_w = 3, .box_h = 5, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 280, .adv_w = 81, .box_w = 4, .box_h = 14, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 308, .adv_w = 81, .box_w = 4, .box_h = 14, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 336, .adv_w = 96, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 354, .adv_w = 140, .box_w = 7, .box_h = 7, .ofs_x = 1, .ofs_y = 2},
    {.bitmap_index = 379, .adv_w = 54, .box_w = 3, .box_h = 5, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 387, .adv_w = 92, .box_w = 5, .box_h = 3, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 395, .adv_w = 54, .box_w = 3, .box_h = 3, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 400, .adv_w = 84, .box_w = 7, .box_h = 14, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 449, .adv_w = 160, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 499, .adv_w = 89, .box_w = 4, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 519, .adv_w = 138, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 564, .adv_w = 137, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 604, .adv_w = 161, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 654, .adv_w = 138, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 699, .adv_w = 148, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 744, .adv_w = 144, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 789, .adv_w = 155, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 834, .adv_w = 148, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 879, .adv_w = 54, .box_w = 3, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 891, .adv_w = 54, .box_w = 3, .box_h = 11, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 908, .adv_w = 140, .box_w = 7, .box_h = 8, .ofs_x = 1, .ofs_y = 1},
    {.bitmap_index = 936, .adv_w = 140, .box_w = 7, .box_h = 6, .ofs_x = 1, .ofs_y = 2},
    {.bitmap_index = 957, .adv_w = 140, .box_w = 7, .box_h = 8, .ofs_x = 1, .ofs_y = 1},
    {.bitmap_index = 985, .adv_w = 138, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1025, .adv_w = 248, .box_w = 15, .box_h = 13, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 1123, .adv_w = 176, .box_w = 12, .box_h = 10, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 1183, .adv_w = 182, .box_w = 10, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1233, .adv_w = 174, .box_w = 11, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1288, .adv_w = 198, .box_w = 11, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1343, .adv_w = 161, .box_w = 9, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1388, .adv_w = 152, .box_w = 8, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1428, .adv_w = 185, .box_w = 11, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1483, .adv_w = 195, .box_w = 10, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1533, .adv_w = 74, .box_w = 3, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1548, .adv_w = 123, .box_w = 8, .box_h = 10, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 1588, .adv_w = 173, .box_w = 10, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1638, .adv_w = 143, .box_w = 8, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1678, .adv_w = 229, .box_w = 12, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1738, .adv_w = 195, .box_w = 10, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1788, .adv_w = 202, .box_w = 12, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1848, .adv_w = 173, .box_w = 10, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1898, .adv_w = 202, .box_w = 13, .box_h = 13, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 1983, .adv_w = 174, .box_w = 10, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2033, .adv_w = 149, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2078, .adv_w = 141, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2123, .adv_w = 190, .box_w = 10, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2173, .adv_w = 171, .box_w = 12, .box_h = 10, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 2233, .adv_w = 270, .box_w = 17, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2318, .adv_w = 162, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2368, .adv_w = 155, .box_w = 11, .box_h = 10, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 2423, .adv_w = 158, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2473, .adv_w = 80, .box_w = 4, .box_h = 14, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 2501, .adv_w = 84, .box_w = 7, .box_h = 14, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 2550, .adv_w = 80, .box_w = 4, .box_h = 14, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 2578, .adv_w = 140, .box_w = 7, .box_h = 6, .ofs_x = 1, .ofs_y = 2},
    {.bitmap_index = 2599, .adv_w = 120, .box_w = 8, .box_h = 1, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2603, .adv_w = 144, .box_w = 5, .box_h = 2, .ofs_x = 1, .ofs_y = 9},
    {.bitmap_index = 2608, .adv_w = 144, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2640, .adv_w = 164, .box_w = 9, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2690, .adv_w = 137, .box_w = 9, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2726, .adv_w = 164, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2776, .adv_w = 147, .box_w = 9, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2812, .adv_w = 85, .box_w = 6, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2845, .adv_w = 166, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 2895, .adv_w = 163, .box_w = 8, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2939, .adv_w = 67, .box_w = 3, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2956, .adv_w = 68, .box_w = 6, .box_h = 14, .ofs_x = -2, .ofs_y = -3},
    {.bitmap_index = 2998, .adv_w = 148, .box_w = 9, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3048, .adv_w = 67, .box_w = 2, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3059, .adv_w = 254, .box_w = 14, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3115, .adv_w = 163, .box_w = 8, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3147, .adv_w = 152, .box_w = 9, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3183, .adv_w = 164, .box_w = 9, .box_h = 11, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 3233, .adv_w = 164, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 3283, .adv_w = 98, .box_w = 5, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3303, .adv_w = 120, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3335, .adv_w = 99, .box_w = 6, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3365, .adv_w = 162, .box_w = 8, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3397, .adv_w = 134, .box_w = 10, .box_h = 8, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 3437, .adv_w = 216, .box_w = 14, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3493, .adv_w = 132, .box_w = 9, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3529, .adv_w = 134, .box_w = 10, .box_h = 11, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 3584, .adv_w = 125, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3616, .adv_w = 84, .box_w = 5, .box_h = 14, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 3651, .adv_w = 72, .box_w = 2, .box_h = 14, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 3665, .adv_w = 84, .box_w = 5, .box_h = 14, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 3700, .adv_w = 140, .box_w = 8, .box_h = 2, .ofs_x = 0, .ofs_y = 4},
    {.bitmap_index = 3708, .adv_w = 240, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3836, .adv_w = 240, .box_w = 15, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3926, .adv_w = 240, .box_w = 15, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4031, .adv_w = 240, .box_w = 15, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4121, .adv_w = 165, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4182, .adv_w = 240, .box_w = 15, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4302, .adv_w = 240, .box_w = 15, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4422, .adv_w = 270, .box_w = 17, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4541, .adv_w = 240, .box_w = 15, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4661, .adv_w = 270, .box_w = 17, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4763, .adv_w = 240, .box_w = 15, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4883, .adv_w = 120, .box_w = 8, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4931, .adv_w = 180, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5003, .adv_w = 270, .box_w = 17, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5131, .adv_w = 240, .box_w = 15, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5221, .adv_w = 165, .box_w = 11, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5309, .adv_w = 210, .box_w = 11, .box_h = 15, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 5392, .adv_w = 210, .box_w = 14, .box_h = 17, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 5511, .adv_w = 210, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5609, .adv_w = 210, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5707, .adv_w = 210, .box_w = 11, .box_h = 15, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 5790, .adv_w = 210, .box_w = 15, .box_h = 14, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 5895, .adv_w = 150, .box_w = 9, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5958, .adv_w = 150, .box_w = 9, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 6021, .adv_w = 210, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 6119, .adv_w = 210, .box_w = 14, .box_h = 4, .ofs_x = 0, .ofs_y = 4},
    {.bitmap_index = 6147, .adv_w = 270, .box_w = 17, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6249, .adv_w = 300, .box_w = 19, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6401, .adv_w = 270, .box_w = 19, .box_h = 16, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 6553, .adv_w = 240, .box_w = 15, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6666, .adv_w = 210, .box_w = 13, .box_h = 9, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 6725, .adv_w = 210, .box_w = 13, .box_h = 9, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 6784, .adv_w = 300, .box_w = 19, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 6908, .adv_w = 240, .box_w = 15, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6998, .adv_w = 240, .box_w = 15, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7118, .adv_w = 240, .box_w = 16, .box_h = 16, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 7246, .adv_w = 210, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 7344, .adv_w = 210, .box_w = 14, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7456, .adv_w = 210, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 7554, .adv_w = 210, .box_w = 14, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 7645, .adv_w = 240, .box_w = 15, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7735, .adv_w = 150, .box_w = 11, .box_h = 16, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 7823, .adv_w = 210, .box_w = 14, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7935, .adv_w = 210, .box_w = 14, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 8047, .adv_w = 270, .box_w = 17, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8149, .adv_w = 240, .box_w = 17, .box_h = 16, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 8285, .adv_w = 180, .box_w = 12, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 8381, .adv_w = 300, .box_w = 19, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 8514, .adv_w = 300, .box_w = 19, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8619, .adv_w = 300, .box_w = 19, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8724, .adv_w = 300, .box_w = 19, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8829, .adv_w = 300, .box_w = 19, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8934, .adv_w = 300, .box_w = 19, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9039, .adv_w = 300, .box_w = 19, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 9163, .adv_w = 210, .box_w = 13, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 9267, .adv_w = 210, .box_w = 14, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 9372, .adv_w = 240, .box_w = 16, .box_h = 16, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 9500, .adv_w = 300, .box_w = 19, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9614, .adv_w = 180, .box_w = 12, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 9710, .adv_w = 241, .box_w = 16, .box_h = 10, .ofs_x = 0, .ofs_y = 1}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_1[] = {
    0x0, 0x7, 0xa, 0xb, 0xc, 0x10, 0x12, 0x14,
    0x18, 0x1b, 0x20, 0x25, 0x26, 0x27, 0x3d, 0x42,
    0x47, 0x4a, 0x4b, 0x4c, 0x50, 0x51, 0x52, 0x53,
    0x66, 0x67, 0x6d, 0x6f, 0x70, 0x73, 0x76, 0x77,
    0x78, 0x7a, 0x92, 0x94, 0xc3, 0xc4, 0xc6, 0xc8,
    0xdf, 0xe6, 0xe9, 0xf2, 0x11b, 0x123, 0x15a, 0x1ea,
    0x23f, 0x240, 0x241, 0x242, 0x243, 0x286, 0x292, 0x2ec,
    0x303, 0x559, 0x7c1, 0x8a1
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 61441, .range_length = 2210, .glyph_id_start = 96,
        .unicode_list = unicode_list_1, .glyph_id_ofs_list = NULL, .list_length = 60, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/


/*Map glyph_ids to kern left classes*/
static const uint8_t kern_left_class_mapping[] =
{
    0, 0, 1, 2, 0, 3, 4, 5,
    2, 6, 7, 8, 9, 10, 9, 10,
    11, 12, 0, 13, 14, 15, 16, 17,
    18, 19, 12, 20, 20, 0, 0, 0,
    21, 22, 23, 24, 25, 22, 26, 27,
    28, 29, 29, 30, 31, 32, 29, 29,
    22, 33, 34, 35, 3, 36, 30, 37,
    37, 38, 39, 40, 41, 42, 43, 0,
    44, 0, 45, 46, 47, 48, 49, 50,
    51, 45, 52, 52, 53, 48, 45, 45,
    46, 46, 54, 55, 56, 57, 51, 58,
    58, 59, 58, 60, 41, 0, 0, 9,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0
};

/*Map glyph_ids to kern right classes*/
static const uint8_t kern_right_class_mapping[] =
{
    0, 0, 1, 2, 0, 3, 4, 5,
    2, 6, 7, 8, 9, 10, 9, 10,
    11, 12, 13, 14, 15, 16, 17, 12,
    18, 19, 20, 21, 21, 0, 0, 0,
    22, 23, 24, 25, 23, 25, 25, 25,
    23, 25, 25, 26, 25, 25, 25, 25,
    23, 25, 23, 25, 3, 27, 28, 29,
    29, 30, 31, 32, 33, 34, 35, 0,
    36, 0, 37, 38, 39, 39, 39, 0,
    39, 38, 40, 41, 38, 38, 42, 42,
    39, 42, 39, 42, 43, 44, 45, 46,
    46, 47, 46, 48, 0, 0, 35, 9,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0
};

/*Kern values between classes*/
static const int8_t kern_class_values[] =
{
    0, 1, 0, 0, 0, 0, 0, 0,
    0, 1, 0, 0, 2, 0, 0, 0,
    0, 2, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 1, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    1, 11, 0, 6, -5, 0, 0, 0,
    0, -13, -14, 2, 11, 5, 4, -10,
    2, 12, 1, 10, 2, 8, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 14, 2, -2, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -7, 0, 0, 0, 0, 0, -5,
    4, 5, 0, 0, -2, 0, -2, 2,
    0, -2, 0, -2, -1, -5, 0, 0,
    0, 0, -2, 0, 0, -3, -4, 0,
    0, -2, 0, -5, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -2, -2, 0,
    0, -6, 0, -29, 0, 0, -5, 0,
    5, 7, 0, 0, -5, 2, 2, 8,
    5, -4, 5, 0, 0, -14, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -9, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -3, -12, 0, -10, -2, 0, 0, 0,
    0, 0, 9, 0, -7, -2, -1, 1,
    0, -4, 0, 0, -2, -18, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -19, -2, 9, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 8, 0, 2, 0, 0, -5,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 9, 2, 1, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -9, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    2, 5, 2, 7, -2, 0, 0, 5,
    -2, -8, -33, 2, 6, 5, 0, -3,
    0, 9, 0, 8, 0, 8, 0, -22,
    0, -3, 7, 0, 8, -2, 5, 2,
    0, 0, 1, -2, 0, 0, -4, 19,
    0, 19, 0, 7, 0, 10, 3, 4,
    0, 0, 0, -9, 0, 0, 0, 0,
    1, -2, 0, 2, -4, -3, -5, 2,
    0, -2, 0, 0, 0, -10, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -16, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    1, -13, 0, -15, 0, 0, 0, 0,
    -2, 0, 24, -3, -3, 2, 2, -2,
    0, -3, 2, 0, 0, -13, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -23, 0, 2, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 14, 0, 0, -9, 0, 8, 0,
    -16, -23, -16, -5, 7, 0, 0, -16,
    0, 3, -6, 0, -4, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 6, 7, -29, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 2, 0, 0, 0, 0, 0, 2,
    2, -3, -5, 0, -1, -1, -2, 0,
    0, -2, 0, 0, 0, -5, 0, -2,
    0, -6, -5, 0, -6, -8, -8, -5,
    0, -5, 0, -5, 0, 0, 0, 0,
    -2, 0, 0, 2, 0, 2, -2, 0,
    0, 0, 0, 2, -2, 0, 0, 0,
    -2, 2, 2, -1, 0, 0, 0, -5,
    0, -1, 0, 0, 0, 0, 0, 1,
    0, 3, -2, 0, -3, 0, -4, 0,
    0, -2, 0, 7, 0, 0, -2, 0,
    0, 0, 0, 0, -1, 1, -2, -2,
    0, -2, 0, -2, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -1, -1, 0,
    -2, -3, 0, 0, 0, 0, 0, 1,
    0, 0, -2, 0, -2, -2, -2, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -1, 0, 0, 0, 0, -2, -3, 0,
    0, -7, -2, -7, 5, 0, 0, -5,
    2, 5, 6, 0, -6, -1, -3, 0,
    -1, -11, 2, -2, 2, -13, 2, 0,
    0, 1, -12, 0, -13, -2, -21, -2,
    0, -12, 0, 5, 7, 0, 3, 0,
    0, 0, 0, 0, 0, -4, -3, 0,
    0, 0, 0, -2, 0, 0, 0, -2,
    0, 0, 0, 0, 0, -1, -1, 0,
    -1, -3, 0, 0, 0, 0, 0, 0,
    0, -2, -2, 0, -2, -3, -2, 0,
    0, -2, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -2, -2, 0,
    0, -2, 0, -5, 2, 0, 0, -3,
    1, 2, 2, 0, 0, 0, 0, 0,
    0, -2, 0, 0, 0, 0, 0, 2,
    0, 0, -2, 0, -2, -2, -3, 0,
    0, 0, 0, 0, 0, 0, 2, 0,
    -2, 0, 0, 0, 0, -3, -4, 0,
    0, 7, -2, 1, -8, 0, 0, 6,
    -12, -12, -10, -5, 2, 0, -2, -16,
    -4, 0, -4, 0, -5, 4, -4, -15,
    0, -6, 0, 0, 1, -1, 2, -2,
    0, 2, 0, -7, -9, 0, -12, -6,
    -5, -6, -7, -3, -6, 0, -5, -6,
    0, 1, 0, -2, 0, 0, 0, 2,
    0, 2, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -2, 0, -1,
    0, -1, -2, 0, -4, -5, -5, -1,
    0, -7, 0, 0, 0, 0, 0, 0,
    -2, 0, 0, 0, 0, 1, -1, 0,
    0, 2, 0, 0, 0, 0, 0, 0,
    0, 0, 12, 0, 0, 0, 0, 0,
    0, 2, 0, 0, 0, -2, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -4, 0, 2, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -2, 0, 0, 0, -5, 0, 0, 0,
    0, -12, -7, 0, 0, 0, -4, -12,
    0, 0, -2, 2, 0, -6, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -4, 0, 0, -5, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -4, 0, 0, 0, 0, 3, 0,
    2, -5, -5, 0, -2, -2, -3, 0,
    0, 0, 0, 0, 0, -7, 0, -2,
    0, -4, -2, 0, -5, -6, -7, -2,
    0, -5, 0, -7, 0, 0, 0, 0,
    19, 0, 0, 1, 0, 0, -3, 0,
    0, -10, 0, 0, 0, 0, 0, -22,
    -4, 8, 7, -2, -10, 0, 2, -4,
    0, -12, -1, -3, 2, -17, -2, 3,
    0, 4, -8, -4, -9, -8, -10, 0,
    0, -14, 0, 14, 0, 0, -1, 0,
    0, 0, -1, -1, -2, -6, -8, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 1, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -2, 0, -1, -2, -4, 0,
    0, -5, 0, -2, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -1, 0, -5, 0, 0, 5,
    -1, 3, 0, -5, 2, -2, -1, -6,
    -2, 0, -3, -2, -2, 0, -4, -4,
    0, 0, -2, -1, -2, -4, -3, 0,
    0, -2, 0, 2, -2, 0, -5, 0,
    0, 0, -5, 0, -4, 0, -4, -4,
    0, 0, 0, 0, 0, 0, 0, 0,
    -5, 2, 0, -3, 0, -2, -3, -7,
    -2, -2, -2, -1, -2, -3, -1, 0,
    0, 0, 0, 0, -2, -2, -2, 0,
    0, 0, 0, 3, -2, 0, -2, 0,
    0, 0, -2, -3, -2, -2, -3, -2,
    2, 10, -1, 0, -6, 0, -2, 5,
    0, -2, -10, -3, 4, 0, 0, -11,
    -4, 2, -4, 2, 0, -2, -2, -8,
    0, -4, 1, 0, 0, -4, 0, 0,
    0, 2, 2, -5, -5, 0, -4, -2,
    -4, -2, -2, 0, -4, 1, -5, -4,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 2, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -4, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -2, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -2, -2, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -4,
    0, 0, -3, 0, 0, -2, -2, 0,
    0, 0, 0, -2, 0, 0, 0, 0,
    -1, 0, 0, 0, 0, 0, -2, 0,
    0, 0, -4, 0, -5, 0, 0, 0,
    -8, 0, 2, -5, 5, 0, -2, -11,
    0, 0, -5, -2, 0, -10, -6, -7,
    0, 0, -10, -2, -10, -9, -12, 0,
    -6, 0, 2, 16, -3, 0, -6, -2,
    -1, -2, -4, -6, -4, -9, -10, -6,
    0, 0, -2, 0, 1, 0, 0, -17,
    -2, 7, 5, -5, -9, 0, 1, -7,
    0, -12, -2, -2, 5, -22, -3, 1,
    0, 0, -16, -3, -12, -2, -18, 0,
    0, -17, 0, 14, 1, 0, -2, 0,
    0, 0, 0, -1, -2, -9, -2, 0,
    0, 0, 0, 0, -8, 0, -2, 0,
    -1, -7, -11, 0, 0, -1, -4, -7,
    -2, 0, -2, 0, 0, 0, 0, -11,
    -2, -8, -8, -2, -4, -6, -2, -4,
    0, -5, -2, -8, -4, 0, -3, -5,
    -2, -5, 0, 1, 0, -2, -8, 0,
    0, -4, 0, 0, 0, 0, 3, 0,
    2, -5, 10, 0, -2, -2, -3, 0,
    0, 0, 0, 0, 0, -7, 0, -2,
    0, -4, -2, 0, -5, -6, -7, -2,
    0, -5, 2, 10, 0, 0, 0, 0,
    19, 0, 0, 1, 0, 0, -3, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -2, -5,
    0, 0, 0, 0, 0, -1, 0, 0,
    0, -2, -2, 0, 0, -5, -2, 0,
    0, -5, 0, 4, -1, 0, 0, 0,
    0, 0, 0, 1, 0, 0, 0, 0,
    5, 2, -2, 0, -8, -4, 0, 7,
    -8, -8, -5, -5, 10, 4, 2, -21,
    -2, 5, -2, 0, -2, 3, -2, -8,
    0, -2, 2, -3, -2, -7, -2, 0,
    0, 7, 5, 0, -7, 0, -13, -3,
    7, -3, -9, 1, -3, -8, -8, -2,
    2, 0, -4, 0, -6, 0, 2, 8,
    -6, -9, -10, -6, 7, 0, 1, -18,
    -2, 2, -4, -2, -6, 0, -5, -9,
    -4, -4, -2, 0, 0, -6, -5, -2,
    0, 7, 6, -2, -13, 0, -13, -3,
    0, -8, -14, -1, -8, -4, -8, -7,
    0, 0, -3, 0, -5, -2, 0, -2,
    -4, 0, 4, -8, 2, 0, 0, -13,
    0, -2, -5, -4, -2, -7, -6, -8,
    -6, 0, -7, -2, -6, -5, -7, -2,
    0, 0, 1, 11, -4, 0, -7, -2,
    0, -2, -5, -6, -6, -7, -9, -3,
    5, 0, -4, 0, -12, -3, 1, 5,
    -8, -9, -5, -8, 8, -2, 1, -22,
    -4, 5, -5, -4, -9, 0, -7, -10,
    -3, -2, -2, -2, -5, -7, -1, 0,
    0, 7, 7, -2, -16, 0, -14, -6,
    6, -9, -16, -5, -8, -10, -12, -8,
    0, 0, 0, 0, -3, 0, 0, 2,
    -3, 5, 2, -5, 5, 0, 0, -7,
    -1, 0, -1, 0, 1, 1, -2, 0,
    0, 0, 0, 0, 0, -2, 0, 0,
    0, 0, 2, 7, 0, 0, -3, 0,
    0, 0, 0, -2, -2, -3, 0, 0,
    1, 2, 0, 0, 0, 0, 2, 0,
    -2, 0, 9, 0, 4, 1, 1, -3,
    0, 5, 0, 0, 0, 2, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 7, 0, 7, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -14, 0, -2, 4, 0, 7, 0,
    0, 24, 3, -5, -5, 2, 2, -2,
    1, -12, 0, 0, 12, -14, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -16, 9, 34, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -4, 0, 0, -5, -2, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -2, 0, -6, 0, 0, 1, 0,
    0, 2, 31, -5, -2, 8, 6, -6,
    2, 0, 0, 2, 2, -3, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -31, 7, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -7, 0, 0, 0, -6,
    0, 0, 0, 0, -5, -1, 0, 0,
    0, -5, 0, -3, 0, -11, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -16, 0, 0, 0, 0, 1, 0,
    0, 0, 0, 0, 0, -2, 0, 0,
    0, -4, 0, -6, 0, 0, 0, -4,
    2, -3, 0, 0, -6, -2, -6, 0,
    0, -6, 0, -2, 0, -11, 0, -3,
    0, 0, -19, -5, -10, -3, -9, 0,
    0, -16, 0, -6, -1, 0, 0, 0,
    0, 0, 0, 0, 0, -4, -4, -2,
    0, 0, 0, 0, -5, 0, -5, 3,
    -3, 5, 0, -2, -6, -2, -4, -5,
    0, -3, -1, -2, 2, -6, -1, 0,
    0, 0, -21, -2, -3, 0, -5, 0,
    -2, -11, -2, 0, 0, -2, -2, 0,
    0, 0, 0, 2, 0, -2, -4, -2,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 3, 0, 0, 0, 0,
    0, -5, 0, -2, 0, 0, 0, -5,
    2, 0, 0, 0, -6, -2, -5, 0,
    0, -7, 0, -2, 0, -11, 0, 0,
    0, 0, -23, 0, -5, -9, -12, 0,
    0, -16, 0, -2, -4, 0, 0, 0,
    0, 0, 0, 0, 0, -2, -4, -1,
    1, 0, 0, 4, -3, 0, 7, 12,
    -2, -2, -7, 3, 12, 4, 5, -6,
    3, 10, 3, 7, 5, 6, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 15, 11, -4, -2, 0, -2, 19,
    10, 19, 0, 0, 0, 2, 0, 0,
    0, 0, -4, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -2, 0, 0, 0,
    0, 0, 0, 0, 0, 3, 0, 0,
    0, 0, -20, -3, -2, -10, -12, 0,
    0, -16, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -4, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -2, 0, 0, 0,
    0, 0, 0, 0, 0, 3, 0, 0,
    0, 0, -20, -3, -2, -10, -12, 0,
    0, -10, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -2, 0, 0, 0,
    -6, 2, 0, -2, 2, 4, 2, -7,
    0, 0, -2, 2, 0, 2, 0, 0,
    0, 0, -6, 0, -2, -2, -5, 0,
    -2, -10, 0, 15, -2, 0, -5, -2,
    0, -2, -4, 0, -2, -7, -5, -3,
    0, 0, -4, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -2, 0, 0, 0,
    0, 0, 0, 0, 0, 3, 0, 0,
    0, 0, -20, -3, -2, -10, -12, 0,
    0, -16, 0, 0, 0, 0, 0, 0,
    12, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -4, 0, -8, -3, -2, 7,
    -2, -2, -10, 1, -1, 1, -2, -6,
    0, 5, 0, 2, 1, 2, -6, -10,
    -3, 0, -9, -5, -6, -10, -9, 0,
    -4, -5, -3, -3, -2, -2, -3, -2,
    0, -2, -1, 4, 0, 4, -2, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -2, -2, -2, 0,
    0, -6, 0, -1, 0, -4, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -14, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -2, -2, 0,
    0, 0, 0, 0, -2, 0, 0, -4,
    -2, 2, 0, -4, -5, -2, 0, -7,
    -2, -5, -2, -3, 0, -4, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -16, 0, 8, 0, 0, -4, 0,
    0, 0, 0, -3, 0, -2, 0, 0,
    0, 0, -2, 0, -6, 0, 0, 10,
    -3, -8, -7, 2, 3, 3, 0, -7,
    2, 4, 2, 7, 2, 8, -2, -6,
    0, 0, -10, 0, 0, -7, -6, 0,
    0, -5, 0, -3, -4, 0, -4, 0,
    -4, 0, -2, 4, 0, -2, -7, -2,
    0, 0, -2, 0, -5, 0, 0, 3,
    -6, 0, 2, -2, 2, 0, 0, -8,
    0, -2, -1, 0, -2, 3, -2, 0,
    0, 0, -10, -3, -5, 0, -7, 0,
    0, -11, 0, 9, -2, 0, -4, 0,
    1, 0, -2, 0, -2, -7, 0, -2,
    0, 0, 0, 0, -2, 0, 0, 2,
    -3, 1, 0, 0, -3, -2, 0, -3,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -15, 0, 5, 0, 0, -2, 0,
    0, 0, 0, 0, 0, -2, -2, 0
};


/*Collect the kern class' data in one place*/
static const lv_font_fmt_txt_kern_classes_t kern_classes =
{
    .class_pair_values   = kern_class_values,
    .left_class_mapping  = kern_left_class_mapping,
    .right_class_mapping = kern_right_class_mapping,
    .left_class_cnt      = 60,
    .right_class_cnt     = 48,
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LVGL_VERSION_MAJOR == 8
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
#endif

#if LVGL_VERSION_MAJOR >= 8
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_classes,
    .kern_scale = 16,
    .cmap_num = 2,
    .bpp = 4,
    .kern_classes = 1,
    .bitmap_format = 0,
#if LVGL_VERSION_MAJOR == 8
    .cache = &cache
#endif
};



/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LVGL_VERSION_MAJOR >= 8
const lv_font_t lv_font_montserratMedium_15 = {
#else
lv_font_t lv_font_montserratMedium_15 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 15,          /*The maximum line height required by the font*/
    .base_line = 2,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -1,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc,          /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
#if LV_VERSION_CHECK(8, 2, 0) || LVGL_VERSION_MAJOR >= 9
    .fallback = NULL,
#endif
    .user_data = NULL,
};



#endif /*#if LV_FONT_MONTSERRATMEDIUM_15*/

