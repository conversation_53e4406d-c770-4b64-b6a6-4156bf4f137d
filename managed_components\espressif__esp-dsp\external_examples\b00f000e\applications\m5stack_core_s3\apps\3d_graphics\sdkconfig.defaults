# This file was generated using idf.py save-defconfig. It can be edited manually.
# Espressif IoT Development Framework (ESP-IDF) 5.5.0 Project Minimal Configuration
#
CONFIG_IDF_TARGET="esp32s3"
CONFIG_COMPILER_OPTIMIZATION_PERF=y
CONFIG_I2C_ENABLE_DEBUG_LOG=y
CONFIG_I2C_ENABLE_SLAVE_DRIVER_VERSION_2=y
CONFIG_HTTPD_MAX_REQ_HDR_LEN=512
CONFIG_SPIRAM=y
CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ_240=y
CONFIG_ESP_MAIN_TASK_STACK_SIZE=4096
CONFIG_ESP_MINIMAL_SHARED_STACK_SIZE=4096
CONFIG_ESP_INT_WDT=n
CONFIG_ESP_TASK_WDT_EN=n
CONFIG_FREERTOS_IDLE_TASK_STACKSIZE=3084
CONFIG_CODEC_I2C_BACKWARD_COMPATIBLE=n
CONFIG_LV_CONF_MINIMAL=y
CONFIG_LV_USE_ASSERT_NULL=y
CONFIG_LV_USE_ASSERT_MALLOC=y
CONFIG_LV_FONT_UNSCII_8=n
CONFIG_LV_FONT_DEFAULT_MONTSERRAT_14=y
CONFIG_LV_TXT_ENC_UTF8=y
CONFIG_LV_WIDGETS_HAS_DEFAULT_VALUE=y
CONFIG_LV_USE_ANIMIMG=y
CONFIG_LV_USE_ARC=y
CONFIG_LV_USE_BUTTON=y
CONFIG_LV_USE_BUTTONMATRIX=y
CONFIG_LV_USE_CALENDAR=y
CONFIG_LV_USE_CANVAS=y
CONFIG_LV_USE_CHART=y
CONFIG_LV_USE_CHECKBOX=y
CONFIG_LV_USE_DROPDOWN=y
CONFIG_LV_USE_IMAGEBUTTON=y
CONFIG_LV_USE_KEYBOARD=y
CONFIG_LV_USE_LED=y
CONFIG_LV_USE_LINE=y
CONFIG_LV_USE_LIST=y
CONFIG_LV_USE_MENU=y
CONFIG_LV_USE_MSGBOX=y
CONFIG_LV_USE_ROLLER=y
CONFIG_LV_USE_SCALE=y
CONFIG_LV_USE_SLIDER=y
CONFIG_LV_USE_SPAN=y
CONFIG_LV_USE_SPINBOX=y
CONFIG_LV_USE_SPINNER=y
CONFIG_LV_USE_SWITCH=y
CONFIG_LV_USE_TEXTAREA=y
CONFIG_LV_USE_TABLE=y
CONFIG_LV_USE_TABVIEW=y
CONFIG_LV_USE_TILEVIEW=y
CONFIG_LV_USE_WIN=y
CONFIG_LV_USE_THEME_DEFAULT=y
CONFIG_LV_USE_THEME_SIMPLE=y
CONFIG_LV_USE_FLEX=y
CONFIG_LV_USE_GRID=y
CONFIG_LV_BUILD_EXAMPLES=y
