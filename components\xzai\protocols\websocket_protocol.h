#ifndef _WEBSOCKET_PROTOCOL_H
#define _WEBSOCKET_PROTOCOL_H

#include "protocol.h"
#include <freertos/FreeRTOS.h>
#include <freertos/event_groups.h>
#include "websocket_client.h"

#define WEBSOCKET_SERVER_HELLO_EVENT BIT1

struct ws_protocol;

typedef struct
{
    const char* ws_url;
    const char* ws_token;
}ws_protocol_cfg_t;

typedef struct ws_protocol
{
    protocol_base_t base;

    EventGroupHandle_t event_group_handle;
    ws_client_t client;

    const char *ws_url;
    const char *ws_token;

} ws_protocol_t;

ws_protocol_t *ws_protocol_create(ws_protocol_cfg_t *cfg);
void ws_protocol_destroy(struct ws_protocol *protocol);

#endif

