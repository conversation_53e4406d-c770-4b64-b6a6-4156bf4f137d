/*
* Copyright (c) 2011-12, vernon adams (<EMAIL>), with Reserved Font Names '<PERSON>'.
* This Font Software is licensed under the SIL Open Font License, Version 1.1.
* And is also available with a FAQ at: http://scripts.sil.org/OFL
*/
/*******************************************************************************
 * Size: 15 px
 * Bpp: 4
 * Opts: undefined
 ******************************************************************************/

#ifdef LV_LVGL_H_INCLUDE_SIMPLE
#include "lvgl.h"
#else
#include "lvgl.h"
#endif

#ifndef LV_FONT_ANTONIO_REGULAR_15
#define LV_FONT_ANTONIO_REGULAR_15 1
#endif

#if LV_FONT_ANTONIO_REGULAR_15

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0xec, 0xdb, 0xca, 0xb9, 0xa8, 0xa7, 0x96, 0x85,
    0x74, 0x53, 0x0, 0x97, 0xeb,

    /* U+0022 "\"" */
    0x5f, 0xf, 0x53, 0xe0, 0xe3, 0x2c, 0xd, 0x10,
    0xb0, 0xb0, 0x5, 0x5, 0x0,

    /* U+0023 "#" */
    0x0, 0x2f, 0x17, 0xd0, 0x0, 0x5e, 0x9, 0xa0,
    0x0, 0x8b, 0xc, 0x70, 0x0, 0xb8, 0xf, 0x40,
    0x0, 0xd6, 0x2f, 0x20, 0x6f, 0xff, 0xff, 0xf1,
    0x5, 0xe0, 0x9a, 0x0, 0xcf, 0xff, 0xff, 0xa0,
    0xd, 0x61, 0xf2, 0x0, 0xf, 0x34, 0xf0, 0x0,
    0x3f, 0x7, 0xc0, 0x0, 0x6d, 0xa, 0x90, 0x0,
    0x8b, 0xd, 0x60, 0x0,

    /* U+0024 "$" */
    0x0, 0x68, 0x0, 0x0, 0x89, 0x0, 0xc, 0xff,
    0xa0, 0x3f, 0x46, 0xf1, 0x5f, 0x22, 0xf3, 0x2f,
    0x71, 0xb3, 0xb, 0xf4, 0x0, 0x1, 0xdf, 0x30,
    0x0, 0x1d, 0xd0, 0x3d, 0x24, 0xf4, 0x3f, 0x31,
    0xf6, 0x1f, 0x73, 0xf5, 0x9, 0xff, 0xd0, 0x0,
    0x6b, 0x0, 0x0, 0x4a, 0x0,

    /* U+0025 "%" */
    0x2d, 0xfb, 0x10, 0x0, 0x3f, 0x0, 0x0, 0xab,
    0x1d, 0x80, 0x0, 0xaa, 0x0, 0x0, 0xd8, 0xa,
    0xa0, 0x1, 0xf3, 0x0, 0x0, 0xd8, 0xa, 0xa0,
    0x6, 0xd0, 0x0, 0x0, 0xd8, 0xa, 0xb0, 0xd,
    0x71, 0xcf, 0xc2, 0xd8, 0xa, 0xa0, 0x3f, 0x18,
    0xd3, 0xca, 0xd8, 0xa, 0xa0, 0xaa, 0xb, 0xa0,
    0x8c, 0xba, 0xd, 0x81, 0xf4, 0xb, 0xa0, 0x8c,
    0x4f, 0xfe, 0x26, 0xd0, 0xb, 0xa0, 0x8d, 0x0,
    0x20, 0xd, 0x70, 0xb, 0xa0, 0x8c, 0x0, 0x0,
    0x3f, 0x10, 0xb, 0xa0, 0x8c, 0x0, 0x0, 0x9a,
    0x0, 0x8, 0xc1, 0xb9, 0x0, 0x0, 0xf4, 0x0,
    0x1, 0xcf, 0xc2,

    /* U+0026 "&" */
    0x0, 0xaf, 0xd2, 0x0, 0x6, 0xf2, 0x9a, 0x0,
    0x8, 0xd0, 0x6b, 0x0, 0x6, 0xf0, 0xa9, 0x0,
    0x1, 0xf8, 0xf3, 0x0, 0x0, 0xbf, 0x90, 0x0,
    0x0, 0xcf, 0x20, 0x0, 0x8, 0xfe, 0x80, 0xb0,
    0x1f, 0x66, 0xf9, 0xc0, 0x6f, 0x0, 0xef, 0x30,
    0x7e, 0x0, 0xae, 0x0, 0x4f, 0x45, 0xff, 0x50,
    0x9, 0xfe, 0x68, 0xc0,

    /* U+0027 "'" */
    0x2f, 0x30, 0xf1, 0xe, 0x0, 0xb0, 0x5, 0x0,

    /* U+0028 "(" */
    0x5, 0xe7, 0xe, 0xb1, 0xf, 0x70, 0xf, 0x70,
    0xf, 0x70, 0xf, 0x70, 0xf, 0x70, 0xf, 0x70,
    0xf, 0x70, 0xf, 0x70, 0xf, 0x70, 0xf, 0x70,
    0xe, 0x90, 0x7, 0xf7, 0x0, 0x1,

    /* U+0029 ")" */
    0x5f, 0x70, 0x8, 0xf0, 0x5, 0xf2, 0x5, 0xf3,
    0x5, 0xf3, 0x5, 0xf3, 0x5, 0xf3, 0x5, 0xf3,
    0x5, 0xf3, 0x5, 0xf3, 0x5, 0xf3, 0x5, 0xf2,
    0x7, 0xf1, 0x5f, 0x90, 0x1, 0x0,

    /* U+002A "*" */
    0x0, 0xf, 0x1, 0x0, 0xd7, 0xe7, 0xe0, 0x1,
    0xbf, 0xc1, 0x0, 0xbc, 0xec, 0xb0, 0x4, 0xf,
    0x5, 0x0, 0x0, 0x50, 0x0,

    /* U+002B "+" */
    0x0, 0x54, 0x0, 0x0, 0x97, 0x0, 0x3e, 0xff,
    0xe2, 0x1, 0xa8, 0x10, 0x0, 0x97, 0x0,

    /* U+002C "," */
    0x1b, 0x82, 0xfc, 0x5, 0x90, 0x84, 0x5, 0x0,

    /* U+002D "-" */
    0x5f, 0xff,

    /* U+002E "." */
    0x53, 0xc8,

    /* U+002F "/" */
    0x0, 0x4, 0xf1, 0x0, 0x8, 0xd0, 0x0, 0xc,
    0x90, 0x0, 0xf, 0x50, 0x0, 0x4f, 0x10, 0x0,
    0x8d, 0x0, 0x0, 0xc9, 0x0, 0x0, 0xf5, 0x0,
    0x3, 0xf1, 0x0, 0x7, 0xd0, 0x0, 0xb, 0x90,
    0x0, 0xf, 0x50, 0x0, 0x3f, 0x10, 0x0,

    /* U+0030 "0" */
    0x5, 0xdf, 0xc2, 0xf, 0xb5, 0xea, 0x3f, 0x40,
    0xad, 0x4f, 0x40, 0x9e, 0x4f, 0x40, 0x9f, 0x4f,
    0x40, 0x9f, 0x4f, 0x40, 0x9f, 0x4f, 0x40, 0x9f,
    0x4f, 0x40, 0x9f, 0x4f, 0x40, 0x9e, 0x2f, 0x50,
    0xad, 0xe, 0xb5, 0xe9, 0x4, 0xdf, 0xc2,

    /* U+0031 "1" */
    0x1, 0xe7, 0x4d, 0xf7, 0x66, 0xf7, 0x1, 0xf7,
    0x1, 0xf7, 0x1, 0xf7, 0x1, 0xf7, 0x1, 0xf7,
    0x1, 0xf7, 0x1, 0xf7, 0x1, 0xf7, 0x1, 0xf7,
    0x1, 0xf7,

    /* U+0032 "2" */
    0x1c, 0xfb, 0x8, 0xf5, 0xf6, 0xbb, 0xb, 0xac,
    0xa0, 0xbb, 0xca, 0xc, 0xa2, 0x20, 0xf6, 0x0,
    0x7f, 0x10, 0x1e, 0x90, 0x8, 0xf1, 0x0, 0xe8,
    0x0, 0x5f, 0x20, 0x9, 0xd2, 0x21, 0xbf, 0xff,
    0x90,

    /* U+0033 "3" */
    0x3, 0xdf, 0xb1, 0xd, 0xc6, 0xf8, 0xf, 0x60,
    0xbc, 0x1e, 0x50, 0x9d, 0x0, 0x0, 0xab, 0x0,
    0x1, 0xe5, 0x0, 0x6f, 0xe2, 0x0, 0x15, 0xf9,
    0x0, 0x0, 0xbd, 0x2e, 0x40, 0x9e, 0x1f, 0x50,
    0xac, 0xe, 0xb5, 0xe8, 0x4, 0xdf, 0xa1,

    /* U+0034 "4" */
    0x0, 0x9, 0xf2, 0x0, 0x0, 0xef, 0x20, 0x0,
    0x3f, 0xf2, 0x0, 0x8, 0xbf, 0x20, 0x0, 0xd7,
    0xf2, 0x0, 0x2e, 0x4f, 0x20, 0x7, 0xa4, 0xf2,
    0x0, 0xc5, 0x4f, 0x20, 0x1f, 0x14, 0xf2, 0x4,
    0xff, 0xff, 0xf3, 0x3, 0x36, 0xf4, 0x0, 0x0,
    0x4f, 0x20, 0x0, 0x4, 0xf2, 0x0,

    /* U+0035 "5" */
    0xf, 0xff, 0xf2, 0xf, 0xa5, 0x50, 0xf, 0x70,
    0x0, 0xf, 0x70, 0x0, 0xf, 0xdf, 0xb0, 0xf,
    0xb6, 0xf2, 0x7, 0x30, 0xf5, 0x0, 0x0, 0xf6,
    0x0, 0x0, 0xf6, 0xd, 0x50, 0xf6, 0xf, 0x60,
    0xf5, 0xe, 0xb7, 0xf2, 0x5, 0xef, 0x90,

    /* U+0036 "6" */
    0x4, 0xdf, 0xb0, 0x0, 0xec, 0x5f, 0x70, 0x2f,
    0x50, 0xca, 0x3, 0xf4, 0xa, 0x90, 0x3f, 0x40,
    0x0, 0x3, 0xfc, 0xfe, 0x30, 0x3f, 0xa4, 0xdc,
    0x3, 0xf4, 0x8, 0xf0, 0x3f, 0x40, 0x7f, 0x3,
    0xf4, 0x7, 0xf0, 0x1f, 0x60, 0x8e, 0x0, 0xdc,
    0x5d, 0xb0, 0x3, 0xdf, 0xc2, 0x0,

    /* U+0037 "7" */
    0x2f, 0xff, 0xff, 0x4, 0x44, 0xbd, 0x0, 0x0,
    0xe8, 0x0, 0x4, 0xf3, 0x0, 0x9, 0xe0, 0x0,
    0xd, 0xa0, 0x0, 0x2f, 0x60, 0x0, 0x6f, 0x20,
    0x0, 0x9e, 0x0, 0x0, 0xcc, 0x0, 0x0, 0xfa,
    0x0, 0x1, 0xf8, 0x0, 0x3, 0xf6, 0x0,

    /* U+0038 "8" */
    0x5, 0xdf, 0xb1, 0xe, 0xb4, 0xf8, 0x1f, 0x60,
    0xcb, 0x1f, 0x60, 0xcb, 0xe, 0x90, 0xf7, 0x5,
    0xff, 0xe0, 0xc, 0xc4, 0xf5, 0x1f, 0x60, 0xd9,
    0x3f, 0x40, 0xcb, 0x3f, 0x40, 0xbc, 0x2f, 0x50,
    0xca, 0xe, 0xb5, 0xf7, 0x4, 0xdf, 0xa0,

    /* U+0039 "9" */
    0x8, 0xee, 0x70, 0x3f, 0x89, 0xf2, 0x7f, 0x2,
    0xf5, 0x8f, 0x1, 0xf6, 0x8f, 0x1, 0xf7, 0x7f,
    0x1, 0xf7, 0x4f, 0x42, 0xf7, 0xb, 0xff, 0xf7,
    0x0, 0x12, 0xf7, 0x3c, 0x21, 0xf6, 0x2f, 0x31,
    0xf5, 0xf, 0x97, 0xf2, 0x6, 0xee, 0x70,

    /* U+003A ":" */
    0xbc, 0xef, 0x0, 0x0, 0x0, 0x0, 0xbc, 0xef,

    /* U+003B ";" */
    0xf, 0xf0, 0xf, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xd1, 0xf, 0xf2, 0x1, 0xf0,
    0x4, 0xb0, 0x8, 0x60,

    /* U+003C "<" */
    0x0, 0x3, 0x80, 0x8, 0xf8, 0x3d, 0xe5, 0x8,
    0xd1, 0x0, 0x4e, 0xd3, 0x0, 0x1a, 0xf7, 0x0,
    0x4, 0x90, 0x0, 0x0,

    /* U+003D "=" */
    0x6f, 0xff, 0xe1, 0x22, 0x22, 0x6f, 0xff, 0xe1,
    0x22, 0x22,

    /* U+003E ">" */
    0x46, 0x0, 0x3, 0xfc, 0x20, 0x2, 0xbf, 0x60,
    0x0, 0x9d, 0x1, 0x9f, 0x83, 0xed, 0x30, 0x58,
    0x0, 0x0, 0x0, 0x0,

    /* U+003F "?" */
    0x4e, 0xe8, 0xc, 0xc9, 0xf1, 0xf8, 0x4f, 0x3f,
    0x84, 0xf3, 0x84, 0x4f, 0x30, 0x7, 0xf1, 0x3,
    0xeb, 0x3, 0xf9, 0x10, 0x4f, 0x10, 0x1, 0x60,
    0x0, 0x2, 0x0, 0x3, 0xf1, 0x0, 0x3f, 0x10,
    0x0,

    /* U+0040 "@" */
    0x0, 0x19, 0xef, 0xd7, 0x0, 0x0, 0xdb, 0x31,
    0x3c, 0x70, 0x8, 0xc0, 0x0, 0x1, 0xe0, 0xe,
    0x42, 0xdc, 0x85, 0xa5, 0x1f, 0xa, 0x84, 0xd5,
    0x87, 0x4e, 0xe, 0x10, 0xa5, 0x69, 0x4d, 0xf,
    0x0, 0xa5, 0x69, 0x4e, 0xe, 0x10, 0xb5, 0x78,
    0x3f, 0xb, 0x51, 0xd7, 0xa5, 0xf, 0x23, 0xee,
    0x3e, 0xd0, 0xa, 0x80, 0x0, 0x0, 0x0, 0x2,
    0xe7, 0x10, 0x10, 0x0, 0x0, 0x2b, 0xff, 0x80,
    0x0,

    /* U+0041 "A" */
    0x0, 0xaf, 0x50, 0x0, 0xc, 0xf8, 0x0, 0x0,
    0xfe, 0xa0, 0x0, 0x1f, 0xad, 0x0, 0x3, 0xf5,
    0xf0, 0x0, 0x6e, 0x2f, 0x20, 0x8, 0xc0, 0xf5,
    0x0, 0xba, 0xd, 0x70, 0xd, 0x80, 0xaa, 0x0,
    0xff, 0xff, 0xd0, 0x2f, 0x53, 0x7f, 0x4, 0xf1,
    0x3, 0xf2, 0x7f, 0x0, 0x1f, 0x40,

    /* U+0042 "B" */
    0xdf, 0xfd, 0x30, 0xdc, 0x4d, 0xc0, 0xdb, 0x8,
    0xf0, 0xdb, 0x7, 0xf0, 0xdb, 0x8, 0xf0, 0xdb,
    0xa, 0xc0, 0xdf, 0xff, 0x50, 0xdb, 0x2a, 0xe0,
    0xdb, 0x5, 0xf2, 0xdb, 0x4, 0xf3, 0xdb, 0x5,
    0xf2, 0xdc, 0x3b, 0xe0, 0xdf, 0xfd, 0x50,

    /* U+0043 "C" */
    0x2, 0xcf, 0xc1, 0xc, 0xd5, 0xe9, 0xf, 0x80,
    0xad, 0x1f, 0x60, 0x9e, 0x1f, 0x60, 0x9e, 0x1f,
    0x60, 0x0, 0x1f, 0x60, 0x0, 0x1f, 0x60, 0x24,
    0x1f, 0x60, 0x9f, 0xf, 0x60, 0x9e, 0xf, 0x80,
    0xad, 0xb, 0xd5, 0xe9, 0x2, 0xcf, 0xb1,

    /* U+0044 "D" */
    0xff, 0xfc, 0x20, 0xfa, 0x3e, 0xb0, 0xf9, 0x8,
    0xf0, 0xf9, 0x7, 0xf0, 0xf9, 0x7, 0xf1, 0xf9,
    0x7, 0xf1, 0xf9, 0x7, 0xf1, 0xf9, 0x7, 0xf1,
    0xf9, 0x7, 0xf1, 0xf9, 0x7, 0xf1, 0xf9, 0x8,
    0xf0, 0xfa, 0x4d, 0xc0, 0xff, 0xfc, 0x20,

    /* U+0045 "E" */
    0xff, 0xfe, 0xfa, 0x43, 0xf9, 0x0, 0xf9, 0x0,
    0xf9, 0x0, 0xf9, 0x0, 0xff, 0xfc, 0xfa, 0x32,
    0xf9, 0x0, 0xf9, 0x0, 0xf9, 0x0, 0xfa, 0x43,
    0xff, 0xff,

    /* U+0046 "F" */
    0xff, 0xfe, 0xfa, 0x43, 0xf9, 0x0, 0xf9, 0x0,
    0xf9, 0x0, 0xf9, 0x0, 0xff, 0xfc, 0xfa, 0x32,
    0xf9, 0x0, 0xf9, 0x0, 0xf9, 0x0, 0xf9, 0x0,
    0xf9, 0x0,

    /* U+0047 "G" */
    0x2, 0xcf, 0xc2, 0xb, 0xd5, 0xdb, 0xf, 0x80,
    0x8e, 0x1f, 0x60, 0x8f, 0x1f, 0x60, 0x59, 0x1f,
    0x60, 0x0, 0x1f, 0x67, 0xff, 0x1f, 0x61, 0x8f,
    0x1f, 0x60, 0x7f, 0x1f, 0x60, 0x7f, 0xf, 0x70,
    0x8f, 0xc, 0xc3, 0xdf, 0x2, 0xce, 0x8e,

    /* U+0048 "H" */
    0xf9, 0x5, 0xf2, 0xf9, 0x5, 0xf2, 0xf9, 0x5,
    0xf2, 0xf9, 0x5, 0xf2, 0xf9, 0x5, 0xf2, 0xf9,
    0x5, 0xf2, 0xff, 0xff, 0xf2, 0xfa, 0x37, 0xf2,
    0xf9, 0x5, 0xf2, 0xf9, 0x5, 0xf2, 0xf9, 0x5,
    0xf2, 0xf9, 0x5, 0xf2, 0xf9, 0x5, 0xf2,

    /* U+0049 "I" */
    0xdb, 0xdb, 0xdb, 0xdb, 0xdb, 0xdb, 0xdb, 0xdb,
    0xdb, 0xdb, 0xdb, 0xdb, 0xdb,

    /* U+004A "J" */
    0x0, 0x4, 0xf4, 0x0, 0x4, 0xf4, 0x0, 0x4,
    0xf4, 0x0, 0x4, 0xf4, 0x0, 0x4, 0xf4, 0x0,
    0x4, 0xf4, 0x0, 0x4, 0xf4, 0x0, 0x4, 0xf4,
    0x4a, 0x4, 0xf4, 0x6f, 0x14, 0xf4, 0x5f, 0x14,
    0xf3, 0x2f, 0x8a, 0xe0, 0x7, 0xee, 0x50,

    /* U+004B "K" */
    0xf9, 0x5, 0xf3, 0xf9, 0xb, 0xd0, 0xf9, 0x1f,
    0x70, 0xf9, 0x6f, 0x10, 0xf9, 0xcb, 0x0, 0xfb,
    0xf5, 0x0, 0xff, 0xf1, 0x0, 0xfb, 0xf6, 0x0,
    0xf9, 0xcc, 0x0, 0xf9, 0x6f, 0x30, 0xf9, 0xf,
    0x90, 0xf9, 0x9, 0xe0, 0xf9, 0x3, 0xf5,

    /* U+004C "L" */
    0xf9, 0x0, 0xf9, 0x0, 0xf9, 0x0, 0xf9, 0x0,
    0xf9, 0x0, 0xf9, 0x0, 0xf9, 0x0, 0xf9, 0x0,
    0xf9, 0x0, 0xf9, 0x0, 0xf9, 0x0, 0xfa, 0x33,
    0xff, 0xff,

    /* U+004D "M" */
    0xff, 0x0, 0x8, 0xf7, 0xff, 0x30, 0xb, 0xf7,
    0xff, 0x50, 0xd, 0xf7, 0xfe, 0x80, 0xf, 0xe7,
    0xfc, 0xb0, 0x3f, 0xd7, 0xf9, 0xe0, 0x5c, 0xd7,
    0xf7, 0xf1, 0x89, 0xe7, 0xf6, 0xe3, 0xa6, 0xe7,
    0xf6, 0xb6, 0xd3, 0xe7, 0xf6, 0x89, 0xf1, 0xe7,
    0xf6, 0x5e, 0xe0, 0xe7, 0xf6, 0x2f, 0xb0, 0xe7,
    0xf6, 0xf, 0x80, 0xe7,

    /* U+004E "N" */
    0xf5, 0x0, 0xf6, 0xfb, 0x0, 0xf6, 0xff, 0x10,
    0xf6, 0xff, 0x60, 0xf6, 0xfd, 0xb0, 0xf6, 0xf9,
    0xf1, 0xf6, 0xf6, 0xd6, 0xe6, 0xf7, 0x8b, 0xd6,
    0xf7, 0x3f, 0xe6, 0xf7, 0xd, 0xf6, 0xf7, 0x8,
    0xf6, 0xf7, 0x3, 0xf6, 0xf7, 0x0, 0xe6,

    /* U+004F "O" */
    0x2, 0xcf, 0xc2, 0xc, 0xd5, 0xeb, 0xf, 0x80,
    0x9e, 0x1f, 0x60, 0x8f, 0x1f, 0x60, 0x8f, 0x1f,
    0x60, 0x8f, 0x1f, 0x60, 0x8f, 0x1f, 0x60, 0x8f,
    0x1f, 0x60, 0x8f, 0x1f, 0x60, 0x8f, 0xf, 0x70,
    0x9e, 0xc, 0xd5, 0xea, 0x2, 0xcf, 0xc2,

    /* U+0050 "P" */
    0xff, 0xfd, 0x30, 0xfa, 0x3d, 0xc0, 0xf9, 0x8,
    0xf0, 0xf9, 0x7, 0xf0, 0xf9, 0x7, 0xf1, 0xf9,
    0x8, 0xf0, 0xf9, 0x1c, 0xc0, 0xff, 0xff, 0x40,
    0xfa, 0x31, 0x0, 0xf9, 0x0, 0x0, 0xf9, 0x0,
    0x0, 0xf9, 0x0, 0x0, 0xf9, 0x0, 0x0,

    /* U+0051 "Q" */
    0x2, 0xcf, 0xc2, 0xc, 0xd5, 0xeb, 0xf, 0x80,
    0x9e, 0x1f, 0x60, 0x8f, 0x1f, 0x60, 0x8f, 0x1f,
    0x60, 0x8f, 0x1f, 0x60, 0x8f, 0x1f, 0x60, 0x8f,
    0x1f, 0x60, 0x8f, 0x1f, 0x60, 0x8f, 0xf, 0x70,
    0x9e, 0xc, 0xd4, 0xeb, 0x3, 0xdf, 0xe2, 0x0,
    0x5, 0xe1, 0x0, 0x0, 0x64,

    /* U+0052 "R" */
    0xff, 0xfe, 0x50, 0xfa, 0x3b, 0xd0, 0xf9, 0x7,
    0xf0, 0xf9, 0x7, 0xf0, 0xf9, 0x7, 0xf0, 0xf9,
    0xa, 0xc0, 0xff, 0xff, 0x50, 0xfa, 0x2b, 0xc0,
    0xf9, 0x7, 0xf0, 0xf9, 0x6, 0xf0, 0xf9, 0x6,
    0xf1, 0xf9, 0x6, 0xf1, 0xf9, 0x5, 0xf1,

    /* U+0053 "S" */
    0x5, 0xdf, 0xa0, 0xf, 0xb6, 0xf6, 0x2f, 0x40,
    0xe9, 0x2f, 0x50, 0xd9, 0xe, 0xc0, 0x64, 0x4,
    0xfb, 0x0, 0x0, 0x6f, 0xa0, 0x0, 0x7, 0xf4,
    0x2f, 0x50, 0xda, 0x2f, 0x50, 0xbc, 0x1f, 0x60,
    0xbb, 0xd, 0xc5, 0xf8, 0x4, 0xdf, 0xb1,

    /* U+0054 "T" */
    0xcf, 0xff, 0x73, 0x8f, 0x51, 0x6, 0xf2, 0x0,
    0x6f, 0x20, 0x6, 0xf2, 0x0, 0x6f, 0x20, 0x6,
    0xf2, 0x0, 0x6f, 0x20, 0x6, 0xf2, 0x0, 0x6f,
    0x20, 0x6, 0xf2, 0x0, 0x6f, 0x20, 0x6, 0xf2,
    0x0,

    /* U+0055 "U" */
    0xf, 0x80, 0x7f, 0x10, 0xf8, 0x7, 0xf1, 0xf,
    0x80, 0x7f, 0x10, 0xf8, 0x7, 0xf1, 0xf, 0x80,
    0x7f, 0x10, 0xf8, 0x7, 0xf1, 0xf, 0x80, 0x7f,
    0x10, 0xf8, 0x7, 0xf1, 0xf, 0x80, 0x7f, 0x10,
    0xf8, 0x7, 0xf1, 0xe, 0x80, 0x7f, 0x0, 0xbd,
    0x4c, 0xc0, 0x2, 0xcf, 0xc3, 0x0,

    /* U+0056 "V" */
    0x7f, 0x0, 0x3f, 0x35, 0xf1, 0x5, 0xf1, 0x2f,
    0x40, 0x7e, 0x0, 0xf6, 0x9, 0xc0, 0xd, 0x90,
    0xba, 0x0, 0xab, 0xd, 0x70, 0x7, 0xe0, 0xf5,
    0x0, 0x5f, 0x2f, 0x20, 0x2, 0xf7, 0xf0, 0x0,
    0xf, 0xbd, 0x0, 0x0, 0xdf, 0xb0, 0x0, 0xa,
    0xf9, 0x0, 0x0, 0x8f, 0x60, 0x0,

    /* U+0057 "W" */
    0x7e, 0x0, 0xab, 0x0, 0xf7, 0x5f, 0x0, 0xce,
    0x0, 0xf5, 0x3f, 0x20, 0xef, 0x2, 0xf3, 0x1f,
    0x41, 0xff, 0x33, 0xf1, 0xf, 0x53, 0xec, 0x55,
    0xf0, 0xd, 0x76, 0xc9, 0x76, 0xd0, 0xa, 0x98,
    0x97, 0xa8, 0xb0, 0x8, 0xba, 0x75, 0xc9, 0x90,
    0x6, 0xcd, 0x52, 0xfb, 0x70, 0x4, 0xef, 0x20,
    0xfe, 0x60, 0x2, 0xff, 0x0, 0xef, 0x40, 0x0,
    0xfd, 0x0, 0xbf, 0x20, 0x0, 0xeb, 0x0, 0x9f,
    0x0,

    /* U+0058 "X" */
    0x6e, 0x0, 0xf6, 0x1f, 0x33, 0xf1, 0xc, 0x87,
    0xd0, 0x8, 0xcb, 0x90, 0x3, 0xff, 0x50, 0x0,
    0xef, 0x0, 0x0, 0xdc, 0x0, 0x1, 0xff, 0x0,
    0x6, 0xff, 0x40, 0xa, 0xac, 0x80, 0xe, 0x67,
    0xd0, 0x3f, 0x23, 0xf2, 0x7e, 0x0, 0xe6,

    /* U+0059 "Y" */
    0xbc, 0x0, 0xad, 0x6f, 0x10, 0xe9, 0x1f, 0x52,
    0xf4, 0xd, 0x96, 0xf0, 0x8, 0xea, 0xb0, 0x3,
    0xff, 0x70, 0x0, 0xef, 0x30, 0x0, 0xaf, 0x0,
    0x0, 0x9e, 0x0, 0x0, 0x9e, 0x0, 0x0, 0x9e,
    0x0, 0x0, 0x9e, 0x0, 0x0, 0x9e, 0x0,

    /* U+005A "Z" */
    0xe, 0xff, 0xf1, 0x3, 0x4b, 0xf0, 0x0, 0xd,
    0xb0, 0x0, 0x1f, 0x70, 0x0, 0x5f, 0x30, 0x0,
    0x9f, 0x0, 0x0, 0xdb, 0x0, 0x1, 0xf7, 0x0,
    0x5, 0xf3, 0x0, 0x9, 0xf0, 0x0, 0xd, 0xb0,
    0x0, 0x1f, 0xa4, 0x40, 0x2f, 0xff, 0xf0,

    /* U+005B "[" */
    0x6f, 0xfa, 0x6f, 0x31, 0x6f, 0x10, 0x6f, 0x10,
    0x6f, 0x10, 0x6f, 0x10, 0x6f, 0x10, 0x6f, 0x10,
    0x6f, 0x10, 0x6f, 0x10, 0x6f, 0x10, 0x6f, 0x31,
    0x6f, 0xfa,

    /* U+005C "\\" */
    0xf, 0x40, 0x0, 0xc, 0x80, 0x0, 0x9, 0xb0,
    0x0, 0x5, 0xf0, 0x0, 0x1, 0xf3, 0x0, 0x0,
    0xe7, 0x0, 0x0, 0xaa, 0x0, 0x0, 0x6e, 0x0,
    0x0, 0x2f, 0x20, 0x0, 0xf, 0x50, 0x0, 0xb,
    0x90, 0x0, 0x7, 0xd0, 0x0, 0x4, 0xf1,

    /* U+005D "]" */
    0xcf, 0xf4, 0x15, 0xf4, 0x4, 0xf4, 0x4, 0xf4,
    0x4, 0xf4, 0x4, 0xf4, 0x4, 0xf4, 0x4, 0xf4,
    0x4, 0xf4, 0x4, 0xf4, 0x4, 0xf4, 0x15, 0xf4,
    0xcf, 0xf4,

    /* U+005E "^" */
    0x2, 0x86, 0x0, 0x7, 0xff, 0x0, 0xb, 0x9f,
    0x40, 0xf, 0x5c, 0x80, 0x4f, 0x19, 0xc0, 0x8d,
    0x5, 0xf1, 0xda, 0x1, 0xf5,

    /* U+005F "_" */
    0x9f, 0xff, 0xf1, 0x12, 0x22, 0x20,

    /* U+0060 "`" */
    0x0, 0x0, 0x1c, 0x40, 0x9, 0xd8, 0x0, 0x0,

    /* U+0061 "a" */
    0x2, 0xcf, 0xc1, 0xb, 0xd4, 0xe9, 0xe, 0x90,
    0xbb, 0x8, 0x50, 0xbc, 0x0, 0x3a, 0xfd, 0x7,
    0xf6, 0xbd, 0xf, 0x80, 0xbd, 0x1f, 0x70, 0xbd,
    0xf, 0x70, 0xbd, 0xe, 0xb3, 0xed, 0x5, 0xed,
    0xcd,

    /* U+0062 "b" */
    0xe9, 0x0, 0xe, 0x90, 0x0, 0xeb, 0xde, 0x3e,
    0xe5, 0xdb, 0xea, 0x9, 0xee, 0x90, 0x8f, 0xe9,
    0x8, 0xfe, 0x90, 0x8f, 0xe9, 0x8, 0xfe, 0x90,
    0x8f, 0xea, 0x9, 0xee, 0xe4, 0xdc, 0xeb, 0xde,
    0x40,

    /* U+0063 "c" */
    0x3, 0xdf, 0xb0, 0xd, 0xd6, 0xf7, 0xf, 0x70,
    0xda, 0x1f, 0x60, 0xca, 0x1f, 0x60, 0x11, 0x1f,
    0x60, 0x0, 0x1f, 0x60, 0x32, 0x1f, 0x60, 0xca,
    0xf, 0x70, 0xd9, 0xd, 0xc5, 0xf7, 0x3, 0xdf,
    0xb0,

    /* U+0064 "d" */
    0x0, 0x0, 0xbc, 0x0, 0x0, 0xbc, 0x4, 0xec,
    0xcc, 0xd, 0xc5, 0xfc, 0xf, 0x70, 0xcc, 0x1f,
    0x60, 0xbc, 0x1f, 0x60, 0xbc, 0x1f, 0x60, 0xbc,
    0x1f, 0x60, 0xbc, 0x1f, 0x60, 0xbc, 0xf, 0x70,
    0xbc, 0xe, 0xb3, 0xfc, 0x5, 0xec, 0xcc,

    /* U+0065 "e" */
    0x3, 0xdf, 0xc1, 0xc, 0xc5, 0xf7, 0xf, 0x70,
    0xca, 0x1f, 0x60, 0xcb, 0x1f, 0xfe, 0xfb, 0x1f,
    0x71, 0x11, 0x1f, 0x60, 0x55, 0x1f, 0x60, 0xca,
    0xf, 0x70, 0xd9, 0xd, 0xc5, 0xf6, 0x4, 0xdf,
    0xb0,

    /* U+0066 "f" */
    0x1, 0xcf, 0x30, 0x6f, 0x30, 0x8, 0xf0, 0x7,
    0xff, 0xf3, 0x19, 0xf2, 0x0, 0x8f, 0x0, 0x8,
    0xf0, 0x0, 0x8f, 0x0, 0x8, 0xf0, 0x0, 0x8f,
    0x0, 0x8, 0xf0, 0x0, 0x8f, 0x0, 0x8, 0xf0,
    0x0,

    /* U+0067 "g" */
    0x4, 0xed, 0xcc, 0xd, 0xc5, 0xfc, 0xf, 0x70,
    0xcc, 0x1f, 0x60, 0xbc, 0x1f, 0x60, 0xbc, 0x1f,
    0x60, 0xbc, 0x1f, 0x60, 0xbc, 0xf, 0x70, 0xcc,
    0xe, 0xb1, 0xec, 0x7, 0xff, 0xec, 0x0, 0x10,
    0xcb, 0x8, 0x44, 0xf8, 0xa, 0xef, 0xa0,

    /* U+0068 "h" */
    0xe9, 0x0, 0x0, 0xe9, 0x0, 0x0, 0xea, 0xbf,
    0x70, 0xee, 0x5c, 0xf0, 0xe9, 0x6, 0xf1, 0xe9,
    0x6, 0xf1, 0xe9, 0x6, 0xf1, 0xe9, 0x6, 0xf1,
    0xe9, 0x6, 0xf1, 0xe9, 0x6, 0xf1, 0xe9, 0x6,
    0xf1, 0xe9, 0x6, 0xf1, 0xe9, 0x6, 0xf1,

    /* U+0069 "i" */
    0xe9, 0x53, 0xe9, 0xe9, 0xe9, 0xe9, 0xe9, 0xe9,
    0xe9, 0xe9, 0xe9, 0xe9, 0xe9,

    /* U+006A "j" */
    0xb, 0xd0, 0x34, 0xa, 0xd0, 0xad, 0xa, 0xd0,
    0xad, 0xa, 0xd0, 0xad, 0xa, 0xd0, 0xad, 0xa,
    0xd0, 0xad, 0xa, 0xd0, 0xcc, 0x7f, 0x70,

    /* U+006B "k" */
    0xe9, 0x0, 0x0, 0xe9, 0x0, 0x0, 0xe9, 0x7,
    0xf1, 0xe9, 0xd, 0x90, 0xe9, 0x4f, 0x30, 0xe9,
    0xbc, 0x0, 0xeb, 0xf5, 0x0, 0xef, 0xf0, 0x0,
    0xea, 0xf6, 0x0, 0xe9, 0xad, 0x0, 0xe9, 0x3f,
    0x40, 0xe9, 0xc, 0xb0, 0xe9, 0x6, 0xf2,

    /* U+006C "l" */
    0xe9, 0xe9, 0xe9, 0xe9, 0xe9, 0xe9, 0xe9, 0xe9,
    0xe9, 0xe9, 0xe9, 0xe9, 0xe9,

    /* U+006D "m" */
    0xea, 0xcf, 0x68, 0xfb, 0xe, 0xe5, 0xcf, 0x97,
    0xf5, 0xe9, 0x7, 0xf0, 0xf, 0x7e, 0x90, 0x7f,
    0x0, 0xf7, 0xe9, 0x7, 0xf0, 0xf, 0x7e, 0x90,
    0x7f, 0x0, 0xf7, 0xe9, 0x7, 0xf0, 0xf, 0x7e,
    0x90, 0x7f, 0x0, 0xf7, 0xe9, 0x7, 0xf0, 0xf,
    0x7e, 0x90, 0x7f, 0x0, 0xf7, 0xe9, 0x7, 0xf0,
    0xf, 0x70,

    /* U+006E "n" */
    0xea, 0xbf, 0x70, 0xed, 0x4b, 0xf0, 0xe9, 0x6,
    0xf1, 0xe9, 0x6, 0xf1, 0xe9, 0x6, 0xf1, 0xe9,
    0x6, 0xf1, 0xe9, 0x6, 0xf1, 0xe9, 0x6, 0xf1,
    0xe9, 0x6, 0xf1, 0xe9, 0x6, 0xf1, 0xe9, 0x6,
    0xf1,

    /* U+006F "o" */
    0x3, 0xdf, 0xb1, 0xc, 0xc5, 0xf8, 0xf, 0x70,
    0xcb, 0x1f, 0x60, 0xbc, 0x1f, 0x60, 0xbc, 0x1f,
    0x60, 0xbc, 0x1f, 0x60, 0xbc, 0x1f, 0x60, 0xbc,
    0xf, 0x70, 0xcb, 0xd, 0xc5, 0xf8, 0x3, 0xdf,
    0xb1,

    /* U+0070 "p" */
    0xea, 0xde, 0x4e, 0xe4, 0xdc, 0xe9, 0x8, 0xee,
    0x90, 0x8f, 0xe9, 0x8, 0xfe, 0x90, 0x8f, 0xe9,
    0x8, 0xfe, 0x90, 0x8f, 0xea, 0x9, 0xee, 0xe4,
    0xdb, 0xeb, 0xed, 0x3e, 0x90, 0x0, 0xe9, 0x0,
    0x0,

    /* U+0071 "q" */
    0x5, 0xec, 0xcc, 0xe, 0xc4, 0xfc, 0xf, 0x70,
    0xcc, 0x1f, 0x60, 0xbc, 0x1f, 0x60, 0xbc, 0x1f,
    0x60, 0xbc, 0x1f, 0x60, 0xbc, 0x1f, 0x60, 0xbc,
    0xf, 0x70, 0xcc, 0xd, 0xc5, 0xfc, 0x4, 0xec,
    0xcc, 0x0, 0x0, 0xbc, 0x0, 0x0, 0xbc,

    /* U+0072 "r" */
    0xea, 0xc7, 0xee, 0x51, 0xe9, 0x0, 0xe9, 0x0,
    0xe9, 0x0, 0xe9, 0x0, 0xe9, 0x0, 0xe9, 0x0,
    0xe9, 0x0, 0xe9, 0x0, 0xe9, 0x0,

    /* U+0073 "s" */
    0x7, 0xee, 0x60, 0x2f, 0x89, 0xf0, 0x5f, 0x23,
    0xf3, 0x3f, 0x62, 0xe3, 0xb, 0xf3, 0x0, 0x1,
    0xcf, 0x30, 0x0, 0xd, 0xd0, 0x3e, 0x23, 0xf4,
    0x3f, 0x31, 0xf6, 0xf, 0xa7, 0xf4, 0x5, 0xef,
    0x90,

    /* U+0074 "t" */
    0xa, 0xd0, 0x0, 0xad, 0x0, 0xaf, 0xff, 0x1,
    0xbd, 0x10, 0xa, 0xd0, 0x0, 0xad, 0x0, 0xa,
    0xd0, 0x0, 0xad, 0x0, 0xa, 0xd0, 0x0, 0xad,
    0x0, 0xa, 0xd0, 0x0, 0xae, 0x30, 0x5, 0xff,
    0x0,

    /* U+0075 "u" */
    0xf8, 0x7, 0xf0, 0xf8, 0x7, 0xf0, 0xf8, 0x7,
    0xf0, 0xf8, 0x7, 0xf0, 0xf8, 0x7, 0xf0, 0xf8,
    0x7, 0xf0, 0xf8, 0x7, 0xf0, 0xf8, 0x7, 0xf0,
    0xf8, 0x7, 0xf0, 0xdc, 0x3c, 0xf0, 0x5e, 0xda,
    0xf0,

    /* U+0076 "v" */
    0x9c, 0x3, 0xf2, 0x7e, 0x5, 0xf0, 0x5f, 0x7,
    0xd0, 0x3f, 0x29, 0xb0, 0xf, 0x4b, 0x90, 0xe,
    0x6d, 0x60, 0xc, 0x8e, 0x40, 0xa, 0xaf, 0x20,
    0x7, 0xdf, 0x0, 0x5, 0xfd, 0x0, 0x3, 0xfb,
    0x0,

    /* U+0077 "w" */
    0x9a, 0x7, 0xf0, 0x2f, 0x7, 0xc0, 0x9f, 0x24,
    0xe0, 0x5e, 0xc, 0xf4, 0x5c, 0x3, 0xf0, 0xeb,
    0x67, 0xb0, 0x1f, 0x1f, 0x79, 0x89, 0x0, 0xf5,
    0xd4, 0xba, 0x70, 0xd, 0x9a, 0x2d, 0xb5, 0x0,
    0xcc, 0x80, 0xfd, 0x30, 0xa, 0xf5, 0xe, 0xf1,
    0x0, 0x8f, 0x30, 0xcf, 0x0, 0x6, 0xf0, 0xa,
    0xd0, 0x0,

    /* U+0078 "x" */
    0x8c, 0x5, 0xf0, 0x3f, 0x29, 0xb0, 0xe, 0x7e,
    0x50, 0x8, 0xef, 0x0, 0x3, 0xfb, 0x0, 0x1,
    0xf7, 0x0, 0x5, 0xfc, 0x0, 0xa, 0xdf, 0x10,
    0xf, 0x4e, 0x70, 0x5f, 0xa, 0xc0, 0xab, 0x4,
    0xf1,

    /* U+0079 "y" */
    0x8e, 0x0, 0xf7, 0x6f, 0x1, 0xf4, 0x3f, 0x23,
    0xf2, 0xf, 0x55, 0xf0, 0xe, 0x77, 0xc0, 0xb,
    0x99, 0xa0, 0x8, 0xbb, 0x70, 0x6, 0xcc, 0x50,
    0x3, 0xee, 0x20, 0x0, 0xff, 0x0, 0x0, 0xed,
    0x0, 0x2, 0xda, 0x0, 0x3f, 0xf5, 0x0,

    /* U+007A "z" */
    0x4f, 0xff, 0x81, 0x46, 0xf6, 0x0, 0x5f, 0x10,
    0xa, 0xd0, 0x0, 0xe8, 0x0, 0x3f, 0x40, 0x7,
    0xf0, 0x0, 0xcb, 0x0, 0x1f, 0x70, 0x5, 0xf6,
    0x41, 0x7f, 0xff, 0x60,

    /* U+007B "{" */
    0x3, 0xdb, 0xa, 0xe2, 0xc, 0xb0, 0xc, 0xb0,
    0xc, 0xb0, 0xe, 0xa0, 0xce, 0x30, 0x8f, 0x60,
    0xd, 0xa0, 0xc, 0xb0, 0xc, 0xb0, 0xc, 0xb0,
    0xa, 0xd0, 0x5, 0xfb, 0x0, 0x12,

    /* U+007C "|" */
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,

    /* U+007D "}" */
    0xcd, 0x30, 0x3e, 0x90, 0xc, 0xb0, 0xc, 0xb0,
    0xc, 0xb0, 0xb, 0xd0, 0x3, 0xeb, 0x7, 0xf7,
    0xb, 0xc0, 0xc, 0xb0, 0xc, 0xb0, 0xc, 0xb0,
    0xe, 0xa0, 0xcf, 0x40, 0x20, 0x0,

    /* U+007E "~" */
    0x0, 0x0, 0x0, 0x10, 0x7, 0xfd, 0xaa, 0xf1,
    0xd, 0x9a, 0xdf, 0x70, 0x0, 0x0, 0x0, 0x0,

    /* U+F001 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x15, 0xae, 0xe0,
    0x0, 0x0, 0x0, 0x2, 0x7c, 0xff, 0xff, 0xf1,
    0x0, 0x0, 0x39, 0xef, 0xff, 0xff, 0xff, 0xf1,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0x0, 0x1, 0xff, 0xff, 0xff, 0xc7, 0x2c, 0xf1,
    0x0, 0x1, 0xff, 0xea, 0x51, 0x0, 0xc, 0xf1,
    0x0, 0x1, 0xfd, 0x0, 0x0, 0x0, 0xc, 0xf1,
    0x0, 0x1, 0xfc, 0x0, 0x0, 0x0, 0xc, 0xf1,
    0x0, 0x1, 0xfc, 0x0, 0x0, 0x0, 0xc, 0xf1,
    0x0, 0x1, 0xfc, 0x0, 0x0, 0x4a, 0xbe, 0xf1,
    0x0, 0x1, 0xfc, 0x0, 0x5, 0xff, 0xff, 0xf1,
    0x8, 0xdd, 0xfc, 0x0, 0x6, 0xff, 0xff, 0xf0,
    0xaf, 0xff, 0xfc, 0x0, 0x0, 0x8e, 0xfc, 0x30,
    0xaf, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x19, 0xdd, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F008 "" */
    0x20, 0x4, 0x44, 0x44, 0x44, 0x44, 0x0, 0x2e,
    0x57, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x5e, 0xfa,
    0xbf, 0x52, 0x22, 0x22, 0x5f, 0xba, 0xff, 0x3,
    0xf3, 0x0, 0x0, 0x3, 0xf3, 0xf, 0xf8, 0x9f,
    0x30, 0x0, 0x0, 0x3f, 0xa8, 0xff, 0x79, 0xf8,
    0x55, 0x55, 0x58, 0xf9, 0x7f, 0xf0, 0x3f, 0xff,
    0xff, 0xff, 0xff, 0x30, 0xff, 0xbc, 0xf4, 0x11,
    0x11, 0x14, 0xfc, 0xbf, 0xf4, 0x6f, 0x30, 0x0,
    0x0, 0x3f, 0x64, 0xff, 0x3, 0xf3, 0x0, 0x0,
    0x3, 0xf3, 0xf, 0xfe, 0xef, 0x96, 0x66, 0x66,
    0x9f, 0xee, 0xfd, 0x14, 0xff, 0xff, 0xff, 0xff,
    0xf4, 0x1d,

    /* U+F00B "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xff, 0xf4, 0x9f, 0xff, 0xff, 0xff, 0xfe, 0xff,
    0xff, 0x5b, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0xbf, 0xff, 0xff, 0xff, 0xff, 0x8b, 0xba,
    0x25, 0xbb, 0xbb, 0xbb, 0xbb, 0x84, 0x66, 0x50,
    0x26, 0x66, 0x66, 0x66, 0x64, 0xff, 0xff, 0x5a,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0xbf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x5a, 0xff,
    0xff, 0xff, 0xff, 0xf2, 0x44, 0x40, 0x14, 0x44,
    0x44, 0x44, 0x42, 0xac, 0xcc, 0x26, 0xcc, 0xcc,
    0xcc, 0xcc, 0xaf, 0xff, 0xf5, 0xbf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x5b, 0xff, 0xff, 0xff,
    0xff, 0xfc, 0xff, 0xe3, 0x8f, 0xff, 0xff, 0xff,
    0xfc,

    /* U+F00C "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xff, 0xf6, 0x4, 0x20,
    0x0, 0x0, 0xc, 0xff, 0xf6, 0x7, 0xfe, 0x20,
    0x0, 0xc, 0xff, 0xf6, 0x0, 0xef, 0xfe, 0x20,
    0xc, 0xff, 0xf6, 0x0, 0x3, 0xff, 0xfe, 0x3c,
    0xff, 0xf6, 0x0, 0x0, 0x3, 0xff, 0xff, 0xff,
    0xf6, 0x0, 0x0, 0x0, 0x3, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xd6, 0x0, 0x0,
    0x0, 0x0,

    /* U+F00D "" */
    0x3c, 0x50, 0x0, 0x2, 0xc7, 0xe, 0xff, 0x50,
    0x2, 0xef, 0xf3, 0x9f, 0xff, 0x52, 0xef, 0xfd,
    0x0, 0x9f, 0xff, 0xef, 0xfd, 0x10, 0x0, 0x9f,
    0xff, 0xfd, 0x10, 0x0, 0x2, 0xff, 0xff, 0x60,
    0x0, 0x2, 0xef, 0xff, 0xff, 0x50, 0x2, 0xef,
    0xfd, 0xaf, 0xff, 0x50, 0xdf, 0xfd, 0x10, 0x9f,
    0xff, 0x2b, 0xfd, 0x10, 0x0, 0x9f, 0xe1, 0x6,
    0x10, 0x0, 0x0, 0x52, 0x0,

    /* U+F011 "" */
    0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x2b, 0x30, 0xef, 0x70, 0x97, 0x0, 0x0, 0x1e,
    0xfc, 0xe, 0xf7, 0x3f, 0xf8, 0x0, 0xb, 0xff,
    0x50, 0xef, 0x70, 0xbf, 0xf4, 0x3, 0xff, 0x60,
    0xe, 0xf7, 0x0, 0xdf, 0xb0, 0x8f, 0xe0, 0x0,
    0xef, 0x70, 0x5, 0xff, 0x1b, 0xfa, 0x0, 0xe,
    0xf7, 0x0, 0x1f, 0xf3, 0xbf, 0x90, 0x0, 0xdf,
    0x60, 0x0, 0xff, 0x49, 0xfc, 0x0, 0x2, 0x40,
    0x0, 0x3f, 0xf3, 0x5f, 0xf2, 0x0, 0x0, 0x0,
    0x9, 0xff, 0x0, 0xef, 0xc0, 0x0, 0x0, 0x4,
    0xff, 0x80, 0x5, 0xff, 0xc3, 0x0, 0x7, 0xff,
    0xe1, 0x0, 0x8, 0xff, 0xff, 0xef, 0xff, 0xe2,
    0x0, 0x0, 0x4, 0xdf, 0xff, 0xff, 0xa1, 0x0,
    0x0, 0x0, 0x0, 0x36, 0x75, 0x10, 0x0, 0x0,

    /* U+F013 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3c, 0xdc, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x8,
    0x25, 0xdf, 0xff, 0xd5, 0x28, 0x0, 0x9, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf9, 0x2, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf2, 0x4f, 0xff, 0xfe,
    0x41, 0x4e, 0xff, 0xff, 0x40, 0x2f, 0xff, 0x60,
    0x0, 0x6f, 0xff, 0x20, 0x1, 0xff, 0xf3, 0x0,
    0x3, 0xff, 0xf1, 0x0, 0x5f, 0xff, 0x70, 0x0,
    0x8f, 0xff, 0x50, 0x5f, 0xff, 0xff, 0x85, 0x8f,
    0xff, 0xff, 0x51, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0x6, 0xfd, 0xff, 0xff, 0xff, 0xfd,
    0xf6, 0x0, 0x4, 0x2, 0xbf, 0xff, 0xb2, 0x4,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x28, 0x98, 0x20, 0x0, 0x0,

    /* U+F015 "" */
    0x0, 0x0, 0x0, 0x0, 0x20, 0x0, 0x22, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xcf, 0xa0, 0xf, 0xf4,
    0x0, 0x0, 0x0, 0x3, 0xef, 0xff, 0xd2, 0xff,
    0x40, 0x0, 0x0, 0x5, 0xff, 0x91, 0xbf, 0xef,
    0xf4, 0x0, 0x0, 0x8, 0xff, 0x63, 0xb2, 0x8f,
    0xff, 0x40, 0x0, 0xb, 0xfe, 0x45, 0xff, 0xf4,
    0x5f, 0xf9, 0x0, 0x2d, 0xfd, 0x28, 0xff, 0xff,
    0xf6, 0x3e, 0xfc, 0x1d, 0xfb, 0x1b, 0xff, 0xff,
    0xff, 0xf9, 0x2c, 0xfb, 0x48, 0x1d, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x9, 0x30, 0x3, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0x0, 0x0, 0x3f, 0xff,
    0xf4, 0x36, 0xff, 0xff, 0x10, 0x0, 0x3, 0xff,
    0xff, 0x0, 0x2f, 0xff, 0xf1, 0x0, 0x0, 0x3f,
    0xff, 0xf0, 0x2, 0xff, 0xff, 0x10, 0x0, 0x2,
    0xff, 0xfe, 0x0, 0x1e, 0xff, 0xe0, 0x0,

    /* U+F019 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xff, 0x60, 0x0, 0x0, 0x0, 0x26, 0x6a,
    0xff, 0xfa, 0x66, 0x20, 0x0, 0x5, 0xff, 0xff,
    0xff, 0xff, 0xf5, 0x0, 0x0, 0x6, 0xff, 0xff,
    0xff, 0xf6, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xf6,
    0x0, 0x0, 0x9, 0xbb, 0xbb, 0x46, 0xf6, 0x4b,
    0xbb, 0xb9, 0xff, 0xff, 0xff, 0x50, 0x5f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x5d, 0x2e,
    0xfb, 0xee, 0xee, 0xee, 0xee, 0xed, 0xed, 0xeb,

    /* U+F01C "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0x60,
    0x0, 0x0, 0x4f, 0xfd, 0xdd, 0xdd, 0xdd, 0xff,
    0x30, 0x0, 0x1e, 0xf4, 0x0, 0x0, 0x0, 0x6,
    0xfd, 0x0, 0xa, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xf8, 0x5, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1e, 0xf3, 0xdf, 0xca, 0xa9, 0x0, 0x0,
    0x19, 0xaa, 0xdf, 0xbf, 0xff, 0xff, 0xf6, 0x0,
    0x8, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xec,
    0xcc, 0xff, 0xff, 0xff, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xd9, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,

    /* U+F021 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0x50,
    0x0, 0x4, 0x9c, 0xdc, 0x82, 0x0, 0xff, 0x0,
    0x1b, 0xff, 0xff, 0xff, 0xf9, 0x1f, 0xf0, 0x1d,
    0xff, 0xa5, 0x35, 0xaf, 0xfd, 0xff, 0xb, 0xfe,
    0x30, 0x0, 0x0, 0x3e, 0xff, 0xf3, 0xff, 0x30,
    0x0, 0x7, 0xdc, 0xdf, 0xff, 0x8f, 0xa0, 0x0,
    0x0, 0x8f, 0xff, 0xff, 0xf1, 0x31, 0x0, 0x0,
    0x1, 0x33, 0x33, 0x32, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xee, 0xee, 0xe7, 0x0,
    0x0, 0x7, 0xe8, 0xff, 0xff, 0xff, 0x80, 0x0,
    0x0, 0xdf, 0x5f, 0xff, 0x91, 0x21, 0x0, 0x0,
    0x7f, 0xf0, 0xff, 0xff, 0x80, 0x0, 0x0, 0x7f,
    0xf7, 0xf, 0xfa, 0xff, 0xd7, 0x46, 0xcf, 0xfa,
    0x0, 0xff, 0x7, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0xa, 0xa0, 0x1, 0x7b, 0xdc, 0x82, 0x0, 0x0,

    /* U+F026 "" */
    0x0, 0x0, 0x0, 0x31, 0x0, 0x0, 0x6, 0xf7,
    0x0, 0x0, 0x6f, 0xf8, 0x79, 0x9a, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xf8,
    0xef, 0xff, 0xff, 0xf8, 0x0, 0x1, 0xcf, 0xf8,
    0x0, 0x0, 0x1c, 0xf8, 0x0, 0x0, 0x1, 0xb5,

    /* U+F027 "" */
    0x0, 0x0, 0x0, 0x31, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf8,
    0x0, 0x0, 0x79, 0x9a, 0xff, 0xf8, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xf8, 0xd, 0x40, 0xff, 0xff,
    0xff, 0xf8, 0xa, 0xf1, 0xff, 0xff, 0xff, 0xf8,
    0x3, 0xf3, 0xff, 0xff, 0xff, 0xf8, 0xc, 0xe0,
    0xef, 0xff, 0xff, 0xf8, 0xa, 0x20, 0x0, 0x1,
    0xcf, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x1c, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xb4, 0x0, 0x0,

    /* U+F028 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x73, 0x0,
    0x0, 0x0, 0x0, 0x3, 0x10, 0x0, 0xb, 0xf5,
    0x0, 0x0, 0x0, 0x6, 0xf7, 0x0, 0x2, 0xa,
    0xf3, 0x0, 0x0, 0x6, 0xff, 0x80, 0x6, 0xf6,
    0xc, 0xd0, 0x79, 0x9a, 0xff, 0xf8, 0x0, 0x9,
    0xf3, 0x3f, 0x4f, 0xff, 0xff, 0xff, 0x80, 0xd5,
    0xd, 0xb0, 0xda, 0xff, 0xff, 0xff, 0xf8, 0x9,
    0xf1, 0x7f, 0xa, 0xcf, 0xff, 0xff, 0xff, 0x80,
    0x3f, 0x36, 0xf0, 0x8d, 0xff, 0xff, 0xff, 0xf8,
    0xc, 0xe0, 0x8e, 0xa, 0xbe, 0xff, 0xff, 0xff,
    0x80, 0x92, 0x1e, 0x90, 0xe8, 0x0, 0x1, 0xcf,
    0xf8, 0x0, 0x1c, 0xe1, 0x5f, 0x30, 0x0, 0x1,
    0xcf, 0x80, 0x6, 0xe3, 0x1e, 0xb0, 0x0, 0x0,
    0x1, 0xb5, 0x0, 0x0, 0x1c, 0xe1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xe2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x41, 0x0, 0x0,

    /* U+F03E "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0xff,
    0xda, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0,
    0x4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x0,
    0x2f, 0xff, 0xfc, 0x5f, 0xff, 0xff, 0xf9, 0x6d,
    0xff, 0xfc, 0x0, 0x4f, 0xff, 0xff, 0xfe, 0xbf,
    0xfc, 0x0, 0x0, 0x4f, 0xff, 0xfe, 0x20, 0xac,
    0x0, 0x0, 0x0, 0xef, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xff, 0xee, 0xee, 0xee, 0xee,
    0xee, 0xef, 0xf9, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf9,

    /* U+F043 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xe0, 0x0, 0x0, 0x0, 0x1, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x7f, 0xfc, 0x0, 0x0, 0x0, 0x1e,
    0xff, 0xf5, 0x0, 0x0, 0x9, 0xff, 0xff, 0xd0,
    0x0, 0x4, 0xff, 0xff, 0xff, 0x90, 0x0, 0xdf,
    0xff, 0xff, 0xff, 0x40, 0x7f, 0xff, 0xff, 0xff,
    0xfc, 0xd, 0xff, 0xff, 0xff, 0xff, 0xf2, 0xff,
    0x7f, 0xff, 0xff, 0xff, 0x4e, 0xf1, 0xff, 0xff,
    0xff, 0xf3, 0xaf, 0x75, 0xef, 0xff, 0xff, 0x2,
    0xff, 0x82, 0x8f, 0xff, 0x70, 0x5, 0xff, 0xff,
    0xff, 0x90, 0x0, 0x2, 0x9d, 0xda, 0x40, 0x0,

    /* U+F048 "" */
    0x2, 0x20, 0x0, 0x0, 0x1, 0x1, 0xff, 0x0,
    0x0, 0xb, 0xf2, 0x2f, 0xf0, 0x0, 0x1c, 0xff,
    0x42, 0xff, 0x0, 0x1d, 0xff, 0xf4, 0x2f, 0xf0,
    0x2e, 0xff, 0xff, 0x42, 0xff, 0x3e, 0xff, 0xff,
    0xf4, 0x2f, 0xff, 0xff, 0xff, 0xff, 0x42, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0x2f, 0xfd, 0xff, 0xff,
    0xff, 0x42, 0xff, 0x1c, 0xff, 0xff, 0xf4, 0x2f,
    0xf0, 0xb, 0xff, 0xff, 0x42, 0xff, 0x0, 0xa,
    0xff, 0xf4, 0x2f, 0xf0, 0x0, 0x9, 0xff, 0x41,
    0xfe, 0x0, 0x0, 0x8, 0xd1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F04B "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xb2, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xfe,
    0x50, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xfb,
    0x20, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe5,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xb2, 0x0, 0xff, 0xff, 0xff,
    0xff, 0xd4, 0x0, 0x0, 0xff, 0xff, 0xff, 0xf7,
    0x0, 0x0, 0x0, 0xff, 0xff, 0xfa, 0x10, 0x0,
    0x0, 0x0, 0xff, 0xfd, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x7c, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F04C "" */
    0x2, 0x22, 0x10, 0x0, 0x12, 0x22, 0x0, 0xbf,
    0xff, 0xf5, 0x4, 0xff, 0xff, 0xc0, 0xff, 0xff,
    0xf9, 0x8, 0xff, 0xff, 0xf1, 0xff, 0xff, 0xfa,
    0x8, 0xff, 0xff, 0xf1, 0xff, 0xff, 0xfa, 0x8,
    0xff, 0xff, 0xf1, 0xff, 0xff, 0xfa, 0x8, 0xff,
    0xff, 0xf1, 0xff, 0xff, 0xfa, 0x8, 0xff, 0xff,
    0xf1, 0xff, 0xff, 0xfa, 0x8, 0xff, 0xff, 0xf1,
    0xff, 0xff, 0xfa, 0x8, 0xff, 0xff, 0xf1, 0xff,
    0xff, 0xfa, 0x8, 0xff, 0xff, 0xf1, 0xff, 0xff,
    0xfa, 0x8, 0xff, 0xff, 0xf1, 0xff, 0xff, 0xfa,
    0x8, 0xff, 0xff, 0xf1, 0xff, 0xff, 0xf9, 0x8,
    0xff, 0xff, 0xf0, 0x8f, 0xff, 0xe3, 0x2, 0xdf,
    0xff, 0x90,

    /* U+F04D "" */
    0x2, 0x22, 0x22, 0x22, 0x22, 0x22, 0x0, 0xbf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xd0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf2, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf1, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xa0,

    /* U+F051 "" */
    0x1, 0x0, 0x0, 0x0, 0x22, 0x1, 0xfc, 0x10,
    0x0, 0xe, 0xf2, 0x3f, 0xfd, 0x10, 0x0, 0xef,
    0x33, 0xff, 0xfe, 0x20, 0xe, 0xf3, 0x3f, 0xff,
    0xfe, 0x30, 0xef, 0x33, 0xff, 0xff, 0xff, 0x4e,
    0xf3, 0x3f, 0xff, 0xff, 0xff, 0xff, 0x33, 0xff,
    0xff, 0xff, 0xff, 0xf3, 0x3f, 0xff, 0xff, 0xfd,
    0xff, 0x33, 0xff, 0xff, 0xfd, 0x1e, 0xf3, 0x3f,
    0xff, 0xfc, 0x10, 0xef, 0x33, 0xff, 0xfb, 0x0,
    0xe, 0xf3, 0x3f, 0xfa, 0x0, 0x0, 0xef, 0x31,
    0xd9, 0x0, 0x0, 0xd, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F052 "" */
    0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x6,
    0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x5, 0xff,
    0xff, 0xff, 0xff, 0xf7, 0x0, 0x4, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf5, 0x0, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0x0, 0x3, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x0, 0x9, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcc, 0xca, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x20, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xd0,

    /* U+F053 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xd1, 0x0, 0x0, 0x9, 0xff, 0x70, 0x0, 0x9,
    0xff, 0xa0, 0x0, 0x9, 0xff, 0xa0, 0x0, 0x9,
    0xff, 0xb0, 0x0, 0x9, 0xff, 0xb0, 0x0, 0x1,
    0xff, 0xf2, 0x0, 0x0, 0x5, 0xff, 0xd1, 0x0,
    0x0, 0x5, 0xff, 0xd1, 0x0, 0x0, 0x5, 0xff,
    0xd1, 0x0, 0x0, 0x5, 0xff, 0xd1, 0x0, 0x0,
    0x5, 0xff, 0x70, 0x0, 0x0, 0x5, 0x90,

    /* U+F054 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9d, 0x10, 0x0,
    0x0, 0x1f, 0xfd, 0x10, 0x0, 0x0, 0x5f, 0xfd,
    0x10, 0x0, 0x0, 0x5f, 0xfd, 0x10, 0x0, 0x0,
    0x5f, 0xfd, 0x10, 0x0, 0x0, 0x5f, 0xfd, 0x10,
    0x0, 0x0, 0xbf, 0xf7, 0x0, 0x0, 0x9f, 0xfb,
    0x0, 0x0, 0x9f, 0xfb, 0x0, 0x0, 0x9f, 0xfb,
    0x0, 0x0, 0x9f, 0xfb, 0x0, 0x0, 0x1f, 0xfb,
    0x0, 0x0, 0x0, 0x59, 0x0, 0x0, 0x0,

    /* U+F067 "" */
    0x0, 0x0, 0x0, 0x21, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0x0,
    0x0, 0x0, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0x9c, 0xcc, 0xcf, 0xff, 0xcc, 0xcc, 0xa0, 0x0,
    0x0, 0xd, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xeb, 0x0,
    0x0, 0x0,

    /* U+F068 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0x9c, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xa0,

    /* U+F06E "" */
    0x0, 0x0, 0x0, 0x2, 0x32, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xcf, 0xff, 0xff, 0xb4, 0x0,
    0x0, 0x0, 0x1c, 0xff, 0xa4, 0x24, 0xbf, 0xfb,
    0x10, 0x0, 0x2d, 0xff, 0x60, 0x16, 0x30, 0x8f,
    0xfd, 0x10, 0xd, 0xff, 0xa0, 0x1, 0xff, 0x70,
    0xcf, 0xfc, 0x9, 0xff, 0xf5, 0x12, 0x8f, 0xff,
    0x17, 0xff, 0xf8, 0xef, 0xff, 0x35, 0xff, 0xff,
    0xf2, 0x5f, 0xff, 0xc7, 0xff, 0xf6, 0x1f, 0xff,
    0xfe, 0x8, 0xff, 0xf5, 0xb, 0xff, 0xc0, 0x5e,
    0xfd, 0x30, 0xef, 0xfa, 0x0, 0xc, 0xff, 0x90,
    0x1, 0x0, 0xbf, 0xfa, 0x0, 0x0, 0x8, 0xff,
    0xd8, 0x68, 0xef, 0xf7, 0x0, 0x0, 0x0, 0x1,
    0x7c, 0xef, 0xec, 0x71, 0x0, 0x0,

    /* U+F070 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xd2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xf5, 0x0, 0x0, 0x23,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xf9, 0x5b,
    0xff, 0xff, 0xfa, 0x30, 0x0, 0x0, 0x0, 0x4e,
    0xff, 0xfa, 0x42, 0x5b, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x1c, 0xff, 0x41, 0x62, 0x9, 0xff, 0xd1,
    0x0, 0x1, 0x50, 0x9, 0xff, 0x9f, 0xf6, 0xd,
    0xff, 0xc0, 0x0, 0xbf, 0x90, 0x5, 0xff, 0xff,
    0xf0, 0x8f, 0xff, 0x70, 0xf, 0xff, 0xc1, 0x2,
    0xdf, 0xff, 0x26, 0xff, 0xfb, 0x0, 0x7f, 0xff,
    0x50, 0x0, 0xaf, 0xf6, 0x9f, 0xff, 0x40, 0x0,
    0xcf, 0xfc, 0x0, 0x0, 0x6f, 0xff, 0xff, 0x80,
    0x0, 0x1, 0xbf, 0xf9, 0x0, 0x0, 0x3e, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0x9f, 0xfd, 0x87, 0x30,
    0x1b, 0xfe, 0x40, 0x0, 0x0, 0x0, 0x28, 0xcf,
    0xfe, 0x30, 0x8, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xef, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xb4,

    /* U+F071 "" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xff, 0xff, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xff, 0x30, 0x5f, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xf3, 0x5,
    0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff,
    0x40, 0x6f, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x5f,
    0xff, 0xf5, 0x7, 0xff, 0xff, 0x30, 0x0, 0x0,
    0xe, 0xff, 0xff, 0xb8, 0xcf, 0xff, 0xfc, 0x0,
    0x0, 0x8, 0xff, 0xff, 0xf8, 0x1a, 0xff, 0xff,
    0xf6, 0x0, 0x1, 0xff, 0xff, 0xff, 0x20, 0x4f,
    0xff, 0xff, 0xe0, 0x0, 0xaf, 0xff, 0xff, 0xf8,
    0x1a, 0xff, 0xff, 0xff, 0x80, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x0, 0x7d,
    0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 0xed, 0x50,

    /* U+F074 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbd, 0x10, 0x45,
    0x54, 0x0, 0x0, 0x3, 0x5d, 0xfd, 0x1f, 0xff,
    0xf8, 0x0, 0x6, 0xff, 0xff, 0xfc, 0xff, 0xff,
    0xf7, 0x5, 0xff, 0xff, 0xff, 0x90, 0x0, 0xbf,
    0x74, 0xff, 0xc1, 0xcf, 0xa0, 0x0, 0x0, 0x63,
    0xff, 0xd1, 0x8, 0x90, 0x0, 0x0, 0x2, 0xef,
    0xe1, 0x0, 0x0, 0x0, 0x0, 0x2, 0xef, 0xe2,
    0x93, 0xb, 0xd1, 0x4, 0x44, 0xef, 0xf3, 0x8f,
    0xe5, 0xdf, 0xd1, 0xff, 0xff, 0xf3, 0x2, 0xef,
    0xff, 0xff, 0xcf, 0xff, 0xf4, 0x0, 0x3, 0xff,
    0xff, 0xfa, 0x1, 0x10, 0x0, 0x0, 0x0, 0x1c,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9a,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F077 "" */
    0x0, 0x0, 0x0, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xc0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0xbf, 0xfc, 0xff,
    0xc0, 0x0, 0x0, 0xbf, 0xf9, 0x7, 0xff, 0xc0,
    0x0, 0xbf, 0xf9, 0x0, 0x7, 0xff, 0xc1, 0x9f,
    0xf9, 0x0, 0x0, 0x7, 0xff, 0xb7, 0xf9, 0x0,
    0x0, 0x0, 0x7, 0xf9, 0x2, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x0,

    /* U+F078 "" */
    0x6, 0x0, 0x0, 0x0, 0x0, 0x6, 0x1a, 0xfc,
    0x0, 0x0, 0x0, 0xa, 0xfc, 0x7f, 0xfc, 0x0,
    0x0, 0xa, 0xff, 0x90, 0x7f, 0xfc, 0x0, 0xb,
    0xff, 0x90, 0x0, 0x7f, 0xfc, 0x1b, 0xff, 0x90,
    0x0, 0x0, 0x7f, 0xff, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x20,
    0x0, 0x0, 0x0,

    /* U+F079 "" */
    0x0, 0x2, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xfe, 0x10, 0x1, 0x11, 0x11,
    0x11, 0x0, 0x0, 0x4, 0xff, 0xfd, 0x16, 0xff,
    0xff, 0xff, 0xff, 0x0, 0x3, 0xff, 0xff, 0xfd,
    0x2a, 0xcc, 0xcc, 0xcf, 0xf0, 0x0, 0xbf, 0x9f,
    0xdb, 0xf6, 0x0, 0x0, 0x0, 0xdf, 0x0, 0x1,
    0x51, 0xfc, 0x5, 0x0, 0x0, 0x0, 0xd, 0xf0,
    0x0, 0x0, 0x1f, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0x0, 0x0, 0x1, 0xfc, 0x0, 0x0, 0x0,
    0x6, 0xd, 0xf0, 0x62, 0x0, 0x1f, 0xc0, 0x0,
    0x0, 0x7, 0xfc, 0xdf, 0x9f, 0xa0, 0x1, 0xff,
    0xdd, 0xdd, 0xdb, 0x2d, 0xff, 0xff, 0xe3, 0x0,
    0xe, 0xff, 0xff, 0xff, 0xf5, 0x1d, 0xff, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1d,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x12, 0x0, 0x0,

    /* U+F07B "" */
    0x3, 0x44, 0x44, 0x0, 0x0, 0x0, 0x0, 0xc,
    0xff, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xfc, 0x66, 0x66, 0x65, 0x1f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf9, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf9,

    /* U+F093 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xe4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x4,
    0xff, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x4, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x39, 0x9b,
    0xff, 0xfb, 0x99, 0x30, 0x0, 0x0, 0x0, 0x6f,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xf6,
    0x0, 0x0, 0x9, 0xbb, 0xb8, 0x4f, 0xff, 0x48,
    0xbb, 0xb9, 0xff, 0xff, 0xf3, 0x22, 0x23, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x5d, 0x2e,
    0xfb, 0xee, 0xee, 0xee, 0xee, 0xed, 0xed, 0xeb,

    /* U+F095 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xfd, 0x95,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xfe, 0x0,
    0x0, 0x6, 0xda, 0x0, 0xa, 0xff, 0xf3, 0x0,
    0x7, 0xef, 0xff, 0x84, 0xdf, 0xff, 0x50, 0x0,
    0xe, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x0, 0x0,
    0xa, 0xff, 0xff, 0xff, 0xfd, 0x30, 0x0, 0x0,
    0x7, 0xff, 0xff, 0xfe, 0x60, 0x0, 0x0, 0x0,
    0x2, 0xdc, 0xa8, 0x30, 0x0, 0x0, 0x0, 0x0,

    /* U+F0C4 "" */
    0x0, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1c,
    0xff, 0x80, 0x0, 0x0, 0x79, 0x40, 0xbf, 0xdf,
    0xf6, 0x0, 0x1d, 0xff, 0xe0, 0xff, 0x5, 0xf9,
    0x2, 0xef, 0xfe, 0x20, 0xcf, 0xce, 0xf9, 0x3e,
    0xff, 0xe2, 0x0, 0x2d, 0xff, 0xff, 0xff, 0xfe,
    0x20, 0x0, 0x0, 0x28, 0xff, 0xff, 0xe2, 0x0,
    0x0, 0x0, 0x2, 0xff, 0xff, 0x60, 0x0, 0x0,
    0x5, 0xae, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x7f,
    0xff, 0xfd, 0xaf, 0xff, 0x40, 0x0, 0xef, 0x48,
    0xf8, 0xa, 0xff, 0xf4, 0x0, 0xef, 0x16, 0xf8,
    0x0, 0xaf, 0xff, 0x50, 0x9f, 0xff, 0xf3, 0x0,
    0xa, 0xff, 0xe0, 0x8, 0xdc, 0x50, 0x0, 0x0,
    0x46, 0x20,

    /* U+F0C5 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xff, 0xff, 0xf5, 0xb4, 0x0, 0x0, 0x4,
    0xff, 0xff, 0xf5, 0xbf, 0x40, 0x35, 0x44, 0xff,
    0xff, 0xf5, 0x8c, 0xb0, 0xff, 0xd4, 0xff, 0xff,
    0xfb, 0x66, 0x60, 0xff, 0xd4, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0xff, 0xd4, 0xff, 0xff, 0xff, 0xff,
    0xf1, 0xff, 0xd4, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0xff, 0xd4, 0xff, 0xff, 0xff, 0xff, 0xf1, 0xff,
    0xd4, 0xff, 0xff, 0xff, 0xff, 0xf1, 0xff, 0xd4,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0xff, 0xd4, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0xff, 0xd2, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0xff, 0xf6, 0x1, 0x11, 0x11,
    0x11, 0x0, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x0,
    0x0, 0xbe, 0xee, 0xee, 0xee, 0xd2, 0x0, 0x0,

    /* U+F0C7 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xff, 0xff, 0xff, 0xff, 0x50, 0x0, 0xff, 0xdd,
    0xdd, 0xdd, 0xdf, 0xf5, 0x0, 0xfe, 0x0, 0x0,
    0x0, 0xa, 0xff, 0x50, 0xfe, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xf0, 0xfe, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xf1, 0xff, 0xcc, 0xcc, 0xcc, 0xcf, 0xff,
    0xf1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0xff, 0xff, 0xf7, 0x27, 0xff, 0xff, 0xf1, 0xff,
    0xff, 0xc0, 0x0, 0xbf, 0xff, 0xf1, 0xff, 0xff,
    0xc0, 0x0, 0xaf, 0xff, 0xf1, 0xff, 0xff, 0xf5,
    0x4, 0xff, 0xff, 0xf1, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x90,

    /* U+F0C9 "" */
    0x12, 0x22, 0x22, 0x22, 0x22, 0x22, 0x20, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xde, 0xee, 0xee, 0xee, 0xee,
    0xee, 0xe1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf1, 0x23, 0x33, 0x33, 0x33, 0x33, 0x33, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0x12, 0x22, 0x22, 0x22,
    0x22, 0x22, 0x10,

    /* U+F0E0 "" */
    0x3, 0x44, 0x44, 0x44, 0x44, 0x44, 0x43, 0xc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x53, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xe3, 0x5f, 0xa1, 0xaf,
    0xff, 0xff, 0xff, 0xa1, 0x9f, 0xff, 0xd3, 0x6f,
    0xff, 0xff, 0x62, 0xdf, 0xff, 0xff, 0xf7, 0x2d,
    0xfd, 0x26, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x12,
    0x1a, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf9, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf9,

    /* U+F0E7 "" */
    0x0, 0x12, 0x22, 0x10, 0x0, 0x0, 0xf, 0xff,
    0xff, 0x70, 0x0, 0x3, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0x5f, 0xff, 0xff, 0x0, 0x0, 0x7, 0xff,
    0xff, 0xa0, 0x0, 0x0, 0x9f, 0xff, 0xfa, 0x77,
    0x61, 0xb, 0xff, 0xff, 0xff, 0xff, 0x40, 0xdf,
    0xff, 0xff, 0xff, 0xc0, 0xf, 0xff, 0xff, 0xff,
    0xf3, 0x0, 0x35, 0x55, 0xef, 0xf9, 0x0, 0x0,
    0x0, 0x1f, 0xff, 0x10, 0x0, 0x0, 0x5, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x9f, 0xd0, 0x0, 0x0,
    0x0, 0xc, 0xf4, 0x0, 0x0, 0x0, 0x0, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x1c, 0x20, 0x0, 0x0,

    /* U+F0EA "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x13,
    0x3a, 0xfb, 0x33, 0x20, 0x0, 0x0, 0xff, 0xff,
    0x3e, 0xff, 0xf1, 0x0, 0x0, 0xff, 0xff, 0xcf,
    0xff, 0xf2, 0x0, 0x0, 0xff, 0xff, 0xc7, 0x77,
    0x71, 0x0, 0x0, 0xff, 0xfe, 0x2a, 0xaa, 0xa1,
    0x61, 0x0, 0xff, 0xfc, 0x6f, 0xff, 0xf2, 0xbc,
    0x10, 0xff, 0xfc, 0x6f, 0xff, 0xf2, 0xbf, 0xc0,
    0xff, 0xfc, 0x6f, 0xff, 0xf2, 0x12, 0x20, 0xff,
    0xfc, 0x6f, 0xff, 0xfc, 0x99, 0x90, 0xff, 0xfc,
    0x6f, 0xff, 0xff, 0xff, 0xf1, 0xff, 0xfc, 0x6f,
    0xff, 0xff, 0xff, 0xf1, 0xef, 0xfc, 0x6f, 0xff,
    0xff, 0xff, 0xf1, 0x12, 0x21, 0x6f, 0xff, 0xff,
    0xff, 0xf1, 0x0, 0x0, 0x6f, 0xff, 0xff, 0xff,
    0xf1, 0x0, 0x0, 0x4e, 0xff, 0xff, 0xff, 0xd0,

    /* U+F0F3 "" */
    0x0, 0x0, 0x0, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2a, 0xfc, 0x30, 0x0, 0x0, 0x0, 0x7, 0xff,
    0xff, 0xf9, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xff,
    0xff, 0x80, 0x0, 0x0, 0xef, 0xff, 0xff, 0xff,
    0xf0, 0x0, 0x1, 0xff, 0xff, 0xff, 0xff, 0xf3,
    0x0, 0x2, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0,
    0x4, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x8,
    0xff, 0xff, 0xff, 0xff, 0xfa, 0x0, 0xe, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x10, 0xbf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xd0, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xe0, 0x1, 0x11, 0x11, 0x11, 0x11,
    0x11, 0x0, 0x0, 0x0, 0x3f, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xd9, 0x0, 0x0, 0x0,

    /* U+F11C "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0xff, 0xdd, 0xfd, 0xef, 0xdf, 0xdd, 0xfd,
    0xdf, 0xdf, 0xe0, 0x1d, 0x3, 0xb0, 0xd1, 0xd,
    0x10, 0xfe, 0xff, 0xbc, 0xfb, 0xcf, 0xbf, 0xcb,
    0xfc, 0xbf, 0xef, 0xff, 0x73, 0xc7, 0x3d, 0x38,
    0xb3, 0x9f, 0xfe, 0xff, 0xf4, 0xa, 0x40, 0xb0,
    0x6a, 0x6, 0xff, 0xef, 0xff, 0xfe, 0xff, 0xef,
    0xef, 0xfe, 0xff, 0xfe, 0xfe, 0x2, 0xe0, 0x0,
    0x0, 0x0, 0xe2, 0x1f, 0xef, 0xe0, 0x1d, 0x0,
    0x0, 0x0, 0xd, 0x10, 0xfe, 0xff, 0xee, 0xfe,
    0xee, 0xee, 0xee, 0xfe, 0xef, 0xd9, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,

    /* U+F124 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xcf,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6d, 0xff,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x7, 0xef, 0xff,
    0xff, 0x90, 0x0, 0x0, 0x1, 0x8f, 0xff, 0xff,
    0xff, 0xf2, 0x0, 0x0, 0x2a, 0xff, 0xff, 0xff,
    0xff, 0xfa, 0x0, 0x2, 0xbf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x30, 0x0, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xb0, 0x0, 0xd, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf4, 0x0, 0x0, 0x14, 0x55, 0x55,
    0x9f, 0xff, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1c, 0xb1, 0x0, 0x0, 0x0,

    /* U+F15B "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xff,
    0xff, 0x97, 0x80, 0x0, 0xff, 0xff, 0xff, 0x98,
    0xf8, 0x0, 0xff, 0xff, 0xff, 0x98, 0xff, 0x80,
    0xff, 0xff, 0xff, 0x95, 0xbb, 0xb1, 0xff, 0xff,
    0xff, 0xd5, 0x55, 0x51, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xe2,

    /* U+F1EB "" */
    0x0, 0x0, 0x0, 0x0, 0x22, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0x9d, 0xff, 0xff, 0xfc,
    0x82, 0x0, 0x0, 0x0, 0x4d, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0x20, 0x0, 0xaf, 0xff, 0xd9,
    0x54, 0x34, 0x6a, 0xef, 0xff, 0x70, 0xcf, 0xfc,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x6e, 0xff, 0x87,
    0xf8, 0x0, 0x0, 0x46, 0x76, 0x30, 0x0, 0x1b,
    0xf4, 0x1, 0x0, 0x19, 0xff, 0xff, 0xff, 0xe7,
    0x0, 0x1, 0x0, 0x0, 0x4e, 0xff, 0xff, 0xef,
    0xff, 0xfd, 0x20, 0x0, 0x0, 0x7, 0xff, 0x82,
    0x0, 0x3, 0xaf, 0xf3, 0x0, 0x0, 0x0, 0x6,
    0x20, 0x0, 0x0, 0x0, 0x44, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8c, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xe8, 0x0, 0x0, 0x0, 0x0,

    /* U+F240 "" */
    0x1, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
    0x0, 0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x70, 0xff, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xfd, 0x1f, 0xe0, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0xf, 0xfb, 0xfe, 0x3f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0xaf, 0xcf,
    0xe3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x1,
    0xfc, 0xfe, 0x3f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0xbf, 0xcf, 0xe0, 0x33, 0x33, 0x33, 0x33,
    0x33, 0x33, 0xf, 0xfb, 0xff, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xfd, 0x9, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F241 "" */
    0x1, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
    0x0, 0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x70, 0xff, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xfd, 0x1f, 0xe0, 0x44, 0x44,
    0x44, 0x44, 0x40, 0x0, 0xf, 0xfb, 0xfe, 0x3f,
    0xff, 0xff, 0xff, 0xff, 0x30, 0x0, 0xaf, 0xcf,
    0xe3, 0xff, 0xff, 0xff, 0xff, 0xf3, 0x0, 0x1,
    0xfc, 0xfe, 0x3f, 0xff, 0xff, 0xff, 0xff, 0x30,
    0x0, 0xbf, 0xcf, 0xe0, 0x33, 0x33, 0x33, 0x33,
    0x30, 0x0, 0xf, 0xfb, 0xff, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xfd, 0x9, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F242 "" */
    0x1, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
    0x0, 0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x70, 0xff, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xfd, 0x1f, 0xe0, 0x44, 0x44,
    0x44, 0x10, 0x0, 0x0, 0xf, 0xfb, 0xfe, 0x3f,
    0xff, 0xff, 0xf6, 0x0, 0x0, 0x0, 0xaf, 0xcf,
    0xe3, 0xff, 0xff, 0xff, 0x60, 0x0, 0x0, 0x1,
    0xfc, 0xfe, 0x3f, 0xff, 0xff, 0xf6, 0x0, 0x0,
    0x0, 0xbf, 0xcf, 0xe0, 0x33, 0x33, 0x33, 0x10,
    0x0, 0x0, 0xf, 0xfb, 0xff, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xfd, 0x9, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F243 "" */
    0x1, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
    0x0, 0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x70, 0xff, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xfd, 0x1f, 0xe0, 0x44, 0x42,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xfb, 0xfe, 0x3f,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xcf,
    0xe3, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xfc, 0xfe, 0x3f, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xcf, 0xe0, 0x33, 0x32, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xfb, 0xff, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xfd, 0x9, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F244 "" */
    0x1, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
    0x0, 0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x70, 0xff, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xfd, 0x1f, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xfb, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xcf,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xfc, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xcf, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xfb, 0xff, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xfd, 0x9, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F287 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x22, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0x6f, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xee,
    0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xc0, 0x1b, 0xb1, 0x0, 0x0, 0x0, 0x18, 0x92,
    0x0, 0xd4, 0x0, 0x0, 0x0, 0x1, 0x20, 0xb,
    0xff, 0xe1, 0x7d, 0x11, 0x11, 0x11, 0x11, 0x6f,
    0x70, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x8a, 0xff, 0xc0, 0x0, 0x4e, 0x0,
    0x0, 0x0, 0x5e, 0x50, 0x5, 0x60, 0x0, 0x0,
    0xb6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xe1, 0x8f, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x22, 0x10, 0x0, 0x0,

    /* U+F293 "" */
    0x0, 0x0, 0x0, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x5c, 0xff, 0xfd, 0x70, 0x0, 0x0, 0x7f, 0xff,
    0x8f, 0xff, 0x90, 0x0, 0x2f, 0xff, 0xf3, 0x6f,
    0xff, 0x30, 0x8, 0xff, 0xff, 0x31, 0x7f, 0xf9,
    0x0, 0xcf, 0x96, 0xf3, 0xb2, 0x9f, 0xd0, 0xf,
    0xfe, 0x26, 0x39, 0x1c, 0xff, 0x0, 0xff, 0xfe,
    0x20, 0xa, 0xff, 0xf0, 0xf, 0xff, 0xfb, 0x5,
    0xff, 0xff, 0x10, 0xff, 0xfd, 0x10, 0x9, 0xff,
    0xf0, 0xf, 0xfd, 0x17, 0x39, 0x1a, 0xff, 0x0,
    0xcf, 0x97, 0xf3, 0xa2, 0x8f, 0xd0, 0x8, 0xff,
    0xff, 0x30, 0x8f, 0xf9, 0x0, 0x1f, 0xff, 0xf4,
    0x7f, 0xff, 0x20, 0x0, 0x5f, 0xff, 0xaf, 0xff,
    0x70, 0x0, 0x0, 0x29, 0xcd, 0xda, 0x40, 0x0,

    /* U+F2ED "" */
    0x0, 0x0, 0xbf, 0xff, 0xd1, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0xcd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xc1, 0x2, 0x22, 0x22,
    0x22, 0x22, 0x22, 0x0, 0x1f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x30, 0x1f, 0xfd, 0xff, 0xdf, 0xfd,
    0xff, 0x30, 0x1f, 0xf4, 0xcf, 0x1f, 0xe2, 0xff,
    0x30, 0x1f, 0xf4, 0xcf, 0x1f, 0xe2, 0xff, 0x30,
    0x1f, 0xf4, 0xcf, 0x1f, 0xe2, 0xff, 0x30, 0x1f,
    0xf4, 0xcf, 0x1f, 0xe2, 0xff, 0x30, 0x1f, 0xf4,
    0xcf, 0x1f, 0xe2, 0xff, 0x30, 0x1f, 0xf4, 0xcf,
    0x1f, 0xe2, 0xff, 0x30, 0x1f, 0xf5, 0xdf, 0x3f,
    0xe3, 0xff, 0x30, 0xf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x20, 0x8, 0xde, 0xee, 0xee, 0xee, 0xd9,
    0x0,

    /* U+F304 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xfe, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xe2,
    0x0, 0x0, 0x0, 0x0, 0x2, 0x4f, 0xff, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0x64, 0xff, 0xfd,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xf6, 0x4f, 0xe3,
    0x0, 0x0, 0x0, 0x6f, 0xff, 0xff, 0x64, 0x30,
    0x0, 0x0, 0x6, 0xff, 0xff, 0xff, 0xe1, 0x0,
    0x0, 0x0, 0x6f, 0xff, 0xff, 0xfe, 0x30, 0x0,
    0x0, 0x6, 0xff, 0xff, 0xff, 0xe3, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0xff, 0xfe, 0x30, 0x0, 0x0,
    0x5, 0xff, 0xff, 0xff, 0xe3, 0x0, 0x0, 0x0,
    0xb, 0xff, 0xff, 0xfe, 0x30, 0x0, 0x0, 0x0,
    0xd, 0xff, 0xff, 0xe3, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xff, 0xfe, 0x30, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xca, 0x93, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F55A "" */
    0x0, 0x0, 0x2, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x31, 0x0, 0x0, 0x7, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf5, 0x0, 0x8, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xb0, 0x8, 0xff, 0xff,
    0xf8, 0x5f, 0xfc, 0x3e, 0xff, 0xfc, 0x8, 0xff,
    0xff, 0xff, 0x20, 0x5b, 0x0, 0xaf, 0xff, 0xc8,
    0xff, 0xff, 0xff, 0xfe, 0x20, 0x0, 0x8f, 0xff,
    0xfc, 0xef, 0xff, 0xff, 0xff, 0xfa, 0x0, 0x3f,
    0xff, 0xff, 0xc4, 0xff, 0xff, 0xff, 0xfc, 0x0,
    0x10, 0x5f, 0xff, 0xfc, 0x4, 0xff, 0xff, 0xff,
    0x10, 0x8e, 0x20, 0x9f, 0xff, 0xc0, 0x4, 0xff,
    0xff, 0xfb, 0x9f, 0xfe, 0x7f, 0xff, 0xfc, 0x0,
    0x4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xa0, 0x0, 0x4, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xd2,

    /* U+F7C2 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xff, 0xff, 0xff, 0x70, 0x0, 0xbe, 0xdf, 0xdf,
    0xdf, 0xf0, 0xb, 0xf4, 0x1d, 0xf, 0xc, 0xf1,
    0xbf, 0xf4, 0x1d, 0xf, 0xc, 0xf1, 0xff, 0xf8,
    0x6e, 0x5f, 0x5d, 0xf1, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x4e, 0xff, 0xff, 0xff, 0xfe, 0x50,

    /* U+F8A2 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xf1,
    0x0, 0x4, 0x40, 0x0, 0x0, 0x0, 0x2f, 0xf1,
    0x0, 0x5f, 0xb0, 0x0, 0x0, 0x0, 0x4f, 0xf1,
    0x6, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x4f, 0xf1,
    0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x1c, 0xff, 0xd6, 0x66, 0x66, 0x66, 0x66, 0x40,
    0x0, 0xcf, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 59, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 62, .box_w = 2, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 13, .adv_w = 111, .box_w = 5, .box_h = 5, .ofs_x = 1, .ofs_y = 8},
    {.bitmap_index = 26, .adv_w = 151, .box_w = 8, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 78, .adv_w = 103, .box_w = 6, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 123, .adv_w = 255, .box_w = 14, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 214, .adv_w = 115, .box_w = 8, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 266, .adv_w = 48, .box_w = 3, .box_h = 5, .ofs_x = 0, .ofs_y = 8},
    {.bitmap_index = 274, .adv_w = 66, .box_w = 4, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 304, .adv_w = 66, .box_w = 4, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 334, .adv_w = 113, .box_w = 7, .box_h = 6, .ofs_x = 0, .ofs_y = 7},
    {.bitmap_index = 355, .adv_w = 95, .box_w = 6, .box_h = 5, .ofs_x = 0, .ofs_y = 4},
    {.bitmap_index = 370, .adv_w = 54, .box_w = 3, .box_h = 5, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 378, .adv_w = 82, .box_w = 4, .box_h = 1, .ofs_x = 0, .ofs_y = 6},
    {.bitmap_index = 380, .adv_w = 62, .box_w = 2, .box_h = 2, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 382, .adv_w = 92, .box_w = 6, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 421, .adv_w = 107, .box_w = 6, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 460, .adv_w = 76, .box_w = 4, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 486, .adv_w = 105, .box_w = 5, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 519, .adv_w = 106, .box_w = 6, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 558, .adv_w = 105, .box_w = 7, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 604, .adv_w = 105, .box_w = 6, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 643, .adv_w = 105, .box_w = 7, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 689, .adv_w = 105, .box_w = 6, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 728, .adv_w = 105, .box_w = 6, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 767, .adv_w = 105, .box_w = 6, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 806, .adv_w = 65, .box_w = 2, .box_h = 8, .ofs_x = 1, .ofs_y = 2},
    {.bitmap_index = 814, .adv_w = 66, .box_w = 4, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 834, .adv_w = 86, .box_w = 5, .box_h = 8, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 854, .adv_w = 85, .box_w = 5, .box_h = 4, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 864, .adv_w = 86, .box_w = 5, .box_h = 8, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 884, .adv_w = 94, .box_w = 5, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 917, .adv_w = 165, .box_w = 10, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 982, .adv_w = 110, .box_w = 7, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1028, .adv_w = 112, .box_w = 6, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1067, .adv_w = 108, .box_w = 6, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1106, .adv_w = 111, .box_w = 6, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1145, .adv_w = 90, .box_w = 4, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1171, .adv_w = 87, .box_w = 4, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1197, .adv_w = 110, .box_w = 6, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1236, .adv_w = 115, .box_w = 6, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1275, .adv_w = 62, .box_w = 2, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1288, .adv_w = 101, .box_w = 6, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1327, .adv_w = 105, .box_w = 6, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1366, .adv_w = 83, .box_w = 4, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1392, .adv_w = 152, .box_w = 8, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1444, .adv_w = 119, .box_w = 6, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1483, .adv_w = 111, .box_w = 6, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1522, .adv_w = 104, .box_w = 6, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1561, .adv_w = 111, .box_w = 6, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1606, .adv_w = 112, .box_w = 6, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1645, .adv_w = 102, .box_w = 6, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1684, .adv_w = 75, .box_w = 5, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1717, .adv_w = 113, .box_w = 7, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1763, .adv_w = 108, .box_w = 7, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1809, .adv_w = 159, .box_w = 10, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1874, .adv_w = 95, .box_w = 6, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1913, .adv_w = 98, .box_w = 6, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1952, .adv_w = 90, .box_w = 6, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1991, .adv_w = 90, .box_w = 4, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2017, .adv_w = 102, .box_w = 6, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2056, .adv_w = 90, .box_w = 4, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2082, .adv_w = 121, .box_w = 6, .box_h = 7, .ofs_x = 1, .ofs_y = 4},
    {.bitmap_index = 2103, .adv_w = 88, .box_w = 6, .box_h = 2, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2109, .adv_w = 76, .box_w = 4, .box_h = 4, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 2117, .adv_w = 109, .box_w = 6, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2150, .adv_w = 110, .box_w = 5, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2183, .adv_w = 103, .box_w = 6, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2216, .adv_w = 110, .box_w = 6, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2255, .adv_w = 105, .box_w = 6, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2288, .adv_w = 73, .box_w = 5, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2321, .adv_w = 109, .box_w = 6, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2360, .adv_w = 114, .box_w = 6, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2399, .adv_w = 59, .box_w = 2, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2412, .adv_w = 62, .box_w = 3, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2435, .adv_w = 101, .box_w = 6, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2474, .adv_w = 59, .box_w = 2, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2487, .adv_w = 168, .box_w = 9, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2537, .adv_w = 114, .box_w = 6, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2570, .adv_w = 107, .box_w = 6, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2603, .adv_w = 110, .box_w = 5, .box_h = 13, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 2636, .adv_w = 109, .box_w = 6, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2675, .adv_w = 76, .box_w = 4, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2697, .adv_w = 94, .box_w = 6, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2730, .adv_w = 72, .box_w = 5, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2763, .adv_w = 113, .box_w = 6, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2796, .adv_w = 88, .box_w = 6, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2829, .adv_w = 136, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2879, .adv_w = 87, .box_w = 6, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2912, .adv_w = 93, .box_w = 6, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2951, .adv_w = 80, .box_w = 5, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2979, .adv_w = 94, .box_w = 4, .box_h = 15, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 3009, .adv_w = 63, .box_w = 2, .box_h = 14, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 3023, .adv_w = 94, .box_w = 4, .box_h = 15, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 3053, .adv_w = 131, .box_w = 8, .box_h = 4, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 3069, .adv_w = 240, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3197, .adv_w = 240, .box_w = 15, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3287, .adv_w = 240, .box_w = 15, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3392, .adv_w = 240, .box_w = 15, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3482, .adv_w = 165, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3543, .adv_w = 240, .box_w = 15, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3663, .adv_w = 240, .box_w = 15, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3783, .adv_w = 270, .box_w = 17, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3902, .adv_w = 240, .box_w = 15, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4022, .adv_w = 270, .box_w = 17, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4124, .adv_w = 240, .box_w = 15, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4244, .adv_w = 120, .box_w = 8, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4292, .adv_w = 180, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4364, .adv_w = 270, .box_w = 17, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4492, .adv_w = 240, .box_w = 15, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4582, .adv_w = 165, .box_w = 11, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4670, .adv_w = 210, .box_w = 11, .box_h = 15, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 4753, .adv_w = 210, .box_w = 14, .box_h = 17, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 4872, .adv_w = 210, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4970, .adv_w = 210, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5068, .adv_w = 210, .box_w = 11, .box_h = 15, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 5151, .adv_w = 210, .box_w = 15, .box_h = 14, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 5256, .adv_w = 150, .box_w = 9, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5319, .adv_w = 150, .box_w = 9, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5382, .adv_w = 210, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5480, .adv_w = 210, .box_w = 14, .box_h = 4, .ofs_x = 0, .ofs_y = 4},
    {.bitmap_index = 5508, .adv_w = 270, .box_w = 17, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5610, .adv_w = 300, .box_w = 19, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5762, .adv_w = 270, .box_w = 19, .box_h = 16, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 5914, .adv_w = 240, .box_w = 15, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6027, .adv_w = 210, .box_w = 13, .box_h = 9, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 6086, .adv_w = 210, .box_w = 13, .box_h = 9, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 6145, .adv_w = 300, .box_w = 19, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 6269, .adv_w = 240, .box_w = 15, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6359, .adv_w = 240, .box_w = 15, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6479, .adv_w = 240, .box_w = 16, .box_h = 16, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 6607, .adv_w = 210, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 6705, .adv_w = 210, .box_w = 14, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6817, .adv_w = 210, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 6915, .adv_w = 210, .box_w = 14, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 7006, .adv_w = 240, .box_w = 15, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7096, .adv_w = 150, .box_w = 11, .box_h = 16, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 7184, .adv_w = 210, .box_w = 14, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7296, .adv_w = 210, .box_w = 14, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7408, .adv_w = 270, .box_w = 17, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7510, .adv_w = 240, .box_w = 17, .box_h = 16, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 7646, .adv_w = 180, .box_w = 12, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7742, .adv_w = 300, .box_w = 19, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 7875, .adv_w = 300, .box_w = 19, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7980, .adv_w = 300, .box_w = 19, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8085, .adv_w = 300, .box_w = 19, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8190, .adv_w = 300, .box_w = 19, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8295, .adv_w = 300, .box_w = 19, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8400, .adv_w = 300, .box_w = 19, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 8524, .adv_w = 210, .box_w = 13, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 8628, .adv_w = 210, .box_w = 14, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 8733, .adv_w = 240, .box_w = 16, .box_h = 16, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 8861, .adv_w = 300, .box_w = 19, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8975, .adv_w = 180, .box_w = 12, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 9071, .adv_w = 241, .box_w = 16, .box_h = 10, .ofs_x = 0, .ofs_y = 1}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_1[] = {
    0x0, 0x7, 0xa, 0xb, 0xc, 0x10, 0x12, 0x14,
    0x18, 0x1b, 0x20, 0x25, 0x26, 0x27, 0x3d, 0x42,
    0x47, 0x4a, 0x4b, 0x4c, 0x50, 0x51, 0x52, 0x53,
    0x66, 0x67, 0x6d, 0x6f, 0x70, 0x73, 0x76, 0x77,
    0x78, 0x7a, 0x92, 0x94, 0xc3, 0xc4, 0xc6, 0xc8,
    0xdf, 0xe6, 0xe9, 0xf2, 0x11b, 0x123, 0x15a, 0x1ea,
    0x23f, 0x240, 0x241, 0x242, 0x243, 0x286, 0x292, 0x2ec,
    0x303, 0x559, 0x7c1, 0x8a1
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 61441, .range_length = 2210, .glyph_id_start = 96,
        .unicode_list = unicode_list_1, .glyph_id_ofs_list = NULL, .list_length = 60, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/


/*Map glyph_ids to kern left classes*/
static const uint8_t kern_left_class_mapping[] =
{
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 1, 2, 3, 4, 0, 5,
    6, 0, 0, 7, 8, 9, 0, 0,
    10, 11, 0, 12, 0, 0, 0, 13,
    14, 0, 15, 0, 0, 0, 0, 0,
    0, 0, 0, 16, 17, 0, 0, 0,
    0, 0, 0, 0, 18, 0, 0, 0,
    19, 0, 0, 20, 0, 0, 0, 21,
    22, 0, 23, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0
};

/*Map glyph_ids to kern right classes*/
static const uint8_t kern_right_class_mapping[] =
{
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 1, 0, 2,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 3, 0, 4, 0, 0, 0,
    5, 0, 0, 6, 0, 0, 0, 0,
    7, 0, 8, 0, 9, 10, 11, 12,
    13, 14, 15, 0, 0, 0, 0, 0,
    0, 0, 16, 0, 17, 0, 18, 0,
    19, 0, 0, 0, 0, 0, 0, 0,
    20, 0, 0, 0, 21, 0, 22, 23,
    24, 25, 26, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0
};

/*Kern values between classes*/
static const int8_t kern_class_values[] =
{
    0, 0, 0, -2, -2, 0, -2, 0,
    0, -3, -2, -5, -6, 0, -5, 0,
    0, 0, 0, 0, 0, -1, -2, -1,
    0, -2, -7, -10, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -1, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -7, -8,
    -1, 0, 0, 0, 0, 0, 0, 0,
    0, -1, -1, 0, -1, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -12, -14, -4, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -1, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -3, -7, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -1, 0, -1, 0, -1, 0,
    0, 0, 0, 0, 0, -1, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -2, -2, 0, -2, -2,
    -2, -2, 0, -4, -4, 0, -5, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -1, -2, -4, -1, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -1,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -27, -30, -4, 0,
    0, -5, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -1, -1, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -9, -8, -5, 0, 0, -5, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -1,
    0, -1, -1, -1, 0, 0, 0, 0,
    0, 0, 0, -5, -4, 0, 0, 0,
    -1, 0, 0, 0, 0, 0, 0, 0,
    0, -1, -1, -1, 0, 0, 0, 0,
    0, 0, 0, 0, -1, -1, -4, 0,
    0, 0, -1, 0, 0, 0, 0, 0,
    0, 0, 0, -2, 0, 0, 0, 0,
    -1, 0, 0, 0, 0, 0, -4, -5,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -5, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -1, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -1, -9,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -1, 0, 0,
    -1, -1, 0, 0, 0, 0, 0, 0,
    -2, -6, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -2, -8, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -1, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -6, -10, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -1,
    0, 0, 0, 0, 0, 0
};


/*Collect the kern class' data in one place*/
static const lv_font_fmt_txt_kern_classes_t kern_classes =
{
    .class_pair_values   = kern_class_values,
    .left_class_mapping  = kern_left_class_mapping,
    .right_class_mapping = kern_right_class_mapping,
    .left_class_cnt      = 23,
    .right_class_cnt     = 26,
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LVGL_VERSION_MAJOR == 8
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
#endif

#if LVGL_VERSION_MAJOR >= 8
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_classes,
    .kern_scale = 16,
    .cmap_num = 2,
    .bpp = 4,
    .kern_classes = 1,
    .bitmap_format = 0,
#if LVGL_VERSION_MAJOR == 8
    .cache = &cache
#endif
};



/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LVGL_VERSION_MAJOR >= 8
const lv_font_t lv_font_Antonio_Regular_15 = {
#else
lv_font_t lv_font_Antonio_Regular_15 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 15,          /*The maximum line height required by the font*/
    .base_line = 2,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = 0,
    .underline_thickness = 0,
#endif
    .dsc = &font_dsc,          /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
#if LV_VERSION_CHECK(8, 2, 0) || LVGL_VERSION_MAJOR >= 9
    .fallback = NULL,
#endif
    .user_data = NULL,
};



#endif /*#if LV_FONT_ANTONIO_REGULAR_15*/

