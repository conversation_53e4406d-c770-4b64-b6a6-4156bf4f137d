/*
 * GC0308 register definitions.
 */
#ifndef __GC0308_REG_REGS_H__
#define __GC0308_REG_REGS_H__

#define RESET_RELATED   0xfe    // Bit[7]: Software reset 
                                // Bit[6:5]: NA
                                // Bit[4]: CISCTL_restart_n
                                // Bit[3:1]: NA
                                // Bit[0]: page select
                                //  0:page0
                                //  1:page1


// page0:



/**
 * @brief register value
 */


#endif // __GC0308_REG_REGS_H__
