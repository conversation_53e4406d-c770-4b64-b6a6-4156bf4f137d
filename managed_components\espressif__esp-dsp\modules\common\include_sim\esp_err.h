// Copyright 2018-2020 spressif Systems (Shanghai) PTE LTD
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// This file include defenitions that are emulate esp-idf error codes

#ifndef _esp_err_h_
#define _esp_err_h_

#include <stdlib.h>
typedef int esp_err_t;

#define ESP_OK 0

#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif // M_PI

#endif // _esp_err_h_
