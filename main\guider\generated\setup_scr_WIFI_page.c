/*
* Copyright 2025 NXP
* NXP Proprietary. This software is owned or controlled by NXP and may only be used strictly in
* accordance with the applicable license terms. By expressly accepting such terms or by downloading, installing,
* activating and/or otherwise using the software, you are agreeing that you have read, and that you agree to
* comply with and are bound by, such license terms.  If you do not agree to be bound by the applicable license
* terms, then you may not retain, install, activate or otherwise use the software.
*/

#include "lvgl.h"
#include <stdio.h>
#include "gui_guider.h"
#include "events_init.h"
#include "widgets_init.h"
#include "custom.h"



void setup_scr_WIFI_page(lv_ui *ui)
{
    //Write codes WIFI_page
    ui->WIFI_page = lv_obj_create(NULL);
    lv_obj_set_size(ui->WIFI_page, 320, 240);
    lv_obj_set_scrollbar_mode(ui->WIFI_page, LV_SCROLLBAR_MODE_OFF);

    //Write style for WIFI_page, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->WIFI_page, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->WIFI_page, lv_color_hex(0x242424), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->WIFI_page, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes WIFI_page_label_state
    ui->WIFI_page_label_state = lv_label_create(ui->WIFI_page);
    lv_label_set_text(ui->WIFI_page_label_state, "服务已开启，浏览器输入IP地址开始配网");
    lv_label_set_long_mode(ui->WIFI_page_label_state, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->WIFI_page_label_state, 0, 8);
    lv_obj_set_size(ui->WIFI_page_label_state, 320, 32);

    //Write style for WIFI_page_label_state, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->WIFI_page_label_state, 1, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_opa(ui->WIFI_page_label_state, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_color(ui->WIFI_page_label_state, lv_color_hex(0x2195f6), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_side(ui->WIFI_page_label_state, LV_BORDER_SIDE_BOTTOM, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->WIFI_page_label_state, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->WIFI_page_label_state, lv_color_hex(0xFFFFFF), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->WIFI_page_label_state, &lv_font_HarmonyOS_Sans_SC_Medium_14, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->WIFI_page_label_state, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->WIFI_page_label_state, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->WIFI_page_label_state, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->WIFI_page_label_state, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->WIFI_page_label_state, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->WIFI_page_label_state, 7, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->WIFI_page_label_state, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->WIFI_page_label_state, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->WIFI_page_label_state, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->WIFI_page_label_state, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes WIFI_page_qrcode_ip
    ui->WIFI_page_qrcode_ip = lv_qrcode_create(ui->WIFI_page, 100, lv_color_hex(0x2C3224), lv_color_hex(0xffffff));
    const char * WIFI_page_qrcode_ip_data = "https://www.nxp.com/";
    lv_qrcode_update(ui->WIFI_page_qrcode_ip, WIFI_page_qrcode_ip_data, 20);
    lv_obj_set_pos(ui->WIFI_page_qrcode_ip, 43, 65);
    lv_obj_set_size(ui->WIFI_page_qrcode_ip, 100, 100);

    //Write codes WIFI_page_label_ip
    ui->WIFI_page_label_ip = lv_label_create(ui->WIFI_page);
    lv_label_set_text(ui->WIFI_page_label_ip, "0.0.0.0");
    lv_label_set_long_mode(ui->WIFI_page_label_ip, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->WIFI_page_label_ip, 43, 180);
    lv_obj_set_size(ui->WIFI_page_label_ip, 100, 21);

    //Write style for WIFI_page_label_ip, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->WIFI_page_label_ip, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->WIFI_page_label_ip, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->WIFI_page_label_ip, lv_color_hex(0xFFFFFF), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->WIFI_page_label_ip, &lv_font_montserratMedium_16, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->WIFI_page_label_ip, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->WIFI_page_label_ip, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->WIFI_page_label_ip, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->WIFI_page_label_ip, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->WIFI_page_label_ip, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->WIFI_page_label_ip, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->WIFI_page_label_ip, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->WIFI_page_label_ip, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->WIFI_page_label_ip, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->WIFI_page_label_ip, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes WIFI_page_btn_sw
    ui->WIFI_page_btn_sw = lv_btn_create(ui->WIFI_page);
    ui->WIFI_page_btn_sw_label = lv_label_create(ui->WIFI_page_btn_sw);
    lv_label_set_text(ui->WIFI_page_btn_sw_label, "开启服务");
    lv_label_set_long_mode(ui->WIFI_page_btn_sw_label, LV_LABEL_LONG_WRAP);
    lv_obj_align(ui->WIFI_page_btn_sw_label, LV_ALIGN_CENTER, 0, 0);
    lv_obj_set_style_pad_all(ui->WIFI_page_btn_sw, 0, LV_STATE_DEFAULT);
    lv_obj_set_width(ui->WIFI_page_btn_sw_label, LV_PCT(100));
    lv_obj_set_pos(ui->WIFI_page_btn_sw, 199, 76);
    lv_obj_set_size(ui->WIFI_page_btn_sw, 100, 38);

    //Write style for WIFI_page_btn_sw, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->WIFI_page_btn_sw, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->WIFI_page_btn_sw, lv_color_hex(0x2195f6), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->WIFI_page_btn_sw, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui->WIFI_page_btn_sw, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->WIFI_page_btn_sw, 5, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->WIFI_page_btn_sw, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->WIFI_page_btn_sw, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->WIFI_page_btn_sw, &lv_font_HarmonyOS_Sans_SC_Medium_16, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->WIFI_page_btn_sw, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->WIFI_page_btn_sw, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes WIFI_page_btn_back
    ui->WIFI_page_btn_back = lv_btn_create(ui->WIFI_page);
    ui->WIFI_page_btn_back_label = lv_label_create(ui->WIFI_page_btn_back);
    lv_label_set_text(ui->WIFI_page_btn_back_label, "返回");
    lv_label_set_long_mode(ui->WIFI_page_btn_back_label, LV_LABEL_LONG_WRAP);
    lv_obj_align(ui->WIFI_page_btn_back_label, LV_ALIGN_CENTER, 0, 0);
    lv_obj_set_style_pad_all(ui->WIFI_page_btn_back, 0, LV_STATE_DEFAULT);
    lv_obj_set_width(ui->WIFI_page_btn_back_label, LV_PCT(100));
    lv_obj_set_pos(ui->WIFI_page_btn_back, 199, 142);
    lv_obj_set_size(ui->WIFI_page_btn_back, 100, 38);

    //Write style for WIFI_page_btn_back, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->WIFI_page_btn_back, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->WIFI_page_btn_back, lv_color_hex(0x2195f6), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->WIFI_page_btn_back, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui->WIFI_page_btn_back, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->WIFI_page_btn_back, 5, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->WIFI_page_btn_back, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->WIFI_page_btn_back, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->WIFI_page_btn_back, &lv_font_HarmonyOS_Sans_SC_Medium_16, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->WIFI_page_btn_back, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->WIFI_page_btn_back, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);

    //The custom code of WIFI_page.


    //Update current screen layout.
    lv_obj_update_layout(ui->WIFI_page);

    //Init events for screen.
    // events_init_WIFI_page(ui);
}
