#include <string.h>
#include "audio_player.h"
#include "audio_mem.h"
#include "audio_thread.h"
#include "pcm_decoder.h"
#include "audio_element.h"
#include "audio_pipeline.h"
#include "raw_stream.h"
#include "i2s_stream.h"
#include "esp_log.h"
#include "math.h"
#include "opus.h"

#define TAG "audio_player"

#define AUDIO_DATA_BUFF_SIZE 1500
#define AUDIO_DATA_QUEUE_SIZE 64

typedef struct
{
    uint8_t data[AUDIO_DATA_BUFF_SIZE];
    size_t len;
} audio_data_t;

struct audio_player
{
    audio_pipeline_handle_t pipeline;

    audio_element_handle_t raw_read;
    audio_element_handle_t pcm_decoder;
    audio_element_handle_t i2s_stream_writer;

    struct OpusDecoder *decoder;
    int frame_size;

    int16_t *pcm_buff;
    int pcm_buff_len;

    int sample_rate;
    int channels;
    int bits;
    int duration_ms;

    int volume;

    xQueueHandle data_q;
    StaticQueue_t *data_q_buff;
    uint8_t *data_q_storage;

    volatile bool run_flag;
    volatile bool write_flag;

    audio_player_callback callback;

    void *user_data;
};

static esp_err_t player_init(audio_player_t player)
{
    esp_err_t err;

    audio_pipeline_cfg_t pipeline_cfg = DEFAULT_AUDIO_PIPELINE_CONFIG();
    player->pipeline = audio_pipeline_init(&pipeline_cfg);
    if (player->pipeline == NULL)
        return ESP_FAIL;

    raw_stream_cfg_t raw_cfg = RAW_STREAM_CFG_DEFAULT();
    raw_cfg.type = AUDIO_STREAM_READER;
    player->raw_read = raw_stream_init(&raw_cfg);
    if (player->raw_read == NULL)
        return ESP_FAIL;

    pcm_decoder_cfg_t pcm_dec_cfg = DEFAULT_PCM_DECODER_CONFIG();
    pcm_dec_cfg.rate = player->sample_rate;
    pcm_dec_cfg.bits = player->bits;
    pcm_dec_cfg.channels = player->channels;
    pcm_dec_cfg.task_core = 1;
    player->pcm_decoder = pcm_decoder_init(&pcm_dec_cfg);
    if (player->pcm_decoder == NULL)
        return ESP_FAIL;

    i2s_stream_cfg_t i2s_cfg = I2S_STREAM_CFG_DEFAULT();
    i2s_cfg.type = AUDIO_STREAM_WRITER;
    i2s_cfg.use_alc = true;
    player->i2s_stream_writer = i2s_stream_init(&i2s_cfg);
    if (player->i2s_stream_writer == NULL)
        return ESP_FAIL;

    i2s_stream_set_clk(player->i2s_stream_writer, player->sample_rate, player->bits, player->channels);
    i2s_alc_volume_set(player->i2s_stream_writer, player->volume);

    audio_pipeline_register(player->pipeline, player->raw_read, "raw");
    audio_pipeline_register(player->pipeline, player->pcm_decoder, "pcm");
    audio_pipeline_register(player->pipeline, player->i2s_stream_writer, "i2s");

    const char *marker_link_tag[3] = {"raw", "pcm", "i2s"};
    audio_pipeline_link(player->pipeline, &marker_link_tag[0], 3);

    ESP_ERROR_CHECK(audio_pipeline_run(player->pipeline));

    return ESP_OK;
}

static void audio_player_process_task(void *arg)
{
    audio_player_t player = (audio_player_t)arg;
    audio_data_t audio_data = {0};

    player->run_flag = true;
    while (player->run_flag)
    {
        if (xQueueReceive(player->data_q, &audio_data, portMAX_DELAY) == pdTRUE)
        {
            if (player->callback)
            {
                player->callback(player->user_data);
            }

            int ret = opus_decode(player->decoder, audio_data.data, audio_data.len, player->pcm_buff, player->frame_size, 0);
            if (ret < 0)
            {
                ESP_LOGE(TAG, "failed to decode audio, error code: %d", ret);
                continue;
            }

            size_t bytes_written = raw_stream_write(player->raw_read, (char *)player->pcm_buff, player->pcm_buff_len);
            if (bytes_written < player->pcm_buff_len)
            {
                ESP_LOGI(TAG, "raw stream write bytes remain %d", player->pcm_buff_len - bytes_written);
            }
        }
    }

    ESP_LOGI(TAG, "audio play task exit");
    vTaskDelete(NULL);
}

audio_player_t audio_player_create(audio_player_cfg_t *cfg)
{
    audio_player_t audio_player = (audio_player_t)audio_calloc(1, sizeof(struct audio_player));
    if (audio_player == NULL)
        goto AUDIO_PLAYER_FAILED;

    audio_player->sample_rate = cfg->sample_rate;
    audio_player->channels = cfg->channels;
    audio_player->bits = cfg->bits;
    audio_player->duration_ms = cfg->duration_ms;

    audio_player->volume = cfg->volume;

    audio_player->frame_size = audio_player->sample_rate / 1000 * audio_player->channels * audio_player->duration_ms;

    audio_player->pcm_buff_len = audio_player->frame_size * sizeof(int16_t);
    audio_player->pcm_buff = (int16_t *)audio_calloc(1, audio_player->pcm_buff_len);
    if (audio_player->pcm_buff == NULL)
        goto AUDIO_PLAYER_FAILED;

    int opus_error;
    audio_player->decoder = opus_decoder_create(audio_player->sample_rate, audio_player->channels, &opus_error);
    if (audio_player->decoder == NULL)
    {
        ESP_LOGE(TAG, "Failed to create audio decoder, error code: %d", opus_error);
        goto AUDIO_PLAYER_FAILED;
    }

    // 使用静态队列
    audio_player->data_q_buff = (StaticQueue_t *)audio_calloc(1, sizeof(StaticQueue_t));
    audio_player->data_q_storage = (uint8_t *)audio_calloc(1, AUDIO_DATA_QUEUE_SIZE * sizeof(audio_data_t));

    if (!audio_player->data_q_buff || !audio_player->data_q_storage)
    {
        ESP_LOGE(TAG, "Failed to allocate queue");
        goto AUDIO_PLAYER_FAILED;
    }

    audio_player->data_q = xQueueCreateStatic(AUDIO_DATA_QUEUE_SIZE, sizeof(audio_data_t),
                                              audio_player->data_q_storage, audio_player->data_q_buff);
    if (audio_player->data_q == NULL)
    {
        ESP_LOGE(TAG, "Failed to create queue");
        goto AUDIO_PLAYER_FAILED;
    }

    if (player_init(audio_player) != ESP_OK)
        goto AUDIO_PLAYER_FAILED;

    // 音频播放任务
    audio_thread_create(NULL, "audio_play", audio_player_process_task, audio_player, 16 * 1024, 5, true, 0);

    return audio_player;

AUDIO_PLAYER_FAILED:
    audio_player_destroy(audio_player);
    return NULL;
}

void audio_player_destroy(audio_player_t player)
{
    if (player)
    {
        player->run_flag = false;

        if (player->data_q)
        {
            // 清空队列数据
            xQueueReset(player->data_q);
            vQueueDelete(player->data_q);
            player->data_q = NULL;
        }

        if (player->data_q_buff)
        {
            audio_free(player->data_q_buff);
            player->data_q_buff = NULL;
        }

        if (player->data_q_storage)
        {
            audio_free(player->data_q_storage);
            player->data_q_storage = NULL;
        }

        if (player->pipeline)
        {
            audio_pipeline_stop(player->pipeline);
            audio_pipeline_wait_for_stop(player->pipeline);
            audio_pipeline_terminate(player->pipeline);

            audio_pipeline_unregister(player->pipeline, player->raw_read);
            audio_pipeline_unregister(player->pipeline, player->pcm_decoder);
            audio_pipeline_unregister(player->pipeline, player->i2s_stream_writer);

            audio_pipeline_deinit(player->pipeline);

            player->pipeline = NULL;
            player->raw_read = NULL;
            player->pcm_decoder = NULL;
            player->i2s_stream_writer = NULL;
        }

        if (player->decoder)
        {
            opus_decoder_destroy(player->decoder);
            player->decoder = NULL;
        }

        if (player->pcm_buff)
        {
            audio_free(player->pcm_buff);
            player->pcm_buff = NULL;
        }

        player->callback = NULL;
        player->user_data = NULL;

        audio_free(player);
        player = NULL;
    }
}

void audio_player_add_data(audio_player_t player, uint8_t *data, size_t len)
{
    audio_data_t _data = {0};

    memcpy(_data.data, data, len);
    _data.len = len;

    if (xQueueSend(player->data_q, &_data, 0) != pdTRUE)
    {
        ESP_LOGE(TAG, "audio data send failed");
    }
}

void audio_player_pause(audio_player_t player)
{
    if (player->pipeline && player->sample_rate >= 0)
    {
        ESP_LOGI(TAG, "audio player pause");
        audio_pipeline_stop(player->pipeline);
        audio_pipeline_wait_for_stop(player->pipeline);
        audio_pipeline_terminate(player->pipeline);

        player->sample_rate = 0;
    }
}

void audio_player_run(audio_player_t player)
{
    if (player->pipeline && player->sample_rate == 0)
    {
        audio_pipeline_stop(player->pipeline);
        audio_pipeline_wait_for_stop(player->pipeline);
        audio_pipeline_terminate(player->pipeline);

        audio_pipeline_reset_ringbuffer(player->pipeline);
        audio_pipeline_reset_elements(player->pipeline);
        audio_pipeline_change_state(player->pipeline, AEL_STATE_INIT);

        ESP_ERROR_CHECK(audio_pipeline_run(player->pipeline));

        ESP_LOGI(TAG, "audio player run");
    }
}

void audio_player_set_samplerate(audio_player_t player, int sample_rate)
{
    if (sample_rate == player->sample_rate)
        return;

    audio_player_pause(player);

    player->frame_size = sample_rate / 1000 * player->channels * player->duration_ms;

    audio_element_set_music_info(player->pcm_decoder, sample_rate, player->channels, player->bits);
    i2s_stream_set_clk(player->i2s_stream_writer, sample_rate, player->bits, player->channels);

    if (player->pcm_buff)
    {
        player->pcm_buff_len = player->frame_size * sizeof(int16_t);
        player->pcm_buff = (int16_t *)audio_realloc(player->pcm_buff, player->pcm_buff_len);
    }

    if (player->decoder)
    {
        opus_decoder_init(player->decoder, sample_rate, player->channels);
    }

    audio_player_run(player);

    int old_sample_rate = player->sample_rate;
    player->sample_rate = sample_rate;
    ESP_LOGI(TAG, "Resampling audio from %d to %d", old_sample_rate, sample_rate);
}

void audio_player_clear_data(audio_player_t player)
{
    xQueueReset(player->data_q);
}

void audio_player_set_volume(audio_player_t player, int volume)
{
    if (player == NULL || player->i2s_stream_writer == NULL)
        return;

    i2s_alc_volume_set(player->i2s_stream_writer, volume);
    i2s_alc_volume_get(player->i2s_stream_writer, &player->volume);
}

void audio_player_set_callback(audio_player_t player, audio_player_callback cb)
{
    player->callback = cb;
}

void audio_player_set_user_data(audio_player_t player, void *user_data)
{
    player->user_data = user_data;
}

void *audio_player_get_user_data(audio_player_t player)
{
    return player->user_data;
}
